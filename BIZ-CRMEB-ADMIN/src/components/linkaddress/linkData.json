{"data": {"list": [{"id": 47, "cate_id": 5, "type": 1, "name": "商城首页", "url": "/pages/index/index", "param": " ", "example": "/pages/index/index", "status": 1, "sort": 999, "add_time": 1626837579}, {"id": 46, "cate_id": 5, "type": 1, "name": "商城分类", "url": "/pages/goods_cate/goods_cate", "param": " ", "example": "/pages/goods_cate/goods_cate", "status": 1, "sort": 998, "add_time": 1626837579}, {"id": 45, "cate_id": 5, "type": 1, "name": "购物车", "url": "/pages/order_addcart/order_addcart", "param": " ", "example": "/pages/order_addcart/order_addcart", "status": 1, "sort": 997, "add_time": 1626837579}, {"id": 43, "cate_id": 5, "type": 1, "name": "分类商品列表", "url": "/pages/goods_list/index", "param": "sid=1&title=测试分类名称", "example": "/pages/goods_list/index?sid=1&title=测试分类名称", "status": 1, "sort": 996, "add_time": 1626837579}, {"id": 11, "cate_id": 5, "type": 3, "name": "充值页面", "url": "/pages/users/user_payment/index", "param": " ", "example": "/pages/users/user_payment/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 15, "cate_id": 5, "type": 3, "name": "佣金排行", "url": "/pages/users/commission_rank/index", "param": " ", "example": "/pages/users/commission_rank/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 16, "cate_id": 5, "type": 3, "name": "推广人排行", "url": "/pages/users/promoter_rank/index", "param": " ", "example": "/pages/users/promoter_rank/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 17, "cate_id": 5, "type": 3, "name": "推广人订单", "url": "/pages/users/promoter-order/index", "param": " ", "example": "/pages/users/promoter-order/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 18, "cate_id": 5, "type": 2, "name": "推广人列表", "url": "/pages/users/promoter-list/index", "param": " ", "example": "/pages/users/promoter-list/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 19, "cate_id": 5, "type": 2, "name": "分销海报", "url": "/pages/users/user_spread_code/index", "param": " ", "example": "/pages/users/user_spread_code/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 20, "cate_id": 5, "type": 3, "name": "提现页面", "url": "/pages/users/user_cash/index", "param": " ", "example": "/pages/users/user_cash/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 21, "cate_id": 5, "type": 2, "name": "我的推广", "url": "/pages/users/user_spread_user/index", "param": " ", "example": "/pages/users/user_spread_user/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 23, "cate_id": 5, "type": 3, "name": "用户等级", "url": "/pages/users/user_vip/index", "param": " ", "example": "/pages/users/user_vip/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 24, "cate_id": 5, "type": 1, "name": "退款列表", "url": "/pages/users/user_return_list/index", "param": " ", "example": "/pages/users/user_return_list/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 26, "cate_id": 5, "type": 1, "name": "我的订单", "url": "/pages/users/order_list/index", "param": " ", "example": "/pages/users/order_list/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 28, "cate_id": 5, "type": 3, "name": "个人资料", "url": "/pages/users/user_info/index", "param": " ", "example": "/pages/users/user_info/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 29, "cate_id": 5, "type": 3, "name": "我的账户", "url": "/pages/users/user_money/index", "param": " ", "example": "/pages/users/user_money/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 30, "cate_id": 5, "type": 3, "name": "地址列表", "url": "/pages/users/user_address_list/index", "param": " ", "example": "/pages/users/user_address_list/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 35, "cate_id": 5, "type": 3, "name": "收藏页面", "url": "/pages/users/user_goods_collection/index", "param": " ", "example": "/pages/users/user_goods_collection/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 36, "cate_id": 5, "type": 3, "name": "签到页面", "url": "/pages/users/user_sgin/index", "param": " ", "example": "/pages/users/user_sgin/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 37, "cate_id": 5, "type": 1, "name": "精品推荐", "url": "/pages/activity/promotionList/index?type=1&name=精品推荐", "param": "type=类型ID，1=精品推荐，2=热门榜单，3=首发新品，4=促销单品", "example": "/activity/promotionList/index?type=1&name=精品推荐", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 38, "cate_id": 5, "type": 1, "name": "热门榜单", "url": "/pages/activity/promotionList/index?type=2&name=热门榜单", "param": "type=类型ID，1=精品推荐，2=热门榜单，3=首发新品，4=促销单品", "example": "/pages/activity/promotionList/index?type=2&name=热门榜单", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 39, "cate_id": 5, "type": 1, "name": "首发新品", "url": "/pages/activity/promotionList/index?type=3&name=首发新品", "param": "type=类型ID，1=精品推荐，2=热门榜单，3=首发新品，4=促销单品", "example": "/pages/activity/promotionList/index?type=3&name=首发新品", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 40, "cate_id": 5, "type": 1, "name": "促销单品", "url": "/pages/activity/promotionList/index?type=4&name=促销单品", "param": "type=类型ID，1=精品推荐，2=热门榜单，3=首发新品，4=促销单品", "example": "/pages/activity/promotionList/index?type=4&name=促销单品", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 42, "cate_id": 5, "type": 1, "name": "文章列表", "url": "/pages/news_list/index", "param": "id=文章ID", "example": "/pages/news_list/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 44, "cate_id": 5, "type": 3, "name": "个人中心", "url": "/pages/user/index", "param": " ", "example": "/pages/user/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 48, "cate_id": 5, "type": 3, "name": "佣金记录", "url": "/pages/users/user_spread_money/index?type=2", "param": " ", "example": "/pages/users/user_spread_money/index?type=2", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 49, "cate_id": 5, "type": 3, "name": "提现记录", "url": "/pages/users/user_spread_money/index?type=1", "param": " ", "example": "/pages/users/user_spread_money/index?type=1", "status": 1, "sort": 0, "add_time": 1626837579}]}}