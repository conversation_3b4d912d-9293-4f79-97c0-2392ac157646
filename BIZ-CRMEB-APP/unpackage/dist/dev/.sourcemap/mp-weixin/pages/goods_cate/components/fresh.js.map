{"version": 3, "sources": ["webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/f_goodList/index.vue?3342", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/f_goodList/index.vue?157c", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/f_goodList/index.vue?84b0", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/f_goodList/index.vue?77af", "uni-app:///components/f_goodList/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/f_goodList/index.vue?02e6", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/f_goodList/index.vue?d190", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/fresh.vue?351a", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/fresh.vue?6165", "uni-app:///main.js", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/fresh.vue?4e2e", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/fresh.vue?f5d4", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/fresh.vue?e0dd", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/fresh.vue?1b3b", "uni-app:///pages/goods_cate/components/fresh.vue"], "names": ["name", "props", "dataConfig", "type", "default", "tempArr", "is<PERSON>ogin", "data", "created", "mounted", "methods", "goDetail", "goCartDuo", "goCartDan", "CartNumDes", "CartNumAdd", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "computed", "components", "productWindow", "goodList", "cartList", "home", "showSlide", "productList", "navActive", "categoryTitle", "categoryErList", "tabLeft", "isWidth", "tabClick", "iSlong", "loading", "loadend", "loadTitle", "page", "limit", "cid", "sid", "isAuto", "isShowAuth", "attr", "cartAttr", "productAttr", "productSelect", "productValue", "attrValue", "storeName", "id", "cartData", "iScart", "cartCount", "totalPrice", "lengthCart", "theme", "that", "uni", "success", "subOrder", "list", "ids", "title", "getTotalPrice", "ChangeSubDel", "ChangeOneDel", "getCartLists", "<PERSON><PERSON><PERSON><PERSON>", "closeList", "getCartNum", "onMyEvent", "DefaultSelect", "value", "ChangeAttr", "attrVal", "indexn", "iptCartNum", "onLoadFun", "productslist", "ChangeCartNumDuo", "num", "ChangeCartList", "ChangeCartNum", "index", "goCatNum", "goCat", "productId", "cartNum", "isNew", "productAttrUnique", "setTimeout", "catch", "getIsLogin", "getAttrs", "attrName", "attrV<PERSON>ues", "isDel", "animationType", "animationDuration", "url", "openTap", "closeTap", "getAllCategory", "res", "item", "tapNav", "longClick", "navSwitch"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC6BnnB;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;EACA;EACAG;IACA,QAEA;EACA;EACAC;EACAC;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACrEA;AAAA;AAAA;AAAA;AAA0oC,CAAgB,gnCAAG,EAAC,C;;;;;;;;;;;ACA9pC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAA0oC,CAAgB,gnCAAG,EAAC,C;;;;;;;;;;;ACA9pC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;ACNL;AAGA;AACA;AAHA;AACAC,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACyD;AACL;AACc;;;AAGlE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACqInnB;AAMA;AAOA;AAGA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;EACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAzB;IACA0B;MACAxB;MACAC;IACA;EACA;EACAG;IACA;MACAqB;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACA9B;MACA+B;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;QACA5B;QACA6B;MACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAlD;IACA;MACA;MACA;IACA;IACA;IACA;IACAmD;IACA;IACAC;MACAC;QACAF;MACA;IACA;EACA;EACAjD;IACA;IACAoD;MACA;QAAAC;QAAAC;MACA;QACA;UACA;YACA;UACA;QACA;QACA;QACAL;MACA;QACA;UACAM;QACA;MACA;IACA;IACA;IACAC;MACA;QAAAH;QAAAP;MACAO;QACA;UACAP;QACA;MACA;MACAG;IACA;IACAQ;MACA;QAAAJ;QAAAC;MACAD;QACAC;MACA;MACA;QACAL;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAS;MACA;QAAAL;MACA;QACAA;QACA;UACAJ;UACAA;UACAA;UACAA;UACAA;QACA;QAAA;QACAA;MACA;IACA;IACAU;MACA;MACA;QACA9B;QACAC;QACA8B;MACA;MACA;QACAX;QACA;UACAA;QACA;UACAA;QACA;QACAA;MACA;IACA;IACAY;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;QACAb;MACA;IACA;IAGAc;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;QACA;UACAC;UACA;QACA;MACA;MACA;QACA;MACA;MACA;MACA;MAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACAC;MACA,uGACAC;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;IACA;IACAC;IACA;IACAC;MACA;MACA;MACA;MACAtB;MACAA;MACA;QACApB;QACAC;QACArC;QACAsC;MACA;QACA;UACAJ;QACAsB;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;QACAA,sBACAA;MACA;IACA;IACA;IACAuB;MACA;MACA;MACA;MACA;MACA,kEACAlC;MACA;MACA;MACA;MACA;MACA;QACAmC;QACA;UACA;UACA;QACA;MACA;QACAA;QACA;UACA;UACA;QACA;MACA;IAEA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;UACAC;QACA;UACAA;UACA;YACA;YACA;UACA;QACA;MACA;QACAA;QACA;UACA;UACA;QACA;QACA;UACA;YACA;YACA;YACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;QACAxC;MACA;MACA;QACA;QACAW;MACA;QACA,gDACAA;MACA;MACA;MACA,0DACA;MACA;MACA,IACAA,gCACAX,6BACAW,sBAEA;QACAM;MACA;MACA;QACA;UACAwB;UACAC;UACAC;UACAC,2DACAjC;QACA;QACA;UACAA;UACAA;UACAA;YACAM;YACAJ;cACAgC;gBACAlC;gBACAA;cACA;YACA;UACA;QACA,GACAmC;UACAnC;UACA;YACAM;UACA;QACA;MACA;QACA;MACA;IACA;IACArD;MACA;QACA;MACA;QACAgD;UACAK;QACA;QACA;QACA;QACA;MACA;IACA;IACA8B;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACApC;QACAD;QACAA;QACA;UACA;YACAsC;YACAC;YACA9C;YACA+C;YACAV;YACAtF;UACA;QACA;QACA;QACA;QACAwD;MACA;IACA;IACA;IACAhD;MACA;QACA;MACA;QACA;UACAiD;YACAwC;YACAC;YACAC;UACA;QACA;MACA;;IAIAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;QACAC;UACA;YACAC;cACAvD;cACApD;YACA;UACA;QACA;QACA;QACA2D;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAiD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA", "file": "pages/goods_cate/components/fresh.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=32344772&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/f_goodList/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=32344772&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"goodsList\">\r\n\t\t<view class=\"item acea-row row-between-wrapper\" v-for=\"(item,index) in tempArr\" :key='index' @click=\"goDetail(item)\">\r\n\t\t\t<view class=\"pic\">\r\n\t\t\t\t<image :src=\"item.image\" mode=\"\"></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"pictxt\">\r\n\t\t\t\t<view class=\"text line2\">{{item.storeName}}</view>\r\n\t\t\t\t<view class=\"bottom acea-row row-between-wrapper\">\r\n\t\t\t\t\t<view class=\"money\">\r\n\t\t\t\t\t\t<text class=\"sign\">￥</text>{{item.price}} \r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"item.stock>0\">\r\n\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t<!-- 多规格 -->\r\n\t\t\t\t\t\t\t<view class=\"bnt\" @click.stop=\"goCartDuo(item)\">\r\n\t\t\t\t\t\t\t\t选规格\r\n\t\t\t\t\t\t\t\t<view class=\"num\" v-if=\"item.cartNum\">{{item.cartNum}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"bnt end\" v-else>已售罄</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tname: 'd_goodList',\r\n\t\tprops: {\r\n\t\t\tdataConfig: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: () => {}\r\n\t\t\t},\r\n\t\t\ttempArr:{\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault:[]\r\n\t\t\t},\r\n\t\t\tisLogin:{\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault:false\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t\r\n\t\t\t};\r\n\t\t},\r\n\t\tcreated() {},\r\n\t\tmounted() {},\r\n\t\tmethods: {\r\n\t\t\tgoDetail(item){\r\n\t\t\t\tthis.$emit('detail',item);\r\n\t\t\t},\r\n\t\t\tgoCartDuo(item){\r\n\t\t\t\tthis.$emit('gocartduo',item);\r\n\t\t\t},\r\n\t\t\tgoCartDan(item,index){\r\n\t\t\t\tthis.$emit('gocartdan',item,index);\r\n\t\t\t},\r\n\t\t\tCartNumDes(index,item){\r\n\t\t\t\tthis.$emit('ChangeCartNumDan', false,index,item);\r\n\t\t\t},\r\n\t\t\tCartNumAdd(index,item){\r\n\t\t\t\tthis.$emit('ChangeCartNumDan', true,index,item);\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.goodsList{\r\n\t\tpadding: 0 30rpx;\r\n\t\t.item{\r\n\t\t\twidth: 100%;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tmargin-bottom: 63rpx;\r\n\t\t\t.pic{\r\n\t\t\t\twidth: 140rpx;\r\n\t\t\t\theight: 140rpx;\r\n\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tborder-radius: 22rpx;\r\n\t\t\t\timage{\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t\tborder-radius: 22rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.pictxt{\r\n\t\t\t\twidth: 372rpx;\r\n\t\t\t\t.text{\r\n\t\t\t\t\tfont-size:26rpx;\r\n\t\t\t\t\tfont-family:PingFang SC;\r\n\t\t\t\t\tfont-weight:500;\r\n\t\t\t\t\tcolor: $crmeb-font-color;\r\n\t\t\t\t}\r\n\t\t\t\t.bottom{\r\n\t\t\t\t\tmargin-top: 22rpx;\r\n\t\t\t\t\t.money{\r\n\t\t\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\t\t\tfont-weight: 800;\r\n\t\t\t\t\t\twidth: 212rpx;\r\n\t\t\t\t\t\t@include price_color(theme);\r\n\t\t\t\t\t\t.sign{\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.otPrice{\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\tfont-family: PingFang SC;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tline-height: 24rpx;\r\n\t\t\t\t\t\tpadding-left: 14rpx;\r\n\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.cart{\r\n\t\t\t\t\t\theight: 46rpx;\r\n\t\t\t\t\t\t.pictrue{\r\n\t\t\t\t\t\t\tcolor: $crmeb-theme-color;\r\n\t\t\t\t\t\t\tfont-size:46rpx;\r\n\t\t\t\t\t\t\twidth: 46rpx;\r\n\t\t\t\t\t\t\theight: 46rpx;\r\n\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t\tline-height: 46rpx;\r\n\t\t\t\t\t\t\t&.icon-jiahao{\r\n\t\t\t\t\t\t\t\t color: $crmeb-theme-color;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t.num{\r\n\t\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t\t\tcolor: $crmeb-font-color;\r\n\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t\twidth: 60rpx;\r\n\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.icon-gouwuche6{\r\n\t\t\t\t\t\twidth: 46rpx;\r\n\t\t\t\t\t\theight: 46rpx;\r\n\t\t\t\t\t\tbackground-color: $crmeb-theme-color;\r\n\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\tcolor: $crmeb-font-color-white;\r\n\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.bnt{\r\n\t\t\t\t\t\tpadding: 0 20rpx;\r\n\t\t\t\t\t\theight: 46rpx;\r\n\t\t\t\t\t\tline-height: 46rpx;\r\n\t\t\t\t\t\t@include main_bg_color(theme);\r\n\t\t\t\t\t\tborder-radius:23rpx;\r\n\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\tcolor: $crmeb-font-color-white;\r\n\t\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\t&.end{\r\n\t\t\t\t\t\t\tbackground:$crmeb-font-color-disable;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t.num{\r\n\t\t\t\t\t\t\tmin-width: 12rpx;\r\n\t\t\t\t\t\t\t@include main_color(theme);\r\n\t\t\t\t\t\t\t@include coupons_border_color(theme);\r\n\t\t\t\t\t\t\tbackground: #fff;\r\n\t\t\t\t\t\t\tborder-radius: 15px;\r\n\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\tright: -13rpx;\r\n\t\t\t\t\t\t\ttop: -11rpx;\r\n\t\t\t\t\t\t\tfont-size: 16rpx;\r\n\t\t\t\t\t\t\tpadding: 0 11rpx;\r\n\t\t\t\t\t\t\theight: 32rpx;\r\n\t\t\t\t\t\t\tline-height: 32rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294180260\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fresh.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fresh.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294179561\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/goods_cate/components/fresh.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./fresh.vue?vue&type=template&id=743fd7a3&%3Adata-theme=theme&\"\nvar renderjs\nimport script from \"./fresh.vue?vue&type=script&lang=js&\"\nexport * from \"./fresh.vue?vue&type=script&lang=js&\"\nimport style0 from \"./fresh.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/goods_cate/components/fresh.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fresh.vue?vue&type=template&id=743fd7a3&%3Adata-theme=theme&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.cartData.cartList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fresh.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fresh.vue?vue&type=script&lang=js&\"", "<template :data-theme=\"theme\">\r\n\t<view class=\"goodCate1\">\r\n\t\t<view class=\"header acea-row row-center-wrapper\">\r\n\t\t\t<navigator url='/pages/index/index' class=\"pageIndex acea-row row-center-wrapper\" hover-class=\"none\" open-type=\"switchTab\">\r\n\t\t\t\t<text class=\"iconfont icon-fanhuishouye\"></text>\r\n\t\t\t</navigator>\r\n\t\t\t<navigator url=\"/pages/goods_search/index\" class=\"search acea-row row-middle\" hover-class=\"none\">\r\n\t\t\t\t<text class=\"iconfont icon-sousuo5\"></text>\r\n\t\t\t\t搜索商品\r\n\t\t\t</navigator>\r\n\t\t</view>\r\n\t\t<view class=\"conter\"  v-if=\"showSlide\">\r\n\t\t\t<view class='aside'>\r\n\t\t\t\t<view class='item acea-row row-center-wrapper' :class='index==navActive?\"on\":\"\"' v-for=\"(item,index) in productList\"\r\n\t\t\t\t :key=\"index\" @click=\"tapNav(index,item)\">\r\n\t\t\t\t\t<text>{{item.name}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"wrapper\">\r\n\t\t\t\t<view class=\"bgcolor\" v-if=\"iSlong\">\r\n\t\t\t\t\t<view class=\"longTab acea-row row-middle\">\r\n\t\t\t\t\t\t<scroll-view scroll-x=\"true\" style=\"white-space: nowrap; display: flex;height:44rpx;\" scroll-with-animation\r\n\t\t\t\t\t\t :scroll-left=\"tabLeft\" show-scrollbar=\"true\">\r\n\t\t\t\t\t\t\t<view class=\"longItem\" :style='\"width:\"+isWidth+\"px\"' :class=\"index===tabClick?'click':''\" v-for=\"(item,index) in categoryErList\"\r\n\t\t\t\t\t\t\t :key=\"index\" @click=\"longClick(index,item)\">{{item.name}}</view>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"openList\" @click=\"openTap\"><text class=\"iconfont icon-xiangxia\"></text></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-else>\r\n\t\t\t\t\t<view class=\"downTab\">\r\n\t\t\t\t\t\t<view class=\"title acea-row row-between-wrapper\">\r\n\t\t\t\t\t\t\t<view>{{categoryTitle}}</view>\r\n\t\t\t\t\t\t\t<view class=\"closeList\" @click=\"closeTap\"><text class=\"iconfont icon-xiangxia\"></text></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"children\">\r\n\t\t\t\t\t\t\t<view class=\"acea-row row-middle\">\r\n\t\t\t\t\t\t\t\t<view class=\"item line1\" :class=\"index===tabClick?'click':''\" v-for=\"(item,index) in categoryErList\" :key=\"index\"\r\n\t\t\t\t\t\t\t\t @click=\"longClick(index,item)\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"mask\" @click=\"closeTap\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<goodList :tempArr=\"tempArr\" :isLogin=\"isLogin\" @gocartduo=\"goCartDuo\" @gocartdan=\"goCartDan\" @ChangeCartNumDan=\"ChangeCartNumDan\"\r\n\t\t\t\t @detail=\"goDetail\"></goodList>\r\n\t\t\t\t<view class='loadingicon acea-row row-center-wrapper mb-2'>\r\n\t\t\t\t\t<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>{{loadTitle}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"conter\"  v-else>\r\n\t\t\t<view class=\"hide_slide\">\r\n\t\t\t\t<view class=\"bgcolor\" v-if=\"iSlong\">\r\n\t\t\t\t\t<view class=\"hongTab acea-row row-middle\">\r\n\t\t\t\t\t\t<scroll-view scroll-x=\"true\" style=\"white-space: nowrap; display: flex;height:44rpx;\" scroll-with-animation\r\n\t\t\t\t\t\t :scroll-left=\"tabLeft\" show-scrollbar=\"true\">\r\n\t\t\t\t\t\t\t<view class=\"longItem\" :style='\"width:\"+isWidth+\"px\"' :class=\"index===tabClick?'click':''\" v-for=\"(item,index) in productList\"\r\n\t\t\t\t\t\t\t :key=\"index\" @click=\"navSwitch(index,item)\">{{item.name}}</view>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"openList\" @click=\"openTap\"><text class=\"iconfont icon-xiangxia\"></text></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-else>\r\n\t\t\t\t\t<view class=\"hownTab\">\r\n\t\t\t\t\t\t<view class=\"title acea-row row-between-wrapper\">\r\n\t\t\t\t\t\t\t<view>{{categoryTitle}}</view>\r\n\t\t\t\t\t\t\t<view class=\"closeList\" @click=\"closeTap\"><text class=\"iconfont icon-xiangxia\"></text></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"children\">\r\n\t\t\t\t\t\t\t<view class=\"acea-row row-middle\">\r\n\t\t\t\t\t\t\t\t<view class=\"item line1\" :class=\"index===tabClick?'click':''\" v-for=\"(item,index) in productList\" :key=\"index\"\r\n\t\t\t\t\t\t\t\t @click=\"navSwitch(index,item)\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"mask\" @click=\"closeTap\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"list_prod\">\r\n\t\t\t\t\t<view class=\"item acea-row row-between-wrapper\" v-for=\"(item,index) in tempArr\" :key='index' @click=\"goDetail(item)\">\r\n\t\t\t\t\t\t<view class=\"pic\">\r\n\t\t\t\t\t\t\t<image :src=\"item.image\" mode=\"\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"pictxt\">\r\n\t\t\t\t\t\t\t<view class=\"text line2\">{{item.storeName}}</view>\r\n\t\t\t\t\t\t\t<view class=\"bottom acea-row row-between-wrapper\">\r\n\t\t\t\t\t\t\t\t<view class=\"money\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"sign\">￥</text>{{item.price}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view v-if=\"item.stock>0\">\r\n\t\t\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t\t\t<!-- 多规格 -->\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"bnt\" @click.stop=\"goCartDuo(item)\">\r\n\t\t\t\t\t\t\t\t\t\t\t选规格\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"num\" v-if=\"item.cartNum\">{{item.cartNum}}</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"bnt end\" v-else>已售罄</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t<text class=\"otPrice\" v-if=\"item.sales\">已售{{item.sales}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='loadingicon acea-row row-center-wrapper mb-2'>\r\n\t\t\t\t\t<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>{{loadTitle}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"footer acea-row row-between-wrapper\">\r\n\t\t\t<view class=\"cart_theme acea-row row-center-wrapper\" v-if=\"cartData.cartList.length\">\r\n\t\t\t\t<view class=\"iconfont icon-gouwuche-yangshi1 hava\" @click=\"getCartLists(0)\"></view>\r\n\t\t\t\t<view class=\"num\">{{cartCount}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"noCart\" v-else>\r\n\t\t\t\t<view class=\"iconfont icon-gouwuche-yangshi1 no_have\"></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"money acea-row row-middle\">\r\n\t\t\t\t<view>￥<text class=\"num\">{{totalPrice}}</text></view>\r\n\t\t\t\t<view class=\"bnt gray_bg\" :class=\"{ 'main_bg': cartCount > 0}\"  @click=\"subOrder\">去结算</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- <home></home> -->\r\n\t\t<cartList :cartData=\"cartData\" @closeList=\"closeList\" @ChangeCartNumDan=\"ChangeCartList\" @ChangeSubDel=\"ChangeSubDel\"\r\n\t\t @ChangeOneDel=\"ChangeOneDel\"></cartList>\r\n\t\t<productWindow :attr=\"attr\" :isShow='1' :iSplus='1' :iScart='1' @myevent=\"onMyEvent\" @ChangeAttr=\"ChangeAttr\"\r\n\t\t @ChangeCartNum=\"ChangeCartNumDuo\" @attrVal=\"attrVal\" @iptCartNum=\"iptCartNum\" @goCat=\"goCatNum\" id='product-window'></productWindow>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tgetCategoryList,\r\n\t\tgetProductslist,\r\n\t\tgetAttr,\r\n\t\tpostCartAdd\r\n\t} from '@/api/store.js';\r\n\timport {\r\n\t\tgetCartList,\r\n\t\tgetCartCounts,\r\n\t\tcartDel,\r\n\t\tchangeCartNum,\r\n\t} from '@/api/order.js';\r\n\timport productWindow from '@/components/productWindow';\r\n\timport goodList from '@/components/f_goodList';\r\n\timport cartList from '@/components/cartList';\r\n\timport home from '@/components/home';\r\n\timport {mapGetters} from 'vuex';\r\n\timport {goShopDetail} from '@/libs/order.js';\r\n\timport {toLogin} from '@/libs/login.js';\r\n\timport animationType from '@/utils/animationType.js'\r\n\texport default {\r\n\t\tcomputed: mapGetters(['isLogin', 'uid']),\r\n\t\tcomponents: {\r\n\t\t\tproductWindow,\r\n\t\t\tgoodList,\r\n\t\t\tcartList,\r\n\t\t\thome\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tshowSlide: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault:true \r\n\t\t\t},\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tproductList: [],\r\n\t\t\t\tnavActive: 0,\r\n\t\t\t\tcategoryTitle: '',\r\n\t\t\t\tcategoryErList: [],\r\n\t\t\t\ttabLeft: 0,\r\n\t\t\t\tisWidth: 0, //每个导航栏占位\r\n\t\t\t\ttabClick: 0, //导航栏被点击\r\n\t\t\t\tiSlong: true,\r\n\t\t\t\ttempArr: [],\r\n\t\t\t\tloading: false,\r\n\t\t\t\tloadend: false,\r\n\t\t\t\tloadTitle: '加载更多',\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tlimit: 10,\r\n\t\t\t\tcid: 0, //一级分类\r\n\t\t\t\tsid: 0, //二级分类\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false, //是否隐藏授权\r\n\t\t\t\tattr: {\r\n\t\t\t\t\tcartAttr: false,\r\n\t\t\t\t\tproductAttr: [],\r\n\t\t\t\t\tproductSelect: {}\r\n\t\t\t\t},\r\n\t\t\t\tproductValue: [],\r\n\t\t\t\tattrValue: '', //已选属性\r\n\t\t\t\tstoreName: '', //多属性产品名称\r\n\t\t\t\tid: 0,\r\n\t\t\t\tcartData: {\r\n\t\t\t\t\tcartList: [],\r\n\t\t\t\t\tiScart: false\r\n\t\t\t\t},\r\n\t\t\t\tcartCount: 0,\r\n\t\t\t\ttotalPrice: 0.00,\r\n\t\t\t\tlengthCart: 0,\r\n\t\t\t\ttheme:'theme1'\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated(){\r\n\t\t\tif(this.isLogin){\r\n\t\t\t\tthis.getCartNum();\r\n\t\t\t\tthis.getCartLists(1);\r\n\t\t\t}\r\n\t\t\tthis.getAllCategory();\r\n\t\t\tlet that = this;\r\n\t\t\tthat.lengthCart = that.cartData.cartList;\r\n\t\t\t// 获取设备宽度\r\n\t\t\tuni.getSystemInfo({\r\n\t\t\t\tsuccess(e) {\r\n\t\t\t\t\tthat.isWidth = e.windowWidth / 5\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 生成订单；\r\n\t\t\tsubOrder: function() {\r\n\t\t\t\tlet that = this,list = that.cartData.cartList,ids = [];\r\n\t\t\t\tif(list.length){\r\n\t\t\t\t\tlet shoppingCartId = list.map(item => {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\t\"shoppingCartId\": Number(item.id)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.$Order.getPreOrder(\"shoppingCart\", shoppingCartId);\r\n\t\t\t\t\tthat.cartData.iScart = false;\r\n\t\t\t\t}else{\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: '请选择产品'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 计算总价；\r\n\t\t\tgetTotalPrice: function(){\r\n\t\t\t\tlet that = this,list = that.cartData.cartList,totalPrice = 0.00;\r\n\t\t\t\tlist.forEach(item=>{\r\n\t\t\t\t\tif(item.attrStatus ){\r\n\t\t\t\t\t\ttotalPrice = that.$util.$h.Add(totalPrice, that.$util.$h.Mul(item.cartNum, item.vipPrice ? item.vipPrice : item.price));\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tthat.$set(that,'totalPrice',totalPrice);\r\n\t\t\t},\r\n\t\t\tChangeSubDel: function(event) {\r\n\t\t\t\tlet that = this,list = that.cartData.cartList,ids = [];\r\n\t\t\t\tlist.forEach(item=>{\r\n\t\t\t\t\tids.push(item.id)\r\n\t\t\t\t});\r\n\t\t\t\tcartDel(ids.join(\",\")).then(res=>{\r\n\t\t\t\t\tthat.$set(that.cartData,'cartList',[]);\r\n\t\t\t\t\tthat.cartData.iScart = false;\r\n\t\t\t\t\tthat.totalPrice = 0.00;\r\n\t\t\t\t\tthat.page = 1;\r\n\t\t\t\t\tthat.loadend = false;\r\n\t\t\t\t\tthat.tempArr = [];\r\n\t\t\t\t\tthat.productslist();\r\n\t\t\t\t\tthat.getCartNum();\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tChangeOneDel:function(id,index){\r\n\t\t\t\tlet that = this,list = that.cartData.cartList;\r\n\t\t\t\tcartDel(id.toString()).then(res=>{\r\n\t\t\t\t\tlist.splice(index,1);\r\n\t\t\t\t\tif(!list.length){\r\n\t\t\t\t\t\tthat.cartData.iScart = false;\r\n\t\t\t\t\t\tthat.page = 1;\r\n\t\t\t\t\t\tthat.loadend = false;\r\n\t\t\t\t\t\tthat.tempArr = [];\r\n\t\t\t\t\t\tthat.productslist();\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthat.getCartNum();\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetCartLists(iSshow) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tpage: 1,\r\n\t\t\t\t\tlimit: that.cartCount,\r\n\t\t\t\t\tisValid: true\r\n\t\t\t\t};\r\n\t\t\t\tgetCartList(data).then(res => {\r\n\t\t\t\t\tthat.$set(that.cartData, 'cartList', res.data.list);\r\n\t\t\t\t\tif(res.data.list.length){\r\n\t\t\t\t\t\tthat.$set(that.cartData, 'iScart', iSshow?false:!that.cartData.iScart);\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.$set(that.cartData, 'iScart', false);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.getTotalPrice();\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tcloseList(e) {\r\n\t\t\t\tthis.$set(this.cartData, 'iScart', e);\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.loadend = false;\r\n\t\t\t\tthis.tempArr = [];\r\n\t\t\t\tthis.productslist();\r\n\t\t\t},\r\n\t\t\tgetCartNum: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetCartCounts(true, 'sum').then(res => {\r\n\t\t\t\t\tthat.$set(that,'cartCount',res.data.count);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\r\n\t\t\tonMyEvent: function() {\r\n\t\t\t\tthis.$set(this.attr, 'cartAttr', false);\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 默认选中属性\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tDefaultSelect: function() {\r\n\t\t\t\tlet productAttr = this.attr.productAttr;\r\n\t\t\t\tlet value = [];\r\n\t\t\t\tfor (let key in this.productValue) {\r\n\t\t\t\t\tif (this.productValue[key].stock > 0) {\r\n\t\t\t\t\t\tvalue = this.attr.productAttr.length ? key.split(\",\") : [];\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tfor (let i = 0; i < productAttr.length; i++) {\r\n\t\t\t\t\tthis.$set(productAttr[i], \"index\", value[i]);\r\n\t\t\t\t}\r\n\t\t\t\t//sort();排序函数:数字-英文-汉字；\r\n\t\t\t\tlet productSelect = this.productValue[value.join(\",\")];\r\n\t\t\t\t\r\n\t\t\t\tif (productSelect && productAttr.length) {\r\n\t\t\t\t\tthis.$set(this.attr.productSelect,\"storeName\",this.storeName);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"image\", productSelect.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"price\", productSelect.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"stock\", productSelect.stock);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"unique\", productSelect.id);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"vipPrice\", productSelect.vipPrice);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", value.join(\",\"));\r\n\t\t\t\t} else if (!productSelect && productAttr.length) {\r\n\t\t\t\t\tthis.$set(this.attr.productSelect,\"storeName\",this.storeName);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"image\", this.storeInfo.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"price\", this.storeInfo.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"stock\", 0);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"unique\", \"\");\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 0);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", \"\");\r\n\t\t\t\t} else if (!productSelect && !productAttr.length) {\r\n\t\t\t\t\tthis.$set(this.attr.productSelect,\"storeName\",this.storeName);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"image\", this.storeInfo.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"price\", this.storeInfo.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"stock\", this.storeInfo.stock);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect,\"unique\",this.storeInfo.unique || \"\");\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", \"\");\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 属性变动赋值\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tChangeAttr: function(res) {\r\n\t\t\t\tlet productSelect = this.productValue[res];\r\n\t\t\t\tif (productSelect) {\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"image\", productSelect.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"price\", productSelect.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"stock\", productSelect.stock);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"unique\", productSelect.id);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"vipPrice\", productSelect.vipPrice);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", res);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"image\", this.productInfo.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"price\", this.productInfo.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"stock\", 0);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"unique\", this.productInfo.id);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"vipPrice\", this.productInfo.vipPrice);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", \"\");\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tattrVal(val) {\r\n\t\t\t\tthis.$set(this.attr.productAttr[val.indexw], 'index', this.attr.productAttr[val.indexw].attrValues[val\r\n\t\t\t\t\t.indexn]);\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 购物车手动填写\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tiptCartNum: function(e) {\r\n\t\t\t\tthis.$set(this.attr.productSelect, 'cart_num', e);\r\n\t\t\t},\r\n\t\t\tonLoadFun() {},\r\n\t\t\t// 产品列表\r\n\t\t\tproductslist: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (that.loadend) return; //如果返回列表长度小于请求分页长度，就让他为true,就不继续请求了\r\n\t\t\t\tif (that.loading) return;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tthat.loadTitle = '';\r\n\t\t\t\tgetProductslist({\r\n\t\t\t\t\tpage: that.page,\r\n\t\t\t\t\tlimit: that.limit,\r\n\t\t\t\t\ttype: 1,\r\n\t\t\t\t\tcid: that.sid,\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tlet list = res.data.list,\r\n\t\t\t\t\t\tloadend = list.length < that.limit;\r\n\t\t\t\t\tthat.tempArr = that.$util.SplitArray(list, that.tempArr);\r\n\t\t\t\t\tthat.$set(that, 'tempArr', that.tempArr);\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.loadend = loadend;\r\n\t\t\t\t\tthat.loadTitle = loadend ? \"😕人家是有底线的~~\" : \"加载更多\";\r\n\t\t\t\t\tthat.page = that.page + 1;\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tthat.loading = false,\r\n\t\t\t\t\t\tthat.loadTitle = '加载更多'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 改变多属性购物车\r\n\t\t\tChangeCartNumDuo(changeValue) {\r\n\t\t\t\t//changeValue:是否 加|减\r\n\t\t\t\t//获取当前变动属性\r\n\t\t\t\tlet productSelect = this.productValue[this.attrValue];\r\n\t\t\t\t//如果没有属性,赋值给商品默认库存\r\n\t\t\t\tif (productSelect === undefined && !this.attr.productAttr.length)\r\n\t\t\t\t\tproductSelect = this.attr.productSelect;\r\n\t\t\t\t//无属性值即库存为0；不存在加减；\r\n\t\t\t\tif (productSelect === undefined) return;\r\n\t\t\t\tlet stock = productSelect.stock || 0;\r\n\t\t\t\tlet num = this.attr.productSelect;\r\n\t\t\t\tif (changeValue) {\r\n\t\t\t\t\tnum.cart_num++;\r\n\t\t\t\t\tif (num.cart_num > stock) {\r\n\t\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", stock);\r\n\t\t\t\t\t\tthis.$set(this, \"cart_num\", stock);\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tnum.cart_num--;\r\n\t\t\t\t\tif (num.cart_num < 1) {\r\n\t\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\t\tthis.$set(this, \"cart_num\", 1);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\t// 已经加入购物车时的购物加减；\r\n\t\t\tChangeCartList(changeValue, index) {\r\n\t\t\t\tlet list = this.cartData.cartList;\r\n\t\t\t\tlet num = list[index];\r\n\t\t\t\tlet stock = list[index].stock;\r\n\t\t\t\tthis.ChangeCartNum(changeValue, num, stock, 0, num.productId,index,1);\r\n\t\t\t\tif(!list.length){\r\n\t\t\t\t\tthis.cartData.iScart = false;\r\n\t\t\t\t\tthis.page = 1;\r\n\t\t\t\t\tthis.loadend = false;\r\n\t\t\t\t\tthis.tempArr = [];\r\n\t\t\t\t\tthis.productslist();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 购物车加减计算函数\r\n\t\t\tChangeCartNum: function(changeValue,index) {\r\n\t\t\t\tif (changeValue) {\r\n\t\t\t\t\tif (index.cartNum >= index.stock) {\r\n\t\t\t\t\t\tindex.cartNum = index.stock;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tindex.cartNum++;\r\n\t\t\t\t\t\tchangeCartNum(index.id, index.cartNum).then(res => {\r\n\t\t\t\t\t\t\tthis.getCartNum(true);\r\n\t\t\t\t\t\t\tthis.getTotalPrice();\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tindex.cartNum--;\r\n\t\t\t\t\tchangeCartNum(index.id, index.cartNum).then(res => {\r\n\t\t\t\t\t\tthis.getCartNum(true);\r\n\t\t\t\t\t\tthis.getTotalPrice();\r\n\t\t\t\t\t});\r\n\t\t\t\t\tif(index.cartNum == 0){\r\n\t\t\t\t\t\tcartDel(index.id).then(res=>{\r\n\t\t\t\t\t\t\tthis.getCartLists(1);\r\n\t\t\t\t\t\t\tthis.getTotalPrice();\r\n\t\t\t\t\t\t\tthis.productslist();\r\n\t\t\t\t\t\t\tthis.getCartNum();\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 多规格加入购物车；\r\n\t\t\tgoCatNum() {\r\n\t\t\t\tthis.goCat(1);\r\n\t\t\t},\r\n\t\t\t/*\r\n\t\t\t * 加入购物车\r\n\t\t\t */\r\n\t\t\tgoCat: function(num) {\r\n\t\t\t\tlet that = this,\r\n\t\t\t\t\tproductSelect = that.productValue[this.attrValue];\r\n\t\t\t\t//打开属性\r\n\t\t\t\tif (that.attrValue) {\r\n\t\t\t\t\t//默认选中了属性，但是没有打开过属性弹窗还是自动打开让用户查看默认选中的属性\r\n\t\t\t\t\tthat.attr.cartAttr = !that.isOpen ? true : false;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (that.isOpen) that.attr.cartAttr = true;\r\n\t\t\t\t\telse that.attr.cartAttr = !that.attr.cartAttr;\r\n\t\t\t\t}\r\n\t\t\t\t//只有关闭属性弹窗时进行加入购物车\r\n\t\t\t\tif (that.attr.cartAttr === true && that.isOpen === false)\r\n\t\t\t\t\treturn (that.isOpen = true);\r\n\t\t\t\t//如果有属性,没有选择,提示用户选择\r\n\t\t\t\tif (\r\n\t\t\t\t\tthat.attr.productAttr.length &&\r\n\t\t\t\t\tproductSelect.stock === 0 &&\r\n\t\t\t\t\tthat.isOpen === true\r\n\t\t\t\t)\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: \"产品库存不足，请选择其它\"\r\n\t\t\t\t\t});\r\n\t\t\t\tif (num === 1) {\r\n\t\t\t\t\tlet q = {\r\n\t\t\t\t\t\tproductId: parseFloat(that.id),\r\n\t\t\t\t\t\tcartNum: parseFloat(that.attr.productSelect.cart_num),\r\n\t\t\t\t\t\tisNew: false,\r\n\t\t\t\t\t\tproductAttrUnique: that.attr.productSelect !== undefined ?\r\n\t\t\t\t\t\t\tthat.attr.productSelect.unique : that.productInfo.id\r\n\t\t\t\t\t};\r\n\t\t\t\t\tpostCartAdd(q).then(function(res) {\r\n\t\t\t\t\t\t\tthat.isOpen = false;\r\n\t\t\t\t\t\t\tthat.attr.cartAttr = false;\r\n\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: \"添加购物车成功\",\r\n\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\t\t\t\t\t\tthat.getCartNum(true);\r\n\t\t\t\t\t\t\t\t\t\tthat.getCartLists(1);\r\n\t\t\t\t\t\t\t\t\t},100)\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(res => {\r\n\t\t\t\t\t\t\tthat.isOpen = false;\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: res\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.getPreOrder();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgoCartDuo(item) {\r\n\t\t\t\tif (!this.isLogin) {\r\n\t\t\t\t\tthis.getIsLogin();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\ttitle: '加载中'\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.storeName = item.storeName;\r\n\t\t\t\t\tthis.getAttrs(item.id,item.storeName);\r\n\t\t\t\t\tthis.$set(this, 'id', item.id);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetIsLogin() {\r\n\t\t\t\ttoLogin();\r\n\t\t\t},\r\n\t\t\t// 商品详情接口；\r\n\t\t\tgetAttrs(id) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetAttr(id).then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthat.$set(that.attr, 'productAttr', res.data.productAttr);\r\n\t\t\t\t\tthat.$set(that, 'productValue', res.data.productValue);\r\n\t\t\t\t\tlet productAttr = that.attr.productAttr.map(item => {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tattrName : item.attrName,\r\n\t\t\t\t\t\tattrValues: item.attrValues.split(','),\r\n\t\t\t\t\t\tid:item.id,\r\n\t\t\t\t\t\tisDel:item.isDel,\r\n\t\t\t\t\t\tproductId:item.productId,\r\n\t\t\t\t\t\ttype:item.type\r\n\t\t\t\t\t }\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.$set(that.attr,'productAttr',productAttr);\r\n\t\t\t\t\tthis.$set(that.attr, 'cartAttr', true);\r\n\t\t\t\t\tthat.DefaultSelect();\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 去详情页\r\n\t\t\tgoDetail(item) {\r\n\t\t\t\tif (!this.isLogin) {\r\n\t\t\t\t\ttoLogin();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tgoShopDetail(item, this.uid).then(res => {\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\tanimationType: animationType.type,\r\t\t\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\t\t\turl: `/pages/goods_details/index?id=${item.id}`\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\r\n\t\t\topenTap() {\r\n\t\t\t\tthis.iSlong = false\r\n\t\t\t},\r\n\t\t\tcloseTap() {\r\n\t\t\t\tthis.iSlong = true\r\n\t\t\t},\r\n\t\t\tgetAllCategory: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetCategoryList().then(res => {\r\n\t\t\t\t\tres.data.forEach((item)=>{\r\n\t\t\t\t\t\tif(item.child){\r\n\t\t\t\t\t\t\titem.child.unshift({\r\n\t\t\t\t\t\t\t\tid:item.id,\r\n\t\t\t\t\t\t\t\tname:'全部'\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\tlet data = res.data;\r\n\t\t\t\t\tthat.categoryTitle = data[0].name;\r\n\t\t\t\t\tthat.sid = data[0].id;\r\n\t\t\t\t\tthat.productList = data;\r\n\t\t\t\t\tthat.categoryErList = res.data[0].child ? res.data[0].child : [];\r\n\t\t\t\t\tthat.page = 1;\r\n\t\t\t\t\tthat.loadend = false;\r\n\t\t\t\t\tthat.tempArr = [];\r\n\t\t\t\t\tthat.productslist();\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttapNav(index, item) {\r\n\t\t\t\tlet list = this.productList[index];\r\n\t\t\t\tthis.navActive = index;\r\n\t\t\t\tthis.categoryTitle = list.name;\r\n\t\t\t\tthis.categoryErList = item.child ? item.child : [];\r\n\t\t\t\tthis.tabClick = 0;\r\n\t\t\t\tthis.tabLeft = 0;\r\n\t\t\t\tthis.sid = item.id;\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.loadend = false;\r\n\t\t\t\tthis.tempArr = [];\r\n\t\t\t\tthis.productslist();\r\n\t\t\t},\r\n\t\t\t// 导航栏点击\r\n\t\t\tlongClick(index,item) {\r\n\t\t\t\tif (this.productList.length > 3) {\r\n\t\t\t\t\tthis.tabLeft = (index - 1) * (this.isWidth + 6) //设置下划线位置\r\n\t\t\t\t};\r\n\t\t\t\tthis.tabClick = index; //设置导航点击了哪一个\r\n\t\t\t\tthis.iSlong = true;\r\n\t\t\t\tthis.sid = item.id;\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.loadend = false;\r\n\t\t\t\tthis.tempArr = [];\r\n\t\t\t\tthis.productslist();\r\n\t\t\t},\r\n\t\t\tnavSwitch(index,item){\r\n\t\t\t\tif (this.productList.length > 3) {\r\n\t\t\t\t\tthis.tabLeft = (index - 1) * (this.isWidth + 6) //设置下划线位置\r\n\t\t\t\t};\r\n\t\t\t\tthis.tabClick = index; //设置导航点击了哪一个\r\n\t\t\t\tthis.iSlong = true;\r\n\t\t\t\tthis.sid = item.id;\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.loadend = false;\r\n\t\t\t\tthis.tempArr = [];\r\n\t\t\t\tthis.productslist();\r\n\t\t\t},\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n\r\n<style lang=\"scss\">\r\n\tpage {\r\n\t\tbackground-color: $crmeb-bg-color;\r\n\t}\r\n\r\n\t::-webkit-scrollbar {\r\n\t\twidth: 0;\r\n\t\theight: 0;\r\n\t\tcolor: transparent;\r\n\t\tdisplay: none;\r\n\t}\r\n\r\n\t.goodCate1 {\r\n\t\tbackground-color: $crmeb-bg-color;\r\n\t\t/deep/.mask {\r\n\t\t\t// z-index: 99;\r\n\t\t}\r\n\r\n\t\t/deep/.attrProduct {\r\n\t\t\t.mask {\r\n\t\t\t\tz-index: 100;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.header {\r\n\t\t\tposition: fixed;\r\n\t\t\theight: 128rpx;\r\n\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 0;\r\n\t\t\twidth: 100%;\r\n\t\t\tz-index: 99;\r\n\t\t\tborder-bottom: 1px solid #D9D9D9;\r\n\r\n\t\t\t.pageIndex {\r\n\t\t\t\twidth: 68rpx;\r\n\t\t\t\theight: 68rpx;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t@include main_bg_color(theme);\r\n\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// image{\r\n\t\t\t\t// \twidth: 29rpx;\r\n\t\t\t\t// \theight: 30rpx;\r\n\t\t\t\t// }\r\n\t\t\t}\r\n\r\n\t\t\t.search {\r\n\t\t\t\twidth: 600rpx;\r\n\t\t\t\t/* #ifdef MP || APP-PLUS */\r\n\t\t\t\twidth: 550rpx;\r\n\t\t\t\t/* #endif */\r\n\t\t\t\theight: 68rpx;\r\n\t\t\t\tborder-radius: 36rpx;\r\n\t\t\t\tbackground-color: $crmeb-bg-color-grey;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: $crmeb-font-color-disable;\r\n\t\t\t\tmargin-left: 22rpx;\r\n\t\t\t\tpadding: 0 30rpx;\r\n\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tmargin-right: 18rpx;\r\n\t\t\t\t\tcolor: $crmeb-font-color-subtitle;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// image{\r\n\t\t\t\t// \twidth: 27rpx;\r\n\t\t\t\t// \theight: 27rpx;\r\n\t\t\t\t// \tmargin-right: 18rpx;\r\n\t\t\t\t// }\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.conter {\r\n\t\t\tpadding-top: 64px;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\t.aside {\r\n\t\t\t\tposition: fixed;\r\n\t\t\t\twidth: 23%;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\tbottom: 0;\r\n\t\t\t\ttop: 0;\r\n\t\t\t\tbackground-color: $crmeb-bg-color-grey;\r\n\t\t\t\toverflow-y: auto;\r\n\t\t\t\toverflow-x: hidden;\r\n\t\t\t\tmargin-top: 128rpx;\r\n\t\t\t\tz-index: 99;\r\n\t\t\t\tpadding-bottom: 140rpx;\r\n\r\n\t\t\t\t.item {\r\n\t\t\t\t\theight: 100rpx;\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: $crmeb-font-color;\r\n\r\n\t\t\t\t\t&.on {\r\n\t\t\t\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t@include main_color(theme);\r\n\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t\t\t&::after {\r\n\t\t\t\t\t\t\tcontent: \"\";\r\n\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\twidth: 6rpx;\r\n\t\t\t\t\t\t\theight: 46rpx;\r\n\t\t\t\t\t\t\t@include main_bg_color(theme);\r\n\t\t\t\t\t\t\tborder-radius: 0 4rpx 4rpx 0;\r\n\t\t\t\t\t\t\tleft: 0\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.wrapper {\r\n\t\t\tmargin-top: 104rpx;\r\n\t\t\twidth: 77%;\r\n\t\t\tfloat: right;\r\n\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t\tpadding-bottom: 130rpx;\r\n\t\t}\r\n\t\t.hide_slide{\r\n\t\t\tmargin-top: 104rpx;\r\n\t\t\twidth: 100%;\r\n\t\t\tfloat: right;\r\n\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t\tpadding-bottom: 130rpx;\r\n\t\t}\r\n\t\t.bgcolor {\r\n\t\t\twidth: 100%;\r\n\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t}\r\n\t\t\t\t\r\n\t\t.goodsList {\r\n\t\t\tmargin-top: 0 !important;\r\n\t\t}\r\n\t\t\t\t\r\n\t\t.longTab {\r\n\t\t\twidth: 65%;\r\n\t\t\tposition: fixed;\r\n\t\t\ttop: 0;\r\n\t\t\tmargin-top: 128rpx;\r\n\t\t\theight: 100rpx;\r\n\t\t\tz-index: 99;\r\n\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t}\r\n\t\t.hongTab{\r\n\t\t\twidth: 100%;\r\n\t\t\tposition: fixed;\r\n\t\t\ttop: 0;\r\n\t\t\tmargin-top: 128rpx;\r\n\t\t\theight: 100rpx;\r\n\t\t\tz-index:99;\r\n\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t}\r\n\t\t.longItem {\r\n\t\t\theight: 44rpx;\r\n\t\t\tdisplay: inline-block;\r\n\t\t\tline-height: 44rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\toverflow: hidden;\r\n\t\t\ttext-overflow: ellipsis;\r\n\t\t\twhite-space: nowrap;\r\n\t\t\tcolor: $crmeb-font-color;\r\n\t\t\tbackground-color: $crmeb-bg-color-grey;\r\n\t\t\tborder-radius: 22rpx;\r\n\t\t\tmargin-left: 12rpx;\r\n\t\t\t\r\n\t\t\t&.click {\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\t@include main_bg_color(theme);\r\n\t\t\t\tcolor: $crmeb-font-color-white;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\t\r\n\t\t.underlineBox {\r\n\t\t\theight: 3px;\r\n\t\t\twidth: 20%;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-content: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\ttransition: .5s;\r\n\t\t\t\r\n\t\t\t.underline {\r\n\t\t\t\twidth: 33rpx;\r\n\t\t\t\theight: 4rpx;\r\n\t\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t\t}\r\n\t\t}\t\r\n\t\t.openList {\r\n\t\t\twidth: 12%;\r\n\t\t\theight: 100rpx;\r\n\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t\tline-height: 100rpx;\r\n\t\t\tpadding-left: 30rpx;\r\n\t\t\tposition: fixed;\r\n\t\t\tright: 0;\r\n\t\t\ttop: 128rpx;\r\n\t\t\tz-index: 99;\r\n\t\t\t\t\r\n\t\t\t.iconfont {\r\n\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\tcolor: $crmeb-font-color-subtitle;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\t\t\r\n\t\t.downTab {\r\n\t\t\twidth: 77%;\r\n\t\t\tposition: fixed;\r\n\t\t\ttop: 0;\r\n\t\t\tmargin-top: 128rpx;\r\n\t\t\tz-index: 99;\r\n\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t\tright: 0;\r\n\t\t}\r\n\t\t.hownTab{\r\n\t\t\twidth: 100%;\r\n\t\t\tposition: fixed;\r\n\t\t\ttop: 0;\r\n\t\t\tmargin-top: 128rpx;\r\n\t\t\tz-index: 99;\r\n\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t\tright: 0;\r\n\t\t}\r\n\t\t.title {\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: $crmeb-font-color-assist;\r\n\t\t\t// padding-left: 20rpx;\r\n\t\t\t\r\n\t\t\t.closeList {\r\n\t\t\t\twidth: 90rpx;\r\n\t\t\t\theight: 100rpx;\r\n\t\t\t\tline-height: 100rpx;\r\n\t\t\t\tpadding-left: 30rpx;\r\n\t\t\t\ttransform: rotate(180deg);\r\n\t\t\t\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\tcolor: $crmeb-font-color-subtitle;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\t\r\n\t\t.children {\r\n\t\t\tmax-height: 500rpx;\r\n\t\t\toverflow-x: hidden;\r\n\t\t\toverflow-y: auto;\r\n\t\t\tpadding-bottom: 20rpx;\r\n\t\t\t\r\n\t\t\t.item {\r\n\t\t\t\theight: 60rpx;\r\n\t\t\t\tbackground-color: $crmeb-bg-color-grey;\r\n\t\t\t\tborder-radius: 30rpx;\r\n\t\t\t\tline-height: 60rpx;\r\n\t\t\t\tpadding: 0 15rpx;\r\n\t\t\t\tmargin: 0 0 20rpx 20rpx;\r\n\t\t\t\twidth: 165rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\r\n\t\t\t\t&.click {\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t@include main_bg_color(theme);\r\n\t\t\t\t\tcolor: $crmeb-font-color-white;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\t\t\r\n\t\t.list_prod{\r\n\t\t\tpadding: 0 30rpx;\r\n\t\t\t.item{\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\tmargin-bottom: 40rpx;\r\n\t\t\t\t.pic{\r\n\t\t\t\t\twidth: 180rpx;\r\n\t\t\t\t\theight: 180rpx;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\timage{\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.pictxt{\r\n\t\t\t\twidth: 490rpx;\r\n\t\t\t\t.text{\r\n\t\t\t\t\tfont-size:26rpx;\r\n\t\t\t\t\tfont-family:PingFang SC;\r\n\t\t\t\t\tfont-weight:500;\r\n\t\t\t\t\tcolor: $crmeb-font-color;\r\n\t\t\t\t}\r\n\t\t\t\t.bottom{\r\n\t\t\t\t\tmargin-top: 22rpx;\r\n\t\t\t\t\t.money{\r\n\t\t\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\t\t\tfont-weight: 800;\r\n\t\t\t\t\t\twidth: 212rpx;\r\n\t\t\t\t\t\t@include price_color(theme);\r\n\t\t\t\t\t\t.sign{\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.cart{\r\n\t\t\t\t\t\theight: 46rpx;\r\n\t\t\t\t\t\t.pictrue{\r\n\t\t\t\t\t\t\tcolor: $crmeb-theme-color;\r\n\t\t\t\t\t\t\tfont-size:46rpx;\r\n\t\t\t\t\t\t\twidth: 46rpx;\r\n\t\t\t\t\t\t\theight: 46rpx;\r\n\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t\tline-height: 46rpx;\r\n\t\t\t\t\t\t\t&.icon-jiahao{\r\n\t\t\t\t\t\t\t\t color: $crmeb-theme-color;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t.num{\r\n\t\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t\t\tcolor: $crmeb-font-color;\r\n\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t\twidth: 60rpx;\r\n\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.icon-gouwuche6{\r\n\t\t\t\t\t\twidth: 46rpx;\r\n\t\t\t\t\t\theight: 46rpx;\r\n\t\t\t\t\t\tbackground-color: $crmeb-theme-color;\r\n\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\tcolor: $crmeb-font-color-white;\r\n\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.bnt{\r\n\t\t\t\t\t\tpadding: 0 20rpx;\r\n\t\t\t\t\t\theight: 46rpx;\r\n\t\t\t\t\t\tline-height: 46rpx;\r\n\t\t\t\t\t\t@include main_bg_color(theme);\r\n\t\t\t\t\t\tborder-radius:23rpx;\r\n\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\tcolor: $crmeb-font-color-white;\r\n\t\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\t&.end{\r\n\t\t\t\t\t\t\tbackground:$crmeb-font-color-disable;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t.num{\r\n\t\t\t\t\t\t\tmin-width: 12rpx;\r\n\t\t\t\t\t\t\t@include main_color(theme);\r\n\t\t\t\t\t\t\t@include coupons_border_color(theme);\r\n\t\t\t\t\t\t\tbackground: #fff;\r\n\t\t\t\t\t\t\tborder-radius: 15px;\r\n\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\tright: -13rpx;\r\n\t\t\t\t\t\t\ttop: -11rpx;\r\n\t\t\t\t\t\t\tfont-size: 16rpx;\r\n\t\t\t\t\t\t\tpadding: 0 11rpx;\r\n\t\t\t\t\t\t\theight: 32rpx;\r\n\t\t\t\t\t\t\tline-height: 32rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t.otPrice{\r\n\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\tfont-family: PingFang SC;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tline-height: 24rpx;\r\n\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.footer {\r\n\t\t\tposition: fixed;\r\n\t\t\tleft: 0;\r\n\t\t\tbottom:env(safe-area-inset-bottom); \r\n\t\t\twidth: 100%;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tbox-shadow: 0px -3rpx 16rpx rgba(36, 12, 12, 0.05);\r\n\t\t\tz-index: 101;\r\n\t\t\tpadding: 0 30rpx;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\theight: 100rpx;\r\n\t\t\t&:after{\r\n\t\t\t\tcontent:'';\r\n\t\t\t\theight:env(safe-area-inset-bottom); // 这里是重点\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop:100%;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\tright:0;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t}\r\n\t\t\t.cart_theme{\r\n\t\t\t\tmargin-top: -50rpx;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\t.hava{\r\n\t\t\t\t\tfont-size: 110rpx;\r\n\t\t\t\t\t@include main_color(theme);\r\n\t\t\t\t}\r\n\t\t\t\t.num{\r\n\t\t\t\t\tmin-width: 12rpx;\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\tborder-radius: 15px;\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tright: -6rpx;\r\n\t\t\t\t\tbottom: 10rpx;\r\n\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\tpadding: 0 10rpx;\r\n\t\t\t\t\theight: 34rpx;\r\n\t\t\t\t\tline-height: 34rpx;\r\n\t\t\t\t\t@include main_color(theme);\r\n\t\t\t\t\t@include coupons_border_color(theme);\r\n\t\t\t\t\tbackground-color: #fff;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.noCart{\r\n\t\t\t\tmargin-top: -50rpx;\r\n\t\t\t\t.no_have{\r\n\t\t\t\t\tfont-size: 110rpx;\r\n\t\t\t\t\tcolor: #cbcbcb;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\r\n\t\t\t.money {\r\n\t\t\t\t@include price_color(theme);\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\r\n\t\t\t\t.num {\r\n\t\t\t\t\tfont-size: 42rpx;\r\n\t\t\t\t}\r\n\t\t\r\n\t\t\t\t.bnt {\r\n\t\t\t\t\twidth: 222rpx;\r\n\t\t\t\t\theight: 76rpx;\r\n\t\t\t\t\tborder-radius: 46rpx;\r\n\t\t\t\t\tline-height: 76rpx;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\tmargin-left: 24rpx;\r\n\t\t\t\t}\r\n\t\t\t\t.main_bg{\r\n\t\t\t\t\t@include main_bg_color(theme);\r\n\t\t\t\t}\r\n\t\t\t\t.gray_bg{\r\n\t\t\t\t\tbackground-color: #B3B3B4;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>"], "sourceRoot": ""}