{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/goods_cate.vue?574e", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/goods_cate.vue?30a3", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/goods_cate.vue?a4eb", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/goods_cate.vue?1a20", "uni-app:///pages/goods_cate/goods_cate.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/default_cate.vue?9a3f", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/default_cate.vue?a7a9", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/default_cate.vue?c3de", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/default_cate.vue?726c", "uni-app:///pages/goods_cate/components/default_cate.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/default_cate.vue?e3e4", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/default_cate.vue?d4ee", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/optimization.vue?3d5a", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/optimization.vue?b94a", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/optimization.vue?7cc1", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/optimization.vue?5c14", "uni-app:///pages/goods_cate/components/optimization.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/d_goodList/index.vue?47cf", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/d_goodList/index.vue?0228", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/d_goodList/index.vue?b3d7", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/d_goodList/index.vue?2364", "uni-app:///components/d_goodList/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/d_goodList/index.vue?bbf0", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/d_goodList/index.vue?3f3c", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/optimization.vue?90b3", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/optimization.vue?f144", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/contracted.vue?65fd", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/contracted.vue?5951", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/contracted.vue?de95", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/contracted.vue?c398", "uni-app:///pages/goods_cate/components/contracted.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/contracted.vue?3ca3", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/contracted.vue?03c3", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/fresh.vue?4e2e", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/fresh.vue?f5d4", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/fresh.vue?e0dd", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/fresh.vue?1b3b", "uni-app:///pages/goods_cate/components/fresh.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/f_goodList/index.vue?3342", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/f_goodList/index.vue?157c", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/f_goodList/index.vue?84b0", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/f_goodList/index.vue?77af", "uni-app:///components/f_goodList/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/f_goodList/index.vue?02e6", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/f_goodList/index.vue?d190", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/fresh.vue?351a", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/components/fresh.vue?6165", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/goods_cate.vue?99f2", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_cate/goods_cate.vue?07cf"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "currentPage", "theme", "showSlide", "winHeight", "config<PERSON>pi", "computed", "onLoad", "that", "uni", "success", "onShow", "setTimeout", "components", "cate", "optimization", "contracted", "fresh", "home", "methods", "shareApi", "setOpenShare", "desc", "title", "link", "imgUrl", "configAppMessage", "onReachBottom", "showSkeleton", "isNodes", "navlist", "productList", "name", "child", "extra", "navActive", "number", "height", "hightArr", "to<PERSON>ie<PERSON>", "tabbarH", "created", "key", "_self", "infoScroll", "query", "tap", "getAllCategory", "scroll", "searchSubmitValue", "animationType", "animationDuration", "url", "productWindow", "goodList", "cartList", "props", "type", "default", "categoryTitle", "categoryErList", "tabLeft", "isWidth", "tabClick", "iSlong", "tempArr", "loading", "loadend", "loadTitle", "page", "limit", "cid", "sid", "isAuto", "isShowAuth", "attr", "cartAttr", "productAttr", "productSelect", "productValue", "attrValue", "storeName", "id", "cartData", "iScart", "cartCount", "totalPrice", "lengthCart", "subOrder", "list", "ids", "getTotalPrice", "ChangeSubDel", "ChangeOneDel", "getCartLists", "<PERSON><PERSON><PERSON><PERSON>", "closeList", "getCartNum", "onMyEvent", "DefaultSelect", "value", "ChangeAttr", "attrVal", "indexn", "iptCartNum", "onLoadFun", "productslist", "ChangeCartNumDuo", "num", "ChangeCartList", "ChangeCartNum", "index", "goCatNum", "goCat", "productId", "cartNum", "isNew", "productAttrUnique", "catch", "goCartDuo", "getIsLogin", "getAttrs", "attrName", "attrV<PERSON>ues", "isDel", "goDetail", "openTap", "closeTap", "res", "item", "tapNav", "navSwitch", "longClick", "dataConfig", "is<PERSON>ogin", "mounted", "recommend", "navLists", "scrollLeft", "active", "hostProduct", "godDetail", "tabSelect", "getProductList", "get_host_product", "goCartDan", "CartNumDes", "CartNumAdd"], "mappings": ";;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACc;;;AAGvE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAomB,CAAgB,8nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACUxnB;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;AAEA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;;EACAC;EACAC;IACA;IACA;IACAC;IACA;MACA;QACAA;QACAC;QACA;MACA;QACAD;QACAC;QACA;MACA;QACAD;QACAC;QACA;MACA;QACAD;QACAC;QACA;IAAA;IAEAA;MACAC;QACAF;MACA;IACA;EAIA;EACAG;IAAA;IACA;MACA;QACAF;QACA;MACA;QACAA;QACA;MACA;QACAA;QACAG;UACA;YACA;YACA;YACA;UACA;QACA;QACA;MACA;QACAH;QACAG;UACA;YACA;YACA;UACA;QACA;QACA;IAAA;EAEA;EACAC;IACAC;IAAAC;IAAAC;IAAAC;IAAAC;EACA;EACAC;IACAC;MAAA;MACA;QACA;MAIA;IACA;IACA;IACAC;MACA;MACA;QACA;UACAC;UACAC;UACAC;UACAC;QACA;QACAjB,mFACAkB;MACA;IACA;EACA;EACAC;IACA;MACA;IACA;IACA;MACA;IACA;IACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC/HA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAsmB,CAAgB,goBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACmD1nB;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACA3B;IACA;MACA4B;MAAA;MACAC;MAAA;MACAC;MACAC;QAAAC;QAAAC;UAAAC;QAAA;UAAAA;QAAA;MAAA;QAAAF;QAAAC;UAAAC;QAAA;UAAAA;QAAA;MAAA;QAAAF;QAAAC;UAAAC;QAAA;UAAAA;QAAA;MAAA;QAAAF;MAAA;MACAG;MACAC;MACAC;MACAC;MACAC;MACAC;MACAtC;IACA;EACA;EACAuC;IAAA;IACA;IACAhC;MACAiC;MACAhC;QACAiC;MACA;IACA;IACA/B;MACA;IACA;IACA;EACA;EACAO;IACAyB;MACA;MACA;MACA;MACA;;MAEA;MACAnC;QACAC;UACAF;QACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;QACAqC;QACAA;UACA;UACAP;UACA9B;QACA;MACA;MAAA;IACA;IACAsC;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACAvC;QACAI;UACAJ;QACA;QACAI;UACA;QACA;MACA;IACA;IACAoC;MACA;MACA;MACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA,gDACAxC;QACAyC;QACAC;QACAC;MACA,QAEA;QACA7B;MACA;IACA;EACA;;;;;;;;;;;;;;;;;;ACvJA;AAAA;AAAA;AAAA;AAAyqC,CAAgB,+oCAAG,EAAC,C;;;;;;;;;;ACA7rC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACc;;;AAGzE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAsmB,CAAgB,goBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACkI1nB;AAMA;AAOA;AAGA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAjB;EACAO;IACAwC;IACAC;IACAC;IACArC;EACA;EACAsC;IACArD;MACAsD;MACAC;IACA;EACA;EACA1D;IACA;MACA+B;MACAI;MACAwB;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;QACA5B;QACA6B;MACA;MACAC;MACAC;MACAC;IACA;EACA;EACA9C;IACA;MACA;MACA;IACA;IACA;IACA;IACAjC;IACA;IACAC;MACAC;QACAF;MACA;IACA;EACA;EACAW;IACA;IACAqE;MACA;QAAAC;QAAAC;MACA;QACA;UACA;YACA;UACA;QACA;QACA;QACAlF;MACA;QACA;UACAe;QACA;MACA;IACA;IACA;IACAoE;MACA;QAAAF;QAAAH;MACAG;QACA;UACAH;QACA;MACA;MACA9E;IACA;IACAoF;MACA;QAAAH;QAAAC;MACAD;QACAC;MACA;MACA;QACAlF;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAqF;MACA;QAAAJ;MACA;QACAA;QACA;UACAjF;UACAA;UACAA;UACAA;UACAA;QACA;QAAA;QACAA;MACA;IACA;IACAsF;MACA;MACA;QACAzB;QACAC;QACAyB;MACA;MACA;QACAvF;QACA;UACAA;QACA;UACAA;QACA;QACAA;MACA;IACA;IACAwF;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;QACAzF;MACA;IACA;IAGA0F;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;QACA;UACAC;UACA;QACA;MACA;MACA;QACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACAC;MACA,uGACAC;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;IACA;IACAC;IACA;IACAC;MACA;MACA;MACA;MACAlG;MACAA;MACA;QACA6D;QACAC;QACAb;QACAc;MACA;QACA;UACAJ;QACA3D;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;QACAA,sBACAA;MACA;IACA;IACA;IACAmG;MACA;MACA;MACA;MACA;MACA,kEACA7B;MACA;MACA;MACA;MACA;MACA;QACA8B;QACA;UACA;UACA;QACA;MACA;QACAA;QACA;UACA;UACA;QACA;MACA;IAEA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;UACAC;QACA;UACAA;UACA;YACA;YACA;UACA;QACA;MACA;QACAA;QACA;UACA;UACA;QACA;QACA;UACA;YACA;YACA;YACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;QACAnC;MACA;MACA;QACA;QACAtE;MACA;QACA,gDACAA;MACA;MACA;MACA,0DACA;MACA;MACA,IACAA,gCACAsE,6BACAtE,sBAEA;QACAe;MACA;MACA;QACA;UACA2F;UACAC;UACAC;UACAC,2DACA7G;QACA;QACA;UACAA;UACAA;UACAA;YACAe;YACAb;cACAE;gBACAJ;gBACAA;cACA;YACA;UACA;QACA,GACA8G;UACA9G;UACA;YACAe;UACA;QACA;MACA;QACA;MACA;IACA;IACAgG;MACA;QACA;MACA;QACA9G;UACAc;QACA;QACA;QACA;QACA;MACA;IACA;IACAiG;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACAhH;QACAD;QACAA;QACA;UACA;YACAkH;YACAC;YACAzC;YACA0C;YACAV;YACAzD;UACA;QACA;QACA;QACA;QACAjD;MACA;IACA;IACA;IACAqH;MACA;QACA;MACA;QACA;UACApH;YACAyC;YACAC;YACAC;UACA;QACA;MACA;;IAIA0E;MACA;IACA;IACAC;MACA;IACA;IACAhF;MACA;MACA;QACAiF;UACA;YACAC;cACA/C;cACAlD;YACA;UACA;QACA;QACA;QACAxB;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACA0H;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;;;;;;;;;;;;;;AC3pBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC6BnnB;EACApG;EACAwB;IACA6E;MACA5E;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACA4E;MACA7E;MACAC;IACA;EACA;EACA1D;IACA,QACA;EACA;EACAyC;EACA8F;EACApH;IACA0G;MACA;IACA;IACAN;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;AC3DA;AAAA;AAAA;AAAA;AAA0oC,CAAgB,gnCAAG,EAAC,C;;;;;;;;;;ACA9pC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAipC,CAAgB,unCAAG,EAAC,C;;;;;;;;;;ACArqC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAAomB,CAAgB,8nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACsCxnB;AAKA;AAIA;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAjH;EACAO;IACA2H;EACA;EACAxI;IACA;MACAyI;MACA1G;MACA2G;MACAC;MACAzE;MACAC;MACAC;MACAC;MACAC;MACAC;MACAqE;IACA;EACA;EACAnG;IACA;IACA;IACA;EACA;EACAtB;IACA;IACA0H;MACA;QACApI;UACAyC;UACAC;UACAC;QACA;MACA;IACA;IACA0F;MAAA;MACA;MACA;MACAjG;QACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAE;MACA;MACA;QACAvC;MACA;IACA;IACAuI;MACA;MACA;MACA;MACAvI;MACAA;MACA;QACA6D;QACAC;QACAC;MACA;QACA;UACAJ;QACA3D;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;QACAA,sBACAA;MACA;IACA;IACA;AACA;AACA;IACAwI;MACA;MACA;QACAxI;MACA;IACA;EACA;EACA;EACA;EACA;;;;;;;;;;;;;;AC7IA;AAAA;AAAA;AAAA;AAAuqC,CAAgB,6oCAAG,EAAC,C;;;;;;;;;;ACA3rC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACyD;AACL;AACc;;;AAGlE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACqInnB;AAMA;AAOA;AAGA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAF;EACAO;IACAwC;IACAC;IACAC;IACArC;EACA;EACAsC;IACArD;MACAsD;MACAC;IACA;EACA;EACA1D;IACA;MACA+B;MACAI;MACAwB;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;QACA5B;QACA6B;MACA;MACAC;MACAC;MACAC;MACArF;IACA;EACA;EACAuC;IACA;MACA;MACA;IACA;IACA;IACA;IACAjC;IACA;IACAC;MACAC;QACAF;MACA;IACA;EACA;EACAW;IACA;IACAqE;MACA;QAAAC;QAAAC;MACA;QACA;UACA;YACA;UACA;QACA;QACA;QACAlF;MACA;QACA;UACAe;QACA;MACA;IACA;IACA;IACAoE;MACA;QAAAF;QAAAH;MACAG;QACA;UACAH;QACA;MACA;MACA9E;IACA;IACAoF;MACA;QAAAH;QAAAC;MACAD;QACAC;MACA;MACA;QACAlF;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAqF;MACA;QAAAJ;MACA;QACAA;QACA;UACAjF;UACAA;UACAA;UACAA;UACAA;QACA;QAAA;QACAA;MACA;IACA;IACAsF;MACA;MACA;QACAzB;QACAC;QACAyB;MACA;MACA;QACAvF;QACA;UACAA;QACA;UACAA;QACA;QACAA;MACA;IACA;IACAwF;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;QACAzF;MACA;IACA;IAGA0F;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;QACA;UACAC;UACA;QACA;MACA;MACA;QACA;MACA;MACA;MACA;MAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACAC;MACA,uGACAC;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;IACA;IACAC;IACA;IACAC;MACA;MACA;MACA;MACAlG;MACAA;MACA;QACA6D;QACAC;QACAb;QACAc;MACA;QACA;UACAJ;QACA3D;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;QACAA,sBACAA;MACA;IACA;IACA;IACAmG;MACA;MACA;MACA;MACA;MACA,kEACA7B;MACA;MACA;MACA;MACA;MACA;QACA8B;QACA;UACA;UACA;QACA;MACA;QACAA;QACA;UACA;UACA;QACA;MACA;IAEA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;UACAC;QACA;UACAA;UACA;YACA;YACA;UACA;QACA;MACA;QACAA;QACA;UACA;UACA;QACA;QACA;UACA;YACA;YACA;YACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;QACAnC;MACA;MACA;QACA;QACAtE;MACA;QACA,gDACAA;MACA;MACA;MACA,0DACA;MACA;MACA,IACAA,gCACAsE,6BACAtE,sBAEA;QACAe;MACA;MACA;QACA;UACA2F;UACAC;UACAC;UACAC,2DACA7G;QACA;QACA;UACAA;UACAA;UACAA;YACAe;YACAb;cACAE;gBACAJ;gBACAA;cACA;YACA;UACA;QACA,GACA8G;UACA9G;UACA;YACAe;UACA;QACA;MACA;QACA;MACA;IACA;IACAgG;MACA;QACA;MACA;QACA9G;UACAc;QACA;QACA;QACA;QACA;MACA;IACA;IACAiG;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACAhH;QACAD;QACAA;QACA;UACA;YACAkH;YACAC;YACAzC;YACA0C;YACAV;YACAzD;UACA;QACA;QACA;QACA;QACAjD;MACA;IACA;IACA;IACAqH;MACA;QACA;MACA;QACA;UACApH;YACAyC;YACAC;YACAC;UACA;QACA;MACA;;IAIA0E;MACA;IACA;IACAC;MACA;IACA;IACAhF;MACA;MACA;QACAiF;UACA;YACAC;cACA/C;cACAlD;YACA;UACA;QACA;QACA;QACAxB;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACA0H;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAE;MACA;QACA;MACA;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAD;MACA;QACA;MACA;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;;;;;;;;;;;;;;AC/pBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC6BnnB;EACAnG;EACAwB;IACA6E;MACA5E;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACA4E;MACA7E;MACAC;IACA;EACA;EACA1D;IACA,QAEA;EACA;EACAyC;EACA8F;EACApH;IACA0G;MACA;IACA;IACAN;MACA;IACA;IACA0B;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;ACrEA;AAAA;AAAA;AAAA;AAA0oC,CAAgB,gnCAAG,EAAC,C;;;;;;;;;;ACA9pC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAA0oC,CAAgB,gnCAAG,EAAC,C;;;;;;;;;;ACA9pC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAA+oC,CAAgB,qnCAAG,EAAC,C;;;;;;;;;;ACAnqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/goods_cate/goods_cate.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/goods_cate/goods_cate.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./goods_cate.vue?vue&type=template&id=2fb93f74&\"\nvar renderjs\nimport script from \"./goods_cate.vue?vue&type=script&lang=js&\"\nexport * from \"./goods_cate.vue?vue&type=script&lang=js&\"\nimport style0 from \"./goods_cate.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/goods_cate/goods_cate.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./goods_cate.vue?vue&type=template&id=2fb93f74&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./goods_cate.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./goods_cate.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\" :data-theme=\"theme\" :style=\"{height:winHeight + 'px'}\">\r\n\t\t<cate v-show=\"currentPage == 'one'\"></cate>\r\n\t\t<contracted v-show=\"currentPage == 'two'\" ref=\"classTwo\"></contracted>\r\n\t\t<optimization v-show=\"currentPage == 'three'\" :showSlide=\"showSlide\" ref=\"classThree\"></optimization>\r\n\t\t<fresh v-show=\"currentPage == 'four'\" :showSlide=\"showSlide\" ref=\"classFour\"></fresh>\r\n\t\t<!-- <home></home> -->\r\n\t</view>\r\n</template>\r\n<script>\r\n\timport cate from './components/default_cate';\r\n\timport optimization from './components/optimization';\r\n\timport contracted from './components/contracted';\r\n\timport fresh from './components/fresh';\r\n\timport {getShare} from '@/api/public.js';\r\n\timport {mapGetters} from 'vuex';\r\n\timport home from '@/components/home/<USER>';\r\n\tconst app = getApp();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcurrentPage:'one',\r\n\t\t\t\ttheme:app.globalData.theme,\r\n\t\t\t\tshowSlide:true,\r\n\t\t\t\twinHeight:'',\r\n\t\t\t\tconfigApi: {}, //分享类容配置\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: mapGetters(['isLogin', 'uid']),\r\n\t\tonLoad(){\r\n\t\t\tlet that = this;\r\n\t\t\tlet config = that.$Cache.getItem('categoryConfig');\r\n\t\t\tthat.showSlide = config.isShowCategory == 'true'? true : false;\r\n\t\t\tswitch (config.categoryConfig) {\r\n\t\t\t\tcase '1':\r\n\t\t\t\t\tthat.$set(that,'currentPage','one');\r\n\t\t\t\t\tuni.showTabBar()\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase '2':\r\n\t\t\t\t\tthat.$set(that,'currentPage','two');\r\n\t\t\t\t\tuni.showTabBar()\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase '3':\r\n\t\t\t\t\tthat.$set(that,'currentPage','three');\r\n\t\t\t\t\tuni.hideTabBar()\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase '4':\r\n\t\t\t\t\tthat.$set(that,'currentPage','four');\r\n\t\t\t\t\tuni.hideTabBar()\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t\tuni.getSystemInfo({\r\n\t\t\t    success: function (res) {\r\n\t\t\t        that.winHeight = res.windowHeight;\r\n\t\t\t    }\r\n\t\t\t});\r\n\t\t\t// #ifdef H5\r\n\t\t\tthat.shareApi();\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tonShow(){\r\n\t\t\tswitch (this.currentPage){\r\n\t\t\t\tcase 'one':\r\n\t\t\t\t\tuni.showTabBar()\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 'two':\r\n\t\t\t\t\tuni.showTabBar()\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 'three':\r\n\t\t\t\t\tuni.hideTabBar()\r\n\t\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\t\tif(this.isLogin){\r\n\t\t\t\t\t\t\t//登录的情况下获取模板3,4的购物车商品数量和列表\r\n\t\t\t\t\t\t\tthis.$refs.classThree.getCartNum();\r\n\t\t\t\t\t\t\tthis.$refs.classThree.getCartLists(1);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},500)\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 'four':\r\n\t\t\t\t\tuni.hideTabBar()\r\n\t\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\t\tif(this.isLogin){\r\n\t\t\t\t\t\t\tthis.$refs.classFour.getCartNum();\r\n\t\t\t\t\t\t\tthis.$refs.classFour.getCartLists(1);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},500)\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomponents:{\r\n\t\t\tcate,optimization,contracted,fresh,home\r\n\t\t},\r\n\t\tmethods:{\r\n\t\t\tshareApi: function() {\r\n\t\t\t\tgetShare().then(res => {\r\n\t\t\t\t\tthis.$set(this, 'configApi', res.data);\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\tthis.setOpenShare(res.data);\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 微信分享；\r\n\t\t\tsetOpenShare: function(data) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (that.$wechat.isWeixin()) {\r\n\t\t\t\t\tlet configAppMessage = {\r\n\t\t\t\t\t\tdesc: data.synopsis,\r\n\t\t\t\t\t\ttitle: data.title,\r\n\t\t\t\t\t\tlink: location.href,\r\n\t\t\t\t\t\timgUrl: data.img\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthat.$wechat.wechatEvevt([\"updateAppMessageShareData\", \"updateTimelineShareData\"],\r\n\t\t\t\t\t\tconfigAppMessage);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t},\r\n\t\tonReachBottom(){\r\n\t\t\tif(this.currentPage=='two'){\r\n\t\t\t\tthis.$refs.classTwo.getProductList();\r\n\t\t\t}\r\n\t\t\tif(this.currentPage=='three'){\r\n\t\t\t\tthis.$refs.classThree.productslist();\r\n\t\t\t}\r\n\t\t\tif(this.currentPage=='four'){\r\n\t\t\t\tthis.$refs.classFour.productslist();\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style lang=\"scss\">\r\n\t.page{\r\n\t\tbackground: #fff;\r\n\t\t// overflow-y: auto;\r\n\t}\r\n</style>", "import { render, staticRenderFns, recyclableRender, components } from \"./default_cate.vue?vue&type=template&id=1554e5b0&scoped=true&\"\nvar renderjs\nimport script from \"./default_cate.vue?vue&type=script&lang=js&\"\nexport * from \"./default_cate.vue?vue&type=script&lang=js&\"\nimport style0 from \"./default_cate.vue?vue&type=style&index=0&id=1554e5b0&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1554e5b0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/goods_cate/components/default_cate.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./default_cate.vue?vue&type=template&id=1554e5b0&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./default_cate.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./default_cate.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :data-theme=\"theme\">\r\n\t\t<view class='productSort'>\r\n\t\t\t<skeleton :show=\"showSkeleton\" :isNodes=\"isNodes\" ref=\"skeleton\" loading=\"chiaroscuro\" selector=\"skeleton\"\r\n\t\t\t\tbgcolor=\"#FFF\"></skeleton>\r\n\t\t\t<view class=\"skeleton\" :style=\"{visibility: showSkeleton ? 'hidden' : 'visible'}\">\r\n\t\t\t\t<view class='header acea-row row-center-wrapper'>\r\n\t\t\t\t\t<view class='acea-row row-between-wrapper input'>\r\n\t\t\t\t\t\t<text class='iconfont icon-sousuo'></text>\r\n\t\t\t\t\t\t<input type='text' placeholder='点击搜索商品信息' @confirm=\"searchSubmitValue\" confirm-type='search' name=\"search\"\r\n\t\t\t\t\t\t placeholder-class='placeholder' maxlength=\"20\"></input>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='aside' :style=\"{bottom: tabbarH + 'px',height: height + 'rpx'}\">\r\n\t\t\t\t\t<scroll-view scroll-y=\"true\" scroll-with-animation='true' style=\"height: 100%;\">\r\n\t\t\t\t\t\t<view class='item acea-row row-center-wrapper' :class='index==navActive?\"on\":\"\"' v-for=\"(item,index) in productList\"\r\n\t\t\t\t\t :key=\"index\" @click='tap(index,\"b\"+index)'><text class=\"skeleton-rect\">{{item.name}}</text></view>\r\n\t\t\t\t\t </scroll-view>\r\n\t\t\t\t\t\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='conter'>\r\n\t\t\t\t\t<scroll-view scroll-y=\"true\" :scroll-into-view=\"toView\" :style='\"height:\"+height+\"rpx;margin-top: 96rpx;\"' @scroll=\"scroll\"\r\n\t\t\t\t\t scroll-with-animation='true'>\r\n\t\t\t\t\t\t<block v-for=\"(item,index) in productList\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class='listw' :id=\"'b'+index\">\r\n\t\t\t\t\t\t\t\t<view class='title acea-row row-center-wrapper'>\r\n\t\t\t\t\t\t\t\t\t<view class='line'></view>\r\n\t\t\t\t\t\t\t\t\t<view class='name'>{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class='line'></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class='list acea-row'>\r\n\t\t\t\t\t\t\t\t\t<block v-for=\"(itemn,indexn) in item.child\" :key=\"indexn\">\r\n\t\t\t\t\t\t\t\t\t\t<navigator hover-class='none' :url='\"/pages/goods_list/index?cid=\"+itemn.id+\"&title=\"+itemn.name' class='item acea-row row-column row-middle'>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class='picture skeleton-rect' :style=\"{'background-color':itemn.extra?'none':'#f7f7f7'}\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<image :src='itemn.extra'></image>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class='name line1'>{{itemn.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t</navigator>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<view :style='\"height:\"+(height-300)+\"rpx;\"' v-if=\"number<15\"></view>\r\n\t\t\t\t\t</scroll-view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\t\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {getCategoryList} from '@/api/store.js';\r\n\timport ClipboardJS from \"@/plugin/clipboard/clipboard.js\";\r\n\timport animationType from '@/utils/animationType.js'\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tshowSkeleton: true, //骨架屏显示隐藏\r\n\t\t\t\tisNodes: 0, //控制什么时候开始抓取元素节点,只要数值改变就重新抓取\r\n\t\t\t\tnavlist: [],\r\n\t\t\t\tproductList: [{name:'占位占位',child:[{extra:''},{extra:''}]},{name:'占位占位',child:[{extra:''},{extra:''}]},{name:'占位占位',child:[{extra:''},{extra:''}]},{name:'占位占位'}],\r\n\t\t\t\tnavActive: 0,\r\n\t\t\t\tnumber: \"\",\r\n\t\t\t\theight: 0,\r\n\t\t\t\thightArr: [],\r\n\t\t\t\ttoView: \"\",\r\n\t\t\t\ttabbarH: 0,\r\n\t\t\t\ttheme:'theme1'\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tlet _self = this;\r\n\t\t\tuni.getStorage({\r\n\t\t\t    key: 'theme',\r\n\t\t\t    success: function (res) {\r\n\t\t\t        _self.theme = res.data;\r\n\t\t\t    }\r\n\t\t\t});\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.isNodes++;\r\n\t\t\t}, 500);\r\n\t\t\tthis.getAllCategory();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tinfoScroll: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet len = that.productList.length;\r\n\t\t\t\tlet child = that.productList[len - 1]&&that.productList[len - 1].child?that.productList[len - 1].child:[];\r\n\t\t\t\tthis.number = child?child.length:0;\r\n\t\t\t\t\r\n\t\t\t\t//设置商品列表高度\r\n\t\t\t\tuni.getSystemInfo({\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tthat.height = (res.windowHeight) * (750 / res.windowWidth) - 98;\r\n\t\t\t\t\t},\r\n\t\t\t\t});\r\n\t\t\t\tlet height = 0;\r\n\t\t\t\tlet hightArr = [];\r\n\t\t\t\tfor (let i = 0; i < len; i++) {\r\n\t\t\t\t\t//获取元素所在位置\r\n\t\t\t\t\tlet query = uni.createSelectorQuery().in(this);\r\n\t\t\t\t\tlet idView = \"#b\" + i;\r\n\t\t\t\t\tquery.select(idView).boundingClientRect();\r\n\t\t\t\t\tquery.exec(function(res) {\r\n\t\t\t\t\t\tlet top = res[0].top;\r\n\t\t\t\t\t\thightArr.push(top);\r\n\t\t\t\t\t\tthat.hightArr = hightArr\r\n\t\t\t\t\t});\r\n\t\t\t\t};\r\n\t\t\t},\r\n\t\t\ttap: function(index, id) {\r\n\t\t\t\tthis.toView = id;\r\n\t\t\t\tthis.navActive = index;\r\n\t\t\t},\r\n\t\t\tgetAllCategory: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetCategoryList().then(res => {\r\n\t\t\t\t\tthat.productList = res.data;\r\n\t\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\t\tthat.infoScroll();\r\n\t\t\t\t\t},500)\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.showSkeleton = false\r\n\t\t\t\t\t}, 1000)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tscroll: function(e) {\r\n\t\t\t\tlet scrollTop = e.detail.scrollTop;\r\n\t\t\t\tlet scrollArr = this.hightArr;\r\n\t\t\t\tfor (let i = 0; i < scrollArr.length; i++) {\r\n\t\t\t\t\tif (scrollTop >= 0 && scrollTop < scrollArr[1] - scrollArr[0]) {\r\n\t\t\t\t\t\tthis.navActive = 0\r\n\t\t\t\t\t} else if (scrollTop >= scrollArr[i] - scrollArr[0] && scrollTop < scrollArr[i + 1] - scrollArr[0]) {\r\n\t\t\t\t\t\tthis.navActive = i\r\n\t\t\t\t\t} else if (scrollTop >= scrollArr[scrollArr.length - 1] - scrollArr[0]) {\r\n\t\t\t\t\t\tthis.navActive = scrollArr.length - 1\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsearchSubmitValue: function(e) {\r\n\t\t\t\tif (this.$util.trim(e.detail.value).length > 0)\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\tanimationType: animationType.type,\r\t\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\t\turl: '/pages/goods_list/index?searchValue=' + e.detail.value\r\n\t\t\t\t\t})\r\n\t\t\t\telse\r\n\t\t\t\t\treturn this.$util.Tips({\r\n\t\t\t\t\t\ttitle: '请填写要搜索的产品信息'\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.productSort .header {\r\n\t\twidth: 100%;\r\n\t\theight: 96rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\ttop: 0;\r\n\t\tz-index: 9;\r\n\t\tborder-bottom: 1rpx solid #f5f5f5;\r\n\t}\r\n\t\r\n\t.productSort .header .input {\r\n\t\twidth: 700rpx;\r\n\t\theight: 60rpx;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tborder-radius: 50rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tpadding: 0 25rpx;\r\n\t}\r\n\t\r\n\t.productSort .header .input .iconfont {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #555;\r\n\t}\r\n\t\r\n\t.productSort .header .input .placeholder {\r\n\t\tcolor: #999;\r\n\t}\r\n\t\r\n\t.productSort .header .input input {\r\n\t\tfont-size: 26rpx;\r\n\t\theight: 100%;\r\n\t\twidth: 597rpx;\r\n\t}\r\n\t\r\n\t.productSort .aside {\r\n\t\tposition: fixed;\r\n\t\twidth: 180rpx;\r\n\t\tleft: 0;\r\n\t\ttop:0;\r\n\t\tbackground-color: #f7f7f7;\r\n\t\toverflow-y: scroll;\r\n\t\toverflow-x: hidden;\r\n\t\t\r\n\t\theight: auto;\r\n\t\tmargin-top: 96rpx;\r\n\t}\r\n\t\r\n\t.productSort .aside .item {\r\n\t\theight: 100rpx;\r\n\t\twidth: 100%;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #424242;\r\n\t\tposition: relative;\r\n\t}\r\n\t.productSort .aside .item.on {\r\n\t\tbackground-color: #fff;\r\n\t\twidth: 100%;\r\n\t\ttext-align: center;\r\n\t\t@include main_color(theme);\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t.productSort .aside .item.on ::before{\r\n\t\tcontent: '';\r\n\t\twidth: 4rpx;\r\n\t\theight: 100rpx;\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\t@include main_bg_color(theme);\r\n\t}\r\n\t\r\n\t.productSort .conter {\r\n\t\tmargin: 96rpx 0 0 180rpx;\r\n\t\tpadding: 0 14rpx;\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\t\r\n\t.productSort .conter .listw {\r\n\t\tpadding-top: 20rpx;\r\n\t}\r\n\t\r\n\t.productSort .conter .listw .title {\r\n\t\theight: 90rpx;\r\n\t}\r\n\t\r\n\t.productSort .conter .listw .title .line {\r\n\t\twidth: 100rpx;\r\n\t\theight: 2rpx;\r\n\t\tbackground-color: #f0f0f0;\r\n\t}\r\n\t\r\n\t.productSort .conter .listw .title .name {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333;\r\n\t\tmargin: 0 30rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t\r\n\t.productSort .conter .list {\r\n\t\tflex-wrap: wrap;\r\n\t}\r\n\t\r\n\t.productSort .conter .list .item {\r\n\t\twidth: 177rpx;\r\n\t\tmargin-top: 26rpx;\r\n\t}\r\n\t\r\n\t.productSort .conter .list .item .picture {\r\n\t\twidth: 120rpx;\r\n\t\theight: 120rpx;\r\n\t\tborder-radius: 50%;\r\n\t}\r\n\t\r\n\t.productSort .conter .list .item .picture image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 50%;\r\n\t\tdiv{\r\n\t\t\tbackground-color: #f7f7f7;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.productSort .conter .list .item .name {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #333;\r\n\t\theight: 56rpx;\r\n\t\tline-height: 56rpx;\r\n\t\twidth: 120rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./default_cate.vue?vue&type=style&index=0&id=1554e5b0&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./default_cate.vue?vue&type=style&index=0&id=1554e5b0&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294179461\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import { render, staticRenderFns, recyclableRender, components } from \"./optimization.vue?vue&type=template&id=8738dff0&\"\nvar renderjs\nimport script from \"./optimization.vue?vue&type=script&lang=js&\"\nexport * from \"./optimization.vue?vue&type=script&lang=js&\"\nimport style0 from \"./optimization.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/goods_cate/components/optimization.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./optimization.vue?vue&type=template&id=8738dff0&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.cartData.cartList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./optimization.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./optimization.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"goodCate\">\r\n\t\t<view class=\"header acea-row row-center-wrapper\">\r\n\t\t\t<navigator url='/pages/index/index' class=\"pageIndex\" hover-class=\"none\" open-type=\"switchTab\">\r\n\t\t\t\t<text class=\"iconfont icon-shouye3\"></text>\r\n\t\t\t</navigator>\r\n\t\t\t<navigator url=\"/pages/goods_search/index\" class=\"search acea-row row-center-wrapper\" hover-class=\"none\"><text class=\"iconfont icon-xiazai5\"></text>\r\n\t\t\t\t搜索商品</navigator>\r\n\t\t</view>\r\n\t\t<view class=\"conter\" v-if=\"showSlide\">\r\n\t\t\t<view class='aside'>\r\n\t\t\t\t<view class='item acea-row row-center-wrapper' :class='index==navActive?\"on\":\"\"' v-for=\"(item,index) in productList\"\r\n\t\t\t\t :key=\"index\" @click=\"tapNav(index,item)\">\r\n\t\t\t\t\t<text>{{item.name}}</text> \r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"wrapper\">\r\n\t\t\t\t<view class=\"bgcolor\" v-if=\"iSlong\">\r\n\t\t\t\t\t<view class=\"longTab acea-row row-middle\">\r\n\t\t\t\t\t\t<scroll-view scroll-x=\"true\" style=\"white-space: nowrap; display: flex;height:44rpx;\" scroll-with-animation\r\n\t\t\t\t\t\t :scroll-left=\"tabLeft\" show-scrollbar=\"true\">\r\n\t\t\t\t\t\t\t<!-- <view class=\"longItem\" :style='\"width:\"+isWidth+\"px\"'>全部</view> -->\r\n\t\t\t\t\t\t\t<view class=\"longItem\" :style='\"width:\"+isWidth+\"px\"' :class=\"index===tabClick?'click':''\" v-for=\"(item,index) in categoryErList\"\r\n\t\t\t\t\t\t\t :key=\"index\" @click=\"longClick(index,item)\">{{item.name}}</view>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"openList\" @click=\"openTap\"><text class=\"iconfont icon-xiala\"></text></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-else>\r\n\t\t\t\t\t<view class=\"downTab\">\r\n\t\t\t\t\t\t<view class=\"title acea-row row-between-wrapper\">\r\n\t\t\t\t\t\t\t<view>{{categoryTitle}}</view>\r\n\t\t\t\t\t\t\t<view class=\"closeList\" @click=\"closeTap\"><text class=\"iconfont icon-xiala\"></text></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"children\">\r\n\t\t\t\t\t\t\t<view class=\"acea-row row-middle\">\r\n\t\t\t\t\t\t\t\t<view class=\"item line1\" :class=\"index===tabClick?'click':''\" v-for=\"(item,index) in categoryErList\" :key=\"index\"\r\n\t\t\t\t\t\t\t\t @click=\"longClick(index,item)\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"mask\" @click=\"closeTap\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<goodList :tempArr=\"tempArr\" :isLogin=\"isLogin\" @gocartduo=\"goCartDuo\" @detail=\"goDetail\"></goodList>\r\n\t\t\t\t<view class='loadingicon acea-row row-center-wrapper mb-2'>\r\n\t\t\t\t\t<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>{{loadTitle}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"conter\" v-else>\r\n\t\t\t<view class=\"hide_slide\">\r\n\t\t\t\t<view class=\"bgcolor\" v-if=\"iSlong\">\r\n\t\t\t\t\t<view class=\"hongTab acea-row row-middle\">\r\n\t\t\t\t\t\t<scroll-view scroll-x=\"true\" style=\"white-space: nowrap; display: flex;height:44rpx;\" scroll-with-animation\r\n\t\t\t\t\t\t :scroll-left=\"tabLeft\" show-scrollbar=\"true\">\r\n\t\t\t\t\t\t\t<view class=\"longItem\" :style='\"width:\"+isWidth+\"px\"' :class=\"index===tabClick?'click':''\" v-for=\"(item,index) in productList\"\r\n\t\t\t\t\t\t\t :key=\"index\" @click=\"navSwitch(index,item)\">{{item.name}}</view>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"openList\" @click=\"openTap\"><text class=\"iconfont icon-xiangxia\"></text></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-else>\r\n\t\t\t\t\t<view class=\"hownTab\">\r\n\t\t\t\t\t\t<view class=\"title acea-row row-between-wrapper\">\r\n\t\t\t\t\t\t\t<view>{{categoryTitle}}</view>\r\n\t\t\t\t\t\t\t<view class=\"closeList\" @click=\"closeTap\"><text class=\"iconfont icon-xiangxia\"></text></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"children\">\r\n\t\t\t\t\t\t\t<view class=\"acea-row row-middle\">\r\n\t\t\t\t\t\t\t\t<view class=\"item line1\" :class=\"index===tabClick?'click':''\" v-for=\"(item,index) in productList\" :key=\"index\"\r\n\t\t\t\t\t\t\t\t @click=\"navSwitch(index,item)\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"mask\" @click=\"closeTap\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"list_prod\">\r\n\t\t\t\t\t<view class=\"item acea-row row-between-wrapper\" v-for=\"(item,index) in tempArr\" :key='index' @click=\"goDetail(item)\">\r\n\t\t\t\t\t\t<view class=\"pic\">\r\n\t\t\t\t\t\t\t<image :src=\"item.image\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"pictxt\">\r\n\t\t\t\t\t\t\t<view class=\"text line2\">{{item.storeName}}</view>\r\n\t\t\t\t\t\t\t<view class=\"bottom acea-row row-between-wrapper\">\r\n\t\t\t\t\t\t\t\t<view class=\"money\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"sign\">￥</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"price_num\">{{item.price}}</text>\r\n\t\t\t\t\t\t\t\t\t<span class=\"item_sales\" v-if=\"item.sales\">已售{{item.sales}}</span>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view v-if=\"item.stock>0\">\r\n\t\t\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t\t\t<!-- 多规格 -->\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"bnt\" @click.stop=\"goCartDuo(item)\">\r\n\t\t\t\t\t\t\t\t\t\t\t选规格\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"num\" v-if=\"item.cartNum\">{{item.cartNum}}</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"bnt end\" v-else>已售罄</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='loadingicon acea-row row-center-wrapper mb-2'>\r\n\t\t\t\t\t<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>{{loadTitle}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"footer acea-row row-between-wrapper\">\r\n\t\t\t<view class=\"cartIcon acea-row row-center-wrapper\" @click=\"getCartLists(0)\" v-if=\"cartData.cartList.length\">\r\n\t\t\t\t<image src=\"../../../static/images/cart.png\"></image>\r\n\t\t\t\t<view class=\"num\">{{cartCount}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"cartIcon acea-row row-center-wrapper noCart\" v-else>\r\n\t\t\t\t<image src=\"../../../static/images/no_cart.png\"></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"money acea-row row-middle\">\r\n\t\t\t\t<view>￥<text class=\"num\">{{totalPrice}}</text></view>\r\n\t\t\t\t<view class=\"bnt gray_bg\" :class=\"{ 'main_bg': cartCount > 0}\" @click=\"subOrder\">去结算</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- <home></home> -->\r\n\t\t<cartList :cartData=\"cartData\" @closeList=\"closeList\" @ChangeCartNumDan=\"ChangeCartList\" @ChangeSubDel=\"ChangeSubDel\" @ChangeOneDel=\"ChangeOneDel\"></cartList>\r\n\t\t<productWindow :attr=\"attr\" :isShow='1' :iSplus='1' :iScart='1' @myevent=\"onMyEvent\" @ChangeAttr=\"ChangeAttr\"\r\n\t\t @ChangeCartNum=\"ChangeCartNumDuo\" @attrVal=\"attrVal\" @iptCartNum=\"iptCartNum\" @goCat=\"goCatNum\" id='product-window'></productWindow>\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\n\timport {\r\n\t\tgetCategoryList,\r\n\t\tgetProductslist,\r\n\t\tgetAttr,\r\n\t\tpostCartAdd\r\n\t} from '@/api/store.js';\r\n\timport {\r\n\t\tgetCartList,\r\n\t\tgetCartCounts,\r\n\t\tcartDel,\r\n\t\tchangeCartNum,\r\n\t} from '@/api/order.js';\r\n\timport productWindow from '@/components/productWindow';\r\n\timport goodList from '@/components/d_goodList';\r\n\timport cartList from '@/components/cartList';\r\n\timport home from '@/components/home';\r\n\timport {mapGetters} from 'vuex';\r\n\timport {goShopDetail} from '@/libs/order.js';\r\n\timport {toLogin} from '@/libs/login.js';\r\n\timport animationType from '@/utils/animationType.js'\r\n\texport default {\r\n\t\tcomputed: mapGetters(['isLogin', 'uid']),\r\n\t\tcomponents: {\r\n\t\t\tproductWindow,\r\n\t\t\tgoodList,\r\n\t\t\tcartList,\r\n\t\t\thome\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tshowSlide: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault:true \r\n\t\t\t},\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tproductList: [],\r\n\t\t\t\tnavActive: 0,\r\n\t\t\t\tcategoryTitle: '',\r\n\t\t\t\tcategoryErList: [],\r\n\t\t\t\ttabLeft: 0,\r\n\t\t\t\tisWidth: 0, //每个导航栏占位\r\n\t\t\t\ttabClick: 0, //导航栏被点击\r\n\t\t\t\tiSlong: true,\r\n\t\t\t\ttempArr: [],\r\n\t\t\t\tloading: false,\r\n\t\t\t\tloadend: false,\r\n\t\t\t\tloadTitle: '加载更多',\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tlimit: 10,\r\n\t\t\t\tcid: 0, //一级分类\r\n\t\t\t\tsid: 0, //二级分类\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false, //是否隐藏授权\r\n\t\t\t\tattr: {\r\n\t\t\t\t\tcartAttr: false,\r\n\t\t\t\t\tproductAttr: [],\r\n\t\t\t\t\tproductSelect: {}\r\n\t\t\t\t},\r\n\t\t\t\tproductValue: [],\r\n\t\t\t\tattrValue: '', //已选属性\r\n\t\t\t\tstoreName: '', //多属性产品名称\r\n\t\t\t\tid: 0,\r\n\t\t\t\tcartData: {\r\n\t\t\t\t\tcartList: [],\r\n\t\t\t\t\tiScart: false\r\n\t\t\t\t},\r\n\t\t\t\tcartCount: 0,\r\n\t\t\t\ttotalPrice: 0.00,\r\n\t\t\t\tlengthCart: 0\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated(){\r\n\t\t\tif(this.isLogin){\r\n\t\t\t\tthis.getCartNum();\r\n\t\t\t\tthis.getCartLists(1);\r\n\t\t\t}\r\n\t\t\tthis.getAllCategory();\r\n\t\t\tlet that = this;\r\n\t\t\tthat.lengthCart = that.cartData.cartList;\r\n\t\t\t// 获取设备宽度\r\n\t\t\tuni.getSystemInfo({\r\n\t\t\t\tsuccess(e) {\r\n\t\t\t\t\tthat.isWidth = e.windowWidth / 5\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 生成订单；\r\n\t\t\tsubOrder: function() {\r\n\t\t\t\tlet that = this,list = that.cartData.cartList,ids = [];\r\n\t\t\t\tif(list.length){\r\n\t\t\t\t\tlet shoppingCartId = list.map(item => {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\t\"shoppingCartId\": Number(item.id)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.$Order.getPreOrder(\"shoppingCart\", shoppingCartId);\r\n\t\t\t\t\tthat.cartData.iScart = false;\r\n\t\t\t\t}else{\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: '请选择产品'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 计算总价；\r\n\t\t\tgetTotalPrice: function(){\r\n\t\t\t\tlet that = this,list = that.cartData.cartList,totalPrice = 0.00;\r\n\t\t\t\tlist.forEach(item=>{\r\n\t\t\t\t\tif(item.attrStatus ){\r\n\t\t\t\t\t\ttotalPrice = that.$util.$h.Add(totalPrice, that.$util.$h.Mul(item.cartNum, item.vipPrice ? item.vipPrice : item.price));\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tthat.$set(that,'totalPrice',totalPrice);\r\n\t\t\t},\r\n\t\t\tChangeSubDel: function(event) {\r\n\t\t\t\tlet that = this,list = that.cartData.cartList,ids = [];\r\n\t\t\t\tlist.forEach(item=>{\r\n\t\t\t\t\tids.push(item.id)\r\n\t\t\t\t});\r\n\t\t\t\tcartDel(ids.join(\",\")).then(res=>{\r\n\t\t\t\t\tthat.$set(that.cartData,'cartList',[]);\r\n\t\t\t\t\tthat.cartData.iScart = false;\r\n\t\t\t\t\tthat.totalPrice = 0.00;\r\n\t\t\t\t\tthat.page = 1;\r\n\t\t\t\t\tthat.loadend = false;\r\n\t\t\t\t\tthat.tempArr = [];\r\n\t\t\t\t\tthat.productslist();\r\n\t\t\t\t\tthat.getCartNum();\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tChangeOneDel:function(id,index){\r\n\t\t\t\tlet that = this,list = that.cartData.cartList;\r\n\t\t\t\tcartDel(id.toString()).then(res=>{\r\n\t\t\t\t\tlist.splice(index,1);\r\n\t\t\t\t\tif(!list.length){\r\n\t\t\t\t\t\tthat.cartData.iScart = false;\r\n\t\t\t\t\t\tthat.page = 1;\r\n\t\t\t\t\t\tthat.loadend = false;\r\n\t\t\t\t\t\tthat.tempArr = [];\r\n\t\t\t\t\t\tthat.productslist();\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthat.getCartNum();\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetCartLists(iSshow) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tpage: 1,\r\n\t\t\t\t\tlimit: that.cartCount,\r\n\t\t\t\t\tisValid: true\r\n\t\t\t\t};\r\n\t\t\t\tgetCartList(data).then(res => {\r\n\t\t\t\t\tthat.$set(that.cartData, 'cartList', res.data.list);\r\n\t\t\t\t\tif(res.data.list.length){\r\n\t\t\t\t\t\tthat.$set(that.cartData, 'iScart', iSshow?false:!that.cartData.iScart);\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.$set(that.cartData, 'iScart', false);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.getTotalPrice();\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tcloseList(e) {\r\n\t\t\t\tthis.$set(this.cartData, 'iScart', e);\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.loadend = false;\r\n\t\t\t\tthis.tempArr = [];\r\n\t\t\t\tthis.productslist();\r\n\t\t\t},\r\n\t\t\tgetCartNum: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetCartCounts(true, 'sum').then(res => {\r\n\t\t\t\t\tthat.$set(that,'cartCount',res.data.count);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\r\n\t\t\tonMyEvent: function() {\r\n\t\t\t\tthis.$set(this.attr, 'cartAttr', false);\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 默认选中属性\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tDefaultSelect: function() {\r\n\t\t\t\tlet productAttr = this.attr.productAttr;\r\n\t\t\t\tlet value = [];\r\n\t\t\t\tfor (let key in this.productValue) {\r\n\t\t\t\t\tif (this.productValue[key].stock > 0) {\r\n\t\t\t\t\t\tvalue = this.attr.productAttr.length ? key.split(\",\") : [];\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tfor (let i = 0; i < productAttr.length; i++) {\r\n\t\t\t\t\tthis.$set(productAttr[i], \"index\", value[i]);\r\n\t\t\t\t}\r\n\t\t\t\t//sort();排序函数:数字-英文-汉字；\r\n\t\t\t\tlet productSelect = this.productValue[value.join(\",\")];\r\n\t\t\t\tif (productSelect && productAttr.length) {\r\n\t\t\t\t\tthis.$set(this.attr.productSelect,\"storeName\",this.storeName);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"image\", productSelect.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"price\", productSelect.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"stock\", productSelect.stock);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"unique\", productSelect.id);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"vipPrice\", productSelect.vipPrice);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", value.join(\",\"));\r\n\t\t\t\t} else if (!productSelect && productAttr.length) {\r\n\t\t\t\t\tthis.$set(this.attr.productSelect,\"storeName\",this.storeName);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"image\", this.storeInfo.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"price\", this.storeInfo.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"stock\", 0);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"unique\", \"\");\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 0);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", \"\");\r\n\t\t\t\t} else if (!productSelect && !productAttr.length) {\r\n\t\t\t\t\tthis.$set(this.attr.productSelect,\"storeName\",this.storeName);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"image\", this.storeInfo.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"price\", this.storeInfo.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"stock\", this.storeInfo.stock);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect,\"unique\",this.storeInfo.unique || \"\");\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", \"\");\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 属性变动赋值\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tChangeAttr: function(res) {\r\n\t\t\t\tlet productSelect = this.productValue[res];\r\n\t\t\t\tif (productSelect) {\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"image\", productSelect.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"price\", productSelect.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"stock\", productSelect.stock);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"unique\", productSelect.id);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"vipPrice\", productSelect.vipPrice);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", res);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"image\", this.productInfo.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"price\", this.productInfo.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"stock\", 0);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"unique\", this.productInfo.id);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"vipPrice\", this.productInfo.vipPrice);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", \"\");\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tattrVal(val) {\r\n\t\t\t\tthis.$set(this.attr.productAttr[val.indexw], 'index', this.attr.productAttr[val.indexw].attrValues[val\r\n\t\t\t\t\t.indexn]);\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 购物车手动填写\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tiptCartNum: function(e) {\r\n\t\t\t\tthis.$set(this.attr.productSelect, 'cart_num', e);\r\n\t\t\t},\r\n\t\t\tonLoadFun() {},\r\n\t\t\t// 产品列表\r\n\t\t\tproductslist: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (that.loadend) return; //如果返回列表长度小于请求分页长度，就让他为true,就不继续请求了\r\n\t\t\t\tif (that.loading) return;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tthat.loadTitle = '';\r\n\t\t\t\tgetProductslist({\r\n\t\t\t\t\tpage: that.page,\r\n\t\t\t\t\tlimit: that.limit,\r\n\t\t\t\t\ttype: 1,\r\n\t\t\t\t\tcid: that.sid\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tlet list = res.data.list,\r\n\t\t\t\t\t\tloadend = list.length < that.limit; //返回列表长度小于请求分页长度为true,反之为false\r\n\t\t\t\t\tthat.tempArr = that.$util.SplitArray(list, that.tempArr); \r\n\t\t\t\t\tthat.$set(that, 'tempArr', that.tempArr);\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.loadend = loadend;\r\n\t\t\t\t\tthat.loadTitle = loadend ? \"😕人家是有底线的~~\" : \"加载更多\";\r\n\t\t\t\t\tthat.page = that.page + 1;\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tthat.loading = false,\r\n\t\t\t\t\t\tthat.loadTitle = '加载更多'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 改变多属性购物车\r\n\t\t\tChangeCartNumDuo(changeValue) {\r\n\t\t\t\t//changeValue:是否 加|减\r\n\t\t\t\t//获取当前变动属性\r\n\t\t\t\tlet productSelect = this.productValue[this.attrValue];\r\n\t\t\t\t//如果没有属性,赋值给商品默认库存\r\n\t\t\t\tif (productSelect === undefined && !this.attr.productAttr.length)\r\n\t\t\t\t\tproductSelect = this.attr.productSelect;\r\n\t\t\t\t//无属性值即库存为0；不存在加减；\r\n\t\t\t\tif (productSelect === undefined) return;\r\n\t\t\t\tlet stock = productSelect.stock || 0;\r\n\t\t\t\tlet num = this.attr.productSelect;\r\n\t\t\t\tif (changeValue) {\r\n\t\t\t\t\tnum.cart_num++;\r\n\t\t\t\t\tif (num.cart_num > stock) {\r\n\t\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", stock);\r\n\t\t\t\t\t\tthis.$set(this, \"cart_num\", stock);\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tnum.cart_num--;\r\n\t\t\t\t\tif (num.cart_num < 1) {\r\n\t\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\t\tthis.$set(this, \"cart_num\", 1);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\t// 已经加入购物车时的购物加减；\r\n\t\t\tChangeCartList(changeValue, index) {\r\n\t\t\t\tlet list = this.cartData.cartList;\r\n\t\t\t\tlet num = list[index];\r\n\t\t\t\tlet stock = list[index].stock;\r\n\t\t\t\tthis.ChangeCartNum(changeValue, num, stock, 0, num.productId,index,1);\r\n\t\t\t\tif(!list.length){\r\n\t\t\t\t\tthis.cartData.iScart = false;\r\n\t\t\t\t\tthis.page = 1;\r\n\t\t\t\t\tthis.loadend = false;\r\n\t\t\t\t\tthis.tempArr = [];\r\n\t\t\t\t\tthis.productslist();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 购物车加减计算函数\r\n\t\t\tChangeCartNum: function(changeValue,index) {\r\n\t\t\t\tif (changeValue) {\r\n\t\t\t\t\tif (index.cartNum >= index.stock) {\r\n\t\t\t\t\t\tindex.cartNum = index.stock;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tindex.cartNum++;\r\n\t\t\t\t\t\tchangeCartNum(index.id, index.cartNum).then(res => {\r\n\t\t\t\t\t\t\tthis.getCartNum(true);\r\n\t\t\t\t\t\t\tthis.getTotalPrice();\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tindex.cartNum--;\r\n\t\t\t\t\tchangeCartNum(index.id, index.cartNum).then(res => {\r\n\t\t\t\t\t\tthis.getCartNum(true);\r\n\t\t\t\t\t\tthis.getTotalPrice();\r\n\t\t\t\t\t});\r\n\t\t\t\t\tif(index.cartNum == 0){\r\n\t\t\t\t\t\tcartDel(index.id).then(res=>{\r\n\t\t\t\t\t\t\tthis.getCartLists(1);\r\n\t\t\t\t\t\t\tthis.getTotalPrice();\r\n\t\t\t\t\t\t\tthis.productslist();\r\n\t\t\t\t\t\t\tthis.getCartNum();\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 多规格加入购物车；\r\n\t\t\tgoCatNum() {\r\n\t\t\t\tthis.goCat(1);\r\n\t\t\t},\r\n\t\t\t/*\r\n\t\t\t * 加入购物车\r\n\t\t\t */\r\n\t\t\tgoCat: function(num) {\r\n\t\t\t\tlet that = this,\r\n\t\t\t\t\tproductSelect = that.productValue[this.attrValue];\r\n\t\t\t\t//打开属性\r\n\t\t\t\tif (that.attrValue) {\r\n\t\t\t\t\t//默认选中了属性，但是没有打开过属性弹窗还是自动打开让用户查看默认选中的属性\r\n\t\t\t\t\tthat.attr.cartAttr = !that.isOpen ? true : false;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (that.isOpen) that.attr.cartAttr = true;\r\n\t\t\t\t\telse that.attr.cartAttr = !that.attr.cartAttr;\r\n\t\t\t\t}\r\n\t\t\t\t//只有关闭属性弹窗时进行加入购物车\r\n\t\t\t\tif (that.attr.cartAttr === true && that.isOpen === false)\r\n\t\t\t\t\treturn (that.isOpen = true);\r\n\t\t\t\t//如果有属性,没有选择,提示用户选择\r\n\t\t\t\tif (\r\n\t\t\t\t\tthat.attr.productAttr.length &&\r\n\t\t\t\t\tproductSelect.stock === 0 &&\r\n\t\t\t\t\tthat.isOpen === true\r\n\t\t\t\t)\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: \"产品库存不足，请选择其它\"\r\n\t\t\t\t\t});\r\n\t\t\t\tif (num === 1) {\r\n\t\t\t\t\tlet q = {\r\n\t\t\t\t\t\tproductId: parseFloat(that.id),\r\n\t\t\t\t\t\tcartNum: parseFloat(that.attr.productSelect.cart_num),\r\n\t\t\t\t\t\tisNew: false,\r\n\t\t\t\t\t\tproductAttrUnique: that.attr.productSelect !== undefined ?\r\n\t\t\t\t\t\t\tthat.attr.productSelect.unique : that.productInfo.id\r\n\t\t\t\t\t};\r\n\t\t\t\t\tpostCartAdd(q).then(function(res) {\r\n\t\t\t\t\t\t\tthat.isOpen = false;\r\n\t\t\t\t\t\t\tthat.attr.cartAttr = false;\r\n\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: \"添加购物车成功\",\r\n\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\t\t\t\t\t\tthat.getCartNum(true);\r\n\t\t\t\t\t\t\t\t\t\tthat.getCartLists(1);\r\n\t\t\t\t\t\t\t\t\t},100)\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(res => {\r\n\t\t\t\t\t\t\tthat.isOpen = false;\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: res\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.getPreOrder();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgoCartDuo(item) {\r\n\t\t\t\tif (!this.isLogin) {\r\n\t\t\t\t\tthis.getIsLogin();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\ttitle: '加载中'\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.storeName = item.storeName;\r\n\t\t\t\t\tthis.getAttrs(item.id,item.storeName);\r\n\t\t\t\t\tthis.$set(this, 'id', item.id);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetIsLogin() {\r\n\t\t\t\ttoLogin();\r\n\t\t\t},\r\n\t\t\t// 商品详情接口；\r\n\t\t\tgetAttrs(id) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetAttr(id).then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthat.$set(that.attr, 'productAttr', res.data.productAttr);\r\n\t\t\t\t\tthat.$set(that, 'productValue', res.data.productValue);\r\n\t\t\t\t\tlet productAttr = that.attr.productAttr.map(item => {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tattrName : item.attrName,\r\n\t\t\t\t\t\tattrValues: item.attrValues.split(','),\r\n\t\t\t\t\t\tid:item.id,\r\n\t\t\t\t\t\tisDel:item.isDel,\r\n\t\t\t\t\t\tproductId:item.productId,\r\n\t\t\t\t\t\ttype:item.type\r\n\t\t\t\t\t }\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.$set(that.attr,'productAttr',productAttr);\r\n\t\t\t\t\tthis.$set(that.attr, 'cartAttr', true);\r\n\t\t\t\t\tthat.DefaultSelect();\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 去详情页\r\n\t\t\tgoDetail(item) {\r\n\t\t\t\tif (!this.isLogin) {\r\n\t\t\t\t\ttoLogin();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tgoShopDetail(item, this.uid).then(res => {\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\tanimationType: animationType.type,\r\t\t\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\t\t\turl: `/pages/goods_details/index?id=${item.id}`\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\r\n\t\t\topenTap() {\r\n\t\t\t\tthis.iSlong = false\r\n\t\t\t},\r\n\t\t\tcloseTap() {\r\n\t\t\t\tthis.iSlong = true\r\n\t\t\t},\r\n\t\t\tgetAllCategory: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetCategoryList().then(res => {\r\n\t\t\t\t\tres.data.forEach((item)=>{\r\n\t\t\t\t\t\tif(item.child){\r\n\t\t\t\t\t\t\titem.child.unshift({\r\n\t\t\t\t\t\t\t\tid:item.id,\r\n\t\t\t\t\t\t\t\tname:'全部'\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\tlet data = res.data;\r\n\t\t\t\t\tthat.categoryTitle = data[0].name;\r\n\t\t\t\t\tthat.sid = data[0].id;\r\n\t\t\t\t\tthat.productList = data;\r\n\t\t\t\t\tthat.categoryErList = res.data[0].child ? res.data[0].child : [];\r\n\t\t\t\t\tthat.page = 1;\r\n\t\t\t\t\tthat.loadend = false;\r\n\t\t\t\t\tthat.tempArr = [];\r\n\t\t\t\t\tthat.productslist();\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttapNav(index, item) {\r\n\t\t\t\tlet list = this.productList[index];\r\n\t\t\t\tthis.navActive = index;\r\n\t\t\t\tthis.categoryTitle = list.name;\r\n\t\t\t\tthis.categoryErList = item.child ? item.child : [];\r\n\t\t\t\tthis.tabClick = 0;\r\n\t\t\t\tthis.tabLeft = 0;\r\n\t\t\t\t// this.cid = list.id;\r\n\t\t\t\tthis.sid = item.id;\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.loadend = false;\r\n\t\t\t\tthis.tempArr = [];\r\n\t\t\t\tthis.productslist();\r\n\t\t\t},\r\n\t\t\tnavSwitch(index,item){\r\n\t\t\t\tif (this.productList.length > 3) {\r\n\t\t\t\t\tthis.tabLeft = (index - 1) * (this.isWidth + 6) //设置下划线位置\r\n\t\t\t\t};\r\n\t\t\t\tthis.tabClick = index; //设置导航点击了哪一个\r\n\t\t\t\tthis.iSlong = true;\r\n\t\t\t\tthis.sid = item.id;\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.loadend = false;\r\n\t\t\t\tthis.tempArr = [];\r\n\t\t\t\tthis.productslist();\r\n\t\t\t},\r\n\t\t\t// 导航栏点击\r\n\t\t\tlongClick(index,item) {\r\n\t\t\t\tif (this.productList.length > 3) {\r\n\t\t\t\t\tthis.tabLeft = (index - 1) * (this.isWidth + 6) //设置下划线位置\r\n\t\t\t\t};\r\n\t\t\t\tthis.tabClick = index; //设置导航点击了哪一个\r\n\t\t\t\tthis.iSlong = true;\r\n\t\t\t\tthis.sid = item.id;\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.loadend = false;\r\n\t\t\t\tthis.tempArr = [];\r\n\t\t\t\tthis.productslist();\r\n\t\t\t},\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\tpage{\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\t::-webkit-scrollbar {\r\n\twidth: 0;\r\n\theight: 0;\r\n\tcolor: transparent;\r\n\tdisplay: none;\r\n\t}\r\n\t.goodCate {\r\n\t\tbackground-color: $crmeb-bg-color;\r\n\t\t.mask {\r\n\t\t\t// z-index: 99; \r\n\t\t}\r\n\t\t/deep/.attrProduct{\r\n\t\t\t.mask {\r\n\t\t\t\tz-index: 100;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.header {\r\n\t\t\tposition: fixed;\r\n\t\t\theight: 128rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 0;\r\n\t\t\twidth: 100%;\r\n\t\t\tz-index: 99;\r\n\t\t\tborder-bottom: 1px solid #F0F0F0;\r\n\t\t\t.pageIndex{\r\n\t\t\t\twidth: 68rpx;\r\n\t\t\t\theight: 68rpx;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t@include main_bg_color(theme);\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tline-height: 68rpx;\r\n\t\t\t\t.iconfont{\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.search {\r\n\t\t\t\twidth: 600rpx;\r\n\t\t\t\t/* #ifdef MP || APP-PLUS */\r\n\t\t\t\twidth: 550rpx;\r\n\t\t\t\t/* #endif */\r\n\t\t\t\theight: 68rpx;\r\n\t\t\t\tborder-radius: 36rpx;\r\n\t\t\t\tbackground-color: #F8F8F8;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #ADADAD;\r\n\t\t\t\tmargin-left: 22rpx;\r\n\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tmargin: 4rpx 16rpx 0 0;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.conter {\r\n\t\t\tpadding-top: 64px;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\r\n\t\t\t.aside {\r\n\t\t\t\tposition: fixed;\r\n\t\t\t\twidth: 23%;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\tbottom: 0;\r\n\t\t\t\ttop: 0;\r\n\t\t\t\tbackground-color: $crmeb-bg-color-grey;\r\n\t\t\t\toverflow-y: auto;\r\n\t\t\t\toverflow-x: hidden;\r\n\t\t\t\tmargin-top: 128rpx;\r\n\t\t\t\tz-index: 99;\r\n\t\t\t\tpadding-bottom: 140rpx;\r\n\t\t\r\n\t\t\t\t.item {\r\n\t\t\t\t\theight: 100rpx;\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: $crmeb-font-color;\r\n\t\t\r\n\t\t\t\t\t&.on {\r\n\t\t\t\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t@include main_color(theme);\r\n\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\tposition: relative;\r\n\t\t\r\n\t\t\t\t\t\t&::after {\r\n\t\t\t\t\t\t\tcontent: \"\";\r\n\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\twidth: 6rpx;\r\n\t\t\t\t\t\t\theight: 46rpx;\r\n\t\t\t\t\t\t\t@include main_bg_color(theme);\r\n\t\t\t\t\t\t\tborder-radius: 0 4rpx 4rpx 0;\r\n\t\t\t\t\t\t\tleft: 0\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.wrapper {\r\n\t\t\tmargin-top: 104rpx;\r\n\t\t\twidth: 77%;\r\n\t\t\tfloat: right;\r\n\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t\tpadding-bottom: 130rpx;\r\n\t\t}\r\n\t\t.hide_slide{\r\n\t\t\tmargin-top: 104rpx;\r\n\t\t\twidth: 100%;\r\n\t\t\tfloat: right;\r\n\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t\tpadding-bottom: 130rpx;\r\n\t\t}\r\n\t\t.bgcolor {\r\n\t\t\twidth: 100%;\r\n\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t}\r\n\t\t\t\t\r\n\t\t.goodsList {\r\n\t\t\tmargin-top: 0 !important;\r\n\t\t}\r\n\t\t\t\t\r\n\t\t.longTab {\r\n\t\t\twidth: 65%;\r\n\t\t\tposition: fixed;\r\n\t\t\ttop: 0;\r\n\t\t\tmargin-top: 128rpx;\r\n\t\t\theight: 100rpx;\r\n\t\t\tz-index: 99;\r\n\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t}\r\n\t\t.hongTab{\r\n\t\t\twidth: 100%;\r\n\t\t\tposition: fixed;\r\n\t\t\ttop: 0;\r\n\t\t\tmargin-top: 128rpx;\r\n\t\t\theight: 100rpx;\r\n\t\t\tz-index: 99;\r\n\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t}\r\n\t\t.longItem {\r\n\t\t\theight: 44rpx;\r\n\t\t\tdisplay: inline-block;\r\n\t\t\tline-height: 44rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\toverflow: hidden;\r\n\t\t\ttext-overflow: ellipsis;\r\n\t\t\twhite-space: nowrap;\r\n\t\t\tcolor: $crmeb-font-color;\r\n\t\t\tbackground-color: $crmeb-bg-color-grey;\r\n\t\t\tborder-radius: 22rpx;\r\n\t\t\tmargin-left: 12rpx;\r\n\t\t\t\r\n\t\t\t&.click {\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\t@include cate-two-btn(theme);\r\n\t\t\t\t@include main_color(theme);\r\n\t\t\t}\r\n\t\t}\r\n\t\t\t\r\n\t\t.underlineBox {\r\n\t\t\theight: 3px;\r\n\t\t\twidth: 20%;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-content: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\ttransition: .5s;\r\n\t\t\t\r\n\t\t\t.underline {\r\n\t\t\t\twidth: 33rpx;\r\n\t\t\t\theight: 4rpx;\r\n\t\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t\t}\r\n\t\t}\t\r\n\t\t.openList {\r\n\t\t\twidth: 12%;\r\n\t\t\theight: 100rpx;\r\n\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t\tline-height: 100rpx;\r\n\t\t\tpadding-left: 30rpx;\r\n\t\t\tposition: fixed;\r\n\t\t\tright: 0;\r\n\t\t\ttop: 128rpx;\r\n\t\t\tz-index: 99;\r\n\t\t\t\t\r\n\t\t\t.iconfont {\r\n\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\tcolor: $crmeb-font-color-subtitle;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\t\t\r\n\t\t.downTab {\r\n\t\t\twidth: 77%;\r\n\t\t\tposition: fixed;\r\n\t\t\ttop: 0;\r\n\t\t\tmargin-top: 128rpx;\r\n\t\t\tz-index: 99;\r\n\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t\tright: 0;\r\n\t\t}\r\n\t\t.hownTab{\r\n\t\t\twidth: 100%;\r\n\t\t\tposition: fixed;\r\n\t\t\ttop: 0;\r\n\t\t\tmargin-top: 128rpx;\r\n\t\t\tz-index: 99;\r\n\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t\tright: 0;\r\n\t\t}\r\n\t\t.title {\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: $crmeb-font-color-assist;\r\n\t\t\t// padding-left: 20rpx;\r\n\t\t\t\r\n\t\t\t.closeList {\r\n\t\t\t\twidth: 90rpx;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tline-height: 100rpx;\r\n\t\t\t\tpadding-left: 30rpx;\r\n\t\t\t\ttransform: rotate(180deg);\r\n\t\t\t\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\tcolor: $crmeb-font-color-subtitle;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\t\r\n\t\t.children {\r\n\t\t\tmax-height: 500rpx;\r\n\t\t\toverflow-x: hidden;\r\n\t\t\toverflow-y: auto;\r\n\t\t\tpadding-bottom: 20rpx;\r\n\t\t\t\r\n\t\t\t.item {\r\n\t\t\t\theight: 60rpx;\r\n\t\t\t\tbackground-color: $crmeb-bg-color-grey;\r\n\t\t\t\tborder-radius: 30rpx;\r\n\t\t\t\tline-height: 60rpx;\r\n\t\t\t\tpadding: 0 15rpx;\r\n\t\t\t\tmargin: 0 0 20rpx 20rpx;\r\n\t\t\t\twidth: 165rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\r\n\t\t\t\t&.click {\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t@include cate-two-btn(theme);\r\n\t\t\t\t\t@include main_color(theme);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.list_prod{\r\n\t\t\tpadding: 0 30rpx;\r\n\t\t\t.item{\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\t.pic{\r\n\t\t\t\t\twidth: 690rpx;\r\n\t\t\t\t\theight: 284rpx;\r\n\t\t\t\t\tmargin: auto;\r\n\t\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\timage{\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t.pictxt{\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t.text{\r\n\t\t\t\t\t\tfont-size:30rpx;\r\n\t\t\t\t\t\tfont-family:PingFang SC;\r\n\t\t\t\t\t\tfont-weight:bold;\r\n\t\t\t\t\t\tcolor: #282828;\r\n\t\t\t\t\t\tmargin: 20rpx 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.bottom{\r\n\t\t\t\t\t\t.money{\r\n\t\t\t\t\t\t\tfont-size: 42rpx;\r\n\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t\tmargin-right: 18rpx;\r\n\t\t\t\t\t\t\t@include price_color(theme);\r\n\t\t\t\t\t\t\t.sign{\r\n\t\t\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t.item_sales{\r\n\t\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\t\tfont-family: PingFang SC;\r\n\t\t\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\t\t\tpadding-left: 17rpx;\r\n\t\t\t\t\t\t\t\tcolor: #8e8e8e;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t.cart{\r\n\t\t\t\t\t\t\theight: 56rpx;\r\n\t\t\t\t\t\t\t.pictrue{\r\n\t\t\t\t\t\t\t\tcolor: #E93323;\r\n\t\t\t\t\t\t\t\tfont-size:46rpx;\r\n\t\t\t\t\t\t\t\twidth: 50rpx;\r\n\t\t\t\t\t\t\t\theight: 50rpx;\r\n\t\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t\t\tline-height: 50rpx;\r\n\t\t\t\t\t\t\t\t&.icon-jiahao{\r\n\t\t\t\t\t\t\t\t\t background:linear-gradient(140deg, #FA6514 0%, #E93323 100%);\r\n\t\t\t\t\t\t\t\t\t-webkit-background-clip:text;\r\n\t\t\t\t\t\t\t\t\t-webkit-text-fill-color:transparent;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t.num{\r\n\t\t\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t\t\t\tcolor: #282828;\r\n\t\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t\t\twidth: 80rpx;\r\n\t\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t.bnt{\r\n\t\t\t\t\t\t\tpadding: 0 30rpx;\r\n\t\t\t\t\t\t\theight: 56rpx;\r\n\t\t\t\t\t\t\tline-height: 56rpx;\r\n\t\t\t\t\t\t\t@include main_bg_color(theme);\r\n\t\t\t\t\t\t\tborder-radius:42rpx;\r\n\t\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\t\t.num{\r\n\t\t\t\t\t\t\t\t@include main_color(theme);\r\n\t\t\t\t\t\t\t\t@include coupons_border_color(theme);\r\n\t\t\t\t\t\t\t\tbackground: #fff;\r\n\t\t\t\t\t\t\t\tmin-width: 12rpx;\r\n\t\t\t\t\t\t\t\tborder-radius: 15px;\r\n\t\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\t\tright: -14rpx;\r\n\t\t\t\t\t\t\t\ttop: -15rpx;\r\n\t\t\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\t\t\tpadding: 0 10rpx;\r\n\t\t\t\t\t\t\t\theight: 34rpx;\r\n\t\t\t\t\t\t\t\tline-height: 34rpx;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t.end{\r\n\t\t\t\t\t\t\tpadding: 0 30rpx;\r\n\t\t\t\t\t\t\theight: 56rpx;\r\n\t\t\t\t\t\t\tline-height: 56rpx;\r\n\t\t\t\t\t\t\tborder-radius:42rpx;\r\n\t\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\t\tbackground:rgba(203,203,203,1);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.footer {\r\n\t\t\tposition: fixed;\r\n\t\t\tleft: 0;\r\n\t\t\tbottom:env(safe-area-inset-bottom); \r\n\t\t\twidth: 100%;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tbox-shadow: 0px -3rpx 16rpx rgba(36, 12, 12, 0.05);\r\n\t\t\tz-index: 101;\r\n\t\t\tpadding: 0 30rpx;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\theight: 100rpx;\r\n\t\t\t&:after{\r\n\t\t\t\tcontent:'';\r\n\t\t\t\theight:env(safe-area-inset-bottom); // 这里是重点\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop:100%;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\tright:0;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t}\r\n\r\n\t\t\t.cartIcon {\r\n\t\t\t\twidth: 96rpx;\r\n\t\t\t\theight: 96rpx;\r\n\t\t\t\t@include main_bg_color(theme);\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tmargin-top: -36rpx;\r\n\r\n\t\t\t\t&.noCart {\r\n\t\t\t\t\tbackground: #CBCBCB !important;\r\n\t\t\t\t}\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 49rpx;\r\n\t\t\t\t\theight: 46rpx;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.num {\r\n\t\t\t\t\tmin-width: 12rpx;\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\tborder-radius: 15px;\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tright: -6rpx;\r\n\t\t\t\t\ttop: -10rpx;\r\n\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\tpadding: 0 10rpx;\r\n\t\t\t\t\theight: 34rpx;\r\n\t\t\t\t\tline-height: 34rpx;\r\n\t\t\t\t\t@include main_color(theme);\r\n\t\t\t\t\t@include coupons_border_color(theme);\r\n\t\t\t\t\tbackground-color: #fff;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.money {\r\n\t\t\t\t@include price_color(theme);\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\r\n\t\t\t\t.num {\r\n\t\t\t\t\tfont-size: 42rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.bnt {\r\n\t\t\t\t\twidth: 222rpx;\r\n\t\t\t\t\theight: 76rpx;\r\n\t\t\t\t\tborder-radius: 46rpx;\r\n\t\t\t\t\tline-height: 76rpx;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\tmargin-left: 24rpx;\r\n\t\t\t\t}\r\n\t\t\t\t.main_bg{\r\n\t\t\t\t\t@include main_bg_color(theme);\r\n\t\t\t\t}\r\n\t\t\t\t.gray_bg{\r\n\t\t\t\t\tbackground-color: #B3B3B4;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=62f09ab4&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/d_goodList/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=62f09ab4&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"goodsList\">\r\n\t\t<view class=\"item\" v-for=\"(item,index) in tempArr\" :key='index' @click=\"goDetail(item)\">\r\n\t\t\t<view class=\"pictrue\">\r\n\t\t\t\t<image :src=\"item.recommend_image\" mode=\"aspectFill\" v-if=\"item.recommend_image\"></image>\r\n\t\t\t\t<image :src=\"item.image\" mode=\"aspectFill\" v-else></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"text line2\">{{item.storeName}}</view>\r\n\t\t\t<view class=\"bottom acea-row row-between-wrapper\">\r\n\t\t\t\t<view class=\"sales acea-row row-middle\">\r\n\t\t\t\t\t<view class=\"money\"><text>￥</text>{{item.price}} <text class=\"item_sales\">已售 {{item.sales}}</text> </view>\r\n\t\t\t\t\t<!-- <view></view> -->\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-if=\"item.stock>0\">\r\n\t\t\t\t    <view class=\"bnt\" v-if=\"item.activity && (item.activity.type === '1' || item.activity.type === '2' || item.activity.type === '3')\">立即购买</view>\r\n\t\t\t\t\t<view v-else>\r\n\t\t\t\t\t\t<view class=\"bnt\" @click.stop=\"goCartDuo(item)\">\r\n\t\t\t\t\t\t\t加入购物车\r\n\t\t\t\t\t\t\t<view class=\"num\" v-if=\"item.cartNum\">{{item.cartNum}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"end\" v-else>已售罄</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tname: 'd_goodList',\r\n\t\tprops: {\r\n\t\t\tdataConfig: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: () => {}\r\n\t\t\t},\r\n\t\t\ttempArr:{\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault:[]\r\n\t\t\t},\r\n\t\t\tisLogin:{\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault:false\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t};\r\n\t\t},\r\n\t\tcreated() {},\r\n\t\tmounted() {},\r\n\t\tmethods: {\r\n\t\t\tgoDetail(item){\r\n\t\t\t\tthis.$emit('detail',item);\r\n\t\t\t},\r\n\t\t\tgoCartDuo(item){\r\n\t\t\t\tthis.$emit('gocartduo',item);\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.goodsList{\r\n\t\tpadding: 0 30rpx;\r\n\t\t.item{\r\n\t\t\twidth: 100%;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tmargin-bottom: 63rpx;\r\n\t\t\t.pictrue{\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 216rpx;\r\n\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\timage{\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.text{\r\n\t\t\t\tfont-size:30rpx;\r\n\t\t\t\tfont-family:PingFang SC;\r\n\t\t\t\tfont-weight:bold;\r\n\t\t\t\tcolor: #282828;\r\n\t\t\t\tmargin: 20rpx 0;\r\n\t\t\t}\r\n\t\t\t.bottom{\r\n\t\t\t\t.sales{\r\n\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\tcolor: #8E8E8E;\r\n\t\t\t\t\t.money{\r\n\t\t\t\t\t\tfont-size: 42rpx;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tmargin-right: 18rpx;\r\n\t\t\t\t\t\t@include price_color(theme);\r\n\t\t\t\t\t\t.item_sales{\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\tfont-family: PingFang SC;\r\n\t\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\t\tpadding-left: 17rpx;\r\n\t\t\t\t\t\t\tcolor: #8e8e8e;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\ttext{\r\n\t\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t.cart{\r\n\t\t\t\t\theight: 56rpx;\r\n\t\t\t\t\t.pictrue{\r\n\t\t\t\t\t\tcolor: #E93323;\r\n\t\t\t\t\t\tfont-size:46rpx;\r\n\t\t\t\t\t\twidth: 50rpx;\r\n\t\t\t\t\t\theight: 50rpx;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\tline-height: 50rpx;\r\n\t\t\t\t\t\t&.icon-jiahao{\r\n\t\t\t\t\t\t\t background:linear-gradient(140deg, #FA6514 0%, #E93323 100%);\r\n\t\t\t\t\t\t\t-webkit-background-clip:text;\r\n\t\t\t\t\t\t\t-webkit-text-fill-color:transparent;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.num{\r\n\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t\tcolor: #282828;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\twidth: 80rpx;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t.bnt{\r\n\t\t\t\t\tpadding: 0 30rpx;\r\n\t\t\t\t\theight: 56rpx;\r\n\t\t\t\t\tline-height: 56rpx;\r\n\t\t\t\t\t@include main_bg_color(theme);\r\n\t\t\t\t\tborder-radius:42rpx;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t.num{\r\n\t\t\t\t\t\t@include main_color(theme);\r\n\t\t\t\t\t\t@include coupons_border_color(theme);\r\n\t\t\t\t\t\tbackground: #fff;\r\n\t\t\t\t\t\tmin-width: 12rpx;\r\n\t\t\t\t\t\tborder-radius: 15px;\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\tright: -14rpx;\r\n\t\t\t\t\t\ttop: -15rpx;\r\n\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\tpadding: 0 10rpx;\r\n\t\t\t\t\t\theight: 34rpx;\r\n\t\t\t\t\t\tline-height: 34rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t.end{\r\n\t\t\t\t\tpadding: 0 30rpx;\r\n\t\t\t\t\theight: 56rpx;\r\n\t\t\t\t\tline-height: 56rpx;\r\n\t\t\t\t\tborder-radius:42rpx;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\tbackground:rgba(203,203,203,1);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294180298\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./optimization.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./optimization.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294179441\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import { render, staticRenderFns, recyclableRender, components } from \"./contracted.vue?vue&type=template&id=289d3ae8&scoped=true&\"\nvar renderjs\nimport script from \"./contracted.vue?vue&type=script&lang=js&\"\nexport * from \"./contracted.vue?vue&type=script&lang=js&\"\nimport style0 from \"./contracted.vue?vue&type=style&index=0&id=289d3ae8&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"289d3ae8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/goods_cate/components/contracted.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./contracted.vue?vue&type=template&id=289d3ae8&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.productList.length\n  var g1 = _vm.productList.length == 0 && _vm.page > 1\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./contracted.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./contracted.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"productSort\">\r\n\t\t<view class='nav acea-row row-middle'>\r\n\t\t\t<scroll-view class=\"scroll-view_x\" scroll-x scroll-with-animation :scroll-left=\"scrollLeft\" style=\"width:auto;overflow:hidden;\">\r\n\t\t\t\t<view class='item' v-for=\"(item,index) in navLists\" :key='index' :class='active==index?\"on\":\"\"' @click='tabSelect(index,item.id)'\r\n\t\t\t\t :id=\"'id'+index\">\r\n\t\t\t\t\t<view>{{item.name}}</view>\r\n\t\t\t\t\t<view class='line' v-if=\"active==index\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t</view>\r\n\t\t<view class='list acea-row row-between-wrapper'>\r\n\t\t\t<view class='item' v-for=\"(item,index) in productList\" :key=\"index\" @click=\"godDetail(item)\">\r\n\t\t\t\t<view class='pictrue'>\r\n\t\t\t\t\t<image :src='item.image'></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='text'>\r\n\t\t\t\t\t<view class='name line1'>{{item.storeName}}</view>\r\n\t\t\t\t\t<view class='money'>￥<text class='num'>{{item.price}}</text></view>\r\n\t\t\t\t\t<view class='vip acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t<view>已售{{item.sales + item.ficti}}件</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class='loadingicon acea-row row-center-wrapper' v-if=\"productList.length\">\r\n\t\t\t\t<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>{{loadTitle}}\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class='noCommodity' v-if=\"productList.length== 0 && page > 1\">\r\n\t\t\t<view class='pictrue'>\r\n\t\t\t\t<image src='../../../static/images/noShopper.png'></image>\r\n\t\t\t</view>\r\n\t\t\t<recommend :hostProduct=\"hostProduct\"></recommend>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tgetCategoryList,\r\n\t\tgetProductslist,\r\n\t\tgetProductHot\r\n\t} from '@/api/store.js';\r\n\timport {\r\n\t\tgoShopDetail\r\n\t} from '@/libs/order.js'\r\n\timport recommend from '@/components/recommend';\r\n\timport {\r\n\t\tmapGetters\r\n\t} from \"vuex\";\r\n\timport animationType from '@/utils/animationType.js'\r\n\texport default {\r\n\t\tcomputed: mapGetters(['uid']),\r\n\t\tcomponents: {\r\n\t\t\trecommend,\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tnavLists: [],\r\n\t\t\t\tproductList: [],\r\n\t\t\t\tscrollLeft: 0,\r\n\t\t\t\tactive: 0,\r\n\t\t\t\tloading: false,\r\n\t\t\t\tloadend: false,\r\n\t\t\t\tloadTitle: '加载更多',\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tlimit: 10,\r\n\t\t\t\tcid: 0,\r\n\t\t\t\thostProduct: []\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated(){\r\n\t\t\tthis.getAllCategory();\r\n\t\t\tthis.getProductList();\r\n\t\t\tthis.get_host_product();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 去详情页\r\n\t\t\tgodDetail(item) {\r\n\t\t\t\tgoShopDetail(item, this.uid).then(res => {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\tanimationType: animationType.type,\r\t\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\t\turl: `/pages/goods_details/index?id=${item.id}`\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttabSelect(index, id) {\r\n\t\t\t\tthis.active = index;\r\n\t\t\t\tconst query = uni.createSelectorQuery().in(this);\r\n\t\t\t\tquery.select('#id' + index).boundingClientRect(data => {\r\n\t\t\t\t\tthis.scrollLeft = (index - 1) * data.width;\r\n\t\t\t\t}).exec();\r\n\t\t\t\tthis.cid = id;\r\n\t\t\t\tthis.loadend = false;\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.$set(this, 'productList', [])\r\n\t\t\t\tthis.getProductList();\r\n\t\t\t},\r\n\t\t\tgetAllCategory: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetCategoryList().then(res => {\r\n\t\t\t\t\tthat.navLists = res.data;\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetProductList: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (that.loadend) return;\r\n\t\t\t\tif (that.loading) return;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tthat.loadTitle = '';\r\n\t\t\t\tgetProductslist({\r\n\t\t\t\t\tpage: that.page,\r\n\t\t\t\t\tlimit: that.limit,\r\n\t\t\t\t\tcid: that.cid\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tlet list = res.data.list,\r\n\t\t\t\t\t\tloadend = list.length < that.limit;\r\n\t\t\t\t\tthat.productList = that.$util.SplitArray(list, that.productList);\r\n\t\t\t\t\tthat.$set(that, 'productList', that.productList);\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.loadend = loadend;\r\n\t\t\t\t\tthat.loadTitle = loadend ? \"😕人家是有底线的~~\" : \"加载更多\";\r\n\t\t\t\t\tthat.page = that.page + 1;\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tthat.loading = false,\r\n\t\t\t\t\t\tthat.loadTitle = '加载更多'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 获取我的推荐\r\n\t\t\t */\r\n\t\t\tget_host_product: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetProductHot(1, 20).then(res => {\r\n\t\t\t\t\tthat.$set(that, 'hostProduct', res.data.list)\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t},\r\n\t\t// onReachBottom() {\r\n\t\t// \tthis.getProductList();\r\n\t\t// }\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.productSort {\r\n\t\t.nav {\r\n\t\t\tpadding: 0 30rpx;\r\n\t\t\twidth: 100%;\r\n\t\t\twhite-space: nowrap;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\theight: 86rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tposition: fixed;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 0;\r\n\t\t\tz-index: 9;\r\n\r\n\t\t\t.item {\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tcolor: #282828;\r\n\t\t\t\tpadding-right: 46rpx;\r\n\r\n\t\t\t\t&.on {\r\n\t\t\t\t\tcolor: #4B56AA;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.line {\r\n\t\t\t\t\twidth: 40rpx;\r\n\t\t\t\t\theight: 4rpx;\r\n\t\t\t\t\tbackground-color: #4B56AA;\r\n\t\t\t\t\tmargin: 10rpx auto 0 auto;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.list {\r\n\t\t\tmargin-top: 86rpx;\r\n\t\t\tpadding: 0 20rpx;\r\n\r\n\t\t\t.item {\r\n\t\t\t\twidth: 345rpx;\r\n\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tborder-radius: 20rpx;\r\n\r\n\t\t\t\t.pictrue {\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 345rpx;\r\n\r\n\t\t\t\t\timage {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 20rpx 20rpx 0 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.text {\r\n\t\t\t\t\tpadding: 20rpx 17rpx 26rpx 17rpx;\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tcolor: #222;\r\n\r\n\t\t\t\t\t.money {\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tmargin-top: 8rpx;\r\n\t\t\t\t\t\t@include price_color(theme); \r\n\t\t\t\t\t\t.num {\r\n\t\t\t\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.scroll-Y{\r\n\t\theight: 100vh;\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./contracted.vue?vue&type=style&index=0&id=289d3ae8&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./contracted.vue?vue&type=style&index=0&id=289d3ae8&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294179410\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import { render, staticRenderFns, recyclableRender, components } from \"./fresh.vue?vue&type=template&id=743fd7a3&%3Adata-theme=theme&\"\nvar renderjs\nimport script from \"./fresh.vue?vue&type=script&lang=js&\"\nexport * from \"./fresh.vue?vue&type=script&lang=js&\"\nimport style0 from \"./fresh.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/goods_cate/components/fresh.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fresh.vue?vue&type=template&id=743fd7a3&%3Adata-theme=theme&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.cartData.cartList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fresh.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fresh.vue?vue&type=script&lang=js&\"", "<template :data-theme=\"theme\">\r\n\t<view class=\"goodCate1\">\r\n\t\t<view class=\"header acea-row row-center-wrapper\">\r\n\t\t\t<navigator url='/pages/index/index' class=\"pageIndex acea-row row-center-wrapper\" hover-class=\"none\" open-type=\"switchTab\">\r\n\t\t\t\t<text class=\"iconfont icon-fanhuishouye\"></text>\r\n\t\t\t</navigator>\r\n\t\t\t<navigator url=\"/pages/goods_search/index\" class=\"search acea-row row-middle\" hover-class=\"none\">\r\n\t\t\t\t<text class=\"iconfont icon-sousuo5\"></text>\r\n\t\t\t\t搜索商品\r\n\t\t\t</navigator>\r\n\t\t</view>\r\n\t\t<view class=\"conter\"  v-if=\"showSlide\">\r\n\t\t\t<view class='aside'>\r\n\t\t\t\t<view class='item acea-row row-center-wrapper' :class='index==navActive?\"on\":\"\"' v-for=\"(item,index) in productList\"\r\n\t\t\t\t :key=\"index\" @click=\"tapNav(index,item)\">\r\n\t\t\t\t\t<text>{{item.name}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"wrapper\">\r\n\t\t\t\t<view class=\"bgcolor\" v-if=\"iSlong\">\r\n\t\t\t\t\t<view class=\"longTab acea-row row-middle\">\r\n\t\t\t\t\t\t<scroll-view scroll-x=\"true\" style=\"white-space: nowrap; display: flex;height:44rpx;\" scroll-with-animation\r\n\t\t\t\t\t\t :scroll-left=\"tabLeft\" show-scrollbar=\"true\">\r\n\t\t\t\t\t\t\t<view class=\"longItem\" :style='\"width:\"+isWidth+\"px\"' :class=\"index===tabClick?'click':''\" v-for=\"(item,index) in categoryErList\"\r\n\t\t\t\t\t\t\t :key=\"index\" @click=\"longClick(index,item)\">{{item.name}}</view>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"openList\" @click=\"openTap\"><text class=\"iconfont icon-xiangxia\"></text></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-else>\r\n\t\t\t\t\t<view class=\"downTab\">\r\n\t\t\t\t\t\t<view class=\"title acea-row row-between-wrapper\">\r\n\t\t\t\t\t\t\t<view>{{categoryTitle}}</view>\r\n\t\t\t\t\t\t\t<view class=\"closeList\" @click=\"closeTap\"><text class=\"iconfont icon-xiangxia\"></text></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"children\">\r\n\t\t\t\t\t\t\t<view class=\"acea-row row-middle\">\r\n\t\t\t\t\t\t\t\t<view class=\"item line1\" :class=\"index===tabClick?'click':''\" v-for=\"(item,index) in categoryErList\" :key=\"index\"\r\n\t\t\t\t\t\t\t\t @click=\"longClick(index,item)\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"mask\" @click=\"closeTap\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<goodList :tempArr=\"tempArr\" :isLogin=\"isLogin\" @gocartduo=\"goCartDuo\" @gocartdan=\"goCartDan\" @ChangeCartNumDan=\"ChangeCartNumDan\"\r\n\t\t\t\t @detail=\"goDetail\"></goodList>\r\n\t\t\t\t<view class='loadingicon acea-row row-center-wrapper mb-2'>\r\n\t\t\t\t\t<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>{{loadTitle}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"conter\"  v-else>\r\n\t\t\t<view class=\"hide_slide\">\r\n\t\t\t\t<view class=\"bgcolor\" v-if=\"iSlong\">\r\n\t\t\t\t\t<view class=\"hongTab acea-row row-middle\">\r\n\t\t\t\t\t\t<scroll-view scroll-x=\"true\" style=\"white-space: nowrap; display: flex;height:44rpx;\" scroll-with-animation\r\n\t\t\t\t\t\t :scroll-left=\"tabLeft\" show-scrollbar=\"true\">\r\n\t\t\t\t\t\t\t<view class=\"longItem\" :style='\"width:\"+isWidth+\"px\"' :class=\"index===tabClick?'click':''\" v-for=\"(item,index) in productList\"\r\n\t\t\t\t\t\t\t :key=\"index\" @click=\"navSwitch(index,item)\">{{item.name}}</view>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"openList\" @click=\"openTap\"><text class=\"iconfont icon-xiangxia\"></text></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-else>\r\n\t\t\t\t\t<view class=\"hownTab\">\r\n\t\t\t\t\t\t<view class=\"title acea-row row-between-wrapper\">\r\n\t\t\t\t\t\t\t<view>{{categoryTitle}}</view>\r\n\t\t\t\t\t\t\t<view class=\"closeList\" @click=\"closeTap\"><text class=\"iconfont icon-xiangxia\"></text></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"children\">\r\n\t\t\t\t\t\t\t<view class=\"acea-row row-middle\">\r\n\t\t\t\t\t\t\t\t<view class=\"item line1\" :class=\"index===tabClick?'click':''\" v-for=\"(item,index) in productList\" :key=\"index\"\r\n\t\t\t\t\t\t\t\t @click=\"navSwitch(index,item)\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"mask\" @click=\"closeTap\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"list_prod\">\r\n\t\t\t\t\t<view class=\"item acea-row row-between-wrapper\" v-for=\"(item,index) in tempArr\" :key='index' @click=\"goDetail(item)\">\r\n\t\t\t\t\t\t<view class=\"pic\">\r\n\t\t\t\t\t\t\t<image :src=\"item.image\" mode=\"\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"pictxt\">\r\n\t\t\t\t\t\t\t<view class=\"text line2\">{{item.storeName}}</view>\r\n\t\t\t\t\t\t\t<view class=\"bottom acea-row row-between-wrapper\">\r\n\t\t\t\t\t\t\t\t<view class=\"money\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"sign\">￥</text>{{item.price}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view v-if=\"item.stock>0\">\r\n\t\t\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t\t\t<!-- 多规格 -->\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"bnt\" @click.stop=\"goCartDuo(item)\">\r\n\t\t\t\t\t\t\t\t\t\t\t选规格\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"num\" v-if=\"item.cartNum\">{{item.cartNum}}</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"bnt end\" v-else>已售罄</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t<text class=\"otPrice\" v-if=\"item.sales\">已售{{item.sales}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='loadingicon acea-row row-center-wrapper mb-2'>\r\n\t\t\t\t\t<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>{{loadTitle}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"footer acea-row row-between-wrapper\">\r\n\t\t\t<view class=\"cart_theme acea-row row-center-wrapper\" v-if=\"cartData.cartList.length\">\r\n\t\t\t\t<view class=\"iconfont icon-gouwuche-yangshi1 hava\" @click=\"getCartLists(0)\"></view>\r\n\t\t\t\t<view class=\"num\">{{cartCount}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"noCart\" v-else>\r\n\t\t\t\t<view class=\"iconfont icon-gouwuche-yangshi1 no_have\"></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"money acea-row row-middle\">\r\n\t\t\t\t<view>￥<text class=\"num\">{{totalPrice}}</text></view>\r\n\t\t\t\t<view class=\"bnt gray_bg\" :class=\"{ 'main_bg': cartCount > 0}\"  @click=\"subOrder\">去结算</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- <home></home> -->\r\n\t\t<cartList :cartData=\"cartData\" @closeList=\"closeList\" @ChangeCartNumDan=\"ChangeCartList\" @ChangeSubDel=\"ChangeSubDel\"\r\n\t\t @ChangeOneDel=\"ChangeOneDel\"></cartList>\r\n\t\t<productWindow :attr=\"attr\" :isShow='1' :iSplus='1' :iScart='1' @myevent=\"onMyEvent\" @ChangeAttr=\"ChangeAttr\"\r\n\t\t @ChangeCartNum=\"ChangeCartNumDuo\" @attrVal=\"attrVal\" @iptCartNum=\"iptCartNum\" @goCat=\"goCatNum\" id='product-window'></productWindow>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tgetCategoryList,\r\n\t\tgetProductslist,\r\n\t\tgetAttr,\r\n\t\tpostCartAdd\r\n\t} from '@/api/store.js';\r\n\timport {\r\n\t\tgetCartList,\r\n\t\tgetCartCounts,\r\n\t\tcartDel,\r\n\t\tchangeCartNum,\r\n\t} from '@/api/order.js';\r\n\timport productWindow from '@/components/productWindow';\r\n\timport goodList from '@/components/f_goodList';\r\n\timport cartList from '@/components/cartList';\r\n\timport home from '@/components/home';\r\n\timport {mapGetters} from 'vuex';\r\n\timport {goShopDetail} from '@/libs/order.js';\r\n\timport {toLogin} from '@/libs/login.js';\r\n\timport animationType from '@/utils/animationType.js'\r\n\texport default {\r\n\t\tcomputed: mapGetters(['isLogin', 'uid']),\r\n\t\tcomponents: {\r\n\t\t\tproductWindow,\r\n\t\t\tgoodList,\r\n\t\t\tcartList,\r\n\t\t\thome\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tshowSlide: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault:true \r\n\t\t\t},\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tproductList: [],\r\n\t\t\t\tnavActive: 0,\r\n\t\t\t\tcategoryTitle: '',\r\n\t\t\t\tcategoryErList: [],\r\n\t\t\t\ttabLeft: 0,\r\n\t\t\t\tisWidth: 0, //每个导航栏占位\r\n\t\t\t\ttabClick: 0, //导航栏被点击\r\n\t\t\t\tiSlong: true,\r\n\t\t\t\ttempArr: [],\r\n\t\t\t\tloading: false,\r\n\t\t\t\tloadend: false,\r\n\t\t\t\tloadTitle: '加载更多',\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tlimit: 10,\r\n\t\t\t\tcid: 0, //一级分类\r\n\t\t\t\tsid: 0, //二级分类\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false, //是否隐藏授权\r\n\t\t\t\tattr: {\r\n\t\t\t\t\tcartAttr: false,\r\n\t\t\t\t\tproductAttr: [],\r\n\t\t\t\t\tproductSelect: {}\r\n\t\t\t\t},\r\n\t\t\t\tproductValue: [],\r\n\t\t\t\tattrValue: '', //已选属性\r\n\t\t\t\tstoreName: '', //多属性产品名称\r\n\t\t\t\tid: 0,\r\n\t\t\t\tcartData: {\r\n\t\t\t\t\tcartList: [],\r\n\t\t\t\t\tiScart: false\r\n\t\t\t\t},\r\n\t\t\t\tcartCount: 0,\r\n\t\t\t\ttotalPrice: 0.00,\r\n\t\t\t\tlengthCart: 0,\r\n\t\t\t\ttheme:'theme1'\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated(){\r\n\t\t\tif(this.isLogin){\r\n\t\t\t\tthis.getCartNum();\r\n\t\t\t\tthis.getCartLists(1);\r\n\t\t\t}\r\n\t\t\tthis.getAllCategory();\r\n\t\t\tlet that = this;\r\n\t\t\tthat.lengthCart = that.cartData.cartList;\r\n\t\t\t// 获取设备宽度\r\n\t\t\tuni.getSystemInfo({\r\n\t\t\t\tsuccess(e) {\r\n\t\t\t\t\tthat.isWidth = e.windowWidth / 5\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 生成订单；\r\n\t\t\tsubOrder: function() {\r\n\t\t\t\tlet that = this,list = that.cartData.cartList,ids = [];\r\n\t\t\t\tif(list.length){\r\n\t\t\t\t\tlet shoppingCartId = list.map(item => {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\t\"shoppingCartId\": Number(item.id)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.$Order.getPreOrder(\"shoppingCart\", shoppingCartId);\r\n\t\t\t\t\tthat.cartData.iScart = false;\r\n\t\t\t\t}else{\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: '请选择产品'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 计算总价；\r\n\t\t\tgetTotalPrice: function(){\r\n\t\t\t\tlet that = this,list = that.cartData.cartList,totalPrice = 0.00;\r\n\t\t\t\tlist.forEach(item=>{\r\n\t\t\t\t\tif(item.attrStatus ){\r\n\t\t\t\t\t\ttotalPrice = that.$util.$h.Add(totalPrice, that.$util.$h.Mul(item.cartNum, item.vipPrice ? item.vipPrice : item.price));\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tthat.$set(that,'totalPrice',totalPrice);\r\n\t\t\t},\r\n\t\t\tChangeSubDel: function(event) {\r\n\t\t\t\tlet that = this,list = that.cartData.cartList,ids = [];\r\n\t\t\t\tlist.forEach(item=>{\r\n\t\t\t\t\tids.push(item.id)\r\n\t\t\t\t});\r\n\t\t\t\tcartDel(ids.join(\",\")).then(res=>{\r\n\t\t\t\t\tthat.$set(that.cartData,'cartList',[]);\r\n\t\t\t\t\tthat.cartData.iScart = false;\r\n\t\t\t\t\tthat.totalPrice = 0.00;\r\n\t\t\t\t\tthat.page = 1;\r\n\t\t\t\t\tthat.loadend = false;\r\n\t\t\t\t\tthat.tempArr = [];\r\n\t\t\t\t\tthat.productslist();\r\n\t\t\t\t\tthat.getCartNum();\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tChangeOneDel:function(id,index){\r\n\t\t\t\tlet that = this,list = that.cartData.cartList;\r\n\t\t\t\tcartDel(id.toString()).then(res=>{\r\n\t\t\t\t\tlist.splice(index,1);\r\n\t\t\t\t\tif(!list.length){\r\n\t\t\t\t\t\tthat.cartData.iScart = false;\r\n\t\t\t\t\t\tthat.page = 1;\r\n\t\t\t\t\t\tthat.loadend = false;\r\n\t\t\t\t\t\tthat.tempArr = [];\r\n\t\t\t\t\t\tthat.productslist();\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthat.getCartNum();\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetCartLists(iSshow) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tpage: 1,\r\n\t\t\t\t\tlimit: that.cartCount,\r\n\t\t\t\t\tisValid: true\r\n\t\t\t\t};\r\n\t\t\t\tgetCartList(data).then(res => {\r\n\t\t\t\t\tthat.$set(that.cartData, 'cartList', res.data.list);\r\n\t\t\t\t\tif(res.data.list.length){\r\n\t\t\t\t\t\tthat.$set(that.cartData, 'iScart', iSshow?false:!that.cartData.iScart);\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.$set(that.cartData, 'iScart', false);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.getTotalPrice();\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tcloseList(e) {\r\n\t\t\t\tthis.$set(this.cartData, 'iScart', e);\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.loadend = false;\r\n\t\t\t\tthis.tempArr = [];\r\n\t\t\t\tthis.productslist();\r\n\t\t\t},\r\n\t\t\tgetCartNum: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetCartCounts(true, 'sum').then(res => {\r\n\t\t\t\t\tthat.$set(that,'cartCount',res.data.count);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\r\n\t\t\tonMyEvent: function() {\r\n\t\t\t\tthis.$set(this.attr, 'cartAttr', false);\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 默认选中属性\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tDefaultSelect: function() {\r\n\t\t\t\tlet productAttr = this.attr.productAttr;\r\n\t\t\t\tlet value = [];\r\n\t\t\t\tfor (let key in this.productValue) {\r\n\t\t\t\t\tif (this.productValue[key].stock > 0) {\r\n\t\t\t\t\t\tvalue = this.attr.productAttr.length ? key.split(\",\") : [];\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tfor (let i = 0; i < productAttr.length; i++) {\r\n\t\t\t\t\tthis.$set(productAttr[i], \"index\", value[i]);\r\n\t\t\t\t}\r\n\t\t\t\t//sort();排序函数:数字-英文-汉字；\r\n\t\t\t\tlet productSelect = this.productValue[value.join(\",\")];\r\n\t\t\t\t\r\n\t\t\t\tif (productSelect && productAttr.length) {\r\n\t\t\t\t\tthis.$set(this.attr.productSelect,\"storeName\",this.storeName);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"image\", productSelect.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"price\", productSelect.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"stock\", productSelect.stock);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"unique\", productSelect.id);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"vipPrice\", productSelect.vipPrice);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", value.join(\",\"));\r\n\t\t\t\t} else if (!productSelect && productAttr.length) {\r\n\t\t\t\t\tthis.$set(this.attr.productSelect,\"storeName\",this.storeName);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"image\", this.storeInfo.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"price\", this.storeInfo.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"stock\", 0);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"unique\", \"\");\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 0);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", \"\");\r\n\t\t\t\t} else if (!productSelect && !productAttr.length) {\r\n\t\t\t\t\tthis.$set(this.attr.productSelect,\"storeName\",this.storeName);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"image\", this.storeInfo.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"price\", this.storeInfo.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"stock\", this.storeInfo.stock);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect,\"unique\",this.storeInfo.unique || \"\");\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", \"\");\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 属性变动赋值\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tChangeAttr: function(res) {\r\n\t\t\t\tlet productSelect = this.productValue[res];\r\n\t\t\t\tif (productSelect) {\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"image\", productSelect.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"price\", productSelect.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"stock\", productSelect.stock);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"unique\", productSelect.id);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"vipPrice\", productSelect.vipPrice);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", res);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"image\", this.productInfo.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"price\", this.productInfo.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"stock\", 0);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"unique\", this.productInfo.id);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"vipPrice\", this.productInfo.vipPrice);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", \"\");\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tattrVal(val) {\r\n\t\t\t\tthis.$set(this.attr.productAttr[val.indexw], 'index', this.attr.productAttr[val.indexw].attrValues[val\r\n\t\t\t\t\t.indexn]);\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 购物车手动填写\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tiptCartNum: function(e) {\r\n\t\t\t\tthis.$set(this.attr.productSelect, 'cart_num', e);\r\n\t\t\t},\r\n\t\t\tonLoadFun() {},\r\n\t\t\t// 产品列表\r\n\t\t\tproductslist: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (that.loadend) return; //如果返回列表长度小于请求分页长度，就让他为true,就不继续请求了\r\n\t\t\t\tif (that.loading) return;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tthat.loadTitle = '';\r\n\t\t\t\tgetProductslist({\r\n\t\t\t\t\tpage: that.page,\r\n\t\t\t\t\tlimit: that.limit,\r\n\t\t\t\t\ttype: 1,\r\n\t\t\t\t\tcid: that.sid,\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tlet list = res.data.list,\r\n\t\t\t\t\t\tloadend = list.length < that.limit;\r\n\t\t\t\t\tthat.tempArr = that.$util.SplitArray(list, that.tempArr);\r\n\t\t\t\t\tthat.$set(that, 'tempArr', that.tempArr);\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.loadend = loadend;\r\n\t\t\t\t\tthat.loadTitle = loadend ? \"😕人家是有底线的~~\" : \"加载更多\";\r\n\t\t\t\t\tthat.page = that.page + 1;\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tthat.loading = false,\r\n\t\t\t\t\t\tthat.loadTitle = '加载更多'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 改变多属性购物车\r\n\t\t\tChangeCartNumDuo(changeValue) {\r\n\t\t\t\t//changeValue:是否 加|减\r\n\t\t\t\t//获取当前变动属性\r\n\t\t\t\tlet productSelect = this.productValue[this.attrValue];\r\n\t\t\t\t//如果没有属性,赋值给商品默认库存\r\n\t\t\t\tif (productSelect === undefined && !this.attr.productAttr.length)\r\n\t\t\t\t\tproductSelect = this.attr.productSelect;\r\n\t\t\t\t//无属性值即库存为0；不存在加减；\r\n\t\t\t\tif (productSelect === undefined) return;\r\n\t\t\t\tlet stock = productSelect.stock || 0;\r\n\t\t\t\tlet num = this.attr.productSelect;\r\n\t\t\t\tif (changeValue) {\r\n\t\t\t\t\tnum.cart_num++;\r\n\t\t\t\t\tif (num.cart_num > stock) {\r\n\t\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", stock);\r\n\t\t\t\t\t\tthis.$set(this, \"cart_num\", stock);\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tnum.cart_num--;\r\n\t\t\t\t\tif (num.cart_num < 1) {\r\n\t\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\t\tthis.$set(this, \"cart_num\", 1);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\t// 已经加入购物车时的购物加减；\r\n\t\t\tChangeCartList(changeValue, index) {\r\n\t\t\t\tlet list = this.cartData.cartList;\r\n\t\t\t\tlet num = list[index];\r\n\t\t\t\tlet stock = list[index].stock;\r\n\t\t\t\tthis.ChangeCartNum(changeValue, num, stock, 0, num.productId,index,1);\r\n\t\t\t\tif(!list.length){\r\n\t\t\t\t\tthis.cartData.iScart = false;\r\n\t\t\t\t\tthis.page = 1;\r\n\t\t\t\t\tthis.loadend = false;\r\n\t\t\t\t\tthis.tempArr = [];\r\n\t\t\t\t\tthis.productslist();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 购物车加减计算函数\r\n\t\t\tChangeCartNum: function(changeValue,index) {\r\n\t\t\t\tif (changeValue) {\r\n\t\t\t\t\tif (index.cartNum >= index.stock) {\r\n\t\t\t\t\t\tindex.cartNum = index.stock;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tindex.cartNum++;\r\n\t\t\t\t\t\tchangeCartNum(index.id, index.cartNum).then(res => {\r\n\t\t\t\t\t\t\tthis.getCartNum(true);\r\n\t\t\t\t\t\t\tthis.getTotalPrice();\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tindex.cartNum--;\r\n\t\t\t\t\tchangeCartNum(index.id, index.cartNum).then(res => {\r\n\t\t\t\t\t\tthis.getCartNum(true);\r\n\t\t\t\t\t\tthis.getTotalPrice();\r\n\t\t\t\t\t});\r\n\t\t\t\t\tif(index.cartNum == 0){\r\n\t\t\t\t\t\tcartDel(index.id).then(res=>{\r\n\t\t\t\t\t\t\tthis.getCartLists(1);\r\n\t\t\t\t\t\t\tthis.getTotalPrice();\r\n\t\t\t\t\t\t\tthis.productslist();\r\n\t\t\t\t\t\t\tthis.getCartNum();\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 多规格加入购物车；\r\n\t\t\tgoCatNum() {\r\n\t\t\t\tthis.goCat(1);\r\n\t\t\t},\r\n\t\t\t/*\r\n\t\t\t * 加入购物车\r\n\t\t\t */\r\n\t\t\tgoCat: function(num) {\r\n\t\t\t\tlet that = this,\r\n\t\t\t\t\tproductSelect = that.productValue[this.attrValue];\r\n\t\t\t\t//打开属性\r\n\t\t\t\tif (that.attrValue) {\r\n\t\t\t\t\t//默认选中了属性，但是没有打开过属性弹窗还是自动打开让用户查看默认选中的属性\r\n\t\t\t\t\tthat.attr.cartAttr = !that.isOpen ? true : false;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (that.isOpen) that.attr.cartAttr = true;\r\n\t\t\t\t\telse that.attr.cartAttr = !that.attr.cartAttr;\r\n\t\t\t\t}\r\n\t\t\t\t//只有关闭属性弹窗时进行加入购物车\r\n\t\t\t\tif (that.attr.cartAttr === true && that.isOpen === false)\r\n\t\t\t\t\treturn (that.isOpen = true);\r\n\t\t\t\t//如果有属性,没有选择,提示用户选择\r\n\t\t\t\tif (\r\n\t\t\t\t\tthat.attr.productAttr.length &&\r\n\t\t\t\t\tproductSelect.stock === 0 &&\r\n\t\t\t\t\tthat.isOpen === true\r\n\t\t\t\t)\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: \"产品库存不足，请选择其它\"\r\n\t\t\t\t\t});\r\n\t\t\t\tif (num === 1) {\r\n\t\t\t\t\tlet q = {\r\n\t\t\t\t\t\tproductId: parseFloat(that.id),\r\n\t\t\t\t\t\tcartNum: parseFloat(that.attr.productSelect.cart_num),\r\n\t\t\t\t\t\tisNew: false,\r\n\t\t\t\t\t\tproductAttrUnique: that.attr.productSelect !== undefined ?\r\n\t\t\t\t\t\t\tthat.attr.productSelect.unique : that.productInfo.id\r\n\t\t\t\t\t};\r\n\t\t\t\t\tpostCartAdd(q).then(function(res) {\r\n\t\t\t\t\t\t\tthat.isOpen = false;\r\n\t\t\t\t\t\t\tthat.attr.cartAttr = false;\r\n\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: \"添加购物车成功\",\r\n\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\t\t\t\t\t\tthat.getCartNum(true);\r\n\t\t\t\t\t\t\t\t\t\tthat.getCartLists(1);\r\n\t\t\t\t\t\t\t\t\t},100)\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(res => {\r\n\t\t\t\t\t\t\tthat.isOpen = false;\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: res\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.getPreOrder();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgoCartDuo(item) {\r\n\t\t\t\tif (!this.isLogin) {\r\n\t\t\t\t\tthis.getIsLogin();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\ttitle: '加载中'\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.storeName = item.storeName;\r\n\t\t\t\t\tthis.getAttrs(item.id,item.storeName);\r\n\t\t\t\t\tthis.$set(this, 'id', item.id);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetIsLogin() {\r\n\t\t\t\ttoLogin();\r\n\t\t\t},\r\n\t\t\t// 商品详情接口；\r\n\t\t\tgetAttrs(id) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetAttr(id).then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthat.$set(that.attr, 'productAttr', res.data.productAttr);\r\n\t\t\t\t\tthat.$set(that, 'productValue', res.data.productValue);\r\n\t\t\t\t\tlet productAttr = that.attr.productAttr.map(item => {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tattrName : item.attrName,\r\n\t\t\t\t\t\tattrValues: item.attrValues.split(','),\r\n\t\t\t\t\t\tid:item.id,\r\n\t\t\t\t\t\tisDel:item.isDel,\r\n\t\t\t\t\t\tproductId:item.productId,\r\n\t\t\t\t\t\ttype:item.type\r\n\t\t\t\t\t }\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.$set(that.attr,'productAttr',productAttr);\r\n\t\t\t\t\tthis.$set(that.attr, 'cartAttr', true);\r\n\t\t\t\t\tthat.DefaultSelect();\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 去详情页\r\n\t\t\tgoDetail(item) {\r\n\t\t\t\tif (!this.isLogin) {\r\n\t\t\t\t\ttoLogin();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tgoShopDetail(item, this.uid).then(res => {\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\tanimationType: animationType.type,\r\t\t\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\t\t\turl: `/pages/goods_details/index?id=${item.id}`\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\r\n\t\t\topenTap() {\r\n\t\t\t\tthis.iSlong = false\r\n\t\t\t},\r\n\t\t\tcloseTap() {\r\n\t\t\t\tthis.iSlong = true\r\n\t\t\t},\r\n\t\t\tgetAllCategory: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetCategoryList().then(res => {\r\n\t\t\t\t\tres.data.forEach((item)=>{\r\n\t\t\t\t\t\tif(item.child){\r\n\t\t\t\t\t\t\titem.child.unshift({\r\n\t\t\t\t\t\t\t\tid:item.id,\r\n\t\t\t\t\t\t\t\tname:'全部'\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\tlet data = res.data;\r\n\t\t\t\t\tthat.categoryTitle = data[0].name;\r\n\t\t\t\t\tthat.sid = data[0].id;\r\n\t\t\t\t\tthat.productList = data;\r\n\t\t\t\t\tthat.categoryErList = res.data[0].child ? res.data[0].child : [];\r\n\t\t\t\t\tthat.page = 1;\r\n\t\t\t\t\tthat.loadend = false;\r\n\t\t\t\t\tthat.tempArr = [];\r\n\t\t\t\t\tthat.productslist();\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttapNav(index, item) {\r\n\t\t\t\tlet list = this.productList[index];\r\n\t\t\t\tthis.navActive = index;\r\n\t\t\t\tthis.categoryTitle = list.name;\r\n\t\t\t\tthis.categoryErList = item.child ? item.child : [];\r\n\t\t\t\tthis.tabClick = 0;\r\n\t\t\t\tthis.tabLeft = 0;\r\n\t\t\t\tthis.sid = item.id;\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.loadend = false;\r\n\t\t\t\tthis.tempArr = [];\r\n\t\t\t\tthis.productslist();\r\n\t\t\t},\r\n\t\t\t// 导航栏点击\r\n\t\t\tlongClick(index,item) {\r\n\t\t\t\tif (this.productList.length > 3) {\r\n\t\t\t\t\tthis.tabLeft = (index - 1) * (this.isWidth + 6) //设置下划线位置\r\n\t\t\t\t};\r\n\t\t\t\tthis.tabClick = index; //设置导航点击了哪一个\r\n\t\t\t\tthis.iSlong = true;\r\n\t\t\t\tthis.sid = item.id;\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.loadend = false;\r\n\t\t\t\tthis.tempArr = [];\r\n\t\t\t\tthis.productslist();\r\n\t\t\t},\r\n\t\t\tnavSwitch(index,item){\r\n\t\t\t\tif (this.productList.length > 3) {\r\n\t\t\t\t\tthis.tabLeft = (index - 1) * (this.isWidth + 6) //设置下划线位置\r\n\t\t\t\t};\r\n\t\t\t\tthis.tabClick = index; //设置导航点击了哪一个\r\n\t\t\t\tthis.iSlong = true;\r\n\t\t\t\tthis.sid = item.id;\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.loadend = false;\r\n\t\t\t\tthis.tempArr = [];\r\n\t\t\t\tthis.productslist();\r\n\t\t\t},\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n\r\n<style lang=\"scss\">\r\n\tpage {\r\n\t\tbackground-color: $crmeb-bg-color;\r\n\t}\r\n\r\n\t::-webkit-scrollbar {\r\n\t\twidth: 0;\r\n\t\theight: 0;\r\n\t\tcolor: transparent;\r\n\t\tdisplay: none;\r\n\t}\r\n\r\n\t.goodCate1 {\r\n\t\tbackground-color: $crmeb-bg-color;\r\n\t\t/deep/.mask {\r\n\t\t\t// z-index: 99;\r\n\t\t}\r\n\r\n\t\t/deep/.attrProduct {\r\n\t\t\t.mask {\r\n\t\t\t\tz-index: 100;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.header {\r\n\t\t\tposition: fixed;\r\n\t\t\theight: 128rpx;\r\n\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 0;\r\n\t\t\twidth: 100%;\r\n\t\t\tz-index: 99;\r\n\t\t\tborder-bottom: 1px solid #D9D9D9;\r\n\r\n\t\t\t.pageIndex {\r\n\t\t\t\twidth: 68rpx;\r\n\t\t\t\theight: 68rpx;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t@include main_bg_color(theme);\r\n\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// image{\r\n\t\t\t\t// \twidth: 29rpx;\r\n\t\t\t\t// \theight: 30rpx;\r\n\t\t\t\t// }\r\n\t\t\t}\r\n\r\n\t\t\t.search {\r\n\t\t\t\twidth: 600rpx;\r\n\t\t\t\t/* #ifdef MP || APP-PLUS */\r\n\t\t\t\twidth: 550rpx;\r\n\t\t\t\t/* #endif */\r\n\t\t\t\theight: 68rpx;\r\n\t\t\t\tborder-radius: 36rpx;\r\n\t\t\t\tbackground-color: $crmeb-bg-color-grey;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: $crmeb-font-color-disable;\r\n\t\t\t\tmargin-left: 22rpx;\r\n\t\t\t\tpadding: 0 30rpx;\r\n\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tmargin-right: 18rpx;\r\n\t\t\t\t\tcolor: $crmeb-font-color-subtitle;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// image{\r\n\t\t\t\t// \twidth: 27rpx;\r\n\t\t\t\t// \theight: 27rpx;\r\n\t\t\t\t// \tmargin-right: 18rpx;\r\n\t\t\t\t// }\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.conter {\r\n\t\t\tpadding-top: 64px;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\t.aside {\r\n\t\t\t\tposition: fixed;\r\n\t\t\t\twidth: 23%;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\tbottom: 0;\r\n\t\t\t\ttop: 0;\r\n\t\t\t\tbackground-color: $crmeb-bg-color-grey;\r\n\t\t\t\toverflow-y: auto;\r\n\t\t\t\toverflow-x: hidden;\r\n\t\t\t\tmargin-top: 128rpx;\r\n\t\t\t\tz-index: 99;\r\n\t\t\t\tpadding-bottom: 140rpx;\r\n\r\n\t\t\t\t.item {\r\n\t\t\t\t\theight: 100rpx;\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: $crmeb-font-color;\r\n\r\n\t\t\t\t\t&.on {\r\n\t\t\t\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t@include main_color(theme);\r\n\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t\t\t&::after {\r\n\t\t\t\t\t\t\tcontent: \"\";\r\n\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\twidth: 6rpx;\r\n\t\t\t\t\t\t\theight: 46rpx;\r\n\t\t\t\t\t\t\t@include main_bg_color(theme);\r\n\t\t\t\t\t\t\tborder-radius: 0 4rpx 4rpx 0;\r\n\t\t\t\t\t\t\tleft: 0\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.wrapper {\r\n\t\t\tmargin-top: 104rpx;\r\n\t\t\twidth: 77%;\r\n\t\t\tfloat: right;\r\n\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t\tpadding-bottom: 130rpx;\r\n\t\t}\r\n\t\t.hide_slide{\r\n\t\t\tmargin-top: 104rpx;\r\n\t\t\twidth: 100%;\r\n\t\t\tfloat: right;\r\n\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t\tpadding-bottom: 130rpx;\r\n\t\t}\r\n\t\t.bgcolor {\r\n\t\t\twidth: 100%;\r\n\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t}\r\n\t\t\t\t\r\n\t\t.goodsList {\r\n\t\t\tmargin-top: 0 !important;\r\n\t\t}\r\n\t\t\t\t\r\n\t\t.longTab {\r\n\t\t\twidth: 65%;\r\n\t\t\tposition: fixed;\r\n\t\t\ttop: 0;\r\n\t\t\tmargin-top: 128rpx;\r\n\t\t\theight: 100rpx;\r\n\t\t\tz-index: 99;\r\n\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t}\r\n\t\t.hongTab{\r\n\t\t\twidth: 100%;\r\n\t\t\tposition: fixed;\r\n\t\t\ttop: 0;\r\n\t\t\tmargin-top: 128rpx;\r\n\t\t\theight: 100rpx;\r\n\t\t\tz-index:99;\r\n\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t}\r\n\t\t.longItem {\r\n\t\t\theight: 44rpx;\r\n\t\t\tdisplay: inline-block;\r\n\t\t\tline-height: 44rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\toverflow: hidden;\r\n\t\t\ttext-overflow: ellipsis;\r\n\t\t\twhite-space: nowrap;\r\n\t\t\tcolor: $crmeb-font-color;\r\n\t\t\tbackground-color: $crmeb-bg-color-grey;\r\n\t\t\tborder-radius: 22rpx;\r\n\t\t\tmargin-left: 12rpx;\r\n\t\t\t\r\n\t\t\t&.click {\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\t@include main_bg_color(theme);\r\n\t\t\t\tcolor: $crmeb-font-color-white;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\t\r\n\t\t.underlineBox {\r\n\t\t\theight: 3px;\r\n\t\t\twidth: 20%;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-content: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\ttransition: .5s;\r\n\t\t\t\r\n\t\t\t.underline {\r\n\t\t\t\twidth: 33rpx;\r\n\t\t\t\theight: 4rpx;\r\n\t\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t\t}\r\n\t\t}\t\r\n\t\t.openList {\r\n\t\t\twidth: 12%;\r\n\t\t\theight: 100rpx;\r\n\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t\tline-height: 100rpx;\r\n\t\t\tpadding-left: 30rpx;\r\n\t\t\tposition: fixed;\r\n\t\t\tright: 0;\r\n\t\t\ttop: 128rpx;\r\n\t\t\tz-index: 99;\r\n\t\t\t\t\r\n\t\t\t.iconfont {\r\n\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\tcolor: $crmeb-font-color-subtitle;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\t\t\r\n\t\t.downTab {\r\n\t\t\twidth: 77%;\r\n\t\t\tposition: fixed;\r\n\t\t\ttop: 0;\r\n\t\t\tmargin-top: 128rpx;\r\n\t\t\tz-index: 99;\r\n\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t\tright: 0;\r\n\t\t}\r\n\t\t.hownTab{\r\n\t\t\twidth: 100%;\r\n\t\t\tposition: fixed;\r\n\t\t\ttop: 0;\r\n\t\t\tmargin-top: 128rpx;\r\n\t\t\tz-index: 99;\r\n\t\t\tbackground-color: $crmeb-bg-color;\r\n\t\t\tright: 0;\r\n\t\t}\r\n\t\t.title {\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: $crmeb-font-color-assist;\r\n\t\t\t// padding-left: 20rpx;\r\n\t\t\t\r\n\t\t\t.closeList {\r\n\t\t\t\twidth: 90rpx;\r\n\t\t\t\theight: 100rpx;\r\n\t\t\t\tline-height: 100rpx;\r\n\t\t\t\tpadding-left: 30rpx;\r\n\t\t\t\ttransform: rotate(180deg);\r\n\t\t\t\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\tcolor: $crmeb-font-color-subtitle;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\t\r\n\t\t.children {\r\n\t\t\tmax-height: 500rpx;\r\n\t\t\toverflow-x: hidden;\r\n\t\t\toverflow-y: auto;\r\n\t\t\tpadding-bottom: 20rpx;\r\n\t\t\t\r\n\t\t\t.item {\r\n\t\t\t\theight: 60rpx;\r\n\t\t\t\tbackground-color: $crmeb-bg-color-grey;\r\n\t\t\t\tborder-radius: 30rpx;\r\n\t\t\t\tline-height: 60rpx;\r\n\t\t\t\tpadding: 0 15rpx;\r\n\t\t\t\tmargin: 0 0 20rpx 20rpx;\r\n\t\t\t\twidth: 165rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\r\n\t\t\t\t&.click {\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t@include main_bg_color(theme);\r\n\t\t\t\t\tcolor: $crmeb-font-color-white;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\t\t\r\n\t\t.list_prod{\r\n\t\t\tpadding: 0 30rpx;\r\n\t\t\t.item{\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\tmargin-bottom: 40rpx;\r\n\t\t\t\t.pic{\r\n\t\t\t\t\twidth: 180rpx;\r\n\t\t\t\t\theight: 180rpx;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\timage{\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.pictxt{\r\n\t\t\t\twidth: 490rpx;\r\n\t\t\t\t.text{\r\n\t\t\t\t\tfont-size:26rpx;\r\n\t\t\t\t\tfont-family:PingFang SC;\r\n\t\t\t\t\tfont-weight:500;\r\n\t\t\t\t\tcolor: $crmeb-font-color;\r\n\t\t\t\t}\r\n\t\t\t\t.bottom{\r\n\t\t\t\t\tmargin-top: 22rpx;\r\n\t\t\t\t\t.money{\r\n\t\t\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\t\t\tfont-weight: 800;\r\n\t\t\t\t\t\twidth: 212rpx;\r\n\t\t\t\t\t\t@include price_color(theme);\r\n\t\t\t\t\t\t.sign{\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.cart{\r\n\t\t\t\t\t\theight: 46rpx;\r\n\t\t\t\t\t\t.pictrue{\r\n\t\t\t\t\t\t\tcolor: $crmeb-theme-color;\r\n\t\t\t\t\t\t\tfont-size:46rpx;\r\n\t\t\t\t\t\t\twidth: 46rpx;\r\n\t\t\t\t\t\t\theight: 46rpx;\r\n\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t\tline-height: 46rpx;\r\n\t\t\t\t\t\t\t&.icon-jiahao{\r\n\t\t\t\t\t\t\t\t color: $crmeb-theme-color;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t.num{\r\n\t\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t\t\tcolor: $crmeb-font-color;\r\n\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t\twidth: 60rpx;\r\n\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.icon-gouwuche6{\r\n\t\t\t\t\t\twidth: 46rpx;\r\n\t\t\t\t\t\theight: 46rpx;\r\n\t\t\t\t\t\tbackground-color: $crmeb-theme-color;\r\n\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\tcolor: $crmeb-font-color-white;\r\n\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.bnt{\r\n\t\t\t\t\t\tpadding: 0 20rpx;\r\n\t\t\t\t\t\theight: 46rpx;\r\n\t\t\t\t\t\tline-height: 46rpx;\r\n\t\t\t\t\t\t@include main_bg_color(theme);\r\n\t\t\t\t\t\tborder-radius:23rpx;\r\n\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\tcolor: $crmeb-font-color-white;\r\n\t\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\t&.end{\r\n\t\t\t\t\t\t\tbackground:$crmeb-font-color-disable;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t.num{\r\n\t\t\t\t\t\t\tmin-width: 12rpx;\r\n\t\t\t\t\t\t\t@include main_color(theme);\r\n\t\t\t\t\t\t\t@include coupons_border_color(theme);\r\n\t\t\t\t\t\t\tbackground: #fff;\r\n\t\t\t\t\t\t\tborder-radius: 15px;\r\n\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\tright: -13rpx;\r\n\t\t\t\t\t\t\ttop: -11rpx;\r\n\t\t\t\t\t\t\tfont-size: 16rpx;\r\n\t\t\t\t\t\t\tpadding: 0 11rpx;\r\n\t\t\t\t\t\t\theight: 32rpx;\r\n\t\t\t\t\t\t\tline-height: 32rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t.otPrice{\r\n\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\tfont-family: PingFang SC;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tline-height: 24rpx;\r\n\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.footer {\r\n\t\t\tposition: fixed;\r\n\t\t\tleft: 0;\r\n\t\t\tbottom:env(safe-area-inset-bottom); \r\n\t\t\twidth: 100%;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tbox-shadow: 0px -3rpx 16rpx rgba(36, 12, 12, 0.05);\r\n\t\t\tz-index: 101;\r\n\t\t\tpadding: 0 30rpx;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\theight: 100rpx;\r\n\t\t\t&:after{\r\n\t\t\t\tcontent:'';\r\n\t\t\t\theight:env(safe-area-inset-bottom); // 这里是重点\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop:100%;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\tright:0;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t}\r\n\t\t\t.cart_theme{\r\n\t\t\t\tmargin-top: -50rpx;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\t.hava{\r\n\t\t\t\t\tfont-size: 110rpx;\r\n\t\t\t\t\t@include main_color(theme);\r\n\t\t\t\t}\r\n\t\t\t\t.num{\r\n\t\t\t\t\tmin-width: 12rpx;\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\tborder-radius: 15px;\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tright: -6rpx;\r\n\t\t\t\t\tbottom: 10rpx;\r\n\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\tpadding: 0 10rpx;\r\n\t\t\t\t\theight: 34rpx;\r\n\t\t\t\t\tline-height: 34rpx;\r\n\t\t\t\t\t@include main_color(theme);\r\n\t\t\t\t\t@include coupons_border_color(theme);\r\n\t\t\t\t\tbackground-color: #fff;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.noCart{\r\n\t\t\t\tmargin-top: -50rpx;\r\n\t\t\t\t.no_have{\r\n\t\t\t\t\tfont-size: 110rpx;\r\n\t\t\t\t\tcolor: #cbcbcb;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\r\n\t\t\t.money {\r\n\t\t\t\t@include price_color(theme);\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\r\n\t\t\t\t.num {\r\n\t\t\t\t\tfont-size: 42rpx;\r\n\t\t\t\t}\r\n\t\t\r\n\t\t\t\t.bnt {\r\n\t\t\t\t\twidth: 222rpx;\r\n\t\t\t\t\theight: 76rpx;\r\n\t\t\t\t\tborder-radius: 46rpx;\r\n\t\t\t\t\tline-height: 76rpx;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\tmargin-left: 24rpx;\r\n\t\t\t\t}\r\n\t\t\t\t.main_bg{\r\n\t\t\t\t\t@include main_bg_color(theme);\r\n\t\t\t\t}\r\n\t\t\t\t.gray_bg{\r\n\t\t\t\t\tbackground-color: #B3B3B4;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=32344772&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/f_goodList/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=32344772&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"goodsList\">\r\n\t\t<view class=\"item acea-row row-between-wrapper\" v-for=\"(item,index) in tempArr\" :key='index' @click=\"goDetail(item)\">\r\n\t\t\t<view class=\"pic\">\r\n\t\t\t\t<image :src=\"item.image\" mode=\"\"></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"pictxt\">\r\n\t\t\t\t<view class=\"text line2\">{{item.storeName}}</view>\r\n\t\t\t\t<view class=\"bottom acea-row row-between-wrapper\">\r\n\t\t\t\t\t<view class=\"money\">\r\n\t\t\t\t\t\t<text class=\"sign\">￥</text>{{item.price}} \r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"item.stock>0\">\r\n\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t<!-- 多规格 -->\r\n\t\t\t\t\t\t\t<view class=\"bnt\" @click.stop=\"goCartDuo(item)\">\r\n\t\t\t\t\t\t\t\t选规格\r\n\t\t\t\t\t\t\t\t<view class=\"num\" v-if=\"item.cartNum\">{{item.cartNum}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"bnt end\" v-else>已售罄</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tname: 'd_goodList',\r\n\t\tprops: {\r\n\t\t\tdataConfig: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: () => {}\r\n\t\t\t},\r\n\t\t\ttempArr:{\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault:[]\r\n\t\t\t},\r\n\t\t\tisLogin:{\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault:false\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t\r\n\t\t\t};\r\n\t\t},\r\n\t\tcreated() {},\r\n\t\tmounted() {},\r\n\t\tmethods: {\r\n\t\t\tgoDetail(item){\r\n\t\t\t\tthis.$emit('detail',item);\r\n\t\t\t},\r\n\t\t\tgoCartDuo(item){\r\n\t\t\t\tthis.$emit('gocartduo',item);\r\n\t\t\t},\r\n\t\t\tgoCartDan(item,index){\r\n\t\t\t\tthis.$emit('gocartdan',item,index);\r\n\t\t\t},\r\n\t\t\tCartNumDes(index,item){\r\n\t\t\t\tthis.$emit('ChangeCartNumDan', false,index,item);\r\n\t\t\t},\r\n\t\t\tCartNumAdd(index,item){\r\n\t\t\t\tthis.$emit('ChangeCartNumDan', true,index,item);\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.goodsList{\r\n\t\tpadding: 0 30rpx;\r\n\t\t.item{\r\n\t\t\twidth: 100%;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tmargin-bottom: 63rpx;\r\n\t\t\t.pic{\r\n\t\t\t\twidth: 140rpx;\r\n\t\t\t\theight: 140rpx;\r\n\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tborder-radius: 22rpx;\r\n\t\t\t\timage{\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t\tborder-radius: 22rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.pictxt{\r\n\t\t\t\twidth: 372rpx;\r\n\t\t\t\t.text{\r\n\t\t\t\t\tfont-size:26rpx;\r\n\t\t\t\t\tfont-family:PingFang SC;\r\n\t\t\t\t\tfont-weight:500;\r\n\t\t\t\t\tcolor: $crmeb-font-color;\r\n\t\t\t\t}\r\n\t\t\t\t.bottom{\r\n\t\t\t\t\tmargin-top: 22rpx;\r\n\t\t\t\t\t.money{\r\n\t\t\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\t\t\tfont-weight: 800;\r\n\t\t\t\t\t\twidth: 212rpx;\r\n\t\t\t\t\t\t@include price_color(theme);\r\n\t\t\t\t\t\t.sign{\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.otPrice{\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\tfont-family: PingFang SC;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tline-height: 24rpx;\r\n\t\t\t\t\t\tpadding-left: 14rpx;\r\n\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.cart{\r\n\t\t\t\t\t\theight: 46rpx;\r\n\t\t\t\t\t\t.pictrue{\r\n\t\t\t\t\t\t\tcolor: $crmeb-theme-color;\r\n\t\t\t\t\t\t\tfont-size:46rpx;\r\n\t\t\t\t\t\t\twidth: 46rpx;\r\n\t\t\t\t\t\t\theight: 46rpx;\r\n\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t\tline-height: 46rpx;\r\n\t\t\t\t\t\t\t&.icon-jiahao{\r\n\t\t\t\t\t\t\t\t color: $crmeb-theme-color;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t.num{\r\n\t\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t\t\tcolor: $crmeb-font-color;\r\n\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t\twidth: 60rpx;\r\n\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.icon-gouwuche6{\r\n\t\t\t\t\t\twidth: 46rpx;\r\n\t\t\t\t\t\theight: 46rpx;\r\n\t\t\t\t\t\tbackground-color: $crmeb-theme-color;\r\n\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\tcolor: $crmeb-font-color-white;\r\n\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.bnt{\r\n\t\t\t\t\t\tpadding: 0 20rpx;\r\n\t\t\t\t\t\theight: 46rpx;\r\n\t\t\t\t\t\tline-height: 46rpx;\r\n\t\t\t\t\t\t@include main_bg_color(theme);\r\n\t\t\t\t\t\tborder-radius:23rpx;\r\n\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\tcolor: $crmeb-font-color-white;\r\n\t\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\t&.end{\r\n\t\t\t\t\t\t\tbackground:$crmeb-font-color-disable;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t.num{\r\n\t\t\t\t\t\t\tmin-width: 12rpx;\r\n\t\t\t\t\t\t\t@include main_color(theme);\r\n\t\t\t\t\t\t\t@include coupons_border_color(theme);\r\n\t\t\t\t\t\t\tbackground: #fff;\r\n\t\t\t\t\t\t\tborder-radius: 15px;\r\n\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\tright: -13rpx;\r\n\t\t\t\t\t\t\ttop: -11rpx;\r\n\t\t\t\t\t\t\tfont-size: 16rpx;\r\n\t\t\t\t\t\t\tpadding: 0 11rpx;\r\n\t\t\t\t\t\t\theight: 32rpx;\r\n\t\t\t\t\t\t\tline-height: 32rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294180260\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fresh.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fresh.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294179561\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./goods_cate.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./goods_cate.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294178768\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}