{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/user/index.vue?1e29", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/user/index.vue?e8cc", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/user/index.vue?1828", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/user/index.vue?a38a", "uni-app:///pages/user/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/user/index.vue?38ab", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/user/index.vue?a891"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "computed", "data", "orderMenu", "img", "title", "url", "num", "imgUrls", "userMenu", "autoplay", "circular", "interval", "duration", "isAuto", "isShowAuth", "orderStatusNum", "MyMenus", "wechatUrl", "servicePic", "sysHeight", "pageHeight", "config<PERSON>pi", "theme", "bgColor", "chatConfig", "consumer_hotline", "telephone_service_switch", "onLoad", "app", "setTimeout", "document", "dom", "e", "active", "that", "uni", "frontColor", "backgroundColor", "onShow", "methods", "bindEdit", "window", "name", "menusTap", "animationType", "animationDuration", "navito", "kefuClick", "phoneNumber", "location", "getOrderData", "item", "openAuto", "<PERSON><PERSON>", "onLoadFun", "bindPhone", "getMyMenus", "console", "res", "goEdit", "goMenuPage", "appUpdate", "shareApi", "setOpenShare", "desc", "link", "imgUrl", "configAppMessage"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC8HnnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAjBA;AAkBA;AAAA,eACA;EACAC;EACAC;IACA;MACAC,YACA;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,EACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MAQAC;MAAA;MACAC;MACAC;MACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACAC;IACA;MACAC;QACA;QACAC;UACAC;YACAC;YACAA;YACA;YACAD;YACAE;YACAA;UACA;QACA;MACA;IACA;IACA;IAIAC;IACAA;IAIAA;IAUAC;MACAC;MACAC;IACA;EAEA;EACAC;IACA;IACAV;IACA;IACA;IACAO;IAQA;MACA;MACA;MACA;IACA;IAEA;IACA;EAEA;EACAI;IACAC;MACA;QACAC,0BACA;UACAC;QACA,GACA,IACA;QACA;MACA;IACA;IACAC;MACA;QACA;MACA;QACA;UACAR;YACAS;YACAC;YACAxC;UACA;QACA;MACA;IACA;IACAyC;MAEAX;QACA9B;MACA;MAEA;QACAoC;MACA;QACA;MACA;IACA;IACAM;MACA;QACAZ;UACAa;QACA;MACA;QASA;UACAC;QACA;UACA;QACA;MAEA;IACA;IACAC;MACA;MACA;QACAhB;UACA;YACA;cACAiB;cACA;YACA;cACAA;cACA;YACA;cACAA;cACA;YACA;cACAA;cACA;YACA;cACAA;cACA;YACA;cACAA;cACA;UAAA;QAEA;QACAjB;MACA;IACA;IACA;IACAkB;MACAC;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACApB;QACAS;QACAC;QACAxC;MACA;IACA;IACA;AACA;AACA;AACA;IACAmD;MACA;MACAC;MACA;MACA;QACAvB;QACAA;UACA;QACA;QACAwB;UACA;QACA;QACA;QACA;UACAxB;QACA;MACA;IACA;IACA;IACAyB;MACA;QACA;MACA;QACAxB;UACAS;UACAC;UACAxC;QACA;MACA;IACA;IACAuD;MACA;QACAzB;UACAS;UACAC;UACAxC;QACA;MACA;QAEA;MAEA;IACA;IACAwD;MACA1B;QACA9B;QACAuC;QACAC;MACA;IACA;IACAiB;MAAA;MACA;QACA;MAIA;IACA;IACA;IACAC;MACA;MACA;QACA;UACAC;UACA5D;UACA6D;UACAC;QACA;QACAhC,mFACAiC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClbA;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/user/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/user/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=137d5072&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=137d5072&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"137d5072\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=137d5072&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.imgUrls != null && _vm.imgUrls.length > 0\n  var g1 = g0 ? _vm.imgUrls.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :data-theme=\"theme\">\r\n\t\t<view class=\"new-users copy-data\"  >\r\n\t\t\t<view class=\"mid\" style=\"flex:1;overflow: hidden;\">\r\n\t\t\t\t<scroll-view scroll-y=\"true\">\r\n\t\t\t\t\t<view class=\"bg\"></view>\r\n\t\t\t\t\t<view class=\"head pad30\">\r\n\t\t\t\t\t\t<view class=\"user-card\">\r\n\t\t\t\t\t\t\t<view class=\"user-info\">\r\n\t\t\t\t\t\t\t\t<image class=\"avatar\" :src='userInfo.avatar' v-if=\"userInfo.avatar && uid\"\tv-on:click=\"goEdit()\"></image>\r\n\t\t\t\t\t\t\t\t<image v-else class=\"avatar\" src=\"/static/images/f.png\" mode=\"\" v-on:click=\"goEdit()\"></image>\r\n\t\t\t\t\t\t\t\t<view class=\"info\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"name\" v-if=\"!uid\" @tap=\"openAuto\">\r\n\t\t\t\t\t\t\t\t\t\t请点击登录\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"name\" v-if=\"uid\">\r\n\t\t\t\t\t\t\t\t\t\t{{userInfo.nickname}}\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"vip\" v-if=\"userInfo.vip\">\r\n\t\t\t\t\t\t\t\t\t\t\t<image :src=\"userInfo.vipIcon\" alt=\"\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view style=\"margin-left: 10rpx;\" class=\"vip-txt\">{{userInfo.vipName || ''}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"num\" v-if=\"userInfo.phone && uid\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"num-txt\">{{userInfo.phone}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"icon\">\r\n\t\t\t\t\t\t\t\t\t\t\t<image src=\"/static/images/edit.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"phone\" v-if=\"!userInfo.phone && isLogin\" @tap=\"bindPhone\">绑定手机号</view>\r\n\t\t\t\t\t\t\t\t\t<!-- #ifdef APP-PLUS -->\r\n\t\t\t\t\t\t\t\t\t<text class=\"iconfont icon-shezhi app_set\" @click.stop=\"appUpdate()\"></text>\r\n\t\t\t\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"num-wrapper\">\r\n\t\t\t\t\t\t\t\t<view class=\"num-item\" @click=\"goMenuPage('/pages/users/user_money/index')\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"num\">{{userInfo.nowMoney && uid ? userInfo.nowMoney:0}}</text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"txt\">余额</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"num-item\" @click=\"goMenuPage('/pages/users/user_integral/index')\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"num\">{{userInfo.integral && uid ? userInfo.integral: 0}}</text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"txt\">积分</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"num-item\" @click=\"goMenuPage('/pages/users/user_coupon/index')\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"num\">{{userInfo.couponCount && uid ? userInfo.couponCount : 0}}</text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"txt\">优惠券</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"num-item\" @click=\"goMenuPage('/pages/users/user_goods_collection/index')\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"num\">{{userInfo.collectCount && uid ? userInfo.collectCount : 0}}</text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"txt\">收藏</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"order-wrapper\">\r\n\t\t\t\t\t\t\t<view class=\"order-hd flex\">\r\n\t\t\t\t\t\t\t\t<view class=\"left\">订单中心</view>\r\n\t\t\t\t\t\t\t\t<view class=\"right flex\" @click=\"menusTap('/pages/users/order_list/index')\">查看全部\r\n\t\t\t\t\t\t\t\t\t<text class=\"iconfont icon-xiangyou\"></text> \r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"order-bd\">\r\n\t\t\t\t\t\t\t\t<block v-for=\"(item,index) in orderMenu\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"order-item\" @click=\"menusTap(item.url)\"> \r\n\t\t\t\t\t\t\t\t\t\t<view class=\"pic\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"iconfont pic_status\" :class=\"item.img\"></text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"order-status-num\" v-if=\"item.num > 0\">{{ item.num }}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"txt\">{{item.title}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"contenBox\" id=\"pageIndex\">\r\n\t\t\t\t\t\t<!-- 轮播 -->\r\n\t\t\t\t\t\t<view class=\"slider-wrapper\" @click.native=\"bindEdit('userBanner')\" v-if=\"imgUrls != null && imgUrls.length > 0\">\r\n\t\t\t\t\t\t\t<swiper v-if=\"imgUrls.length>0\" indicator-dots=\"true\" :autoplay=\"autoplay\" :circular=\"circular\" :interval=\"interval\"\r\n\t\t\t\t\t\t\t\t:duration=\"duration\" indicator-color=\"rgba(255,255,255,0.6)\" indicator-active-color=\"#fff\">\r\n\t\t\t\t\t\t\t\t<block v-for=\"(item,index) in imgUrls\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t<swiper-item class=\"borRadius14\">\r\n\t\t\t\t\t\t\t\t\t\t<image :src=\"item.pic\" class=\"slide-image\" @click=\"navito(item.url)\"></image>\r\n\t\t\t\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</swiper>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- 会员菜单 -->\r\n\t\t\t\t\t\t<view class=\"user-menus\" style=\"margin-top: 20rpx;\" @click.native=\"bindEdit('userMenus')\">\r\n\t\t\t\t\t\t\t<view class=\"menu-title\">我的服务</view>\r\n\t\t\t\t\t\t\t<view class=\"list-box\">\r\n\t\t\t\t\t\t\t\t<block v-for=\"(item,index) in MyMenus\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"item\" @click=\"menusTap(item.url)\"\r\n\t\t\t\t\t\t\t\t\t\tv-if=\"!(item.url =='/pages/service/index' || (item.url =='/pages/users/user_spread_user/index' && !userInfo.isPromoter))\">\r\n\t\t\t\t\t\t\t\t\t\t<image :src=\"item.pic\"></image>\r\n\t\t\t\t\t\t\t\t\t\t<text>{{item.name}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t<!-- #ifndef MP -->\r\n\t\t\t\t\t\t\t\t<view class=\"item\" @click=\"kefuClick\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"servicePic\"></image>\r\n\t\t\t\t\t\t\t\t\t<text>联系客服</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t\t\t<!-- #ifdef MP -->\r\n\t\t\t\t\t\t\t\t<!--  v-if=\"chatConfig.telephone_service_switch\" -->\r\n\t\t\t\t\t\t\t\t<button class=\"item\" hover-class='none' @click=\"kefuClick\" v-if=\"chatConfig.telephone_service_switch === 'true'\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"servicePic\"></image>\r\n\t\t\t\t\t\t\t\t\t<text>联系客服</text>\r\n\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t<button class=\"item\" open-type='contact' hover-class='none' v-else>\r\n\t\t\t\t\t\t\t\t\t<image :src=\"servicePic\"></image>\r\n\t\t\t\t\t\t\t\t\t<text>联系客服</text>\r\n\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- 这里可以填写力的版权信息 <image src=\"/static/images/support.png\" alt=\"\" class='support'> -->\r\n\t\t\t\t\t\t<view class=\"uni-p-b-98\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n<script>\r\n\tlet sysHeight = uni.getSystemInfoSync().statusBarHeight + 'px';\r\n\timport Cache from '@/utils/cache';\r\n\timport {goPage} from '@/libs/iframe.js'\r\n\timport {BACK_URL} from '@/config/cache';\r\n\timport {getMenuList} from '@/api/user.js';\r\n\timport {orderData} from '@/api/order.js';\r\n\timport {getCity} from '@/api/api.js';\r\n\timport {toLogin} from '@/libs/login.js';\r\n\timport {mapGetters} from \"vuex\";\r\n\timport {\r\n\t\tgetCityList\r\n\t} from \"@/utils\";\r\n\t// #ifdef H5\r\n\timport Auth from '@/libs/wechat';\r\n\t// #endif\r\n\timport {getShare} from '@/api/public.js';\r\n\timport {setThemeColor} from '@/utils/setTheme.js'\r\n\timport animationType from '@/utils/animationType.js'\r\n\tconst app = getApp();\r\n\texport default {\r\n\t\tcomputed: mapGetters(['isLogin', 'chatUrl', 'userInfo', 'uid']),\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\torderMenu: [\r\n\t\t\t\t\t{img: 'icon-daipingjia',title: '全部',url: '/pages/users/order_list/index?status=99',num: 0},\r\n\t\t\t\t\t{img: 'icon-daifukuan',title: '待付款',url: '/pages/users/order_list/index?status=0',num: 0},\r\n\t\t\t\t\t{img: 'icon-daifahuo',title: '待发货',url: '/pages/users/order_list/index?status=1',num: 0},\r\n\t\t\t\t\t{img: 'icon-daishouhuo',title: '待收货',url: '/pages/users/order_list/index?status=2',num: 0},\r\n\t\t\t\t\t{img: 'icon-daipingjia',title: '待评价',url: '/pages/users/order_list/index?status=3',num: 0},\r\n\t\t\t\t\t{img: 'icon-a-shouhoutuikuan',title: '售后/退款',url: '/pages/users/user_return_list/index',num: 0},\r\n\t\t\t\t],\r\n\t\t\t\timgUrls: [],\r\n\t\t\t\tuserMenu: [],\r\n\t\t\t\tautoplay: true,\r\n\t\t\t\tcircular: true,\r\n\t\t\t\tinterval: 3000,\r\n\t\t\t\tduration: 500,\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false, //是否隐藏授权\r\n\t\t\t\torderStatusNum: {},\r\n\t\t\t\tMyMenus: [],\r\n\t\t\t\twechatUrl: [],\r\n\t\t\t\tservicePic: '/static/images/customer.png',\r\n\t\t\t\tsysHeight: sysHeight,\r\n\t\t\t\t// #ifdef MP\r\n\t\t\t\tpageHeight: '100%',\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef H5 || APP-PLUS\r\n\t\t\t\tpageHeight: app.globalData.windowHeight,\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tisWeixin: Auth.isWeixin(),\r\n\t\t\t\t//#endif\r\n\t\t\t\tconfigApi: {}, //分享类容配置\r\n\t\t\t\ttheme: '',\r\n\t\t\t\tbgColor:'#e93323',\r\n\t\t\t\tchatConfig:{\r\n\t\t\t\t\tconsumer_hotline:'',\r\n\t\t\t\t\ttelephone_service_switch:'false'\r\n\t\t\t\t} ,//客服配置\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tapp.globalData.theme = this.$Cache.get('theme')\r\n\t\t\tif(app.globalData.isIframe){\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tlet active;\r\n\t\t\t\t\tdocument.getElementById('pageIndex').children.forEach(dom=>{\r\n\t\t\t\t\t\tdom.addEventListener('click', (e)=>{\r\n\t\t\t\t\t\t\te.stopPropagation();\r\n\t\t\t\t\t\t\te.preventDefault();\r\n\t\t\t\t\t\t\tif(dom === active) return;\r\n\t\t\t\t\t\t\tdom.classList.add('borderShow');\r\n\t\t\t\t\t\t\tactive && active.classList.remove('borderShow');\r\n\t\t\t\t\t\t\tactive = dom;\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\tlet that = this;\r\n\t\t\t// #ifdef H5 || APP-PLUS\r\n\t\t\tthat.$set(that, 'pageHeight', app.globalData.windowHeight);\r\n\t\t\t// #endif\r\n\t\t\tthat.$set(that, 'MyMenus', app.globalData.MyMenus);\r\n\t\t\tthat.$set(that,'chatConfig',Cache.getItem('chatConfig'));\r\n\t\t\t// #ifdef H5\r\n\t\t\tthat.shareApi();\r\n\t\t\t// #endif\r\n\t\t\tthat.bgColor = setThemeColor();\r\n\t\t\t // #ifdef APP-PLUS\r\n\t\t\tsetTimeout(()=>{\r\n\t\t\t \tuni.setNavigationBarColor({\r\n\t\t\t \t\tfrontColor: '#ffffff',\r\n\t\t\t \t\tbackgroundColor:that.bgColor,   \r\n\t\t\t \t});\r\n\t\t\t },500)\r\n\t\t\t // #endif\r\n\t\t\t // #ifdef MP\r\n\t\t\t uni.setNavigationBarColor({\r\n\t\t\t \tfrontColor: '#ffffff',\r\n\t\t\t \tbackgroundColor:that.bgColor,   \r\n\t\t\t });\r\n\t\t\t // #endif\r\n\t\t},\r\n\t\tonShow: function() {\r\n\t\t\tthis.theme = this.$Cache.get('theme')\r\n\t\t\tapp.globalData.theme = this.$Cache.get('theme')\r\n\t\t\tlet that = this;\r\n\t\t\tif (!that.$Cache.getItem('cityList')) getCityList();\r\n\t\t\tuni.showTabBar();\r\n\t\t\t// #ifdef H5\r\n\t\t\tuni.getSystemInfo({\r\n\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\tthat.pageHeight = res.windowHeight + 'px'\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t// #endif\r\n\t\t\tif (that.isLogin) {\r\n\t\t\t\tthis.getMyMenus();\r\n\t\t\t\tthis.getOrderData();\r\n\t\t\t\tthis.$store.dispatch('USERINFO');\r\n\t\t\t}\r\n\t\t\t// #ifdef MP\r\n\t\t\tlet  query  = uni.createSelectorQuery(); \r\n\t\t\tlet dom = query.select('.new-users');\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tbindEdit(name) {\r\n\t\t\t\tif (app.globalData.isIframe) {\r\n\t\t\t\t\twindow.parent.postMessage(\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tname: name\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t'*'\r\n\t\t\t\t\t);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tmenusTap(url) {\r\n\t\t\t\tif (!this.isLogin) {\r\n\t\t\t\t\tthis.openAuto(); \r\n\t\t\t\t}else{\r\n\t\t\t\t\tgoPage().then(res => {\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\tanimationType: animationType.type,\r\n\t\t\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\t\t\turl: url\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tnavito(e) {\r\n\t\t\t\t// #ifdef APP-PLUS || MP\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/users/web_page/index?webUel=' + e\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t\tif (!app.globalData.isIframe) {\r\n\t\t\t\t\twindow.location.href = 'https://' + e;\r\n\t\t\t\t}else{\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tkefuClick() {\r\n\t\t\t\tif(this.chatConfig.telephone_service_switch === 'true'){\r\n\t\t\t\t\tuni.makePhoneCall({\r\n\t\t\t\t\t    phoneNumber: this.chatConfig.consumer_hotline //仅为示例\r\n\t\t\t\t\t});\r\n\t\t\t\t}else{\r\n\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\tanimationType: animationType.type,\r\n\t\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\t\turl: '/pages/users/web_page/index?webUel=' + this.chatUrl + '&title=客服'\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifndef APP-PLUS\r\n\t\t\t\t\tif (!app.globalData.isIframe) {\r\n\t\t\t\t\t\tlocation.href = this.chatUrl;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\treturn false\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetOrderData() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\torderData().then(res => {\r\n\t\t\t\t\tthat.orderMenu.forEach((item, index) => {\r\n\t\t\t\t\t\tswitch (item.title) {\r\n\t\t\t\t\t\t\tcase '全部':\r\n\t\t\t\t\t\t\t\titem.num = res.data.orderCount\r\n\t\t\t\t\t\t\t\tbreak\r\n\t\t\t\t\t\t\tcase '待付款':\r\n\t\t\t\t\t\t\t\titem.num = res.data.unPaidCount\r\n\t\t\t\t\t\t\t\tbreak\r\n\t\t\t\t\t\t\tcase '待发货':\r\n\t\t\t\t\t\t\t\titem.num = res.data.unShippedCount\r\n\t\t\t\t\t\t\t\tbreak\r\n\t\t\t\t\t\t\tcase '待收货':\r\n\t\t\t\t\t\t\t\titem.num = res.data.receivedCount\r\n\t\t\t\t\t\t\t\tbreak\r\n\t\t\t\t\t\t\tcase '待评价':\r\n\t\t\t\t\t\t\t\titem.num = res.data.evaluatedCount\r\n\t\t\t\t\t\t\t\tbreak\r\n\t\t\t\t\t\t\tcase '售后/退款':\r\n\t\t\t\t\t\t\t\titem.num = res.data.refundCount\r\n\t\t\t\t\t\t\t\tbreak\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthat.$set(that, 'orderMenu', that.orderMenu);\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 打开授权\r\n\t\t\topenAuto() {\r\n\t\t\t\tCache.set(BACK_URL, '')\r\n\t\t\t\ttoLogin();\r\n\t\t\t},\r\n\t\t\t// 授权回调\r\n\t\t\tonLoadFun() {\r\n\t\t\t\tthis.getMyMenus();\r\n\t\t\t\tthis.getOrderData();\r\n\t\t\t},\r\n\t\t\t// 绑定手机\r\n\t\t\tbindPhone() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\tanimationType: animationType.type,\r\n\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\turl: '/pages/users/app_login/index'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * \r\n\t\t\t * 获取个人中心图标\r\n\t\t\t */\r\n\t\t\tgetMyMenus: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tconsole.log(app.globalData)\r\n\t\t\t\t// if (this.MyMenus.length) return;\r\n\t\t\t\tgetMenuList().then(res => {\r\n\t\t\t\t\tthat.$set(that, 'MyMenus', res.data.routine_my_menus);\r\n\t\t\t\t\tthat.wechatUrl = res.data.routine_my_menus.filter((item) => {\r\n\t\t\t\t\t\treturn item.url.indexOf('service') !== -1\r\n\t\t\t\t\t})\r\n\t\t\t\t\tres.data.routine_my_menus.map((item) => {\r\n\t\t\t\t\t\tif (item.url.indexOf('service') !== -1) that.servicePic = item.pic\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// that.imgUrls = res.data.routine_my_banner\r\n\t\t\t\t\tif(res.data.routine_my_banner){\r\n\t\t\t\t\t\tthat.imgUrls = res.data.routine_my_banner\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 编辑页面\r\n\t\t\tgoEdit() {\r\n\t\t\t\tif (this.isLogin == false) {\r\n\t\t\t\t\tthis.openAuto();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\tanimationType: animationType.type,\r\n\t\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\t\turl: '/pages/users/user_info/index'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgoMenuPage(url) {\r\n\t\t\t\tif (this.isLogin) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\tanimationType: animationType.type,\r\n\t\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\t\turl\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// #ifdef MP\r\n\t\t\t\t\tthis.openAuto()\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tappUpdate(){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:'/pages/users/app_update/app_update',\r\n\t\t\t\t\tanimationType: animationType.type,\r\n\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tshareApi: function() {\r\n\t\t\t\tgetShare().then(res => {\r\n\t\t\t\t\tthis.$set(this, 'configApi', res.data);\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\tthis.setOpenShare(res.data);\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 微信分享；\r\n\t\t\tsetOpenShare: function(data) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (that.$wechat.isWeixin()) {\r\n\t\t\t\t\tlet configAppMessage = {\r\n\t\t\t\t\t\tdesc: data.synopsis,\r\n\t\t\t\t\t\ttitle: data.title,\r\n\t\t\t\t\t\tlink: location.href,\r\n\t\t\t\t\t\timgUrl: data.img\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthat.$wechat.wechatEvevt([\"updateAppMessageShareData\", \"updateTimelineShareData\"],\r\n\t\t\t\t\t\tconfigAppMessage);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\tpage,\r\n\tbody {\r\n\t\theight: 100%;\r\n\t}\r\n\t.mp-header{\r\n\t\t@include main_bg_color(theme);\r\n\t}\r\n\t.bg {\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\twidth:100%;\r\n\t\theight: 420rpx;\r\n\t\tbackground-image: url('~@/static/images/user_bg.png');\r\n\t\tbackground-repeat: no-repeat;\r\n\t\tbackground-size: 100% 100%;\r\n\t}\r\n\t.contenBox {\r\n\t\tpadding: 0 30rpx;\r\n\t}\r\n\r\n\t.support {\r\n\t\twidth: 219rpx;\r\n\t\theight: 74rpx;\r\n\t\tmargin: 54rpx auto;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.new-users {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\theight: 100%;\r\n\r\n\t\t.sys-head {\r\n\t\t\tposition: relative;\r\n\t\t\twidth: 100%;\r\n\t\t\tbackground: linear-gradient(90deg, $bg-star1 0%, $bg-end1 100%);\r\n\r\n\t\t\t.sys-title {\r\n\t\t\t\tz-index: 10;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\theight: 43px;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tline-height: 43px;\r\n\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.head {\r\n\t\t\t@include index-gradient(theme);\r\n\t\t\t.user-card {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tmargin: 0 auto;\r\n\t\t\t\tpadding: 35rpx 0 30rpx 0;\r\n\t\t\t\t.user-info {\r\n\t\t\t\t\tz-index: 20;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\tdisplay: flex;\r\n\r\n\t\t\t\t\t.avatar {\r\n\t\t\t\t\t\twidth: 120rpx;\r\n\t\t\t\t\t\theight: 120rpx;\r\n\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.info {\r\n\t\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\t\tmargin-left: 20rpx;\r\n\t\t\t\t\t\tpadding: 15rpx 0;\r\n\t\t\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t\t\t.name {\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\t\tfont-size: 31rpx;\r\n\r\n\t\t\t\t\t\t\t.vip {\r\n\t\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\t\tpadding: 6rpx 20rpx;\r\n\t\t\t\t\t\t\t\tbackground: rgba(0, 0, 0, 0.2);\r\n\t\t\t\t\t\t\t\tborder-radius: 18px;\r\n\t\t\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\t\t\tmargin-left: 12rpx;\r\n\r\n\t\t\t\t\t\t\t\timage {\r\n\t\t\t\t\t\t\t\t\twidth: 27rpx;\r\n\t\t\t\t\t\t\t\t\theight: 27rpx;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t.app_set{\r\n\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\t\ttop: 40rpx;\r\n\t\t\t\t\t\t\tright: 20rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.num {\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\t\tcolor: rgba(255, 255, 255, 0.6);\r\n\r\n\t\t\t\t\t\t\timage {\r\n\t\t\t\t\t\t\t\twidth: 22rpx;\r\n\t\t\t\t\t\t\t\theight: 23rpx;\r\n\t\t\t\t\t\t\t\tmargin-left: 20rpx;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.num-wrapper {\r\n\t\t\t\t\tz-index: 30;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\tmargin-top: 30rpx;\r\n\t\t\t\t\tcolor: #fff;\r\n\r\n\t\t\t\t\t.num-item {\r\n\t\t\t\t\t\twidth: 33.33%;\r\n\t\t\t\t\t\ttext-align: center;\r\n\r\n\t\t\t\t\t\t.num {\r\n\t\t\t\t\t\t\tfont-size: 42rpx;\r\n\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.txt {\r\n\t\t\t\t\t\t\tmargin-top: 10rpx;\r\n\t\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\t\tcolor: rgba(255, 255, 255, 0.6);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.sign {\r\n\t\t\t\t\tz-index: 200;\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tright: -12rpx;\r\n\t\t\t\t\ttop: 80rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\twidth: 120rpx;\r\n\t\t\t\t\theight: 60rpx;\r\n\t\t\t\t\tbackground: linear-gradient(90deg, rgba(255, 225, 87, 1) 0%, rgba(238, 193, 15, 1) 100%);\r\n\t\t\t\t\tborder-radius: 29rpx 4rpx 4rpx 29rpx;\r\n\t\t\t\t\tcolor: #282828;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.order-wrapper {\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tborder-radius: 14rpx;\r\n\t\t\t\tpadding: 30rpx 16rpx;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tz-index: 11;\r\n                \r\n\t\t\t\t.order-hd {\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tcolor: #282828;\r\n\t\t\t\t\tmargin-bottom: 40rpx;\r\n\t\t\t\t\tpadding: 0 16rpx;\r\n\r\n\t\t\t\t\t.left {\r\n\t\t\t\t\t\tcolor: #282828;\r\n\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.right {\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tcolor: #666666;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\r\n\t\t\t\t\t\t.icon-xiangyou {\r\n\t\t\t\t\t\t\tmargin-left: 5rpx;\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.order-bd {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\tpadding: 0;\r\n\r\n\t\t\t\t\t.order-item {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t\t\t.pic {\r\n\t\t\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\t\ttext-align: center;\r\n\r\n\t\t\t\t\t\t\timage {\r\n\t\t\t\t\t\t\t\twidth: 48rpx;\r\n\t\t\t\t\t\t\t\theight: 48rpx;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.txt {\r\n\t\t\t\t\t\t\tmargin-top: 15rpx;\r\n\t\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\t\tcolor: #454545;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.slider-wrapper {\r\n\t\t\tmargin: 20rpx 0;\r\n\t\t\theight: 138rpx;\r\n\r\n\t\t\tswiper,\r\n\t\t\tswiper-item {\r\n\t\t\t\theight: 100%;\r\n\t\t\t}\r\n\r\n\t\t\timage {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.user-menus {\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-radius: 14rpx;\r\n\r\n\t\t\t.menu-title {\r\n\t\t\t\tpadding: 30rpx 30rpx 40rpx;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tcolor: #282828;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t}\r\n\r\n\t\t\t.list-box {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-wrap: wrap;\r\n\t\t\t\tpadding: 0;\r\n\t\t\t}\r\n\r\n\t\t\t.item {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\twidth: 25%;\r\n\t\t\t\tmargin-bottom: 47rpx;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #333333;\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 52rpx;\r\n\t\t\t\t\theight: 52rpx;\r\n\t\t\t\t\tmargin-bottom: 18rpx;\r\n\t\t\t\t}\r\n\r\n\r\n\t\t\t\t&:last-child::before {\r\n\t\t\t\t\tdisplay: none;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tbutton {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.phone {\r\n\t\t\tcolor: #fff;\r\n\t\t}\r\n\t\t.pic_status{\r\n\t\t\tfont-size: 43rpx;\r\n\t\t\t@include main_color(theme);\r\n\t\t}\r\n\t\t.order-status-num {\r\n\t\t\tmin-width: 13rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\t@include main_color(theme);\r\n\t\t\tborder-radius: 15px;\r\n\t\t\tposition: absolute;\r\n\t\t\tright: -14rpx;\r\n\t\t\ttop: -15rpx;\r\n\t\t\tfont-size: 20rpx;\r\n\t\t\tpadding: 0 8rpx;\r\n\t\t\t@include coupons_border_color(theme);\r\n\t\t}\r\n\t\t\r\n\t}\r\n\t.sub_btn{\r\n\t\twidth: 690rpx;\r\n\t\theight: 86rpx;\r\n\t\tline-height: 86rpx;\r\n\t\tmargin-top: 60rpx;\r\n\t\tbackground: $theme-color;\r\n\t\tborder-radius: 43rpx;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 28rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=137d5072&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=137d5072&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294178757\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}