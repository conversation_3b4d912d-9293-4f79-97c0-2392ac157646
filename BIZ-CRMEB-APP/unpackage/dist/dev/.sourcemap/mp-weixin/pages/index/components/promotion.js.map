{"version": 3, "sources": ["webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/promotion.vue?9f1d", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/promotion.vue?b671", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/promotion.vue?1140", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/promotion.vue?3547", "uni-app:///pages/index/components/promotion.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/promotion.vue?a747", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/promotion.vue?3a24", "uni-app:///main.js"], "names": ["name", "props", "tabData", "type", "default", "showType", "components", "promotionGood", "created", "setTimeout", "data", "tempArr", "params", "page", "limit", "methods", "productslist", "resolve", "reloadData", "i", "goodsInfo", "gopage", "uni", "animationType", "animationDuration", "url", "goDetail", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmmB,CAAgB,6nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC8CvnB;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AAAA,eACA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;EACA;EACAE;IACAC;EACA;EACAC;IAAA;IACAC;MACA;IACA;EACA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;MACA;QACA;UAAA;UACAC;QACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBACA;cAAA;gBAFAD;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAIA;IACAE;MACAC;QACAC;QACAC;QACAC;MACA;IACA;IACAC;MACAJ;QACAC;QACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9GA;AAAA;AAAA;AAAA;AAA8oC,CAAgB,onCAAG,EAAC,C;;;;;;;;;;;ACAlqC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;ACNL;AAGA;AACA;AAHA;AACAE,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C", "file": "pages/index/components/promotion.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./promotion.vue?vue&type=template&id=11954480&\"\nvar renderjs\nimport script from \"./promotion.vue?vue&type=script&lang=js&\"\nexport * from \"./promotion.vue?vue&type=script&lang=js&\"\nimport style0 from \"./promotion.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/components/promotion.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promotion.vue?vue&type=template&id=11954480&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promotion.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promotion.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"\">\r\n\t\t<view class=\"index-wrapper\" v-show=\"showType == 2\">\r\n\t\t\t<view class='wrapper' v-for=\"(item,index) in tabData\" :key=\"index\" index>\r\n\t\t\t\t<view class='title1 acea-row row-between-wrapper'>\r\n\t\t\t\t\t<view class='text'>\r\n\t\t\t\t\t\t<view class='name line1'>{{item.name}}</view>\r\n\t\t\t\t\t\t<view class='line1 txt-btn'>{{item.info}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='more' @click=\"gopage(item)\">更多\r\n\t\t\t\t\t\t<text class='iconfont icon-jiantou'></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='newProducts'>\r\n\t\t\t\t\t<scroll-view class=\"scroll-view_x\" scroll-x style=\"width:auto;overflow:hidden;\">\r\n\t\t\t\t\t\t<block v-for=\"(item1,index1) in tempArr[index]\" :key='index1'>\r\n\t\t\t\t\t\t\t<view class='item' @click=\"goDetail(item1.id)\">\r\n\t\t\t\t\t\t\t\t<view class='img-box'>\r\n\t\t\t\t\t\t\t\t\t<image :src='item1.image'></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class='pro-info line1'>{{item1.storeName}}</view>\r\n\t\t\t\t\t\t\t\t<view class='money font-color'>￥{{item1.price}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</scroll-view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"index-wrapper\" v-show=\"showType == 3\">\r\n\t\t\t<view class='wrapper' v-for=\"(item,index) in tabData\" :key=\"index\" index>\r\n\t\t\t\t<view class='title1 acea-row row-between-wrapper'>\r\n\t\t\t\t\t<view class='text'>\r\n\t\t\t\t\t\t<view class='name line1'>{{item.name}}</view>\r\n\t\t\t\t\t\t<view class='line1 txt-btn'>{{item.info}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='more' @click=\"gopage(item)\">更多\r\n\t\t\t\t\t\t<text class='iconfont icon-jiantou'></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<promotionGood :benefit=\"tempArr[index]\"></promotionGood>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {getGroomList} from '@/api/store.js';\r\n\timport promotionGood from '@/components/promotionGood/index.vue';\r\n\timport animationType from '@/utils/animationType.js'\r\n\tlet app = getApp()\r\n\texport default {\r\n\t\tname: 'promotion',\r\n\t\tprops: {\r\n\t\t\ttabData: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault: []\r\n\t\t\t},\r\n\t\t\tshowType:{\r\n\t\t\t\ttype:Number,\r\n\t\t\t\tdefault:1\r\n\t\t\t},\r\n\t\t},\r\n\t\tcomponents: {\r\n\t\t\tpromotionGood\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tsetTimeout(()=>{\r\n\t\t\t\tthis.reloadData();\r\n\t\t\t},1000)\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttempArr: [],\r\n\t\t\t\tparams: {\r\n\t\t\t\t\tpage: 1,\r\n\t\t\t\t\tlimit: 6,\r\n\t\t\t\t},\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 产品列表\r\n\t\t\tproductslist: function(item) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\t\tgetGroomList(item.type,this.params).then(({data}) => {\r\n\t\t\t\t\t\tresolve(data.list);\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tasync reloadData(){\r\n\t\t\t\tfor (let i = 0; i < this.tabData.length; i++) {\r\n\t\t\t\t\tlet goodsInfo = await this.productslist(this.tabData[i]);\r\n\t\t\t\t\tthis.tempArr.push(goodsInfo);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgopage(type) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\tanimationType: animationType.type,\r\n\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\turl: '/pages/activity/promotionList/index?type=' + JSON.stringify(type)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgoDetail(id) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\tanimationType: animationType.type,\r\n\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\turl: `/pages/goods_details/index?id=${id}`\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.index-wrapper {\r\n\t\tborder-radius: 14rpx;\r\n\t\tmargin: 30rpx auto 110rpx;\r\n\t}\r\n\r\n\t.wrapper {\r\n\t\tmargin: 30rpx auto 0;\r\n\t\tborder-radius: 14rpx;\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\r\n\t.title1 {\r\n\t\tpadding-top: 20rpx;\r\n\t\tmargin: 0 20rpx;\r\n\r\n\t\t.text {\r\n\t\t\tdisplay: flex;\r\n\r\n\t\t\t.name {\r\n\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t}\r\n\r\n\t\t\t.txt-btn {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: flex-end;\r\n\t\t\t\tmargin-bottom: 4rpx;\r\n\t\t\t\tmargin-left: 12rpx;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #999;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.more {\r\n\t\t\tfont-size: 12px;\r\n\t\t\tcolor: #999;\r\n\r\n\t\t\t.icon-jiantou {\r\n\t\t\t\tmargin-left: 10rpx;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\r\n\t.wrapper .newProducts {\r\n\t\twhite-space: nowrap;\r\n\t\tpadding: 0rpx 20rpx 0rpx 20rpx;\r\n\t\tmargin: 20rpx 0;\r\n\t}\r\n\r\n\t.wrapper .newProducts .item {\r\n\t\tdisplay: inline-block;\r\n\t\twidth: 240rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t}\r\n\r\n\t.wrapper .newProducts .item:nth-last-child(1) {\r\n\t\tmargin-right: 0;\r\n\t}\r\n\r\n\t.wrapper .newProducts .item .img-box {\r\n\t\twidth: 100%;\r\n\t\theight: 240rpx;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.wrapper .newProducts .item .img-box image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 12rpx 12rpx 0 0;\r\n\t}\r\n\r\n\t.wrapper .newProducts .item .pro-info {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333;\r\n\t\ttext-align: center;\r\n\t\tpadding: 19rpx 10rpx 0 10rpx;\r\n\t}\r\n\r\n\t.wrapper .newProducts .item .money {\r\n\t\tpadding: 0 10rpx 18rpx 10rpx;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 26rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.empty-img {\r\n\t\twidth: 640rpx;\r\n\t\theight: 300rpx;\r\n\t\tborder-radius: 14rpx;\r\n\t\tmargin: 26rpx auto 0 auto;\r\n\t\tbackground-color: #ccc;\r\n\t\ttext-align: center;\r\n\t\tline-height: 300rpx;\r\n\r\n\t\t.iconfont {\r\n\t\t\tfont-size: 50rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.font-color {\r\n\t\t@include main_color(theme);\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promotion.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promotion.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294179533\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/components/promotion.vue'\ncreatePage(Page)"], "sourceRoot": ""}