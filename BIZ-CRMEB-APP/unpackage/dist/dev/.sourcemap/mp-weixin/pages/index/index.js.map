{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/index.vue?c3bf", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/index.vue?e8bb", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/index.vue?6871", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/index.vue?d264", "uni-app:///pages/index/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/a_seckill.vue?5b2d", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/a_seckill.vue?6ff2", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/a_seckill.vue?413d", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/a_seckill.vue?4e9f", "uni-app:///pages/index/components/a_seckill.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/a_seckill.vue?d08d", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/a_seckill.vue?ef4e", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/b_combination.vue?002b", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/b_combination.vue?401b", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/b_combination.vue?ae87", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/b_combination.vue?b862", "uni-app:///pages/index/components/b_combination.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/b_combination.vue?b12d", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/b_combination.vue?7659", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/promotion.vue?9f1d", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/promotion.vue?b671", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/promotion.vue?1140", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/promotion.vue?3547", "uni-app:///pages/index/components/promotion.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/promotion.vue?a747", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/promotion.vue?3a24", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/goodsRank.vue?4972", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/goodsRank.vue?2f70", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/goodsRank.vue?151c", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/goodsRank.vue?ea83", "uni-app:///pages/index/components/goodsRank.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/goodsRank.vue?ca66", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/components/goodsRank.vue?f724", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/index.vue?8c05", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/index.vue?2b63", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/index.vue?b31b", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/index/index.vue?52c0"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "computed", "components", "goodsRank", "promotion", "Loading", "aTip", "atModel", "data", "showSkeleton", "isNodes", "loaded", "loading", "isAuto", "isShowAuth", "statusBarHeight", "navIndex", "navTop", "followUrl", "followHid", "followCode", "logoUrl", "imgUrls", "url", "pic", "id", "name", "menus", "indicatorDots", "circular", "autoplay", "interval", "duration", "window", "navH", "ProductNavindex", "marTop", "sortProduct", "hostProduct", "hotPage", "hotLimit", "hotScroll", "explosiveMoney", "title", "searchH", "goodType", "goodScroll", "params", "page", "limit", "tempArr", "roll", "site_name", "iSshowH", "config<PERSON>pi", "privacyStatus", "tabsScrollLeft", "scrollLeft", "listActive", "theme", "imgHost", "picBg", "appUpdate", "wxText", "locationContent", "cardShow", "locationStatus", "watch", "onLaunch", "mounted", "onLoad", "uni", "setTimeout", "document", "dom", "e", "active", "success", "that", "app", "self", "info", "onShow", "methods", "menusTap", "animationType", "animationDuration", "toNewsList", "bindEdit", "reloadData", "scroll", "setTabList", "scrollIntoView", "lineLeft", "getElementData", "callback", "xieyiApp", "getTemlIds", "getTem", "type", "onColse", "get_host_product", "getIndexConfig", "value", "categoryConfig", "isShowCategory", "<PERSON><PERSON>", "consumer_hotline", "telephone_service_switch", "appVersionConfig", "plus", "content", "showCancel", "cancelColor", "confirmColor", "android<PERSON><PERSON><PERSON>", "iosAddress", "shareApi", "setOpenShare", "desc", "link", "imgUrl", "configAppMessage", "auth<PERSON><PERSON><PERSON>", "ProductNavTab", "goDetail", "<PERSON><PERSON><PERSON><PERSON>", "getGroomList", "stopTouchMove", "modelCancel", "confirmModel", "altitude", "geocode", "imageUrl", "path", "countDown", "bgColor", "spikeList", "image", "price", "otPrice", "point", "datatime", "status", "created", "getSeckillIndexTime", "toSeckillList", "combinationList", "isBorader", "assistUserList", "assistUserCount", "getCombinationList", "toCombinationList", "props", "tabData", "default", "showType", "promotionGood", "productslist", "resolve", "i", "goodsInfo", "gopage", "dataConfig", "productList", "toDetail", "getProductRank", "more"], "mappings": ";;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACa;AACyB;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACgInnB;AACA;AAGA;AAMA;AAOA;AACA;AACA;AAGA;AACA;AAEA;AAEA;AACA;AACA;AACA;AAKA;AACA;AACA;AAAA;AAnCA;AACA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAuCA;AAAA;EAEAC;EACAC;IACA;IACA;IACA;IACA;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA;MACAC;QAAAJ;QAAAC;QAAAC;QAAAC;MAAA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA;MACAE;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QAAAC;MAAA;QAAAA;MAAA;QAAAA;MAAA;QAAAA;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;QAAA;QACAC;QACAC;MACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAhC;MAAA;MACA;IACA;IACAuB;MAAA;MACA;IACA;EACA;EACAU;IACA;EAAA,CACA;EACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;IACA;MACAC;MACAA;QACAhD;MACA;MACA;IACA;IAEA;MACAiD;QACA;QACAC;UACAC;YACAC;YACAA;YACA;YACAD;YACAE;YACAA;UACA;QACA;MACA;IACA;IACAJ;MACA;IACA;IACA;;IAaA;IACAD;MACAM;QACAC;MACA;IACA;IAEA;IACA;IACA;MACAC;MACAC;MACAA;IACA;;IAEA;IACA;IACA;IACAC;MACAD;MACAA;IACA;IACA;MAAA;MACA;MACA;MACAD;IACA;IACA;;IAKA;IAEA;IAEA;MACA;IACA;EAcA;EACAG;IACA;IACA;IAUAX;EACA;EACAY;IACAC;MACA;QACA;UACAb;YACAhD;UACA;QACA;UACAgD;YACAc;YACAC;YACA/D;UACA;QACA;MACA;IACA;IACAgE;MACAhB;QACAc;QACAC;QACA/D;MACA;IACA;IACAiE;MACA;QACAvD;UACAP;QACA,GACA,IACA;QACA;MACA;IACA;IACA+D;MAAA;MACAjB;QACA;MACA;IACA;IACA;IACAkB;MACA;IACA;IACAC;MAAA;MACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MAAA;MACA;MACA;QACA;QACA;UACA;UACAC;UACA;QACA;MACA;IACA;IACAC;MACAvB;QACAwB;MACA;IACA;IACAC;MACAzB;QACAhD;MACA;IACA;IAoBA0E;MACA;QACA;MACA;IACA;IACAC;MACA;QACAC;MACA;QACA;UACA;YACA;UACA;UACAvG;QACA;MACA;IACA;IAEA;IACAwG;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACAvB;MAEA;MACA,0BACAA,cACAA,cACA;QACAA;QACAA;QACAA;MACA;IACA;IAEA;IACAwB;MAAA;MACA;MACA;MACA;QACAxB;QACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;QACAA;UACApD;UACA6E;YACAC;YACAC;UACA;QACA;QAKAC;UACAhF;UACA6E;YACAI;YACAC;UACA;QACA;QACA9B;QACAA;QACA;QACA;QACA;QACA;QACA;;QAEA;UACA;QACA;MAGA;IACA;IACA+B;MACA;MACA;MACA;MACA;QACA/B;QACAA;QACAA;QACAA;QACAgC;UACA;UACA;UACAvC;YACAM;cACA;gBACAN;kBACA5B;kBACAoE;kBACAC,0CACA;kBACAC;kBACAC;kBACArC;oBACA;sBACA;wBACA;0BACAiC,0BACAhD,UACAqD;0BACA;wBACA;0BACAL,+BACAhC,eACAsC;0BACA;sBAAA;oBAGA;kBACA;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA9C;UACA5B;QACA;MAIA;IACA;IACA;IACA2E;MACA;MACA;QACA;UACAC;UACA5E;UACA6E;UACAC;QACA;QACA3C,mFACA4C;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;QACAC;MACA;QACA;UACAvD;YACAc;YACAC;YACA/D;UACA;QACA;MACA;IACA;IACA;IACAwG;MAAA;MACA;MACA;MACA;MACA;QACA;MACA;MACA,iEAEA;QAAA,IADAvH;QAEA;QACA;QACA;QACA;QACA;MACA;IACA;IACAwH;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA3D;QACA4B;QACAgC;QACAC;QACAvD;UACA;YACAN;YACAA;UACA;QACA;MACA;MACA;IACA;EACA;AAAA,sFACA;EACA;AAQA,8EAKA;EACA;IACA5B;IACA0F;IACAd;IACAe;EACA;AACA,mGAGA;EACA;IACA;IACA;MACA;IACA;EACA;IACA;IACA;MACA;IAAA,CACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACltBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAmmB,CAAgB,6nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACwCvnB;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAJA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAKA;EACA5G;EACAxB;IACAqI;EACA;EACA/H;IACA;MACAgI;QACA;QACA;QACA;QACA;QACA;MACA;MACAC;QAAAC;QAAA/F;QAAAgG;QAAAC;MAAA;QAAAF;QAAA/F;QAAAgG;QAAAC;MAAA;QAAAF;QAAA/F;QAAAgG;QAAAC;MAAA;MAAA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAxE;MACAM;IACA;IACA;EACA;EACAK;IACA8D;MAAA;MACA;MACA;QAAA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACApB;MACAtD;QACAc;QACAC;QACA/D;MACA;IACA;IACA2H;MACA3E;QACAc;QACAC;QACA/D;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;;AChGA;AAAA;AAAA;AAAA;AAAsqC,CAAgB,4oCAAG,EAAC,C;;;;;;;;;;ACA1rC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AC/BA;AAAA;AAAA;AAAA;AAAumB,CAAgB,ioBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC2C3nB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAFA;AAAA,eAGA;EACAG;EACAlB;IACA;MACA2I;MACAC;MACAC;MACAC;IACA;EACA;EACAN;IACA;EACA;EACA3E;EACAc;IACA;IACAoE;MACA;MACA;QACAzE;QACAA;QACAA;MACA;QACA;UACAnC;QACA;MACA;IACA;IACAkF;MACAtD;QACAc;QACAC;QACA/D;MACA;IACA;IACAiI;MACAjF;QACAc;QACAC;QACA/D;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACxFA;AAAA;AAAA;AAAA;AAA0qC,CAAgB,gpCAAG,EAAC,C;;;;;;;;;;ACA9rC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmmB,CAAgB,6nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC8CvnB;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AAAA,eACA;EACAG;EACA+H;IACAC;MACAvD;MACAwD;IACA;IACAC;MACAzD;MACAwD;IACA;EACA;EACAzJ;IACA2J;EACA;EACAb;IAAA;IACAxE;MACA;IACA;EACA;EACAhE;IACA;MACA0C;MACAH;QACAC;QACAC;MACA;IACA;EACA;EACAkC;IACA;IACA2E;MAAA;MACA;MACA;QACA;UAAA;UACAC;QACA;MACA;IACA;IACAtE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAuE;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBACA;cAAA;gBAFAD;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAIA;IACAE;MACA3F;QACAc;QACAC;QACA/D;MACA;IACA;IACAsG;MACAtD;QACAc;QACAC;QACA/D;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC9GA;AAAA;AAAA;AAAA;AAA8oC,CAAgB,onCAAG,EAAC,C;;;;;;;;;;ACAlqC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AC5BA;AAAA;AAAA;AAAA;AAAmmB,CAAgB,6nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC0CvnB;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAHA;AAAA,gBAIA;EACAG;EACA+H;IACAU;MACAhE;MACAwD;IACA;EACA;EACAnJ;IACA;MACAmD;MACAyG;IACA;EACA;EACApB;IACA;EACA;EACA7D;IACAkF;MACA9F;QACAc;QACAC;QACA/D;MACA;IACA;IACA+I;MAAA;MACA;QACA;MACA;IACA;IACAC;MACA;QACApE;QACAzE;MACA;MACA6C;QACAc;QACAC;QACA/D;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACvFA;AAAA;AAAA;AAAA;AAA8oC,CAAgB,onCAAG,EAAC,C;;;;;;;;;;ACAlqC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAi3B,CAAgB,k3BAAG,EAAC,C;;;;;;;;;;ACAr4B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./index.vue?vue&type=style&index=1&id=57280228&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57280228\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=57280228&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.navIndex == 0 && _vm.cardShow == 1 ? _vm.tempArr.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :data-theme=\"theme\">\r\n\t\t<skeleton :show=\"showSkeleton\" :isNodes=\"isNodes\" ref=\"skeleton\" loading=\"chiaroscuro\" selector=\"skeleton\"\r\n\t\t\tbgcolor=\"#FFF\"></skeleton>\r\n\t\t<view class=\"page-index skeleton\" :class=\"{'bgf':navIndex >0}\" :style=\"{visibility: showSkeleton ? 'hidden' : 'visible'}\">\r\n\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t<view class=\"header\">\r\n\t\t\t\t<view class=\"serch-wrapper flex\">\r\n\t\t\t\t\t<view class=\"logo skeleton-rect\">\r\n\t\t\t\t\t\t<image :src=\"logoUrl\" mode=\"\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<navigator url=\"/pages/goods_search/index\" class=\"input\" hover-class=\"none\"><text\r\n\t\t\t\t\t\t\tclass=\"iconfont icon-xiazai5\"></text>\r\n\t\t\t\t\t\t搜索商品</navigator>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<!-- #ifdef MP || APP-PLUS -->\r\n\t\t\t<view class=\"mp-header\">\r\n\t\t\t\t<view class=\"sys-head skeleton-rect\" :style=\"{ height: statusBarHeight }\"></view>\r\n\t\t\t\t<view class=\"serch-box skeleton-rect\" style=\"height: 40px;\">\r\n\t\t\t\t\t<view class=\"serch-wrapper flex\">\r\n\t\t\t\t\t\t<view class=\"logo\">\r\n\t\t\t\t\t\t\t<image :src=\"logoUrl\" mode=\"\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<navigator url=\"/pages/goods_search/index\" class=\"input\" hover-class=\"none\"><text\r\n\t\t\t\t\t\t\t\tclass=\"iconfont icon-xiazai5\"></text>\r\n\t\t\t\t\t\t\t搜索商品</navigator>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<!-- 首页展示 -->\r\n\t\t\t<view class=\"page_content skeleton\" :style=\"'margin-top:'+(marTop)+'px;'\" v-if=\"navIndex == 0\">\r\n\t\t\t\t<view class=\"mp-bg\"></view>\r\n\t\t\t\t<view id=\"pageIndex\">\r\n\t\t\t\t\t<!-- banner -->\r\n\t\t\t\t\t<view class=\"swiper skeleton-rect\" @click.native=\"bindEdit('indexBanner')\">\r\n\t\t\t\t\t\t<swiper indicator-dots=\"true\" :autoplay=\"true\" :circular=\"circular\" :interval=\"interval\"\r\n\t\t\t\t\t\t\t:duration=\"duration\" indicator-color=\"rgba(255,255,255,0.6)\" indicator-active-color=\"#fff\">\r\n\t\t\t\t\t\t\t<block v-for=\"(item,index) in imgUrls\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<swiper-item>\r\n\t\t\t\t\t\t\t\t\t<view @click=\"menusTap(item.url)\">\r\n\t\t\t\t\t\t\t\t\t\t<image :src=\"item.pic\" class=\"slide-image\" lazy-load></image>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</swiper>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='nav acea-row' @click.native=\"bindEdit('indexMenu')\">\r\n\t\t\t\t\t\t<block v-for=\"(item,index) in menus\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class='item' @click=\"menusTap(item.url)\">\r\n\t\t\t\t\t\t\t\t<view class='pictrue'>\r\n\t\t\t\t\t\t\t\t\t<image :src='item.pic' class=\"skeleton-radius\"></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"menu-txt skeleton-rect\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 优惠券 -->\r\n\t\t\t\t<coupons class=\"skeleton skeleton-rect\"></coupons>\r\n\t\t\t\t<!-- 秒杀-->\r\n\t\t\t\t<seckill class=\"skeleton skeleton-rect\"></seckill>\r\n\t\t\t\t<!-- 拼团-->\r\n\t\t\t\t<combination class=\"skeleton skeleton-rect\"></combination>\r\n\t\t\t\t<!-- 砍价-->\r\n\t\t\t\t<bargain class=\"skeleton-rect\"></bargain>\r\n\t\t\t\t<!-- 排行榜 -->\r\n\t\t\t\t<goods-rank class=\"skeleton skeleton-rect\"></goods-rank>\r\n\t\t\t\t<!-- 商品列表模板选择性显示 -->\r\n\t\t\t\t<promotion :tabData=\"explosiveMoney\" :showType=\"cardShow\" v-if=\"cardShow !== 1\"></promotion>\r\n\r\n\t\t\t\t<!-- 首发新品 -->\r\n\t\t\t\t<view class=\"index-product-wrapper\" :class=\"iSshowH?'on':''\" v-if=\"cardShow == 1\">\r\n\t\t\t\t\t<view class=\"list-box animated\" :class='tempArr.length > 0?\"fadeIn on\":\"\"'>\r\n\t\t\t\t\t\t<view class=\"item\" v-for=\"(item,index) in tempArr\" :key=\"index\" @click=\"goDetail(item)\">\r\n\t\t\t\t\t\t\t<view class=\"pictrue\">\r\n\t\t\t\t\t\t\t\t<span class=\"pictrue_log pictrue_log_class\"\r\n\t\t\t\t\t\t\t\t\tv-if=\"item.activityH5 && item.activityH5.type === '1'\">秒杀</span>\r\n\t\t\t\t\t\t\t\t<span class=\"pictrue_log pictrue_log_class\"\r\n\t\t\t\t\t\t\t\t\tv-if=\"item.activityH5 && item.activityH5.type === '2'\">砍价</span>\r\n\t\t\t\t\t\t\t\t<span class=\"pictrue_log pictrue_log_class\"\r\n\t\t\t\t\t\t\t\t\tv-if=\"item.activityH5 && item.activityH5.type === '3'\">拼团</span>\r\n\t\t\t\t\t\t\t\t<image :src=\"item.image\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"text-info\">\r\n\t\t\t\t\t\t\t\t<view class=\"title line1\">{{item.storeName}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"old-price\"><text>¥{{item.otPrice}}</text></view>\r\n\t\t\t\t\t\t\t\t<view class=\"price\" v-if=\"item.vipPrice  && item.vipPrice > 0\" ><text>￥</text>{{item.vipPrice}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"price\" v-else ><text>￥</text>{{item.price}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='loadingicon acea-row row-center-wrapper' v-if=\"goodScroll\">\r\n\t\t\t\t\t\t<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"mores-txt flex\" v-if=\"!goodScroll\">\r\n\t\t\t\t\t\t<text>我是有底线的</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- #ifdef MP -->\r\n\t\t\t<aTip :isCustom=\"true\" :text=\"wxText\" :borderR=\"5\"></aTip>\r\n\t\t\t<atModel v-if=\"locationStatus\" :locationType=\"true\" @closeModel=\"modelCancel\" @confirmModel=\"confirmModel\" :content=\"locationContent\"></atModel>\r\n\t\t\t<!-- #endif -->\r\n\t\t</view>\r\n\t\t<!-- #ifdef APP-PLUS -->\r\n\t\t<view class=\"privacy-wrapper\" v-if=\"privacyStatus\">\r\n\t\t\t<view class=\"privacy-box\">\r\n\t\t\t\t<view class=\"title\">服务协议与隐私政策</view>\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t尊敬的用户，欢迎您注册成为本应用用户。请您仔细阅读<i\r\n\t\t\t\t\t\t@click=\"xieyiApp\">《服务协议与隐私政策》</i>了解我们对您使用我们APP制定的规则，您个人信息的处理以及申请权限的目的和使用范围。\r\n\t\t\t\t\t经您确认后，本用户协议和隐私权政策即在您和本应用之间产生法律效力。请您务必在注册之前认真阅读全部服务协议内容，如有任何疑问，可向本应用客服咨询。\r\n\t\t\t\t\t如你同意，请点击“我同意”开始接受我们的服务。\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"btn-box\">\r\n\t\t\t\t\t<view class=\"btn-item\" @click=\"confirmApp\">我同意</view>\r\n\t\t\t\t\t<view class=\"btn\" @click=\"closeModel\">随便逛逛</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- #endif -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport Auth from '@/libs/wechat';\r\n\timport Cache from '../../utils/cache';\r\n\tvar statusBarHeight = uni.getSystemInfoSync().statusBarHeight + 'px';\r\n\tlet app = getApp();\r\n\timport {\r\n\t\tgetIndexData,\r\n\t\tsetCouponReceive,\r\n\t\tgetTheme,\r\n\t\tgetAppVersion,\r\n\t} from '@/api/api.js';\r\n\timport { spread } from \"@/api/user\";\r\n\t// #ifdef MP-WEIXIN || APP-PLUS\r\n\timport { getTemlIds } from '@/api/api.js';\r\n\t// #endif\r\n\t// #ifdef H5  \r\n\timport { follow } from '@/api/public.js';\r\n\t// #endif\r\n\timport { getShare } from '@/api/public.js';\r\n\timport a_seckill from './components/a_seckill';\r\n\timport b_combination from './components/b_combination';\r\n\timport c_bargain from './components/c_bargain';\r\n\timport d_coupons from './components/d_coupons';\r\n\timport promotion from './components/promotion';\r\n\timport goodsRank from './components/goodsRank';\r\n\timport atModel from '@/components/accredit/index.vue'\r\n\timport ClipboardJS from \"@/plugin/clipboard/clipboard.js\";\r\n\timport aTip from '@/components/add-tips/index.vue';\r\n\timport { goShopDetail } from '@/libs/order.js'\r\n\timport { goPage } from '@/libs/iframe.js'\r\n\timport { mapGetters } from \"vuex\";\r\n\timport {\r\n\t\tgetCategoryList,\r\n\t\tgetProductHot,\r\n\t\tgetGroomList\r\n\t} from '@/api/store.js';\r\n\timport { silenceBindingSpread, getCityList } from '@/utils';\r\n\timport animationType from '@/utils/animationType.js' \r\n\timport {checkOverdue} from '@/utils/checkOverdue.js'\r\n\t// #ifndef MP\r\n\timport { getWechatConfig } from \"@/api/public\";\r\n\t// #endif\r\n\timport Loading from '@/components/Loading/index.vue';\r\n\tconst arrTemp = [\"beforePay\", \"afterPay\",\"createBargain\", \"pink\"];\r\n\texport default {\r\n\t\tcomputed: mapGetters(['isLogin', 'uid']),\r\n\t\tcomponents: {\r\n\t\t\t'seckill':a_seckill,\r\n\t\t\t'combination':b_combination,\r\n\t\t\t'bargain':c_bargain,\r\n\t\t\t'coupons':d_coupons,\r\n\t\t\tgoodsRank,\r\n\t\t\tpromotion,\r\n\t\t\tLoading,\r\n\t\t\taTip,\r\n\t\t\tatModel\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tshowSkeleton: true, //骨架屏显示隐藏\r\n\t\t\t\tisNodes: 0, //控制什么时候开始抓取元素节点,只要数值改变就重新抓取\r\n\t\t\t\tloaded: false,\r\n\t\t\t\tloading: false,\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false, //是否隐藏授权\r\n\t\t\t\tstatusBarHeight: statusBarHeight,\r\n\t\t\t\tnavIndex: 0,\r\n\t\t\t\tnavTop: [],\r\n\t\t\t\tfollowUrl: \"\",\r\n\t\t\t\tfollowHid: true,\r\n\t\t\t\tfollowCode: false,\r\n\t\t\t\tlogoUrl: \"\",\r\n\t\t\t\timgUrls: [{url: '',pic: '',id: '',name: ''}],\r\n\t\t\t\tmenus: [{url: '',pic: '',id: '',name: ''},{url: '',pic: '',id: '',name: ''},{url: '',pic: '',id: '',name: ''},{url: '',pic: '',id: '',name: ''},{url: '',pic: '',id: '',name: ''},{url: '',pic: '',id: '',name: ''},{url: '',pic: '',id: '',name: ''},{url: '',pic: '',id: '',name: ''},{url: '',pic: '',id: '',name: ''},{url: '',pic: '',id: '',name: ''}, ],\r\n\t\t\t\tindicatorDots: false,\r\n\t\t\t\tcircular: true,\r\n\t\t\t\tautoplay: true,\r\n\t\t\t\tinterval: 2500,\r\n\t\t\t\tduration: 500,\r\n\t\t\t\twindow: false,\r\n\t\t\t\tnavH: \"\",\r\n\t\t\t\tProductNavindex: 0,\r\n\t\t\t\tmarTop: 0,\r\n\t\t\t\tsortProduct: [],\r\n\t\t\t\thostProduct: [],\r\n\t\t\t\thotPage: 1,\r\n\t\t\t\thotLimit: 10,\r\n\t\t\t\thotScroll: false,\r\n\t\t\t\texplosiveMoney: [{title: ''}, {title: ''}, {title: ''}, {title: ''}],\r\n\t\t\t\tsearchH: 0,\r\n\t\t\t\tgoodType: 0, //精品推荐Type\r\n\t\t\t\tgoodScroll: true, //精品推荐开关\r\n\t\t\t\tparams: { //精品推荐分页\r\n\t\t\t\t\tpage: 1,\r\n\t\t\t\t\tlimit: 10,\r\n\t\t\t\t},\r\n\t\t\t\ttempArr: [], //精品推荐临时数组\r\n\t\t\t\troll: [], // 新闻简报\r\n\t\t\t\tsite_name: '', //首页title\r\n\t\t\t\tiSshowH: false,\r\n\t\t\t\tconfigApi: {}, //分享类容配置\r\n\t\t\t\tprivacyStatus: false, // 隐私政策是否同意过\r\n\t\t\t\ttabsScrollLeft: 0, // tabs当前偏移量\r\n\t\t\t\tscrollLeft: 0,\r\n\t\t\t\tlistActive: 0, // 当前选中项\r\n\t\t\t\t// duration: 2 // 下划线动画时长\r\n\t\t\t\ttheme: 'theme1',\r\n\t\t\t\timgHost: '',\r\n\t\t\t\tpicBg: 'crmebimage/change/new_header/new_header1.png',\r\n\t\t\t\tappUpdate: {},\r\n\t\t\t\twxText: \"点击添加到我的小程序，微信首页下拉即可访问商城。\",\r\n\t\t\t\tlocationContent:'授权位置信息，提供完整服务',\r\n\t\t\t\tcardShow:1,\r\n\t\t\t\tlocationStatus:false\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tProductNavindex(newVal) { // 监听当前选中项\r\n\t\t\t\tthis.setTabList()\r\n\t\t\t},\r\n\t\t\tlistActive(newVal) { // 监听当前选中项\r\n\t\t\t\tthis.setTabList()\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLaunch: function() {\r\n\t\t\t//this.isNodes++;\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.setTabList()\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\t// 首次进入默认跳转到分类页面\r\n\t\t\tconst hasVisited = uni.getStorageSync('hasVisited');\r\n\t\t\tif (!hasVisited) {\r\n\t\t\t\tuni.setStorageSync('hasVisited', true);\r\n\t\t\t\tuni.switchTab({\r\n\t\t\t\t\turl: '/pages/goods_cate/goods_cate'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (app.globalData.isIframe) {\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tlet active;\r\n\t\t\t\t\tdocument.getElementById('pageIndex').children.forEach(dom => {\r\n\t\t\t\t\t\tdom.addEventListener('click', (e) => {\r\n\t\t\t\t\t\t\te.stopPropagation();\r\n\t\t\t\t\t\t\te.preventDefault();\r\n\t\t\t\t\t\t\tif (dom === active) return;\r\n\t\t\t\t\t\t\tdom.classList.add('borderShow');\r\n\t\t\t\t\t\t\tactive && active.classList.remove('borderShow');\r\n\t\t\t\t\t\t\tactive = dom;\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.isNodes++;\r\n\t\t\t}, 100);\r\n\t\t\tvar that = this;\r\n\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\t// 自定义app隐私协议弹窗\r\n\t\t\t// try {\r\n\t\t\t// \tlet val = uni.getStorageSync('privacyStatus')\r\n\t\t\t// \tif (val) {\r\n\t\t\t// \t} else {\r\n\t\t\t// \t\tthat.privacyStatus = true\r\n\t\t\t// \t}\r\n\t\t\t// } catch (e) {}\r\n\t\t\tthis.appVersionConfig(); //APP版本检测\r\n\t\t\t// #endif\r\n\t\t\t// 获取系统信息\r\n\t\t\tuni.getSystemInfo({\r\n\t\t\t\tsuccess(res) {\r\n\t\t\t\t\tthat.$store.commit(\"SYSTEM_PLATFORM\", res.platform);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\tlet self = this\r\n\t\t\t//根据换色配置动态获取‘新闻简报’图片\r\n\t\t\tgetTheme().then(resP => {\r\n\t\t\t\tapp.globalData.theme = `theme${Number(resP.data.value)}`\r\n\t\t\t\tself.theme = `theme${Number(resP.data.value)}`;\r\n\t\t\t\tself.picBg = `crmebimage/change/new_header/new_header${Number(resP.data.value)}.png`;\r\n\t\t\t})\r\n\t\t\t// #ifdef MP\r\n\t\t\t// 获取小程序头部高度\r\n\t\t\tthis.navH = app.globalData.navHeight;\r\n\t\t\tlet info = uni.createSelectorQuery().select(\".mp-header\");\r\n\t\t\tinfo.boundingClientRect(function(data) {\r\n\t\t\t\tself.marTop = data.height\r\n\t\t\t\tself.poTop = Number(data.height) + 84\r\n\t\t\t}).exec()\r\n\t\t\tif (options.scene) { // 仅仅小程序扫码进入\r\n\t\t\t    let qrCodeValue = this.$util.getUrlParams(decodeURIComponent(options.scene));\r\n\t\t\t    let mapeMpQrCodeValue = this.$util.formatMpQrCodeData(qrCodeValue);\r\n\t\t\t    app.globalData.spread = mapeMpQrCodeValue.spread;\r\n\t\t\t}\r\n\t\t\tif(options.spread) app.globalData.spread = options.spread; //非小程序扫码进入情况下，直接获取url中的分销员id保存在globalDta中\r\n\t\t\t// #endif\r\n\t\t\t// #ifndef MP || APP-PLUS\r\n\t\t\tthis.navH = 0;\r\n\t\t\t// #endif\r\n\t\t\tthis.getIndexConfig();\r\n\t\t\t// #ifdef MP || APP-PLUS\r\n\t\t\tthis.getTemlIds()\r\n\t\t\t// #endif\r\n\t\t\tif(this.isLogin && app.globalData.spread){\r\n\t\t\t\tsilenceBindingSpread()\r\n\t\t\t}\r\n\t\t\t// #ifdef H5 || APP-PLUS\r\n\t\t\tuni.getLocation({\r\n\t\t\t\ttype: 'gcj02',\r\n\t\t\t\taltitude: true,\r\n\t\t\t\tgeocode: true,\r\n\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tuni.setStorageSync('user_latitude', res.latitude);\r\n\t\t\t\t\t\tuni.setStorageSync('user_longitude', res.longitude);\r\n\t\t\t\t\t} catch {}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tlet self = this;\r\n\t\t\tif (!self.$Cache.getItem('cityList')) getCityList()\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\tlet barHeight = uni.getSystemInfoSync().statusBarHeight;\r\n\t\t\tself.marTop = barHeight + 40; //刘海屏\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tif (self.appUpdate.openUpgrade == 'true') {\r\n\t\t\t\t\tself.appVersionConfig();\r\n\t\t\t\t}\r\n\t\t\t}, 1000)\r\n\t\t\t// #endif\r\n\t\t\tuni.showTabBar();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tmenusTap(url) {\r\n\t\t\t\tgoPage().then(res => {\r\n\t\t\t\t\tif (url == '/pages/goods_cate/goods_cate') {\r\n\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\turl: url\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\tanimationType: animationType.type,\r\n\t\t\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\t\t\turl: url\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttoNewsList(){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\tanimationType: animationType.type,\r\n\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\turl:'/pages/news_list/index'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tbindEdit(name) {\r\n\t\t\t\tif (app.globalData.isIframe) {\r\n\t\t\t\t\twindow.parent.postMessage({\r\n\t\t\t\t\t\t\tname: name\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t'*'\r\n\t\t\t\t\t);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\treloadData() {\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.showSkeleton = false\r\n\t\t\t\t}, 1000)\r\n\t\t\t},\r\n\t\t\t// scroll-view滑动事件\r\n\t\t\tscroll(e) {\r\n\t\t\t\tthis.scrollLeft = e.detail.scrollLeft;\r\n\t\t\t},\r\n\t\t\tsetTabList() {\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.scrollIntoView()\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 计算tabs位置\r\n\t\t\tscrollIntoView() { // item滚动\r\n\t\t\t\tlet lineLeft = 0;\r\n\t\t\t\tthis.getElementData('#tab_list', (data) => {\r\n\t\t\t\t\tlet list = data[0]\r\n\t\t\t\t\tthis.getElementData(`#tab_item`, (data) => {\r\n\t\t\t\t\t\tlet el = data[this.listActive]\r\n\t\t\t\t\t\tlineLeft = el.width / 2 + (-list.left) + el.left - list.width / 2 - this.scrollLeft\r\n\t\t\t\t\t\tthis.tabsScrollLeft = this.scrollLeft + lineLeft\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetElementData(el, callback) {\r\n\t\t\t\tuni.createSelectorQuery().in(this).selectAll(el).boundingClientRect().exec((data) => {\r\n\t\t\t\t\tcallback(data[0]);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\txieyiApp() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/users/web_page/index?webUel=https://admin.java.crmeb.net/useragreement/xieyi.html&title=协议内容'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\txieyiApp() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\tanimationType: animationType.type,\r\n\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\turl: '/pages/users/web_page/index?webUel=https://admin.java.crmeb.net/useragreement/xieyi.html&title=协议内容'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 同意隐私协议\r\n\t\t\tconfirmApp() {\r\n\t\t\t\tuni.setStorageSync('privacyStatus', true)\r\n\t\t\t\tthis.privacyStatus = false\r\n\t\t\t},\r\n\t\t\t// 关闭Model\r\n\t\t\tcloseModel() {\r\n\t\t\t\tthis.privacyStatus = false\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef MP || APP-PLUS\r\n\t\t\tgetTemlIds() {\r\n\t\t\t\tfor (var i in arrTemp) {\r\n\t\t\t\t\tthis.getTem(arrTemp[i]);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetTem(data) {\r\n\t\t\t\tgetTemlIds({\r\n\t\t\t\t\ttype: data\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.data) {\r\n\t\t\t\t\t\tlet arr = res.data.map((item) => {\r\n\t\t\t\t\t\t\treturn item.tempId\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\twx.setStorageSync('tempID' + data, arr);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\t// 关闭优惠券弹窗\r\n\t\t\tonColse() {\r\n\t\t\t\tthis.$set(this, \"window\", false);\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 获取我的推荐\r\n\t\t\t */\r\n\t\t\tget_host_product: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthat.loading = true;\r\n\r\n\t\t\t\tif (that.hotScroll) return\r\n\t\t\t\tgetProductHot(\r\n\t\t\t\t\tthat.hotPage,\r\n\t\t\t\t\tthat.hotLimit,\r\n\t\t\t\t).then(res => {\r\n\t\t\t\t\tthat.hotPage++\r\n\t\t\t\t\tthat.hotScroll = res.data.list.length < that.hotLimit\r\n\t\t\t\t\tthat.hostProduct = that.hostProduct.concat(res.data.list)\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 首页数据\r\n\t\t\tgetIndexConfig: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\t//this.isNodes++;\r\n\t\t\t\tgetIndexData().then(res => {\r\n\t\t\t\t\tthat.$set(that, \"logoUrl\", res.data.logoUrl);\r\n\t\t\t\t\tlet imgHost = res.data.logoUrl.split('crmebimage')[0];\r\n\t\t\t\t\tthat.imgHost = imgHost;\r\n\t\t\t\t\tthat.$Cache.set('imgHost', imgHost);\r\n\t\t\t\t\tthat.$set(that, \"imgUrls\", res.data.banner);\r\n\t\t\t\t\tthat.$set(that, \"menus\", res.data.menus);\r\n\t\t\t\t\tthat.$set(that, \"roll\", res.data.roll ? res.data.roll : []);\r\n\t\t\t\t\tthat.$set(that,'cardShow',res.data.homePageSaleListStyle == '' ? 1 : Number(res.data.homePageSaleListStyle)); //首页商品列表模板获取配置\r\n\t\t\t\t\t// 保存商品分类页配置\r\n\t\t\t\t\tthat.$Cache.setItem({\r\n\t\t\t\t\t\tname: 'categoryConfig',\r\n\t\t\t\t\t\tvalue: {\r\n\t\t\t\t\t\t\tcategoryConfig: res.data.categoryPageConfig,\r\n\t\t\t\t\t\t\tisShowCategory: res.data.isShowCategory\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// #ifdef H5 || APP-PLUS\r\n\t\t\t\t\tthat.$store.commit(\"SET_CHATURL\", res.data.yzfUrl);\r\n\t\t\t\t\tCache.set('chatUrl', res.data.yzfUrl);\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tCache.setItem({\r\n\t\t\t\t\t\tname:'chatConfig',\r\n\t\t\t\t\t\tvalue:{\r\n\t\t\t\t\t\t\tconsumer_hotline:res.data.consumerHotline,\r\n\t\t\t\t\t\t\ttelephone_service_switch:res.data.telephoneServiceSwitch\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthat.$set(that, \"explosiveMoney\", res.data.explosiveMoney);\r\n\t\t\t\t\tthat.goodType = res.data.explosiveMoney[0].type\r\n\t\t\t\t\tthis.getGroomList();\r\n\t\t\t\t\tthis.shareApi();\r\n\t\t\t\t\tthis.reloadData();\r\n\t\t\t\t\t// 不在首页直接调用checkOverdue，避免重复登录提示\r\n\t\t\t\t\t// checkOverdue();\r\n\t\t\t\t\t// #ifdef MP\r\n\t\t\t\t\tif(!Cache.has('user_latitude')){\r\n\t\t\t\t\t\tthis.locationStatus = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tappVersionConfig() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\t//app升级\r\n\t\t\t\t// 获取本地应用资源版本号  \r\n\t\t\t\tgetAppVersion().then(res => {\r\n\t\t\t\t\tthat.$set(that.appUpdate, 'androidAddress', res.data.androidAddress);\r\n\t\t\t\t\tthat.$set(that.appUpdate, 'appVersion', res.data.appVersion);\r\n\t\t\t\t\tthat.$set(that.appUpdate, 'iosAddress', res.data.iosAddress);\r\n\t\t\t\t\tthat.$set(that.appUpdate, 'openUpgrade', res.data.openUpgrade);\r\n\t\t\t\t\tplus.runtime.getProperty(plus.runtime.appid, function(inf) {\r\n\t\t\t\t\t\tlet nowVersion = (inf.version).split('.').join('');\r\n\t\t\t\t\t\tlet appVersion = (res.data.appVersion).split('.').join('');\r\n\t\t\t\t\t\tuni.getSystemInfo({\r\n\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\tif (appVersion > nowVersion) {\r\n\t\t\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '更新提示',\r\n\t\t\t\t\t\t\t\t\t\tcontent: '发现新版本，是否前去下载?',\r\n\t\t\t\t\t\t\t\t\t\tshowCancel: that.appUpdate.openUpgrade ==\r\n\t\t\t\t\t\t\t\t\t\t\t'false' ? true : false,\r\n\t\t\t\t\t\t\t\t\t\tcancelColor: '#eeeeee',\r\n\t\t\t\t\t\t\t\t\t\tconfirmColor: '#FF0000',\r\n\t\t\t\t\t\t\t\t\t\tsuccess(response) {\r\n\t\t\t\t\t\t\t\t\t\t\tif (response.confirm) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tswitch (res.platform) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcase \"android\":\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tplus.runtime.openURL(that\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.appUpdate\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.androidAddress);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcase \"ios\":\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tplus.runtime.openURL(encodeURI(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tthat.appUpdate\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.iosAddress));\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tshareApi: function() {\r\n\t\t\t\tgetShare().then(res => {\r\n\t\t\t\t\tthis.$set(this, 'configApi', res.data);\r\n\t\t\t\t\tthis.$set(this, \"site_name\", res.data.title);\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: this.site_name\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\tthis.setOpenShare(res.data);\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 微信分享；\r\n\t\t\tsetOpenShare: function(data) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (that.$wechat.isWeixin()) {\r\n\t\t\t\t\tlet configAppMessage = {\r\n\t\t\t\t\t\tdesc: data.synopsis,\r\n\t\t\t\t\t\ttitle: data.title,\r\n\t\t\t\t\t\tlink: location.href,\r\n\t\t\t\t\t\timgUrl: data.img\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthat.$wechat.wechatEvevt([\"updateAppMessageShareData\", \"updateTimelineShareData\"],\r\n\t\t\t\t\t\tconfigAppMessage);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 授权关闭\r\n\t\t\tauthColse: function(e) {\r\n\t\t\t\tthis.isShowAuth = e\r\n\t\t\t},\r\n\t\t\t// 首发新品切换\r\n\t\t\tProductNavTab(item, index) {\r\n\t\t\t\tif (this.listActive !== index) {\r\n\t\t\t\t\tthis.listActive = index\r\n\t\t\t\t\tthis.goodType = item.type\r\n\t\t\t\t\tthis.listActive = index\r\n\t\t\t\t\tthis.ProductNavindex = index\r\n\t\t\t\t\tthis.tempArr = []\r\n\t\t\t\t\tthis.params.page = 1\r\n\t\t\t\t\tthis.goodScroll = true\r\n\t\t\t\t\tlet onloadH = true\r\n\t\t\t\t\tthis.getGroomList(onloadH)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 首发新品详情\r\n\t\t\tgoDetail(item) {\r\n\t\t\t\tif (item.activityH5 && item.activityH5.type === \"2\" && !this.isLogin) {\r\n\t\t\t\t\ttoLogin();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tgoShopDetail(item, this.uid).then(res => {\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\tanimationType: animationType.type,\r\n\t\t\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\t\t\turl: `/pages/goods_details/index?id=${item.id}` \r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 精品推荐\r\n\t\t\tgetGroomList(onloadH) {\r\n\t\t\t\tthis.loading = true\r\n\t\t\t\tlet type = this.goodType;\r\n\t\t\t\tif (!this.goodScroll) return\r\n\t\t\t\tif (onloadH) {\r\n\t\t\t\t\tthis.iSshowH = true\r\n\t\t\t\t}\r\n\t\t\t\tgetGroomList(type, this.params).then(({\r\n\t\t\t\t\tdata\r\n\t\t\t\t}) => {\r\n\t\t\t\t\tthis.iSshowH = false\r\n\t\t\t\t\tthis.loading = false\r\n\t\t\t\t\tthis.goodScroll = data.list.length >= this.params.limit\r\n\t\t\t\t\tthis.params.page++\r\n\t\t\t\t\tthis.tempArr = this.tempArr.concat(data.list)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tstopTouchMove() {\r\n\t\t\t\treturn true //禁止新闻swiper手动滑动\r\n\t\t\t},\r\n\t\t\tmodelCancel(){\r\n\t\t\t\tthis.locationStatus = false;\r\n\t\t\t},\r\n\t\t\tconfirmModel(){\r\n\t\t\t\tuni.getLocation({\r\n\t\t\t\t\ttype: 'gcj02',\r\n\t\t\t\t\taltitude: true,\r\n\t\t\t\t\tgeocode: true,\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\tuni.setStorageSync('user_latitude', res.latitude);\r\n\t\t\t\t\t\t\tuni.setStorageSync('user_longitude', res.longitude);\r\n\t\t\t\t\t\t} catch {}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\tthis.locationStatus = false;\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tlet self = this;\r\n\t\t\t// #ifdef H5 || APP-PLUS\r\n\t\t\t// 获取H5 搜索框高度\r\n\t\t\tlet appSearchH = uni.createSelectorQuery().select(\".serch-wrapper\");\r\n\t\t\tappSearchH.boundingClientRect(function(data) {\r\n\t\t\t\tself.searchH = data.height\r\n\t\t\t}).exec()\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\t/**\r\n\t\t * 用户点击右上角分享\r\n\t\t */\r\n\t\t// #ifdef MP\r\n\t\tonShareAppMessage: function() {\r\n\t\t\treturn {\r\n\t\t\t\ttitle: this.configApi.title,\r\n\t\t\t\timageUrl: this.configApi.img,\r\n\t\t\t\tdesc: this.configApi.synopsis,\r\n\t\t\t\tpath: '/pages/index/index'\r\n\t\t\t};\r\n\t\t},\r\n\t\t// #endif\r\n\t\t// 滚动到底部\r\n\t\tonReachBottom() {\r\n\t\t\tif (this.navIndex == 0) {\r\n\t\t\t\t// 首页加载更多\r\n\t\t\t\tif (this.params.page != 1) {\r\n\t\t\t\t\tthis.getGroomList();\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\t// 分类栏目加载更多\r\n\t\t\t\tif (this.sortProduct.length > 0) {\r\n\t\t\t\t\t//this.get_product_list();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.get_host_product();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t}\r\n</script>\r\n<style>\r\n\tpage {\r\n\t\theight: auto;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\theight: 100%;\r\n\t\t/* #ifdef H5 */\r\n\t\tbackground-color: #fff;\r\n\t\t/* #endif */\r\n\r\n\t}\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n\t.notice {\r\n\t\twidth: 100%;\r\n\t\theight: 70rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tmargin-bottom: 25rpx;\r\n\t\tline-height: 70rpx;\r\n\t\tpadding: 0 14rpx;\r\n\r\n\t\t.line {\r\n\t\t\tcolor: #CCCCCC;\r\n\t\t}\r\n\r\n\t\t.pic {\r\n\t\t\twidth: 130rpx;\r\n\t\t\theight: 36rpx;\r\n\t\t\tbackground-size: 100%;\r\n\t\t\t// @include index_new_img(theme);\r\n\r\n\t\t\timage {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tdisplay: block !important;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.swipers {\r\n\t\t\theight: 100%;\r\n\t\t\twidth: 444rpx;\r\n\t\t\toverflow: hidden;\r\n\r\n\t\t\tswiper {\r\n\t\t\t\theight: 100%;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\tcolor: #333333;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.iconfont {\r\n\t\t\tcolor: #999999;\r\n\t\t\tfont-size: 20rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.sticky-box {\r\n\t\t/* #ifndef APP-PLUS-NVUE */\r\n\t\tdisplay: flex;\r\n\t\tposition: -webkit-sticky;\r\n\t\t/* #endif */\r\n\t\tposition: sticky;\r\n\t\t/* #ifdef H5*/\r\n\t\ttop: var(--window-top);\r\n\t\t/* #endif */\r\n\r\n\t\tz-index: 99;\r\n\t\tflex-direction: row;\r\n\t\tmargin: 0px;\r\n\t\tbackground: #f5f5f5;\r\n\t\tpadding: 30rpx 0;\r\n\t\t/* #ifdef MP || APP-PLUS*/\r\n\t\t//top: 110rpx;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.tab {\r\n\t\tposition: relative;\r\n\t\tdisplay: flex;\r\n\t\tfont-size: 28rpx;\r\n\t\twhite-space: nowrap;\r\n\r\n\t\t&__item {\r\n\t\t\tflex: 1;\r\n\t\t\tpadding: 0 20rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\theight: 60rpx;\r\n\t\t\tline-height: 60rpx;\r\n\t\t\tcolor: #666;\r\n\r\n\t\t\t&.active {\r\n\t\t\t\tcolor: #09C2C9;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.scroll-view_H {\r\n\t\twhite-space: nowrap;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.privacy-wrapper {\r\n\t\tz-index: 999;\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground: #7F7F7F;\r\n\r\n\t\t.privacy-box {\r\n\t\t\tposition: absolute;\r\n\t\t\tleft: 50%;\r\n\t\t\ttop: 50%;\r\n\t\t\ttransform: translate(-50%, -50%);\r\n\t\t\twidth: 560rpx;\r\n\t\t\tpadding: 50rpx 45rpx 0;\r\n\t\t\tbackground: #fff;\r\n\t\t\tborder-radius: 20rpx;\r\n\r\n\t\t\t.title {\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tfont-weight: 700;\r\n\t\t\t}\r\n\r\n\t\t\t.content {\r\n\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\tline-height: 1.5;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #666;\r\n\t\t\t\ttext-indent: 54rpx;\r\n\r\n\t\t\t\ti {\r\n\t\t\t\t\tfont-style: normal;\r\n\t\t\t\t\tcolor: $theme-color;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.btn-box {\r\n\t\t\t\tmargin-top: 40rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 30rpx;\r\n\r\n\t\t\t\t.btn-item {\r\n\t\t\t\t\theight: 82rpx;\r\n\t\t\t\t\tline-height: 82rpx;\r\n\t\t\t\t\tbackground: linear-gradient(90deg, #F67A38 0%, #F11B09 100%);\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\tborder-radius: 41rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.btn {\r\n\t\t\t\t\tpadding: 30rpx 0;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.page-index {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tmin-height: 100%;\r\n\t\tbackground: linear-gradient(180deg, #fff 0%, #f5f5f5 100%);\r\n\r\n\t\t.header {\r\n\t\t\twidth: 100%;\r\n\t\t\t@include main_bg_color(theme) padding: 28rpx 30rpx;\r\n\r\n\t\t\t.serch-wrapper {\r\n\t\t\t\talign-items: center;\r\n\r\n\r\n\t\t\t\t.logo {\r\n\t\t\t\t\twidth: 118rpx;\r\n\t\t\t\t\theight: 42rpx;\r\n\t\t\t\t\tmargin-right: 24rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 118rpx;\r\n\t\t\t\t\theight: 42rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.input {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\twidth: 546rpx;\r\n\t\t\t\t\theight: 58rpx;\r\n\t\t\t\t\tpadding: 0 0 0 30rpx;\r\n\t\t\t\t\tbackground: rgba(247, 247, 247, 1);\r\n\t\t\t\t\tborder: 1px solid rgba(241, 241, 241, 1);\r\n\t\t\t\t\tborder-radius: 29rpx;\r\n\t\t\t\t\tcolor: #BBBBBB;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\r\n\t\t\t\t\t.iconfont {\r\n\t\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tcolor: #666666;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.tabNav {\r\n\t\t\t\tpadding-top: 24rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t/* #ifdef MP || APP-PLUS */\r\n\t\t.mp-header {\r\n\t\t\tz-index: 999;\r\n\t\t\tposition: fixed;\r\n\t\t\tleft: 0;\r\n\t\t\ttop: 0;\r\n\t\t\twidth: 100%;\r\n\t\t\t/* #ifdef H5 */\r\n\t\t\tpadding-bottom: 20rpx;\r\n\t\t\t/* #endif */\r\n\t\t\t@include main_bg_color(theme);\r\n\r\n\t\t\t.serch-wrapper {\r\n\t\t\t\theight: 100%;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tpadding: 0 50rpx 0 53rpx;\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 118rpx;\r\n\t\t\t\t\theight: 42rpx;\r\n\t\t\t\t\tmargin-right: 30rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.input {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t/* #ifdef MP */\r\n\t\t\t\t\twidth: 305rpx;\r\n\t\t\t\t\t/* #endif */\r\n\t\t\t\t\t/* #ifdef APP-PLUS */\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t/* #endif */\r\n\t\t\t\t\theight: 50rpx;\r\n\t\t\t\t\tpadding: 0 0 0 30rpx;\r\n\t\t\t\t\tbackground: rgba(247, 247, 247, 1);\r\n\t\t\t\t\tborder: 1px solid rgba(241, 241, 241, 1);\r\n\t\t\t\t\tborder-radius: 29rpx;\r\n\t\t\t\t\tcolor: #BBBBBB;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\r\n\t\t\t\t\t.iconfont {\r\n\t\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t/* #endif */\r\n\r\n\t\t.page_content {\r\n\t\t\tbackground-color: #f5f5f5;\r\n\t\t\t/* #ifdef H5 */\r\n\t\t\t// margin-top: 20rpx !important;\r\n\t\t\t/* #endif */\r\n\t\t\tpadding: 0 30rpx;\r\n\r\n\t\t\t.swiper {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 320rpx;\r\n\t\t\t\tmargin: 0 auto;\r\n\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\tmargin-bottom: 25rpx;\r\n\t\t\t\t/* #ifdef MP */\r\n\t\t\t\tz-index: 10;\r\n\t\t\t\tmargin-top: 20rpx;\r\n\r\n\t\t\t\t/* #endif */\r\n\t\t\t\tswiper,\r\n\t\t\t\t.swiper-item,\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 320rpx;\r\n\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.nav {\r\n\t\t\t\tpadding-bottom: 26rpx;\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t\topacity: 1;\r\n\t\t\t\tborder-radius: 14rpx;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tmargin-bottom: 30rpx;\r\n\r\n\t\t\t\t.item {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\twidth: 20%;\r\n\t\t\t\t\tmargin-top: 30rpx;\r\n\r\n\t\t\t\t\timage {\r\n\t\t\t\t\t\twidth: 82rpx;\r\n\t\t\t\t\t\theight: 82rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.nav-bd {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t.item {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\r\n\t\t\t\t\t.txt {\r\n\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\tcolor: #282828;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.label {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\twidth: 124rpx;\r\n\t\t\t\t\t\theight: 32rpx;\r\n\t\t\t\t\t\tmargin-top: 5rpx;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t&.active {\r\n\t\t\t\t\t\tcolor: $theme-color;\r\n\r\n\t\t\t\t\t\t.txt {\r\n\t\t\t\t\t\t\t@include main-color(theme);\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.label {\r\n\t\t\t\t\t\t\t// background: linear-gradient(90deg, $bg-star 0%, $bg-end 100%);\r\n\t\t\t\t\t\t\t@include linear-gradient(theme);\r\n\t\t\t\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.index-product-wrapper {\r\n\t\t\t\tmargin-bottom: 110rpx;\r\n\r\n\t\t\t\t&.on {\r\n\t\t\t\t\tmin-height: 1500rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.list-box {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-wrap: wrap;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t\t.item {\r\n\t\t\t\t\t\twidth: 335rpx;\r\n\t\t\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\t\t\tbackground-color: #fff;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t\toverflow: hidden;\r\n\r\n\t\t\t\t\t\timage {\r\n\t\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\t\theight: 330rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.text-info {\r\n\t\t\t\t\t\t\tpadding: 10rpx 20rpx 15rpx;\r\n\r\n\t\t\t\t\t\t\t.title {\r\n\t\t\t\t\t\t\t\tcolor: #222222;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t.old-price {\r\n\t\t\t\t\t\t\t\tmargin-top: 8rpx;\r\n\t\t\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\t\t\tcolor: #AAAAAA;\r\n\t\t\t\t\t\t\t\ttext-decoration: line-through;\r\n\r\n\t\t\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\t\t\tmargin-right: 2px;\r\n\t\t\t\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t.price {\r\n\t\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\t\talign-items: flex-end;\r\n\t\t\t\t\t\t\t\t@include price-color(theme);\r\n\t\t\t\t\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\t\t\t\t\tfont-weight: 800;\r\n\t\t\t\t\t\t\t\tmargin-top:0;\r\n\t\t\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\t\t\tpadding-bottom: 4rpx;\r\n\t\t\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\t\t\tfont-weight: 800;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t.txt {\r\n\t\t\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\t\t\t\twidth: 28rpx;\r\n\t\t\t\t\t\t\t\t\theight: 28rpx;\r\n\t\t\t\t\t\t\t\t\tmargin-left: 15rpx;\r\n\t\t\t\t\t\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t\t\t\t\t\t\tborder: 1px solid $theme-color;\r\n\t\t\t\t\t\t\t\t\tborder-radius: 4rpx;\r\n\t\t\t\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\t\t\t\tfont-weight: normal;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t&.on {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.pictrue {\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.fixed {\r\n\t\tz-index: 100;\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\tbackground: linear-gradient(90deg, red 50%, #ff5400 100%);\r\n\r\n\t}\r\n\r\n\t.mores-txt {\r\n\t\twidth: 100%;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\theight: 70rpx;\r\n\t\tcolor: #999;\r\n\t\tfont-size: 24rpx;\r\n\r\n\t\t.iconfont {\r\n\t\t\tmargin-top: 2rpx;\r\n\t\t\tfont-size: 20rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.menu-txt {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #454545;\r\n\t}\r\n\r\n\t.mp-bg {\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\t/* #ifdef H5 */\r\n\t\ttop: 98rpx;\r\n\t\t/* #endif */\r\n\t\twidth: 100%;\r\n\t\theight: 344rpx;\r\n\t\t@include index-gradient(theme);\r\n\t}\r\n</style>\r\n", "import { render, staticRenderFns, recyclableRender, components } from \"./a_seckill.vue?vue&type=template&id=7d45776e&scoped=true&\"\nvar renderjs\nimport script from \"./a_seckill.vue?vue&type=script&lang=js&\"\nexport * from \"./a_seckill.vue?vue&type=script&lang=js&\"\nimport style0 from \"./a_seckill.vue?vue&type=style&index=0&id=7d45776e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7d45776e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/components/a_seckill.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./a_seckill.vue?vue&type=template&id=7d45776e&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.spikeList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./a_seckill.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./a_seckill.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<!-- 秒杀 -->\r\n\t<view v-if=\"spikeList.length\">\r\n\t\t\t<view class=\"seckill\">\r\n\t\t\t\t<view class=\"title acea-row row-between-wrapper\">\r\n\t\t\t\t\t<view class=\"acea-row row-middle\">\r\n\t\t\t\t\t\t<view class=\"pictrue skeleton-rect\">\r\n\t\t\t\t\t\t\t<image src=\"/static/images/seckillTitle.png\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"lines\"></view>\r\n\t\t\t\t\t\t<view class=\"point skeleton-rect\">{{point}} 场</view>\r\n\t\t\t\t\t\t<countDown :is-day=\"false\" :tip-text=\"' '\" :day-text=\"' '\" :hour-text=\"' : '\" :minute-text=\"' : '\" :second-text=\"' '\"\r\n\t\t\t\t\t\t :datatime=\"datatime\" :is-col=\"true\" :bgColor=\"bgColor\"></countDown>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"more acea-row row-center-wrapper skeleton-rect\" @click=\"toSeckillList()\">\r\n\t\t\t\t\t\tGO<text class=\"iconfont icon-xiangyou\"></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"conter\">\r\n\t\t\t\t\t<scroll-view scroll-x=\"true\" style=\"white-space: nowrap; vertical-align: middle;\" show-scrollbar=\"false\">\r\n\t\t\t\t\t\t<view class=\"itemCon\" v-for=\"(item, index) in spikeList\" :key=\"index\" @click=\"goDetail(item)\">\r\n\t\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t\t<view class=\"pictrue skeleton-rect\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"item.image\"></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"name line2 skeleton-rect\">{{item.title}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"x_money line1 skeleton-rect\">¥<text class=\"num\">{{item.price}}</text></view>\r\n\t\t\t\t\t\t\t\t<view class=\"y_money line1\">¥{{item.otPrice}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</scroll-view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t</view>\r\n\t\r\n</template>\r\n\r\n<script>\r\n\tlet app = getApp();\r\n\timport countDown from \"@/components/countDown\";\r\n\timport {getSeckillIndexApi} from '@/api/activity.js';\r\n\timport {setThemeColor} from '@/utils/setTheme.js'\r\n\timport animationType from '@/utils/animationType.js'\r\n\texport default {\r\n\t\tname: 'a_seckill',\r\n\t\tcomponents: {\r\n\t\t\tcountDown\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tbgColor: {\r\n\t\t\t\t\t'bgColor': '#fff',\r\n\t\t\t\t\t'Color': '',\r\n\t\t\t\t\t'width': '44rpx',\r\n\t\t\t\t\t'timeTxtwidth': '16rpx',\r\n\t\t\t\t\t'isDay': true\r\n\t\t\t\t},\r\n\t\t\t\tspikeList: [{image: '', title: '', price:'',otPrice: ''},{image: '', title: '', price:'',otPrice: ''},{image: '', title: '', price:'',otPrice: ''}], // 秒杀\r\n\t\t\t\tpoint: '',\r\n\t\t\t\tdatatime: 0,\r\n\t\t\t\tstatus: 0\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tlet that = this;\r\n\t\t\tsetTimeout(()=>{\r\n\t\t\t\tthat.bgColor.Color =  setThemeColor();\r\n\t\t\t},1000)\r\n\t\t\tthis.getSeckillIndexTime();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetSeckillIndexTime() {\r\n\t\t\t\t//this.spikeList = [{title:'',image:'',price:''}];\r\n\t\t\t\tgetSeckillIndexApi().then(({data}) => {\r\n\t\t\t\t\tthis.spikeList= [];\r\n\t\t\t\t\tthis.spikeList = data ? data.productList : [];\r\n\t\t\t\t\tthis.point = data ? data.secKillResponse.time.split(',')[0] : '';\r\n\t\t\t\t\tthis.datatime = data ? parseFloat(data.secKillResponse.timeSwap) : '';\r\n\t\t\t\t\tthis.status =  data ? data.secKillResponse.status : 0;\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgoDetail(item){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\tanimationType: animationType.type,\r\n\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\turl: '/pages/activity/goods_seckill_details/index?id=' + item.id\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttoSeckillList(){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\tanimationType: animationType.type,\r\n\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\turl: '/pages/activity/goods_seckill/index'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.default{\r\n\t\twidth: 690rpx;\r\n\t\theight: 300rpx;\r\n\t\tborder-radius: 14rpx;\r\n\t\tmargin: 26rpx auto 0 auto;\r\n\t\tbackground-color: #ccc;\r\n\t\ttext-align: center;\r\n\t\tline-height: 300rpx;\r\n\t\t.iconfont{\r\n\t\t\tfont-size: 80rpx;\r\n\t\t}\r\n\t}\r\n\t.seckill {\r\n\t\twidth: auto;\r\n\t\theight: 420rpx;\r\n\t\t@include seckill-gradient(theme);\r\n\t\tbackground-image: url('data:image/png;base64,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');\r\n\t\tbackground-repeat: no-repeat;\r\n\t\tbackground-size: 100%;\r\n\t\tborder-radius: 14rpx;\r\n\t\tmargin: 0 auto 30rpx auto;\r\n\t\tpadding: 24rpx;\r\n        box-sizing: border-box;\r\n\t\t.title {\r\n\t\t\t.pictrue {\r\n\t\t\t\twidth: 148rpx;\r\n\t\t\t\theight: 40rpx;\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.lines {\r\n\t\t\t\twidth: 1rpx;\r\n\t\t\t\theight: 24rpx;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\topacity: 0.6;\r\n\t\t\t\tmargin-left: 16rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.point {\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tmargin-left: 21rpx;\r\n\t\t\t\tmargin-right: 4rpx;\r\n\t\t\t\tfont-weight: 800;\r\n\t\t\t}\r\n\t\t\t\t.styleAll {\r\n\t\t\t\t\twidth: 35rpx;\r\n\t\t\t\t\theight: 35rpx;\r\n\t\t\t\t\tbackground-color: #2F2F2F;\r\n\t\t\t\t\tborder-radius: 6rpx;\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t}\r\n\r\n\t\t\t.more {\r\n\t\t\t\twidth: 86rpx;\r\n\t\t\t\theight: 40rpx;\r\n\t\t\t\tbackground: linear-gradient(142deg, #FFE9CE 0%, #FFD6A7 100%);\r\n\t\t\t\topacity: 1;\r\n\t\t\t\tborder-radius: 18px;\r\n\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\tcolor: #FE960F;\r\n\t\t\t\tpadding-left: 8rpx;\r\n                font-weight: 800;\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tfont-size: 21rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.conter {\r\n\t\t\twidth: 666rpx;\r\n\t\t\theight: 320rpx;\r\n\t\t\tborder-radius: 12px;\r\n\t\t\tmargin-top: 24rpx;\r\n\r\n\t\t\t.itemCon {\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\twidth: 186rpx;\r\n\t\t\t\tmargin-right: 24rpx;\r\n\r\n\t\t\t\t.item {\r\n\t\t\t\t\twidth: 100%;\r\n\r\n\t\t\t\t\t.pictrue {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 186rpx;\r\n\t\t\t\t\t\tborder-radius: 6rpx;\r\n\r\n\t\t\t\t\t\timage {\r\n\t\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\t\tborder-radius: 6rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.name {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #000;\r\n\t\t\t\t\t\tmargin-top: 14rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.y_money {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t\ttext-decoration: line-through;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.x_money {\r\n\t\t\t\t\t\t// color: #FD502F;\r\n\t\t\t\t\t\t@include price-color(theme);\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t    margin: 2rpx 0;\r\n\t\t\t\t\t\t.num {\r\n\t\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.money {\r\n\t\t\t\t\t\t// background: url(\"data:image/png;base64,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\") no-repeat;\r\n\t\t\t\t\t\tmargin-top: 14rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./a_seckill.vue?vue&type=style&index=0&id=7d45776e&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./a_seckill.vue?vue&type=style&index=0&id=7d45776e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294179592\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import { render, staticRenderFns, recyclableRender, components } from \"./b_combination.vue?vue&type=template&id=a1cf24e2&scoped=true&\"\nvar renderjs\nimport script from \"./b_combination.vue?vue&type=script&lang=js&\"\nexport * from \"./b_combination.vue?vue&type=script&lang=js&\"\nimport style0 from \"./b_combination.vue?vue&type=style&index=0&id=a1cf24e2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"a1cf24e2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/components/b_combination.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./b_combination.vue?vue&type=template&id=a1cf24e2&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.combinationList.length\n  var g1 = g0 ? _vm.assistUserList.length : null\n  var l0 =\n    g0 && g1 > 0\n      ? _vm.__map(_vm.assistUserList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = index === 2 && Number(_vm.assistUserCount) > 3\n          return {\n            $orig: $orig,\n            m0: m0,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./b_combination.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./b_combination.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :class=\"{borderShow:isBorader}\" v-if=\"combinationList.length\">\r\n\t\t<view class=\"combination\">\r\n\t\t\t<view class=\"title acea-row row-between\">\r\n\t\t\t\t<view class=\"spike-bd\">\r\n\t\t\t\t\t<view v-if=\"assistUserList.length > 0\" class=\"activity_pic\">\r\n\t\t\t\t\t\t<view v-for=\"(item,index) in assistUserList\" :key=\"index\" class=\"picture\"\r\n\t\t\t\t\t\t\t:style='index===2?\"position: relative\":\"position: static\"'>\r\n\t\t\t\t\t\t\t<span class=\"avatar\" :style='\"background-image: url(\"+item+\")\"'></span>\r\n\t\t\t\t\t\t\t<span v-if=\"index===2 && Number(assistUserCount) > 3\" class=\"mengceng\">\r\n\t\t\t\t\t\t\t\t<i>···</i>\r\n\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"pic_count\">{{assistUserCount}}人参与</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"more acea-row row-center-wrapper\" @click=\"toCombinationList()\">\r\n\t\t\t\t\tGO<text class=\"iconfont icon-xiangyou\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"conter acea-row\">\r\n\t\t\t\t<scroll-view scroll-x=\"true\" style=\"white-space: nowrap; vertical-align: middle;\"\r\n\t\t\t\t\tshow-scrollbar=\"false\">\r\n\t\t\t\t\t<view class=\"itemCon\" v-for=\"(item, index) in combinationList\" :key=\"index\" @click=\"goDetail(item)\">\r\n\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t<view class=\"pictrue\">\r\n\t\t\t\t\t\t\t\t<image :src=\"item.image\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"text lines1\">\r\n\t\t\t\t\t\t\t\t<view class=\"name line1\">{{item.title}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"x-money\">¥<text class=\"num\">{{item.price}}</text></view>\r\n\t\t\t\t\t\t\t\t<view class=\"y_money\">¥{{item.otPrice}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tlet app = getApp();\r\n\timport {getCombinationIndexApi} from '@/api/activity.js';\r\n\timport animationType from '@/utils/animationType.js'\r\n\texport default {\r\n\t\tname: 'b_combination',\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcombinationList: [],\r\n\t\t\t\tisBorader: false,\r\n\t\t\t\tassistUserList: [],\r\n\t\t\t\tassistUserCount: 0\r\n\t\t\t};\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.getCombinationList();\r\n\t\t},\r\n\t\tmounted() {},\r\n\t\tmethods: {\r\n\t\t\t// 拼团列表\r\n\t\t\tgetCombinationList: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetCombinationIndexApi().then(function(res) {\r\n\t\t\t\t\tthat.combinationList = res.data.productList;\r\n\t\t\t\t\tthat.assistUserList = res.data.avatarList;\r\n\t\t\t\t\tthat.assistUserCount = res.data.totalPeople;\r\n\t\t\t\t}).catch((res) => {\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: res\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgoDetail(item) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\tanimationType: animationType.type,\r\n\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\turl: `/pages/activity/goods_combination_details/index?id=${item.id}`\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttoCombinationList(){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\tanimationType: animationType.type,\r\n\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\turl: `/pages/activity/goods_combination/index`\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.mengceng {\r\n\t\twidth: 38rpx;\r\n\t\theight: 38rpx;\r\n\t\tline-height: 36rpx;\r\n\t\tbackground: rgba(51, 51, 51, 0.6);\r\n\t\ttext-align: center;\r\n\t\tborder-radius: 50%;\r\n\t\topacity: 1;\r\n\t\tposition: absolute;\r\n\t\tleft: 0px;\r\n\t\ttop: 2rpx;\r\n\t\tcolor: #FFF;\r\n\t\ti{\r\n\t\t\tfont-style: normal;\r\n\t\t\tfont-size: 20rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.activity_pic {\r\n\t\tmargin-left: 28rpx;\r\n\t\tpadding-left: 20rpx;\r\n\r\n\t\t.picture {\r\n\t\t\tdisplay: inline-block;\r\n\t\t}\r\n\r\n\t\t.avatar {\r\n\t\t\twidth: 38rpx;\r\n\t\t\theight: 38rpx;\r\n\t\t\tdisplay: inline-table;\r\n\t\t\tvertical-align: middle;\r\n\t\t\t-webkit-user-select: none;\r\n\t\t\t-moz-user-select: none;\r\n\t\t\t-ms-user-select: none;\r\n\t\t\tuser-select: none;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tbackground-repeat: no-repeat;\r\n\t\t\tbackground-size: cover;\r\n\t\t\tbackground-position: 0 0;\r\n\t\t\tmargin-right: -10rpx;\r\n\t\t\tbox-shadow: 0 0 0 1px #fff;\r\n\t\t}\r\n\r\n\t\t.pic_count {\r\n\t\t\tmargin-left: 30rpx;\r\n\t\t\t@include main_color(theme);\r\n\t\t\tfont-size: 22rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t}\r\n\t}\r\n\r\n\t.default {\r\n\t\twidth: 690rpx;\r\n\t\theight: 300rpx;\r\n\t\tborder-radius: 14rpx;\r\n\t\tmargin: 26rpx auto 0 auto;\r\n\t\tbackground-color: #ccc;\r\n\t\ttext-align: center;\r\n\t\tline-height: 300rpx;\r\n\r\n\t\t.iconfont {\r\n\t\t\tfont-size: 80rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.combination {\r\n\t\twidth: auto;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 14rpx;\r\n\t\tmargin: 0 auto 30rpx auto;\r\n\t\tpadding: 16rpx 24rpx 24rpx 24rpx;\r\n\t\t// background-image: url(../../../static/images/pth.png);\r\n\t\tbackground-image: url('data:image/png;base64,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');\r\n\t\tbackground-repeat: no-repeat;\r\n\t\tbackground-size: 100%;\r\n\r\n\t\t.title {\r\n\t\t\twidth: 80%;\r\n\t\t\tmargin-left: 128rpx;\r\n\r\n\t\t\t.sign {\r\n\t\t\t\twidth: 40rpx;\r\n\t\t\t\theight: 40rpx;\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.name {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #282828;\r\n\t\t\t\tmargin-left: 12rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\r\n\t\t\t\ttext {\r\n\t\t\t\t\tcolor: #797979;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tmargin-left: 14rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.more {\r\n\t\t\t\twidth: 86rpx;\r\n\t\t\t\theight: 40rpx;\r\n\t\t\t\tbackground: linear-gradient(142deg, #FFE9CE 0%, #FFD6A7 100%);\r\n\t\t\t\topacity: 1;\r\n\t\t\t\tborder-radius: 18px;\r\n\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\tcolor: #FE960F;\r\n\t\t\t\tpadding-left: 8rpx;\r\n                 font-weight: 800;\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tfont-size: 21rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.conter {\r\n\t\t\tmargin-top: 24rpx;\r\n\r\n\t\t\t.itemCon {\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\twidth: 220rpx;\r\n\t\t\t\tmargin-right: 24rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.item {\r\n\t\t\t\twidth: 100%;\r\n\r\n\t\t\t\t.pictrue {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 220rpx;\r\n\t\t\t\t\tborder-radius: 6rpx;\r\n\r\n\t\t\t\t\timage {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 6rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.text {\r\n\t\t\t\t\tmargin-top: 4rpx;\r\n\r\n\t\t\t\t\t.y_money {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t\ttext-decoration: line-through;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.name {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #000;\r\n\t\t\t\t\t\tmargin-top: 14rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.money {\r\n\t\t\t\t\t\tcolor: #FD502F;\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tmargin: 10rpx 0 0rpx 0;\r\n\r\n\t\t\t\t\t\t.num {\r\n\t\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.x-money{\r\n\t\t\t@include price-color(theme);\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\theight: 100%;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tmargin: 5rpx 0 0;\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./b_combination.vue?vue&type=style&index=0&id=a1cf24e2&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./b_combination.vue?vue&type=style&index=0&id=a1cf24e2&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294179581\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import { render, staticRenderFns, recyclableRender, components } from \"./promotion.vue?vue&type=template&id=11954480&\"\nvar renderjs\nimport script from \"./promotion.vue?vue&type=script&lang=js&\"\nexport * from \"./promotion.vue?vue&type=script&lang=js&\"\nimport style0 from \"./promotion.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/components/promotion.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promotion.vue?vue&type=template&id=11954480&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promotion.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promotion.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"\">\r\n\t\t<view class=\"index-wrapper\" v-show=\"showType == 2\">\r\n\t\t\t<view class='wrapper' v-for=\"(item,index) in tabData\" :key=\"index\" index>\r\n\t\t\t\t<view class='title1 acea-row row-between-wrapper'>\r\n\t\t\t\t\t<view class='text'>\r\n\t\t\t\t\t\t<view class='name line1'>{{item.name}}</view>\r\n\t\t\t\t\t\t<view class='line1 txt-btn'>{{item.info}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='more' @click=\"gopage(item)\">更多\r\n\t\t\t\t\t\t<text class='iconfont icon-jiantou'></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='newProducts'>\r\n\t\t\t\t\t<scroll-view class=\"scroll-view_x\" scroll-x style=\"width:auto;overflow:hidden;\">\r\n\t\t\t\t\t\t<block v-for=\"(item1,index1) in tempArr[index]\" :key='index1'>\r\n\t\t\t\t\t\t\t<view class='item' @click=\"goDetail(item1.id)\">\r\n\t\t\t\t\t\t\t\t<view class='img-box'>\r\n\t\t\t\t\t\t\t\t\t<image :src='item1.image'></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class='pro-info line1'>{{item1.storeName}}</view>\r\n\t\t\t\t\t\t\t\t<view class='money font-color'>￥{{item1.price}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</scroll-view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"index-wrapper\" v-show=\"showType == 3\">\r\n\t\t\t<view class='wrapper' v-for=\"(item,index) in tabData\" :key=\"index\" index>\r\n\t\t\t\t<view class='title1 acea-row row-between-wrapper'>\r\n\t\t\t\t\t<view class='text'>\r\n\t\t\t\t\t\t<view class='name line1'>{{item.name}}</view>\r\n\t\t\t\t\t\t<view class='line1 txt-btn'>{{item.info}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='more' @click=\"gopage(item)\">更多\r\n\t\t\t\t\t\t<text class='iconfont icon-jiantou'></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<promotionGood :benefit=\"tempArr[index]\"></promotionGood>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {getGroomList} from '@/api/store.js';\r\n\timport promotionGood from '@/components/promotionGood/index.vue';\r\n\timport animationType from '@/utils/animationType.js'\r\n\tlet app = getApp()\r\n\texport default {\r\n\t\tname: 'promotion',\r\n\t\tprops: {\r\n\t\t\ttabData: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault: []\r\n\t\t\t},\r\n\t\t\tshowType:{\r\n\t\t\t\ttype:Number,\r\n\t\t\t\tdefault:1\r\n\t\t\t},\r\n\t\t},\r\n\t\tcomponents: {\r\n\t\t\tpromotionGood\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tsetTimeout(()=>{\r\n\t\t\t\tthis.reloadData();\r\n\t\t\t},1000)\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttempArr: [],\r\n\t\t\t\tparams: {\r\n\t\t\t\t\tpage: 1,\r\n\t\t\t\t\tlimit: 6,\r\n\t\t\t\t},\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 产品列表\r\n\t\t\tproductslist: function(item) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\t\tgetGroomList(item.type,this.params).then(({data}) => {\r\n\t\t\t\t\t\tresolve(data.list);\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tasync reloadData(){\r\n\t\t\t\tfor (let i = 0; i < this.tabData.length; i++) {\r\n\t\t\t\t\tlet goodsInfo = await this.productslist(this.tabData[i]);\r\n\t\t\t\t\tthis.tempArr.push(goodsInfo);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgopage(type) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\tanimationType: animationType.type,\r\n\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\turl: '/pages/activity/promotionList/index?type=' + JSON.stringify(type)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgoDetail(id) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\tanimationType: animationType.type,\r\n\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\turl: `/pages/goods_details/index?id=${id}`\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.index-wrapper {\r\n\t\tborder-radius: 14rpx;\r\n\t\tmargin: 30rpx auto 110rpx;\r\n\t}\r\n\r\n\t.wrapper {\r\n\t\tmargin: 30rpx auto 0;\r\n\t\tborder-radius: 14rpx;\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\r\n\t.title1 {\r\n\t\tpadding-top: 20rpx;\r\n\t\tmargin: 0 20rpx;\r\n\r\n\t\t.text {\r\n\t\t\tdisplay: flex;\r\n\r\n\t\t\t.name {\r\n\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t}\r\n\r\n\t\t\t.txt-btn {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: flex-end;\r\n\t\t\t\tmargin-bottom: 4rpx;\r\n\t\t\t\tmargin-left: 12rpx;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #999;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.more {\r\n\t\t\tfont-size: 12px;\r\n\t\t\tcolor: #999;\r\n\r\n\t\t\t.icon-jiantou {\r\n\t\t\t\tmargin-left: 10rpx;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\r\n\t.wrapper .newProducts {\r\n\t\twhite-space: nowrap;\r\n\t\tpadding: 0rpx 20rpx 0rpx 20rpx;\r\n\t\tmargin: 20rpx 0;\r\n\t}\r\n\r\n\t.wrapper .newProducts .item {\r\n\t\tdisplay: inline-block;\r\n\t\twidth: 240rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t}\r\n\r\n\t.wrapper .newProducts .item:nth-last-child(1) {\r\n\t\tmargin-right: 0;\r\n\t}\r\n\r\n\t.wrapper .newProducts .item .img-box {\r\n\t\twidth: 100%;\r\n\t\theight: 240rpx;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.wrapper .newProducts .item .img-box image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 12rpx 12rpx 0 0;\r\n\t}\r\n\r\n\t.wrapper .newProducts .item .pro-info {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333;\r\n\t\ttext-align: center;\r\n\t\tpadding: 19rpx 10rpx 0 10rpx;\r\n\t}\r\n\r\n\t.wrapper .newProducts .item .money {\r\n\t\tpadding: 0 10rpx 18rpx 10rpx;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 26rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.empty-img {\r\n\t\twidth: 640rpx;\r\n\t\theight: 300rpx;\r\n\t\tborder-radius: 14rpx;\r\n\t\tmargin: 26rpx auto 0 auto;\r\n\t\tbackground-color: #ccc;\r\n\t\ttext-align: center;\r\n\t\tline-height: 300rpx;\r\n\r\n\t\t.iconfont {\r\n\t\t\tfont-size: 50rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.font-color {\r\n\t\t@include main_color(theme);\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promotion.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promotion.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294179533\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import { render, staticRenderFns, recyclableRender, components } from \"./goodsRank.vue?vue&type=template&id=79edf382&\"\nvar renderjs\nimport script from \"./goodsRank.vue?vue&type=script&lang=js&\"\nexport * from \"./goodsRank.vue?vue&type=script&lang=js&\"\nimport style0 from \"./goodsRank.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/components/goodsRank.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./goodsRank.vue?vue&type=template&id=79edf382&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.productList.length\n  var l0 = g0 > 2 ? _vm.productList.slice(0, 3) : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, item) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        item = _temp2.item\n      var _temp, _temp2\n      return _vm.toDetail(item.id)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./goodsRank.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./goodsRank.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :data-theme=\"theme\">\r\n\t\t<view class='hotList' v-if=\"productList.length > 2\">\r\n\t\t\t<view class='title acea-row row-between-wrapper'>\r\n\t\t\t\t<view class='text line1'>\r\n\t\t\t\t\t<text class=\"iconfont icon-jingpintuijian1\"></text> \r\n\t\t\t\t\t<text class='label'>商品排行榜</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='more' hover-class=\"none\" @click=\"more()\">更多\r\n\t\t\t\t\t<text class=\"iconfont icon-jiantou\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class='list'>\r\n\t\t\t\t<block>\r\n\t\t\t\t\t<view class=\"item acea-row row-middle\" :class=\"{'lei' : index < 2}\" v-for=\"(item,index) in productList.slice(0,3)\" :key=\"index\" @click=\"toDetail(item.id)\">\r\n\t\t\t\t\t\t<view class=\"img_box\">\r\n\t\t\t\t\t\t\t<image class=\"pictrue\" :src=\"item.image\"></image>\r\n\t\t\t\t\t\t\t<view  class=\"rank_bdg top_1\" v-if=\"index == 0\">热榜TOP1</view>\r\n\t\t\t\t\t\t\t<view  class=\"rank_bdg top_2\" v-else-if=\"index == 1\">热榜TOP2</view>\r\n\t\t\t\t\t\t\t<view  class=\"rank_bdg top_3\" v-else-if=\"index == 2\">热榜TOP3</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"ml_11 flex-column justify-between flex-1\">\r\n\t\t\t\t\t\t\t<view class=\"goods_name\">{{item.storeName}}</view>\r\n\t\t\t\t\t\t\t<view class=\"price flex justify-between\">\r\n\t\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t\t<text class=\"price_bdg\">￥</text>{{item.price}}\r\n\t\t\t\t\t\t\t\t\t<text class=\"sales\">销量 {{item.sales}}件</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"cart_icon\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"iconfont icon-gouwuche7\"></text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tlet app = getApp()\r\n\timport {mapState} from 'vuex';\r\n\timport animationType from '@/utils/animationType.js'\r\n\timport {productRank} from '@/api/api.js'\r\n\texport default {\r\n\t\tname: 'goodList',\r\n\t\tprops: {\r\n\t\t\tdataConfig: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: () => {}\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttheme:app.globalData.theme,\r\n\t\t\t\tproductList: [],\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.getProductRank();\r\n\t\t},\r\n\t\tmethods:{\r\n\t\t\ttoDetail(id){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\tanimationType: animationType.type,\r\n\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\turl:'/pages/goods_details/index?id=' + id\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetProductRank(){\r\n\t\t\t\tproductRank().then(res=>{\r\n\t\t\t\t\tthis.productList = res.data;\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tmore(){\r\n\t\t\t\tlet typeInfo = {\r\n\t\t\t\t\ttype:'rank',\r\n\t\t\t\t\tname:'商品排行'\r\n\t\t\t\t}\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\tanimationType: animationType.type,\r\n\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\turl:'/pages/activity/promotionList/index?type=' + JSON.stringify(typeInfo)\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.hotList {\r\n\t\tbackground-color: #fff;\r\n\t\tmargin: 30rpx 0 0;\r\n\t\tborder-radius: 12rpx;\r\n\t}\r\n\r\n\t.hotList .hot-bg {\r\n\t\twidth: 100%;\r\n\t\theight: 120rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #282828;\r\n\t\tmargin-top: 15rpx;\r\n\r\n\t}\r\n\r\n\t.hotList .title {\r\n\t\tpadding: 24rpx 20rpx 0 20rpx;\r\n\t}\r\n\r\n\t.hotList .title .text {\r\n\t\twidth: 500rpx;\r\n\t\tcolor: #999999;\r\n\t\tfont-size: 12px;\r\n\t\tdisplay: flex;\r\n\t\talign-items: flex-end;\r\n\t}\r\n\t\r\n\t.icon-jingpintuijian1{\r\n\t\t@include main_color(theme);\r\n\t\tfont-size: 42rpx;\r\n\t}\r\n\t.hotList .title .text .label {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #282828;\r\n\t\tmargin-right: 12rpx;\r\n\t\tmargin-left:12rpx;\r\n\t}\r\n\r\n\t.hotList .title .more {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #999999;\r\n\t\t;\r\n\t}\r\n\r\n\t.hotList .title .more .iconfont {\r\n\t\tfont-size: 25rpx;\r\n\t\tmargin-left: 10rpx;\r\n\t}\r\n\r\n\t.hotList .list {\r\n\t\twidth: 690rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tmargin: 0rpx auto 0 auto;\r\n\t\tpadding: 0 20rpx 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\t.hotList .list .item {\r\n\t\tbackground: #fff;\r\n\t\tmargin-top: 26rpx;\r\n\t}\r\n\t.lei{\r\n\t\tpadding-bottom: 26rpx;\r\n\t\tposition: relative;\r\n\t\t&::after{\r\n\t\t\tcontent: '';\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: 0;\r\n\t\t\tright: 0;\r\n\t\t\twidth: 460rpx;\r\n\t\t\theight: 2rpx;\r\n\t\t\tbackground-color: #eee;\r\n\t\t}\r\n\t}\r\n\t.img_box{\r\n\t\twidth: 180rpx;\r\n\t\theight: 180rpx;\r\n\t\tbackground: #F3F3F3;\r\n\t\tposition: relative;\r\n\t\t.pictrue{\r\n\t\t\twidth:100%;\r\n\t\t\theight:100%;\r\n\t\t\tborder-radius: 10rpx;\r\n\t\t}\r\n\t\t.rank_bdg{\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 46rpx;\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: 0;\r\n\t\t\tleft: 0;\r\n\t\t\tright: 0;\r\n\t\t\tmargin: auto;\r\n\t\t\ttext-align: center;\r\n\t\t\tcolor: #fff;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tline-height: 46rpx;\r\n\t\t\tbackground-size: 100%;\r\n\t\t\tbackground-repeat: no-repeat;\r\n\t\t}\r\n\t}\r\n\t.ml_11{\r\n\t\tmargin-left: 22rpx;\r\n\t}\r\n\t.flex-1{\r\n\t\tflex: 1;\r\n\t}\r\n\t.goods_name{\r\n\t\twidth: 420rpx;\r\n\t\theight: 80rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: 400;\r\n\t\tcolor: #333333;\r\n\t\tline-height: 40rpx;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow:ellipsis;\r\n\t\twhite-space: wrap;\r\n\t}\r\n\t.price{\r\n\t\tmargin-top: 60rpx;\r\n\t\tfont-size: 34rpx;\r\n\t\tfont-weight: 600;\r\n\t\t@include price_color(theme);\r\n\t\t.price_bdg{\r\n\t\t\tfont-size: 26rpx;\r\n\t\t}\r\n\t\t.sales{\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: #999999;\r\n\t\t\tfont-weight: 400;\r\n\t\t\tpadding-left: 12rpx;\r\n\t\t}\r\n\t\t.cart_icon{\r\n\t\t\twidth: 48rpx;\r\n\t\t\theight: 48rpx;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\t@include main_bg_color(theme);\r\n\t\t\t// text-align: center;\r\n\t\t\t// line-height: 40rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\t.iconfont{\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.top_1{\r\n\t\tbackground-image: url('data:image/png;base64,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');\r\n\t}\r\n\t.top_2{\r\n\t\tbackground-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAAAXCAYAAACLbliwAAAAAXNSR0IArs4c6QAABMRJREFUaEPtWUtvG1UU/s487NghTWrHeSqp1RKVhyohla5YILECqeJXsEZlidhk0aZLJFjSX9EAO9giwaaiqoJaSEnTJM2jll+J45m59yDPe8bj6bhSJdo4sjR2ZN+59zvf+c4531Bj/eYeCPdI0gNJ+EtlemSY3X9K92mXVlclRn+ZEVhdhfJp6YuFHOiSFLQCku8w8D5L/oAa67e4txKzffGvYBggfgLGNhN2wLzDEvuS8IyAAyHkoUK5w/rOs9rKl993M+/mNfziz999lq/ml0odoVZIygoIM4qkOTDNMstFEBYZtMRSLDMoBzAcUN13DAwE2kXfAd+JQO/lfbLf95YjEEuWJwTUmFEHuM5AA5JbAJoMagF8DMZx7ypBHUB2AOqQ4FOhyC4xdQWzoZBiqpZlClJMwZYQrIocDGFJTXBeZ2kZPG7p3AZAmklK1yTScmQolqpBqKYhVY10VTLrGliX0HRWRM6SyIM5T8RjklFQJApSoQJYjjNovHcF0QQkTzBwDsCklDxFin0tE6HIksmhYgBgHA8bEfdLNtTpQDsrRYB2AU5cJBY5PyjBjoJAefvszx7vjn4wUw/h/d49TCQjPVIEDAnWTNprDBA3r33AbIKF14xkf5R4AdABo73/JTA6Beism/c25x/CzYgY0P4hEjafhS1JkhcBxSNIDPyAbT7yTqCdl0M0j76ZgfakN4xfsNZgjXYPH6hFLLJpLA+lT9qGA7b4ORTIk3tor3ZE0tJftL+2JMmbJ5gR3XxBJvrZm3IvXxocHoUC9Ao02qVAiBHRyPos8ZkRRDwAMQxYbMMhYQzO7DDOY+Fg6XAPPEBOPPaGtTQgcrw5CK/l3DtL1nl7TJUObfoiWBiwnm9H0iki9CONRnG2ClJ1tHce9tWDTBqtlZagnl/E6aPfQgUyPUWGKYZvikZPX/kYx/tbONl/PAzQTsqolYsQrSPo85fR3fzdae8GVvQgjSPp/IKi8mZoNFB+7yM0/r2P/NQMWk8fRkiZIh0O0NrcZShvlWHubkAc1wb20SONZuQmyjhXvYLT+gGaWw+8viVbHw1SoM2uAPoYjK17I432W9b+wj218iGE0UHryQakEIkyO7AY6tWrgGVCtI9g1XZGGp0CdKGyjNxkBYqq4/lGcj0b2EdTcQr6/LsgVYNV24Z5sDnS6ISJtrjwNoozF8DCRH3zTxit2nAabSs1aVCn5qCOl2DV92A19vu8jrOs0fnz8xgrL8BoHOHk6KkN9kt5HUphEmp5GZAS8rRlA81Gp2846Wv4M47g/sDiDR8JE5bb6ERrxP/A61DzRfSA1ooTICK09x7DbNeGae9Cs7qiQpuuQhkvgbRcj+L28GIebqYbNRmBfl376OL8JRQqF+zgS6OLbvMI7d2/Q4zOZCr5M6/tTY+8jige4XF/WK+jCWAibvxHbFJvUEnwo8+yRg/hdbSoub72C4M/CYBOsUmTTO2R1+EMKAPcO3dm/pWaP61dB+OulJIiPwg+DBT4pEc2Z9HriPriUT+aiJlAn9vgNu/evMVEXzMzxa3LkUa/vEaTY1zcvnbjzjc20A7Ya9cl5FfMuAbYz868fEh9ZjjS6NiTlV6rytwiwh8EfHv1xg8/9qD8D1XwwuIISKhxAAAAAElFTkSuQmCC');\r\n\t}\r\n\t.top_3{\r\n\t\tbackground-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAAAXCAYAAACLbliwAAAAAXNSR0IArs4c6QAABH9JREFUaEPtWU1vG1UUPfdNHDuxJ+M4SaNCoQpFIIRY8gPCGrFBSLCFX8ASsUYs+QndZ4nYQje0GxqpEQvEogiVtomxseuP8Uc871404/n0zDhuVBY0M7L1LM/M851zzz3v+jw6uX/3jEV+JeA3UfI7azw0BH9YI+fRweHhBMWxMgJ3bt+udDfU61D8BjTfEsHbArwjwHv04P5dAcSbTLxB/NH70BKSxxB6KsynIGkKoSmMFkTaYqBNZHSqNnffPTwcrhzR/+jCo6OjmrWut8/ZaUDTrha9S0rtsZZ9UrIvgusQeQXADYHsQUDi4um9AmgFPtAuyC7KWYAH32WP4YTADIIeID0h9KB5CEUDYR4KKZuEbRYaATIGywREYxGeEjB1hM/XYEy11o4YamYwzzSRJnI0UUkza5bgYPYCIaVIOw65o1JKycwxZoBhGDCYucQsJQO0psFlBWN9PlJZtN4QQkUEGwTZFJEqiKrCUgPEJKGaQCzvzbAAKflPHhIxDqIHZoykqXPz0wHQ/uVzSi8wO5Yd75x/Z5Cx2HfRuWi+IMgouXnngt8JAwgZEWdGWHVRoKl454WZQ4yMeMO4w2oOq9qbZxHoIIbwmWL3ZSXBnWOB0XHpCH7sAkavBPT8wQMAEkAslFkyMekS9NXNT8IlKjAr3gyAU6TJBTOJWTbQMUZnMTmZuSTg0YRBxuNsT7MgxegUwIugRhqXz+ikDmazfTEZfkU+L+AXsjYd73+l0ZGsBA8Rll2S0fOkRsnIY0EmwKFyuYkJqmSBVUvkISRPXAbDz5FkrM7oF6DR5lYdWmvYg/68VAuNjnVl0ZpS397xVuF26yzRbYS1fpFGV2tbqNZMNE8fh2wNMh0tIJFk5C+GL7dGH9x6C93OP+h2WjlAL9Fol82TsQ1rexet5pMU0IVGR4y+efCmR0aXmO2/zzK7pdw+2qo3UK5s4Fmnhel0ks/ojDYvlJgrotGbmzXsX78Be9hH8/RJBtBL+mgiYMtqQBkGOq1modGpFjBi9Kuv3YQzO0fz7BTCnATab+dy++idvX0wMybjEUb2oNDoJX20Vd9GtWp6C+KjPx9mAL1Eo9fXy6g3dqGUwnDQQ7/Xze46YvJwcUsUWxRfkj66sXsNbtfBWuP06V8Yj+zn02h3+SRS2NisoVKpwLYH80kW/trGvI4r10ebpgXT2oY9HKD/rAPNOt11rOJ1uKyumpYH4Ox8ipE9hOPM5i7JFfc6SqUSzC0L5XIFIEKn3cJ4bF+uj3YZXTMtlCsVKLXmoeuurK6MXOU+urFzDW5X5h4u8Wx76DUMmYz2SbnEj46ZSlktXOi3Fl5HosJjPvSi19EHYL4AP/rKaXRSPkM9TS+GggGdHN/7UYQ/SHq4OdZf4XVkeh3RrlTSgQxqnQQ/0YPjex8S8D0zU7DKJbe0Cj96+Q7Lcj+aiISBjzxwT45//gagr5iF4tkp/OiYx34JPxrk3fTtx5998bXPYsBlNkS+FOH3RWAmymHZ9k/hdWTtGQ5I8IsmfPfJp5//4F7wL0+zShOpHg5WAAAAAElFTkSuQmCC');\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./goodsRank.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./goodsRank.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294179497\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294173471\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=1&id=57280228&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=1&id=57280228&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294178802\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}