{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/order_details/index.vue?39e7", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/order_details/index.vue?212a", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/order_details/index.vue?1e89", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/order_details/index.vue?ccaa", "uni-app:///pages/order_details/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/order_details/index.vue?ef39", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/order_details/index.vue?0345"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "payment", "home", "orderGoods", "authorize", "data", "codeImg", "qrcodeSize", "order_id", "evaluate", "cartInfo", "orderInfo", "systemStore", "pstatus", "system_store", "isGoodsReturn", "status", "isClose", "payMode", "name", "icon", "value", "title", "payStatus", "number", "pay_close", "pay_order_id", "totalPrice", "isAuto", "isShowAuth", "id", "uniId", "utils", "again<PERSON><PERSON>us", "type", "isShow", "theme", "bgColor", "chatConfig", "consumer_hotline", "telephone_service_switch", "computed", "onLoad", "options", "tab", "url", "that", "uni", "frontColor", "backgroundColor", "onShow", "onHide", "onReady", "mounted", "methods", "kefuClick", "phoneNumber", "location", "onChangeFun", "action", "makePhone", "showMaoLocation", "latitude", "longitude", "scale", "address", "success", "payClose", "pay_open", "pay_complete", "pay_fail", "onLoadFun", "getOrderInfo", "verifyCode", "markCode", "height", "text", "width", "copy", "copyDeliveryId", "goTel", "getOrderStatus", "_status", "delivery_type", "seckill_id", "bargain_id", "combination_id", "class_status", "goJoinPink", "goOrderConfirm", "orderNo", "confirmOrder", "content", "delOrder", "cancelText", "confirmText", "showCancel", "confirmColor", "cancelOrder", "then", "self", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnCA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACyRnnB;AAUA;AACA;AACA;AAGA;AAEA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AAAA,eACA;EACAC;IACAC;IACAC;IACAC;IAEAC;EAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;QACAC;QACAC;MACA;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAJ;QACAC;QACAC;QACAC;QACAE;QACAD;MACA,EAUA;MACAE;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;MACA;IACA;EACA;;EACAC;EACAC;IACAC;IACA;MACArB;IACA;MACAsB;MACAC;IACA;IACA;IACA;IACAC;IACAC;MACAC;MACAC;IACA;IACAH;EACA;EACAI;IACA;MACA;MACA;MACA;IACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC,6BAYA;EACAC,6BAIA;EACAC;IACAC;MACA;QACAR;UACAS;QACA;MACA;QAOAC;MAEA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACAC;IACA;IACA;AACA;AACA;IACAC;MACAb;QACAS;MACA;IACA;IACA;AACA;AACA;AACA;IACAK;MACA;QACAvC;MACA;MACAyB;QACAe;QACAC;QACAC;QACA7C;QACA8C;QACAC,6BAEA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MAAA;MACA;MACAzB;QACAzB;MACA;MACA;QACAyB;QACAD;QACAA;QACAA;QACAA;QACAA;QACA;UACAA;QACA;QAAA;QACA,oFACA2B;QACA;UACA1B;YACAC;YACAC;UACA;QACA;QACA;UACA;QACA;MACA;QACAH;UACAxB;QACA;UACAsB;UACAC;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACA6B;MAAA;MACA;QACAC;QACAC;QACAC;MACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;;IAIAC;MACA;MAEA/B;QACA1C;MACA;IACA;IAGA0E;MACA;MACAhC;QACA1C;MACA;IACA;IAGA;AACA;AACA;IACA2E;MACAjC;QACAS;MACA;IACA;IACA;AACA;AACA;AACA;IACAyB;MACA;QACAC;UACAhD;QACA;QACAlB;MACA;QACAmE;QACAC;QACAC;QACAC;MACAtE;QACAkB;QACAqD;MACA;MACA;MACA;MACA;MACA;MACA,qGACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACAzC;QACAF;MACA;IACA;IACA;AACA;AACA;AACA;IACA4C;MACA;QACAC;MACA;IACA;IACAC;MACA;MACA5C;QACAzB;QACAsE;QACA1B;UACA;YACA;cACA;gBACA5C;gBACAF;cACA;gBACA0B;cACA;YACA;cACA;gBACAxB;cACA;YACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAuE;MAAA;MACA9C;QACA6C;QACAE;QACAC;QACAC;QACAC;QACA/B;UACA;YACA;YACA;cACA;gBACA5C;gBACAF;cACA;gBACAwB;gBACAC;cACA;YACA;cACA;gBACAvB;cACA;YACA;UACA,QAEA;QACA;MACA;IACA;IACA4E;MACA;MACAnD;QACAzB;QACAsE;QACA1B;UACA;YACA,2CACAiC;cACAC;gBACA9E;cACA;gBACAsB;gBACAC;cACA;YACA;cACAuD;gBACA9E;cACA;cACA8E;YACA;UACA;YACAC;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACptBA;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/order_details/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/order_details/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=251d72aa&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=251d72aa&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"251d72aa\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/order_details/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=251d72aa&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 =\n    _vm.isGoodsReturn == false &&\n    _vm.orderInfo.shippingType == 2 &&\n    _vm.orderInfo.paid &&\n    _vm.orderInfo.systemStore\n      ? _vm.orderInfo.systemStore.dayTime.replace(\",\", \"-\")\n      : null\n  var g1 = _vm.orderInfo.mark && _vm.orderInfo.mark.length <= 15\n  var g2 = _vm.orderInfo.mark && _vm.orderInfo.mark.length > 15\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.isShow = !_vm.isShow\n    }\n    _vm.e1 = function ($event) {\n      _vm.isShow = !_vm.isShow\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :data-theme=\"theme\">\r\n\t\t<view class='order-details'>\r\n\t\t\t<!-- 给header上与data上加on为退款订单-->\r\n\t\t\t<view class='header bg_color' :class='isGoodsReturn ? \"on\":\"\"'>\r\n\t\t\t\t<view class='picTxt acea-row row-middle'>\r\n\t\t\t\t\t<view class='pictrue' v-if=\"isGoodsReturn==false\">\r\n\t\t\t\t\t\t<image :src=\"orderInfo.statusPic\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='data' :class='isGoodsReturn ? \"on\":\"\"'>\r\n\t\t\t\t\t\t<view class='state'>{{orderInfo.orderStatusMsg}}</view>\r\n\t\t\t\t\t\t<view v-if=\"orderInfo.refundReasonTime !== null\">{{orderInfo.refundReasonTime}}</view>\r\n\t\t\t\t\t\t<view v-else>{{orderInfo.payTime ? orderInfo.payTime :''}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view v-if=\"isGoodsReturn==false\" class=\"pad30\">\r\n\t\t\t\t<view class='nav'>\r\n\t\t\t\t\t<view class='navCon acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t<view :class=\"!orderInfo.paid ? 'on':''\">待付款</view>\r\n\t\t\t\t\t\t<view :class=\"orderInfo.paid && orderInfo.status == 0 ? 'on':''\">\r\n\t\t\t\t\t\t\t{{orderInfo.shippingType==1 ? '待发货':'待核销'}}</view>\r\n\t\t\t\t\t\t<view :class=\"orderInfo.status == 1 ? 'on':''\" v-if=\"orderInfo.shippingType == 1\">待收货</view>\r\n\t\t\t\t\t\t<view :class=\"orderInfo.status == 2 ? 'on':''\">待评价</view>\r\n\t\t\t\t\t\t<view :class=\"orderInfo.status == 3 ? 'on':''\">已完成</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='progress acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t<view class='iconfont'\r\n\t\t\t\t\t\t\t:class='(!orderInfo.paid ? \"icon-webicon318\":\"icon-yuandianxiao\") + \" \" + ( orderInfo.paid ? \"font_color\":\"\")'>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='line' :class='orderInfo.paid > 0 ? \"bg_color\":\"\"'></view>\r\n\t\t\t\t\t\t<view class='iconfont'\r\n\t\t\t\t\t\t\t:class='(orderInfo.status == 0 ? \"icon-webicon318\":\"icon-yuandianxiao\") + \" \" + (orderInfo.status >= 0 ? \"font_color\":\"\")'>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='line' :class='orderInfo.status > 0 ? \"bg_color\":\"\"'\r\n\t\t\t\t\t\t\tv-if=\"orderInfo.shippingType == 1\"></view>\r\n\t\t\t\t\t\t<view class='iconfont'\r\n\t\t\t\t\t\t\t:class='(orderInfo.status == 1 ? \"icon-webicon318\":\"icon-yuandianxiao\") + \" \" +(orderInfo.status >= 1 ? \"font_color\":\"\")'\r\n\t\t\t\t\t\t\tv-if=\"orderInfo.shippingType == 1\"></view>\r\n\t\t\t\t\t\t<view class='line' :class='orderInfo.status > 1 ? \"bg_color\":\"\"'></view>\r\n\t\t\t\t\t\t<view class='iconfont'\r\n\t\t\t\t\t\t\t:class='(orderInfo.status == 2 ? \"icon-webicon318\":\"icon-yuandianxiao\") + \" \" + (orderInfo.status >= 2 ? \"font_color\":\"\")'>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='line' :class='orderInfo.status > 2 ? \"bg_color\":\"\"'></view>\r\n\t\t\t\t\t\t<view class='iconfont'\r\n\t\t\t\t\t\t\t:class='(orderInfo.status == 3 ? \"icon-webicon318\":\"icon-yuandianxiao\") + \" \" + (orderInfo.status >= 3 ? \"font_color\":\"\")'>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-if=\"orderInfo.shippingType == 2 && orderInfo.paid\" class=\"writeOff borRadius14\">\r\n\t\t\t\t\t<view class=\"title\">核销信息</view>\r\n\t\t\t\t\t<view class=\"grayBg\">\r\n\t\t\t\t\t\t<view class=\"pictrue\">\r\n\t\t\t\t\t\t\t<!-- <div class=\"qrcode\" ref=\"qrcode\"></div> -->\r\n\t\t\t\t\t\t\t<!-- <canvas canvas-id=\"qrcode\" :style=\"{width: `${qrcodeSize}100%`, height: `${qrcodeSize}100%`}\"/> -->\r\n\t\t\t\t\t\t\t<image :src=\"codeImg\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"gear\">\r\n\t\t\t\t\t\t<image src=\"../../static/images/writeOff.jpg\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"num\">{{orderInfo.verifyCode}}</view>\r\n\t\t\t\t\t<view class=\"rules\" v-if='orderInfo.systemStore'>\r\n\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t<view class=\"rulesTitle acea-row row-middle\">\r\n\t\t\t\t\t\t\t\t<text class=\"iconfont icon-shijian\"></text>核销时间\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"info\">\r\n\t\t\t\t\t\t\t\t每日：<text class=\"time\">{{orderInfo.systemStore.dayTime.replace(',','-')}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t<view class=\"rulesTitle acea-row row-middle\">\r\n\t\t\t\t\t\t\t\t<text class=\"iconfont icon-shuoming1\"></text>使用说明\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"info\">可将二维码出示给店员扫描或提供数字核销码</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"refund borRadius14\" v-if=\"orderInfo.refundReason\">\r\n\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t<image src=\"/static/images/shuoming.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t商家拒绝退款\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"con\">拒绝原因：{{orderInfo.refundReason}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-if=\"orderInfo.shippingType == 2\" class=\"map acea-row row-between-wrapper borRadius14\">\r\n\t\t\t\t\t<view>自提地址信息</view>\r\n\t\t\t\t\t<view class=\"place cart-color acea-row row-center-wrapper\" @tap=\"showMaoLocation\">\r\n\t\t\t\t\t\t<text class=\"iconfont icon-weizhi\"></text>查看位置\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-if=\"orderInfo.shippingType === 1\" class='address borRadius14'>\r\n\t\t\t\t\t<view class='name'>{{orderInfo.realName}}<text class='phone'>{{orderInfo.userPhone}}</text></view>\r\n\t\t\t\t\t<view>{{orderInfo.userAddress}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-else class='address' style=\"margin-top:15rpx;\">\r\n\t\t\t\t\t<view class='name' @tap=\"makePhone\">{{orderInfo.systemStore?orderInfo.systemStore.name:''}}<text\r\n\t\t\t\t\t\t\tclass='phone'>{{orderInfo.systemStore?orderInfo.systemStore.phone:''}}</text><text\r\n\t\t\t\t\t\t\tclass=\"iconfont icon-tonghua font-color\"></text></view>\r\n\t\t\t\t\t<view>{{orderInfo.systemStore?orderInfo.systemStore.address + orderInfo.systemStore.detailedAddress:''}}</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<orderGoods :evaluate='evaluate' :productType=\"orderInfo.type\" :orderId=\"order_id\" :ids=\"id\" :uniId=\"uniId\" :cartInfo=\"cartInfo\"\r\n\t\t\t\t\t:jump=\"true\"></orderGoods>\r\n\t\t\t\t<!-- #ifndef MP -->\r\n\t\t\t\t<div class=\"goodCall borRadius14\" @click=\"kefuClick\">\r\n\t\t\t\t\t<span class=\"iconfont icon-kefu\"></span><span>联系客服</span>\r\n\t\t\t\t</div>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<!-- #ifdef MP -->\r\n\t\t\t\t<div class=\"goodCall borRadius14\">\r\n\t\t\t\t\t<button hover-class='none' @click=\"kefuClick\" v-if=\"chatConfig.telephone_service_switch === 'true'\">\r\n\t\t\t\t\t\t<span class=\"iconfont icon-kefu\"></span><span>联系客服</span>\r\n\t\t\t\t\t</button>\r\n\t\t\t\t\t<button open-type='contact' hover-class='none' v-else>\r\n\t\t\t\t\t\t<span class=\"iconfont icon-kefu\"></span><span>联系客服</span>\r\n\t\t\t\t\t</button>\r\n\t\t\t\t</div>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"pad30\">\r\n\t\t\t\t<!-- <view class='nav refund' v-if=\"orderInfo.refundStatus>0\">\r\n\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t<image src=\"/static/images/shuoming.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t{{orderInfo.refundStatus==1?'商家审核中':orderInfo.refundStatus==2?'商家已退款':'商家拒绝退款'}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"con pad30\">{{orderInfo.refundStatus==1 ? \"您已成功发起退款申请，请耐心等待商家处理；退款前请与商家协商一致，有助于更好的处理售后问题\": orderInfo.refundStatus==2? \"退款已成功受理，如商家已寄出商品请尽快退回；感谢您的支持\": \"拒绝原因：\" + orderInfo.refundReason}}</view>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<view class='wrapper borRadius14'>\r\n\t\t\t\t\t<view class='item acea-row row-between'>\r\n\t\t\t\t\t\t<view>订单编号：</view>\r\n\t\t\t\t\t\t<view class='conter acea-row row-middle row-right'><text class=\"text-overflow\">{{orderInfo.orderId}}</text>\r\n\t\t\t\t\t\t\t<!-- #ifndef H5 -->\r\n\t\t\t\t\t\t\t<text class='copy' @tap='copy'>复制</text>\r\n\t\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t\t\t\t<text class='copy copy-data' :data-clipboard-text=\"orderInfo.orderId\">复制</text>\r\n\t\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item acea-row row-between'>\r\n\t\t\t\t\t\t<view>下单时间：</view>\r\n\t\t\t\t\t\t<view class='conter'>{{(orderInfo.createTime || 0)}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item acea-row row-between'>\r\n\t\t\t\t\t\t<view>支付状态：</view>\r\n\t\t\t\t\t\t<view class='conter' v-if=\"orderInfo.paid\">已支付</view>\r\n\t\t\t\t\t\t<view class='conter' v-else>未支付</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item acea-row row-between'>\r\n\t\t\t\t\t\t<view>支付方式：</view>\r\n\t\t\t\t\t\t<view class='conter'>{{orderInfo.payTypeStr}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item flex justify-between align-center' v-if=\"orderInfo.mark && orderInfo.mark.length <= 15\">\r\n\t\t\t\t\t\t<view>买家留言：</view>\r\n\t\t\t\t\t\t<view class='conter' >{{orderInfo.mark}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item flex justify-between' v-if=\"orderInfo.mark && orderInfo.mark.length > 15\">\r\n\t\t\t\t\t\t<view>买家留言：</view>\r\n\t\t\t\t\t\t<view class=\"flex align-center\" v-show=\"isShow\" @click=\"isShow=!isShow\">\r\n\t\t\t\t\t\t\t<view class='conter' >{{orderInfo.mark}}</view>\r\n\t\t\t\t\t\t\t<text class=\"iconfont icon-xiangyou\" style=\"font-size: 12px;\"></text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view v-show=\"!isShow\" @click=\"isShow=!isShow\">\r\n\t\t\t\t\t\t\t<view class='mark_show' >{{orderInfo.mark}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 退款订单详情 \"-->\r\n\t\t\t\t<view v-if=\"isGoodsReturn\" class='wrapper borRadius14' >\r\n\t\t\t\t\t<view class='item acea-row row-between'>\r\n\t\t\t\t\t\t<view>收货人：</view>\r\n\t\t\t\t\t\t<view class='conter'>{{orderInfo.realName}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item acea-row row-between'>\r\n\t\t\t\t\t\t<view>联系电话：</view>\r\n\t\t\t\t\t\t<view class='conter'>{{orderInfo.userPhone}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item acea-row row-between'>\r\n\t\t\t\t\t\t<view>收货地址：</view>\r\n\t\t\t\t\t\t<view class='conter'>{{orderInfo.userAddress}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item acea-row row-between' v-if=\"orderInfo.refundReasonWap\">\r\n\t\t\t\t\t\t<view>退款原因：</view>\r\n\t\t\t\t\t\t<view class='conter'>{{orderInfo.refundReasonWap}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item acea-row row-between' v-if=\"orderInfo.refundReasonWapExplain\">\r\n\t\t\t\t\t\t<view>退款说明：</view>\r\n\t\t\t\t\t\t<view class='conter'>{{orderInfo.refundReasonWapExplain}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-if=\"orderInfo.status>0\">\r\n\t\t\t\t\t<view class='wrapper borRadius14' v-if='orderInfo.deliveryType==\"express\"'>\r\n\t\t\t\t\t\t<view class='item acea-row row-between'>\r\n\t\t\t\t\t\t\t<view>配送方式：</view>\r\n\t\t\t\t\t\t\t<view class='conter'>发货</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='item acea-row row-between'>\r\n\t\t\t\t\t\t\t<view>快递公司：</view>\r\n\t\t\t\t\t\t\t<view class='conter'>{{orderInfo.deliveryName || ''}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='item acea-row row-between'>\r\n\t\t\t\t\t\t\t<view>快递号：</view>\r\n\t\t\t\t\t\t\t<view class='conter'>{{orderInfo.deliveryId || ''}}<text class='copy' style=\"font-size: 20rpx; color: #333; border-radius: 20rpx; border: 1rpx solid #666; padding: 3rpx 15rpx;\"\r\n\t\t\t\t\t\t\t @tap='copyDeliveryId'>复制</text></view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='wrapper borRadius14' v-else-if='orderInfo.deliveryType==\"send\"'>\r\n\t\t\t\t\t\t<view class='item acea-row row-between'>\r\n\t\t\t\t\t\t\t<view>配送方式：</view>\r\n\t\t\t\t\t\t\t<view class='conter'>送货</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='item acea-row row-between'>\r\n\t\t\t\t\t\t\t<view>配送人姓名：</view>\r\n\t\t\t\t\t\t\t<view class='conter'>{{orderInfo.deliveryName || ''}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='item acea-row row-between'>\r\n\t\t\t\t\t\t\t<view>联系电话：</view>\r\n\t\t\t\t\t\t\t<view class='conter acea-row row-middle row-right'>{{orderInfo.deliveryId || ''}}<text\r\n\t\t\t\t\t\t\t\t\tclass='copy' @tap='goTel'>拨打</text></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='wrapper borRadius14' v-else-if='orderInfo.deliveryType==\"fictitious\"'>\r\n\t\t\t\t\t\t<view class='item acea-row row-between'>\r\n\t\t\t\t\t\t\t<view>虚拟发货：</view>\r\n\t\t\t\t\t\t\t<view class='conter'>已发货，请注意查收</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='wrapper borRadius14'>\r\n\t\t\t\t<!-- \t<view class='item acea-row row-between'>\r\n\t\t\t\t\t\t<view>商品总价：</view>\r\n\t\t\t\t\t\t<view class='conter'>￥{{orderInfo.proTotalPrice}}</view>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t\t<view class='item acea-row row-between' v-if=\"orderInfo.payPostage > 0\">\r\n\t\t\t\t\t\t<view>运费：</view>\r\n\t\t\t\t\t\t<view class='conter'>￥{{orderInfo.payPostage}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item acea-row row-between' v-if='orderInfo.couponId'>\r\n\t\t\t\t\t\t<view>优惠券抵扣：</view>\r\n\t\t\t\t\t\t<view class='conter'>-￥{{orderInfo.couponPrice}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item acea-row row-between' v-if=\"orderInfo.useIntegral > 0\">\r\n\t\t\t\t\t\t<view>积分抵扣：</view>\r\n\t\t\t\t\t\t\t<view class='conter'>-￥{{orderInfo.deductionPrice}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- <view class='actualPay acea-row row-right'>实付款：<text\r\n\t\t\t\t\t\t\tclass='money'>￥{{orderInfo.payPrice}}</text></view> -->\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style='height:120rpx;'></view>\r\n\t\t\t\t<view class='footer acea-row row-right row-middle' v-if=\"isGoodsReturn==false\">\r\n\t\t\t\t\t<view class=\"qs-btn\" v-if=\"!orderInfo.paid\" @click.stop=\"cancelOrder\">取消订单</view>\r\n\t\t\t\t\t<view class='bnt bg_color' v-if=\"!orderInfo.paid\" @tap='pay_open(orderInfo.orderId)'>立即付款</view>\r\n\t\t\t\t\t<navigator hover-class=\"none\" :url=\"'/pages/users/goods_return/index?orderId='+orderInfo.orderId\"\r\n\t\t\t\t\t\tclass='bnt cancel' v-else-if=\"orderInfo.paid === true && orderInfo.refundStatus === 0 && orderInfo.type!==1\">申请退款\r\n\t\t\t\t\t</navigator>\r\n\t\t\t\t\t<view class='bnt bg_color' v-if=\"orderInfo.combinationId > 0\" @tap='goJoinPink'>查看拼团</view>\r\n\t\t\t\t\t<navigator class='bnt cancel' v-if=\"orderInfo.deliveryType == 'express' && orderInfo.status >0\"\r\n\t\t\t\t\t\thover-class='none' :url=\"'/pages/users/goods_logistics/index?orderId='+ orderInfo.orderId\">查看物流\r\n\t\t\t\t\t</navigator>\r\n\t\t\t\t\t<view class='bnt bg_color' v-if=\"orderInfo.status==1\" @tap='confirmOrder'>确认收货</view>\r\n\t\t\t\t\t<view class='bnt cancel' v-if=\"orderInfo.status==3\" @tap='delOrder'>删除订单</view>\r\n\t\t\t\t\t<view class='bnt bg_color' v-if=\"orderInfo.status==3 && orderInfo.type!==1 && againStatus !== 1 \" @tap='goOrderConfirm'>再次购买</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\r\n\t\t</view>\r\n\t\t<!-- <home></home> -->\r\n\t\t<!-- #ifdef MP -->\r\n\t\t<!-- <authorize @onLoadFun=\"onLoadFun\" :isAuto=\"isAuto\" :isShowAuth=\"isShowAuth\" @authColse=\"authColse\"></authorize> -->\r\n\t\t<!-- #endif -->\r\n\t\t<payment :payMode='payMode' :pay_close=\"pay_close\" @onChangeFun='onChangeFun' :order_id=\"pay_order_id\"\r\n\t\t\t:totalPrice='totalPrice'></payment>\r\n\t</view>\r\n</template>\r\n<script>\r\n\timport {\r\n\t\tgetOrderDetail,\r\n\t\torderTake,\r\n\t\torderDel,\r\n\t\torderCancel,\r\n\t\tqrcodeApi\r\n\t} from '@/api/order.js';\r\n\timport home from '@/components/home';\r\n\timport payment from '@/components/payment';\r\n\timport orderGoods from \"@/components/orderGoods\";\r\n\timport ClipboardJS from \"@/plugin/clipboard/clipboard.js\";\r\n\timport {toLogin} from '@/libs/login.js';\r\n\timport {mapGetters} from \"vuex\";\r\n\t// #ifdef MP\r\n\timport authorize from '@/components/Authorize';\r\n\timport uQRCode from '@/js_sdk/Sansnn-uQRCode/uqrcode.js'\r\n\t// #endif\r\n\timport {setThemeColor} from '@/utils/setTheme.js'\r\n\timport {Debounce} from '@/utils/validate.js'\r\n\tconst app = getApp();\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tpayment,\r\n\t\t\thome,\r\n\t\t\torderGoods,\r\n\t\t\t// #ifdef MP\r\n\t\t\tauthorize\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcodeImg: '',\r\n\t\t\t\tqrcodeSize: 100,\r\n\t\t\t\torder_id: '',\r\n\t\t\t\tevaluate: 0,\r\n\t\t\t\tcartInfo: [], //购物车产品\r\n\t\t\t\torderInfo: {\r\n\t\t\t\t\tsystemStore: {},\r\n\t\t\t\t\tpstatus: {}\r\n\t\t\t\t}, //订单详情\r\n\t\t\t\tsystem_store: {},\r\n\t\t\t\tisGoodsReturn: false, //是否为退款订单\r\n\t\t\t\tstatus: {}, //订单底部按钮状态\r\n\t\t\t\tisClose: false,\r\n\t\t\t\tpayMode: [{\r\n\t\t\t\t\t\tname: \"微信支付\",\r\n\t\t\t\t\t\ticon: \"icon-weixinzhifu\",\r\n\t\t\t\t\t\tvalue: 'weixin',\r\n\t\t\t\t\t\ttitle: '微信快捷支付',\r\n\t\t\t\t\t\tpayStatus: 1,\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: \"余额支付\",\r\n\t\t\t\t\t\ticon: \"icon-yuezhifu\",\r\n\t\t\t\t\t\tvalue: 'yue',\r\n\t\t\t\t\t\ttitle: '可用余额:',\r\n\t\t\t\t\t\tnumber: 0,\r\n\t\t\t\t\t\tpayStatus: 1,\r\n\t\t\t\t\t},\r\n\t\t\t\t\t// #ifndef MP\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"name\": \"支付宝支付\",\r\n\t\t\t\t\t\t\"icon\": \"icon-zhifubao\",\r\n\t\t\t\t\t\tvalue: 'alipay',\r\n\t\t\t\t\t\ttitle: '支付宝快捷支付',\r\n\t\t\t\t\t\tpayStatus: 1,\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t],\r\n\t\t\t\tpay_close: false,\r\n\t\t\t\tpay_order_id: '',\r\n\t\t\t\ttotalPrice: '0',\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false, //是否隐藏授权\r\n\t\t\t\tid: 0, //订单id\r\n\t\t\t\tuniId: '',\r\n\t\t\t\tutils: this.$util,\r\n\t\t\t\tagainStatus:0,\r\n\t\t\t\ttype: 'normal',\r\n\t\t\t\tisShow:true,\r\n\t\t\t\ttheme:app.globalData.theme,\r\n\t\t\t\tbgColor:'#e93323',\r\n\t\t\t\tchatConfig:{\r\n\t\t\t\t\tconsumer_hotline:'',\r\n\t\t\t\t\ttelephone_service_switch:'false'\r\n\t\t\t\t} //客服配置\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: mapGetters(['isLogin', 'chatUrl', 'userInfo']),\r\n\t\tonLoad: function(options) {\r\n\t\t\toptions.type == undefined || options.type == null ? this.type = 'normal' : this.type = options.type;\r\n\t\t\tif (!options.order_id && !options.uniId) return this.$util.Tips({\r\n\t\t\t\ttitle: '缺少参数'\r\n\t\t\t}, {\r\n\t\t\t\ttab: 3,\r\n\t\t\t\turl: 1\r\n\t\t\t});\r\n\t\t\tthis.$set(this, 'order_id', options.order_id);\r\n\t\t\tlet that = this;\r\n\t\t\tthat.bgColor = setThemeColor();\r\n\t\t\tuni.setNavigationBarColor({\r\n\t\t\t\tfrontColor: '#ffffff',\r\n\t\t\t\tbackgroundColor:that.bgColor,\r\n\t\t\t});\r\n\t\t\tthat.$set(that,'chatConfig',that.$Cache.getItem('chatConfig'));\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tif (this.isLogin) {\r\n\t\t\t\tthis.getOrderInfo();\r\n\t\t\t\tthis.payMode[1].number = this.userInfo.nowMoney;\r\n\t\t\t\tthis.$set(this, 'payMode', this.payMode);\r\n\t\t\t} else {\r\n\t\t\t\ttoLogin();\r\n\t\t\t}\r\n\t\t},\r\n\t\tonHide: function() {\r\n\t\t\tthis.isClose = true;\r\n\t\t},\r\n\t\tonReady: function() {\r\n\t\t\t// #ifdef H5\r\n\t\t\tthis.$nextTick(function() {\r\n\t\t\t\tconst clipboard = new ClipboardJS(\".copy-data\");\r\n\t\t\t\tclipboard.on(\"success\", () => {\r\n\t\t\t\t\tthis.$util.Tips({\r\n\t\t\t\t\t\ttitle: '复制成功'\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t\t// #endif\r\n\t\t\t \r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\t// #ifdef H5\r\n\t\t\tif(this.$wechat.isWeixin()) this.payMode.pop();\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tkefuClick() {\r\n\t\t\t\tif(this.chatConfig.telephone_service_switch === 'true'){\r\n\t\t\t\t\tuni.makePhoneCall({\r\n\t\t\t\t\t    phoneNumber: this.chatConfig.consumer_hotline //仅为示例\r\n\t\t\t\t\t});\r\n\t\t\t\t}else{\r\n\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/users/web_page/index?webUel=' + this.chatUrl + '&title=客服'\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifndef APP-PLUS\r\n\t\t\t\t\tlocation.href = this.chatUrl;\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 事件回调\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tonChangeFun: function(e) {\r\n\t\t\t\tlet opt = e;\r\n\t\t\t\tlet action = opt.action || null;\r\n\t\t\t\tlet value = opt.value != undefined ? opt.value : null;\r\n\t\t\t\t(action && this[action]) && this[action](value);\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 拨打电话\r\n\t\t\t */\r\n\t\t\tmakePhone: function() {\r\n\t\t\t\tuni.makePhoneCall({\r\n\t\t\t\t\tphoneNumber: this.system_store.phone\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 打开地图\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tshowMaoLocation: function() {\r\n\t\t\t\tif (!this.system_store.latitude || !this.system_store.longitude) return this.$util.Tips({\r\n\t\t\t\t\ttitle: '缺少经纬度信息无法查看地图！'\r\n\t\t\t\t});\r\n\t\t\t\tuni.openLocation({\r\n\t\t\t\t\tlatitude: parseFloat(this.system_store.latitude),\r\n\t\t\t\t\tlongitude: parseFloat(this.system_store.longitude),\r\n\t\t\t\t\tscale: 8,\r\n\t\t\t\t\tname: this.system_store.name,\r\n\t\t\t\t\taddress: this.system_store.address + this.system_store.detailedAddress,\r\n\t\t\t\t\tsuccess: function() {\r\n\r\n\t\t\t\t\t},\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 关闭支付组件\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tpayClose: function() {\r\n\t\t\t\tthis.pay_close = false;\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 打开支付组件\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tpay_open: function() {\r\n\t\t\t\tthis.pay_close = true;\r\n\t\t\t\tthis.pay_order_id = this.orderInfo.orderId;\r\n\t\t\t\tthis.totalPrice = this.orderInfo.payPrice;\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 支付成功回调\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tpay_complete: function() {\r\n\t\t\t\tthis.pay_close = false;\r\n\t\t\t\tthis.pay_order_id = '';\r\n\t\t\t\tthis.getOrderInfo();\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 支付失败回调\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tpay_fail: function() {\r\n\t\t\t\tthis.pay_close = false;\r\n\t\t\t\tthis.pay_order_id = '';\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 登录授权回调\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tonLoadFun: function() {\r\n\t\t\t\tthis.getOrderInfo();\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 获取订单详细信息\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tgetOrderInfo: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: \"正在加载中\"\r\n\t\t\t\t});\r\n\t\t\t\tgetOrderDetail(that.order_id).then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthat.$set(that, 'orderInfo', res.data);\r\n\t\t\t\t\tthat.$set(that, 'evaluate', res.data.status == 2 ? 2 : 0);\r\n\t\t\t\t\tthat.$set(that, 'system_store', res.data.systemStore);\r\n\t\t\t\t\tthat.$set(that, 'id', res.data.id);\r\n\t\t\t\t\tthat.$set(that, 'cartInfo', res.data.orderInfoList);\r\n\t\t\t\t\tif (res.data.refundStatus != 0) {\r\n\t\t\t\t\t\tthat.isGoodsReturn = true;\r\n\t\t\t\t\t};\r\n\t\t\t\t\tif (that.orderInfo.shippingType == 2 && that.orderInfo.paid) that.markCode(res.data\r\n\t\t\t\t\t\t.verifyCode);\r\n\t\t\t\t\tif(that.orderInfo.refundStatus>0){\r\n\t\t\t\t\t\tuni.setNavigationBarColor({\r\n\t\t\t\t\t\t    frontColor: '#fff',\r\n\t\t\t\t\t\t    backgroundColor: '#666666'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\t\r\n\t\t\t\t\tif(res.data.combinationId > 0 || res.data.bargainId > 0 ||res.data.seckillId > 0 ){\r\n\t\t\t\t\t\tthis.againStatus = 1;\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t},{\r\n\t\t\t\t\t\ttab: 4,\r\n\t\t\t\t\t\turl: '/pages/user/index'\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * \r\n\t\t\t * 生成二维码\r\n\t\t\t */\r\n\t\t\tmarkCode(text) {\r\n\t\t\t\tqrcodeApi({\r\n\t\t\t\t\theight: '145',\r\n\t\t\t\t\ttext: text,\r\n\t\t\t\t\twidth: '145'\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tthis.codeImg = res.data.code\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * \r\n\t\t\t * 剪切订单号\r\n\t\t\t */\r\n\t\t\t// #ifndef H5\r\n\t\t\t\r\n\t\t\t\r\n\t\t\tcopy: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\t\r\n\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\tdata: this.orderInfo.orderId\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t\r\n\t\t\tcopyDeliveryId: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\tdata: this.orderInfo.deliveryId\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// #endif\r\n\t\t\t/**\r\n\t\t\t * 打电话\r\n\t\t\t */\r\n\t\t\tgoTel: function() {\r\n\t\t\t\tuni.makePhoneCall({\r\n\t\t\t\t\tphoneNumber: this.orderInfo.deliveryId\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 设置底部按钮\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tgetOrderStatus: function() {\r\n\t\t\t\tlet orderInfo = this.orderInfo || {},\r\n\t\t\t\t\t_status = orderInfo.pstatus || {\r\n\t\t\t\t\t\ttype: 0\r\n\t\t\t\t\t},\r\n\t\t\t\t\tstatus = {};\r\n\t\t\t\tlet type = parseInt(_status.type),\r\n\t\t\t\t\tdelivery_type = orderInfo.deliveryType,\r\n\t\t\t\t\tseckill_id = orderInfo.seckillId ? parseInt(orderInfo.seckillId) : 0,\r\n\t\t\t\t\tbargain_id = orderInfo.bargainId ? parseInt(orderInfo.bargainId) : 0,\r\n\t\t\t\t\tcombination_id = orderInfo.combinationId ? parseInt(orderInfo.combinationId) : 0;\r\n\t\t\t\tstatus = {\r\n\t\t\t\t\ttype: type == 9 ? -9 : type,\r\n\t\t\t\t\tclass_status: 0\r\n\t\t\t\t};\r\n\t\t\t\tif (type == 1 && combination_id > 0) status.class_status = 1; //查看拼团\r\n\t\t\t\tif (type == 2 && delivery_type == 'express') status.class_status = 2; //查看物流\r\n\t\t\t\tif (type == 2) status.class_status = 3; //确认收货\r\n\t\t\t\tif (type == 4 || type == 0) status.class_status = 4; //删除订单\r\n\t\t\t\tif (!seckill_id && !bargain_id && !combination_id && (type == 3 || type == 4)) status.class_status =\r\n\t\t\t\t5; //再次购买\r\n\t\t\t\tthis.$set(this, 'status', status);\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 去拼团详情\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tgoJoinPink: function() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/activity/goods_combination_status/index?id=' + this.orderInfo.pinkId,\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 再此购买\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tgoOrderConfirm: Debounce(function() {\r\n\t\t\t\tthis.$Order.getPreOrder(\"again\",[{\r\n\t\t\t\t\torderNo: this.order_id\r\n\t\t\t\t}]);\r\n\t\t\t}),\r\n\t\t\tconfirmOrder: Debounce(function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '确认收货',\r\n\t\t\t\t\tcontent: '为保障权益，请收到货确认无误后，再确认收货',\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\torderTake(that.id).then(res => {\r\n\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\ttitle: '操作成功',\r\n\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t}, function() {\r\n\t\t\t\t\t\t\t\t\tthat.getOrderInfo();\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}),\r\n\t\t\t/**\r\n\t\t\t * \r\n\t\t\t * 删除订单\r\n\t\t\t */\r\n\t\t\tdelOrder: Debounce(function() {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\tcontent: '确定删除该订单',\r\n\t\t\t\t\tcancelText: \"取消\", \r\n\t\t\t\t\tconfirmText: \"确定\", \r\n\t\t\t\t\tshowCancel: true, \r\n\t\t\t\t\tconfirmColor: '#f55850',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif(res.confirm) {  \r\n\t\t\t\t\t\t\tlet that = this;\r\n\t\t\t\t\t\t\torderDel(this.id).then(res => {\r\n\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\ttitle: '删除成功',\r\n\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\t\ttab: 4,\r\n\t\t\t\t\t\t\t\t\turl: '/pages/user/index'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else {  \r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t}  \r\n\t\t\t\t\t} \r\n\t\t\t\t})\r\n\t\t\t}),\r\n\t\t\tcancelOrder:Debounce(function(){\r\n\t\t\t\tlet self = this\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\tcontent: '确认取消该订单?',\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\torderCancel(self.orderInfo.id)\r\n\t\t\t\t\t\t\t\t.then((data) => {\r\n\t\t\t\t\t\t\t\t\tself.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '取消成功'\r\n\t\t\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\t\ttab: 4,\r\n\t\t\t\t\t\t\t\t\turl: '/pages/user/index'\r\n\t\t\t\t\t\t\t\t   })\r\n\t\t\t\t\t\t\t\t}).catch((err) => {\r\n\t\t\t\t\t\t\t\t\tself.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\tself.getDetail();\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}) \r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.qs-btn {\r\n\t\twidth: auto;\r\n\t\theight: 60rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 60rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 27rpx;\r\n\t\tpadding: 0 3%;\r\n\t\tcolor: #aaa;\r\n\t\tborder: 1px solid #ddd;\r\n\t\tmargin-right: 20rpx;\r\n\t}\r\n\t.text-overflow{\r\n\t\twidth: 300rpx;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: nowrap;\r\n\t}\r\n\t.shuoming{\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t}\r\n\t.mp-header{\r\n\t\twidth: 100%;\r\n\t\t@include main_bg_color(theme);\r\n\t}\r\n\t.goodCall {\r\n\t\t@include main_color(theme);\r\n\t\ttext-align: center;\r\n\t\twidth: 100%;\r\n\t\theight: 86rpx;\r\n\t\tpadding: 0 30rpx;\r\n\t\tborder-bottom: 1rpx solid #eee;\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 86rpx;\r\n\t\tbackground: #fff;\r\n\r\n\t\t.icon-kefu {\r\n\t\t\tfont-size: 36rpx;\r\n\t\t\tmargin-right: 15rpx;\r\n\t\t}\r\n\r\n\t\t/* #ifdef MP */\r\n\t\tbutton {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\theight: 86rpx;\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\t@include main_color(theme);\r\n\t\t}\r\n\r\n\t\t/* #endif */\r\n\t}\r\n\t.justify-between {\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\t.align-center{\r\n\t\talign-items: center;\r\n\t}\r\n\t.order-details .header {\r\n\t\theight: 250rpx;\r\n\t\tpadding: 0 30rpx;\r\n\t}\r\n\t.bg_color{\r\n\t\t@include main_bg_color(theme);\r\n\t}\r\n\t.order-details .header.on {\r\n\t\tbackground-color: #666 !important;\r\n\t}\r\n\r\n\t.order-details .header .pictrue {\r\n\t\twidth: 110rpx;\r\n\t\theight: 110rpx;\r\n\t}\r\n\r\n\t.order-details .header .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.order-details .header .data {\r\n\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t\tfont-size: 24rpx;\r\n\t\tmargin-left: 27rpx;\r\n\t}\r\n\r\n\t.order-details .header .data.on {\r\n\t\tmargin-left: 0;\r\n\t}\r\n\r\n\t.order-details .header .data .state {\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #fff;\r\n\t\tmargin-bottom: 7rpx;\r\n\t}\r\n\r\n\t.order-details .header .data .time {\r\n\t\tmargin-left: 20rpx;\r\n\t}\r\n\r\n\t.picTxt {\r\n\t\theight: 150rpx;\r\n\t}\r\n\r\n\t.order-details .nav {\r\n\t\tbackground-color: #fff;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #282828;\r\n\t\tpadding: 27rpx 0;\r\n\t\twidth: 100%;\r\n\t\tborder-radius: 14rpx;\r\n\t\tmargin: -100rpx auto 0 auto;\r\n\t}\r\n\r\n\t.order-details .nav .navCon {\r\n\t\tpadding: 0 40rpx;\r\n\t}\r\n\r\n\t.order-details .nav .on {\r\n\t\t@include main_color(theme);\r\n\t}\r\n\t.font_color{\r\n\t\t@include main_color(theme);\r\n\t}\r\n\t.order-details .nav .progress {\r\n\t\tpadding: 0 65rpx;\r\n\t\tmargin-top: 10rpx;\r\n\t}\r\n\r\n\t.order-details .nav .progress .line {\r\n\t\twidth: 100rpx;\r\n\t\theight: 2rpx;\r\n\t\tbackground-color: #939390;\r\n\t}\r\n\r\n\t.order-details .nav .progress .iconfont {\r\n\t\tfont-size: 25rpx;\r\n\t\tcolor: #939390;\r\n\t\tmargin-top: -2rpx;\r\n\t}\r\n\r\n\t.order-details .address {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #868686;\r\n\t\tbackground-color: #fff;\r\n\t\tmargin-top: 15rpx;\r\n\t\tpadding: 30rpx 24rpx;\r\n\t}\r\n\r\n\t.order-details .address .name {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #282828;\r\n\t\tmargin-bottom: 15rpx;\r\n\t}\r\n\r\n\t.order-details .address .name .phone {\r\n\t\tmargin-left: 40rpx;\r\n\t}\r\n\r\n\t.order-details .line {\r\n\t\twidth: 100%;\r\n\t\theight: 3rpx;\r\n\t}\r\n\r\n\t.order-details .line image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tdisplay: block;\r\n\t}\r\n\t.order-details .wrapper {\r\n\t\tbackground-color: #fff;\r\n\t\tmargin-top: 12rpx;\r\n\t\tpadding: 30rpx 24rpx;\r\n\t}\r\n\r\n\t.order-details .wrapper .item {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #282828;\r\n\t}\r\n\r\n\t.order-details .wrapper .item~.item {\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\r\n\t.order-details .wrapper .item .conter {\r\n\t\tcolor: #868686;\r\n\t\twidth: 470rpx;\r\n\t\ttext-align: right;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: nowrap;\r\n\t}\r\n\t.mark_show{\r\n\t\tcolor: #868686;\r\n\t\twidth: 470rpx;\r\n\t\ttext-align: right;\r\n\t}\r\n\t.order-details .wrapper .item .conter .copy {\r\n\t\tfont-size: 20rpx;\r\n\t\tcolor: #333;\r\n\t\tborder-radius: 20rpx;\r\n\t\tborder: 1rpx solid #666;\r\n\t\tpadding: 3rpx 15rpx;\r\n\t\tmargin-left: 24rpx;\r\n\t}\r\n\t\r\n\t\r\n\t\r\n\r\n\t.order-details .wrapper .actualPay {\r\n\t\tborder-top: 1rpx solid #eee;\r\n\t\tmargin-top: 30rpx;\r\n\t\tpadding-top: 30rpx;\r\n\t}\r\n\r\n\t.order-details .wrapper .actualPay .money {\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 30rpx;\r\n\t\t@include price_color(theme);\r\n\t}\r\n\r\n\t.order-details .footer {\r\n\t\twidth: 100%;\r\n\t\theight: 100rpx;\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\t\tleft: 0;\r\n\t\tbackground-color: #fff;\r\n\t\tpadding-right: 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.order-details .footer .bnt {\r\n\t\twidth: 158rpx;\r\n\t\theight: 54rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 54rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 27rpx;\r\n\t}\r\n\r\n\t.order-details .footer .bnt.cancel {\r\n\t\tcolor: #aaa;\r\n\t\tborder: 1rpx solid #ddd;\r\n\t}\r\n\r\n\t.order-details .footer .bnt~.bnt {\r\n\t\tmargin-left: 18rpx;\r\n\t}\r\n\r\n\t.order-details .writeOff {\r\n\t\tbackground-color: #fff;\r\n\t\tmargin-top: 15rpx;\r\n\t\tpadding-bottom: 50rpx;\r\n\t}\r\n\r\n\t.order-details .writeOff .title {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #282828;\r\n\t\theight: 87rpx;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t\tpadding: 0 24rpx;\r\n\t\tline-height: 87rpx;\r\n\t}\r\n\r\n\t.order-details .writeOff .grayBg {\r\n\t\tbackground-color: #f2f5f7;\r\n\t\twidth: 590rpx;\r\n\t\theight: 384rpx;\r\n\t\tborder-radius: 20rpx 20rpx 0 0;\r\n\t\tmargin: 50rpx auto 0 auto;\r\n\t\tpadding-top: 55rpx;\r\n\t}\r\n\r\n\t.order-details .writeOff .grayBg .pictrue {\r\n\t\twidth: 290rpx;\r\n\t\theight: 290rpx;\r\n\t\tmargin: 0 auto;\r\n\t}\r\n\r\n\t.order-details .writeOff .grayBg .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.order-details .writeOff .gear {\r\n\t\twidth: 590rpx;\r\n\t\theight: 30rpx;\r\n\t\tmargin: 0 auto;\r\n\t}\r\n\r\n\t.order-details .writeOff .gear image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.order-details .writeOff .num {\r\n\t\tbackground-color: #f0c34c;\r\n\t\twidth: 590rpx;\r\n\t\theight: 84rpx;\r\n\t\tcolor: #282828;\r\n\t\tfont-size: 48rpx;\r\n\t\tmargin: 0 auto;\r\n\t\tborder-radius: 0 0 20rpx 20rpx;\r\n\t\ttext-align: center;\r\n\t\tpadding-top: 4rpx;\r\n\t}\r\n\r\n\t.order-details .writeOff .rules {\r\n\t\tmargin: 46rpx 30rpx 0 30rpx;\r\n\t\tborder-top: 1px solid #f0f0f0;\r\n\t\tpadding-top: 10rpx;\r\n\t}\r\n\r\n\t.order-details .writeOff .rules .item {\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\r\n\t.order-details .writeOff .rules .item .rulesTitle {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #282828;\r\n\t}\r\n\r\n\t.order-details .writeOff .rules .item .rulesTitle .iconfont {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #333;\r\n\t\tmargin-right: 8rpx;\r\n\t\tmargin-top: 5rpx;\r\n\t}\r\n\r\n\t.order-details .writeOff .rules .item .info {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #999;\r\n\t\tmargin-top: 7rpx;\r\n\t}\r\n\r\n\t.order-details .writeOff .rules .item .info .time {\r\n\t\tmargin-left: 20rpx;\r\n\t}\r\n\r\n\t.order-details .map {\r\n\t\theight: 86rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #282828;\r\n\t\tline-height: 86rpx;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t\tmargin-top: 15rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tpadding: 0 24rpx;\r\n\t}\r\n\r\n\t.order-details .map .place {\r\n\t\tfont-size: 26rpx;\r\n\t\twidth: 176rpx;\r\n\t\theight: 50rpx;\r\n\t\tborder-radius: 25rpx;\r\n\t\tline-height: 50rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.order-details .map .place .iconfont {\r\n\t\tfont-size: 27rpx;\r\n\t\theight: 27rpx;\r\n\t\tline-height: 27rpx;\r\n\t\tmargin: 2rpx 3rpx 0 0;\r\n\t}\r\n\r\n\t.order-details .address .name .iconfont {\r\n\t\tfont-size: 34rpx;\r\n\t\tmargin-left: 10rpx;\r\n\t}\r\n\r\n\t.refund {\r\n\t\tpadding:  0 !important;\r\n\t\tmargin-top: 15rpx;\r\n\t\tbackground-color: #fff;\r\n\r\n\t\t.title {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tcolor: #333;\r\n\t\t\theight: 86rpx;\r\n\t\t\tborder-bottom: 1px solid #f5f5f5;\r\n            font-weight: 400;\r\n\t\t\tpadding: 0 24rpx;\r\n\t\t\t\r\n\t\t\timage {\r\n\t\t\t\twidth: 32rpx;\r\n\t\t\t\theight: 32rpx;\r\n\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.con {\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: #666666;\r\n\t\t\tpadding: 30rpx 24rpx;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=251d72aa&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=251d72aa&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294180334\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}