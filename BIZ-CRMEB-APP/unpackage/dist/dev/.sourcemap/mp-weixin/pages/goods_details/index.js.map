{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_details/index.vue?b194", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_details/index.vue?491e", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_details/index.vue?605f", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_details/index.vue?b512", "uni-app:///pages/goods_details/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_details/index.vue?09f8", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/goods_details/index.vue?4501"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "productConSwiper", "couponListWindow", "productWindow", "userEvaluation", "shareRedPackets", "cusPreviewImg", "authorize", "data", "showSkeleton", "isNodes", "coupon", "type", "list", "count", "attrTxt", "attrValue", "animated", "id", "replyCount", "reply", "productInfo", "productValue", "couponList", "cart_num", "isAuto", "isShowAuth", "isOpen", "actionSheetHidden", "storeImage", "PromotionCode", "posterbackgd", "sharePacket", "isState", "touchstart", "circular", "autoplay", "interval", "duration", "clientHeight", "systemStore", "good_list", "replyC<PERSON>ce", "CartCount", "isDown", "posters", "weixin<PERSON>tatus", "attr", "cartAttr", "productAttr", "productSelect", "description", "navActive", "H5ShareBox", "activityH5", "retunTop", "navH", "navList", "opacity", "scrollY", "topArr", "to<PERSON>ie<PERSON>", "height", "heightArr", "lock", "scrollTop", "tagStyle", "img", "table", "video", "sliderImage", "videoLink", "qrcodeSize", "canvasStatus", "imagePath", "imgTop", "errT", "homeTop", "navbarRight", "userCollect", "options", "returnShow", "theme", "indicatorBg", "shareStatus", "sku<PERSON><PERSON>", "currentPage", "selectSku", "selectNavList", "name", "icon", "url", "after", "chatConfig", "consumer_hotline", "telephone_service_switch", "defaultCoupon", "couponDeaultType", "useType", "computed", "watch", "is<PERSON>ogin", "handler", "that", "deep", "immediate", "onLoad", "uni", "success", "title", "app", "onReady", "query", "select", "boundingClientRect", "exec", "onShareAppMessage", "imageUrl", "path", "methods", "kefuClick", "phoneNumber", "location", "goActivity", "iptCartNum", "returns", "showNav", "tap", "scroll", "goDetail", "onLoadFun", "ChangCouponsClone", "ChangeCartNum", "num", "attrVal", "ChangeAttr", "Chang<PERSON>ou<PERSON>ns", "setClientHeight", "view", "size", "getGoods", "getGoodsDetails", "attrName", "attrV<PERSON>ues", "isDel", "productId", "setTimeout", "tab", "getProductReplyList", "getProductReplyCount", "infoScroll", "DefaultSelect", "value", "getCouponList", "obj", "page", "limit", "getCouponType", "dataList", "tabCouponType", "ChangCouponsUseState", "setCollect", "selecAttr", "couponTap", "onMyEvent", "joinCart", "goCat", "cartNum", "isNew", "productAttrUnique", "catch", "getCartCount", "goBuy", "getPreOrder", "auth<PERSON><PERSON><PERSON>", "listenerActionSheet", "closePosters", "posterImageClose", "setDomain", "downloadFilestoreImage", "fail", "goFriend", "getQrcode", "pid", "make", "uQRCode", "canvasId", "text", "margin", "complete", "getImageBase64", "downloadFilePromotionCode", "getProductCode", "then", "tempFile<PERSON>ath", "go<PERSON><PERSON><PERSON>", "mask", "arrImagesUrlTop", "otPrice", "getpreviewImage", "photoList", "urls", "current", "savePosterPath", "scope", "filePath", "ShareInfo", "href", "desc", "link", "imgUrl", "console", "showShare", "<PERSON><PERSON><PERSON>", "linkPage", "showImg", "changeSwitch", "skuList", "getFileType", "suffix", "result", "videoPause"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,aAAa,4NAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjEA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC+TnnB;AAEA;AACA;AACA;AACA;AAUA;AACA;AACA;AACA;AACA;AAOA;AAIA;AAKA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAnCA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAiCA;AAAA,eAGA;EACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACA;IAEAC;EAEA;EACAC;IACA;IACA;MACAC;MAAA;MACAC;MAAA;MACA;MACAC;QACAA;QACAC;QACAC;QACAC;MACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;QACAC;QAAA;QACAC;MACA;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACArE;MAAA;MACAsE;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,gBACA;QAAAC;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;MAAA,EACA;MACAE;QACAC;QACAC;MACA;MAAA;MACAC;MACAC,mBACA;QAAAC;MAAA;IAEA;EACA;EACAC;EACAC;IACAC;MACAC;QACA;QACA;UACAC;UACAA;QACA;MACA;MACAC;IACA;IACAnF;MACAiF;QACA;MACA;MACAG;IACA;EACA;EACAC;IACA;IACA;IACA;IACAH;IACA;MACAA;IACA;IACAA;IACAA;IAIAA;IACAI;MACAC;QACAL;QACA;;QAEA;MAEA;IACA;;IACA;MACA;MACA;QACAM;MACA;QACAlB;MACA;MACA;IACA;IACA;MACA;QAAA;QACA;QACA;QACAmB;QACA;QACA;QACA;QACA;MAEA;QACA;MACA;MACA9B;MACAuB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACA;IACA;IACA;EACA;EACAQ;IACA;IACA;MAAA;MAEA;MACA;MACAC,MACAC,gBACAC;QACA;MACA,GACAC;IAKA;EACA;EACA;AACA;AACA;;EAEAC;IACA;IACAb;IACA;MACAM;MACAQ;MACAC;IACA;EACA;EAEAC;IA4BAC;MACA;QACAb;UACAc;QACA;MACA;QAOAC;MAEA;IACA;IACAC;MACA;MACA;QACAhB;UACAhB;QACA;MACA;QACAgB;UACAhB;QACA;MACA;QACAgB;UACAhB;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAiC;MACA;IACA;IACA;IACAC;MACAlB;IACA;IACAmB;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA,kGACAnE;IACA;IACAoE;MACA;QACArE;MACA;MACAD;MACA6C;MACAA;MACA;QACAA;QACA;MACA;MACA;QACA;UACAA;UACA;QACA;MACA;MACAA;IACA;IACA;AACA;AACA;IACA0B;MACA;QACAtB;UACAhB;QACA;QACA;MACA;MACA;QACAgB;UACAhB;QACA;QACA;MACA;MACA;MACA;QACAgB;UACAhB;QACA;QACA;MACA;MACA;MACA;QACAgB;UACAhB;QACA;QACA;MACA;MACA;MACA;QACA;QACAgB;UACAhB;QACA;QACA;MACA;IACA;IACA;IACAuC;MACA;MACA;IACA;IACAC;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;MACA,kEACAlF;MACA;MACA;MACA;MACA;MACA;QACAmF;QACA;UACA;UACA;QACA;MACA;QACAA;QACA;UACA;UACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACAC;QACAC;MACA;QACApC;MACA;IACA;IACA;AACA;AACA;AACA;IACAqC;MAAA;MACA;QACA;QACA;QACA;QACA;UACA;UACA;YACA/H;UACA;QACA;QACA;QACA;QACA;UACA4C;QACA;QACA;QACA;UACA;YAEA;UAOA;UAAA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAoF;MAAA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;UACA;UACA;UACAvE;QACA;QACAiC;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;UACA;UACAA;QACA;QACA;QACAA;QACA;QACAA;QACAA;QACAI;UACAE;QACA;QAEA;UACA;YACAiC;YACAC;YACA7H;YACA8H;YACAC;YACArI;UACA;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;UACA2F;UAOAA;QAEA;QAAA;QACA2C;UACA3C;QACA;QAEAA;QAGAA;QAEAA;QACA2C;UACA;UACA;QACA;MACA;QACA;QACA3C;UACAM;QACA;UACAsC;UACAxD;QACA;QACAuD;UACA;QACA;MACA;IACA;IACAE;MAAA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACA9C;QACAA;MACA;IACA;IACA+C;MACA;QACA1F;QACAG;MACA;QAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACAiD;QACAA;UACA;UACA;UACApD;UACAG;UACAwC;UACAA;QACA;MACA;MAAA;IACA;IACA;AACA;AACA;AACA;IACAgD;MACA;MACA;MACA;MACAtG;QACAuG;MACA;MACA;QACA;MACA;;MAEA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;QACAC;UACAC;UACAC;UACAX;UACArI;QACA;MACA;QACA8I;MACA;QACAA;MACA;MACA;QACAnD;MACA;IACA;IACAsD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;kBAAAZ;gBAAA;cAAA;gBAAAa;gBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAC;MACA;MACA;IACA;IAEAC;MACA;MACAzD;MACAA;MACAA;IACA;IACA;AACA;AACA;AACA;AACA;IACA0D;MACA;MACA;QACA;MACA;QACA;UACA;YACA1D;UACA;QACA;UACA;YACAA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACA2D;MACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;QACA;MACA;QACA5D;QACAA;MACA;IACA;IACA6D;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;QACApH;MACA;MACA;QACA;QACAqD;MACA;QACA,gDACAA;MACA;MACA;MACA,0DACA;MACA;MACA,IACAA,gCACArD,6BACAqD,sBAEA;QACAM;MACA;MACA;QACA;UACAoC;UACAsB;UACAC;UACAC,2DACAlE;QACA;QACA;UACAA;UACAA;UACAA;YACAM;YACAD;cACAL;YACA;UACA;QACA,GACAmE;UACAnE;UACA;YACAM;UACA;QACA;MACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACA8D;MACA;MACA;MACA;QACA;UACApE;UACA;UACA;YACAA;YACA2C;cACA3C;YACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAqE;MACA;QACA;MACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;QACA;MACA;QAMA;QACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACAvF;MACA;MACA,kDACA;IACA;IACA;IACAwF;MACA;MACAxE;QACAhB;QACAiB;UACAL;QACA;QACA6E;UACA;YACAvE;UACA;UACAN;QACA;MACA;IACA;IACA;IACA8E;MACA;IACA;IACA;IACAC;MACA;MACA;QACAC;QACArK;QACAoG;MACA;MACA;QACA;UACAf;QACA;MAEA;QACAA;MACA;IACA;IACA;IACAiF;MAAA;MACA;MACAC;QACAC;QACAC;QACAhD;QACAiD;QACAhF;UACA;QACA;QACAiF;QACAT;UACA;YACAvE;UACA;QACA;MACA;IACA;IACAiF;MACA;MACA;QACAnG;MACA;QACAY;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACAwF;MACA;MACAC,wBACAC;QACAtF;UACAhB;UACAiB;YACAL;YACA,+DACA2F,mBACA3F;UACA;UACA6E;YACA7E;YACAA;UACA;QACA;MACA,GACAmE;QACAnE;QACAA;MACA;IACA;IACA;AACA;AACA;IACA4F;MACA;MACAxF;QACAE;QACAuF;MACA;MACA7F;MACA;MACA;MACA;QACAI;QACAJ;UACAM;QACA;QACA;MACA;MACAqC;QACA;UACAvC;UACAJ;YACAM;UACA;UACA;QACA;MACA;MACAF;QACAhB;QAAA;QACAiB;UACAyF;UACA;UACA;UACA;UACAnD;YACA3C,sEACA+F,SACA;cACA/F;cACAA;cACAI;YACA;UACA;QACA;MACA;IACA;IACA;IACA4F;MACA;QACA;QACAC;QACA7F;UACA8F;UACAC;QACA;MACA;QACA;UACA7F;QACA;MACA;IACA;IACA;AACA;AACA;;IAEA8F;MACA;MACAhG;QACAC;UACA;YACAD;cACAiG;cACAhG;gBACAD;kBACAkG;kBACAjG;oBACAL;oBACAA;sBACAM;sBACAnB;oBACA;kBACA;kBACA0F;oBACA7E;sBACAM;oBACA;kBACA;gBACA;cACA;YACA;UACA;YACAF;cACAkG;cACAjG;gBACAL;gBACAA;kBACAM;kBACAnB;gBACA;cACA;cACA0F;gBACA7E;kBACAM;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEAiG;MACA;MACA;MACA;QACAC;QACA;UACAC;UACAnG;UACAoG;UACAC;QACA;QACA,0BACA,6BACA,2BACA,yBACA,sBACA;UACA;QAAA,CACA;UACAC;QACA;MACA;IACA;IACAC;MACA;MACA7G;IACA;IACA8G;MACA;IACA;IACA;IACAC;MACA;QACA3G;UACAhB;QACA;MACA;QACAgB;UACAhB;QACA;MACA;MACA;IACA;IACA;IACA4H;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACAC;QACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;QACA;QACAC;MACA;QACAA;MACA;MACA;MACA;QAAA;MAAA;MACAA;MACA;MACA;MACA;MACAC;QAAA;MAAA;MACA;QACA;MACA;MACA;MACA;MACAA;QAAA;MAAA;MACA;QACA;MACA;MACA;MACA;IACA;IACAC,mCAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvhDA;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/goods_details/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/goods_details/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=78ee64b3&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=78ee64b3&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"78ee64b3\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/goods_details/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=78ee64b3&scoped=true&\"", "var components\ntry {\n  components = {\n    jyfParser: function () {\n      return import(\n        /* webpackChunkName: \"components/jyf-parser/jyf-parser\" */ \"@/components/jyf-parser/jyf-parser.vue\"\n      )\n    },\n    cusPreviewImg: function () {\n      return import(\n        /* webpackChunkName: \"components/cus-previewImg/cus-previewImg\" */ \"@/components/cus-previewImg/cus-previewImg.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 =\n    Math.floor(_vm.productInfo.sales) + Math.floor(_vm.productInfo.ficti) || 0\n  var g1 = _vm.defaultCoupon.length > 0 && _vm.type == \"normal\"\n  var g2 = _vm.activityH5.length\n  var g3 = _vm.skuArr.length\n  var l0 = g3 > 1 ? _vm.skuArr.slice(0, 4) : null\n  var g4 = g3 > 1 ? _vm.skuArr.length : null\n  var g5 = _vm.type === \"normal\" ? Math.floor(_vm.CartCount) : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.H5ShareBox = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        l0: l0,\n        g4: g4,\n        g5: g5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :data-theme=\"theme\">\r\n\t\t<skeleton :show=\"showSkeleton\" :isNodes=\"isNodes\" ref=\"skeleton\" loading=\"chiaroscuro\" selector=\"skeleton\"\r\n\t\t\tbgcolor=\"#FFF\"></skeleton>\r\n\t\t<view class=\"product-con skeleton\" :style=\"{visibility: showSkeleton ? 'hidden' : 'visible'}\">\r\n\t\t\t<view class='navbar' :class=\"opacity>0.6?'bgwhite':''\">\r\n\t\t\t\t<view class='navbarH' :style='\"height:\"+navH+\"rpx;\"'>\r\n\t\t\t\t\t<view class='navbarCon acea-row' :style=\"{ paddingRight: navbarRight + 'px' }\">\r\n\t\t\t\t\t\t<!-- #ifdef MP -->\r\n\t\t\t\t\t\t<view class=\"select_nav flex justify-center align-center\" id=\"home\" :style=\"{ top: homeTop + 'rpx' }\">\r\n\t\t\t\t\t\t\t<text class=\"iconfont icon-fanhui2 px-20\" @tap=\"returns\"></text>\r\n\t\t\t\t\t\t\t<text class=\"iconfont icon-gengduo5 px-20\" @tap=\"showNav\"></text>\r\n\t\t\t\t\t\t\t<text class=\"nav_line\"></text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t<!-- #ifdef H5 || APP-PLUS -->\r\n\t\t\t\t\t\t<view id=\"home\" class=\"home acea-row row-center-wrapper iconfont icon-xiangzuo h5_back\" :class=\"opacity>0.5?'on':''\"\r\n\t\t\t\t\t\t\t:style=\"{ top: homeTop + 'rpx' }\" v-if=\"returnShow\" @tap=\"returns\">\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t<navigator url=\"/pages/goods_search/index\" class=\"input\" hover-class=\"none\" :style=\"{ top: homeTop + 'rpx' }\"><text\r\n\t\t\t\t\t\t\tclass=\"iconfont icon-xiazai5\"></text>\r\n\t\t\t\t\t\t搜索商品</navigator>\r\n\t\t\t\t\t\t<!-- #ifdef H5 || APP-PLUS -->\r\n\t\t\t\t\t\t<view class=\"right_select\" :style=\"{ top: homeTop + 'rpx' }\" @tap=\"showNav\">\r\n\t\t\t\t\t\t\t<text class=\"iconfont icon-gengduo2\"></text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tab_nav\" v-show=\"opacity > 0.6\">\r\n\t\t\t\t\t<view class=\"header flex justify-between align-center\">\r\n\t\t\t\t\t\t<view class=\"item\" :class=\"navActive === index ? 'on' : ''\" v-for=\"(item,index) in navList\"\r\n\t\t\t\t\t\t\t:key='index' @tap=\"tap(index)\">\r\n\t\t\t\t\t\t\t{{ item }}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"dialog_nav\" v-show=\"currentPage\" :style=\"{ top: navH + 'rpx' }\">\r\n\t\t\t\t<view class=\"dialog_nav_item\" :class=\"item.after\" v-for=\"(item,index) in selectNavList\" :key=\"index\" @click=\"linkPage(item.url)\">\r\n\t\t\t\t\t<text class=\"iconfont\" :class=\"item.icon\"></text>\r\n\t\t\t\t\t<text class=\"pl-20\">{{item.name}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"detail_container\" >\r\n\t\t\t\t<scroll-view :scroll-top=\"scrollTop\" scroll-y='true' scroll-with-animation=\"true\"\r\n\t\t\t\t\t:style='\"height:\"+height+\"px;\"' @scroll=\"scroll\">\r\n\t\t\t\t\t<view id=\"past0\">\r\n\t\t\t\t\t\t<productConSwiper class=\"skeleton-rect\" :imgUrls=\"sliderImage\" :videoline=\"videoLink\"\r\n\t\t\t\t\t\t\t@videoPause=\"videoPause\"></productConSwiper> \r\n\t\t\t\t\t\t<view class=\"pad30\">\r\n\t\t\t\t\t\t\t<view class='wrapper mb30 borRadius14'>\r\n\t\t\t\t\t\t\t\t<view class='share acea-row row-between row-bottom'>\r\n\t\t\t\t\t\t\t\t\t<view class='x-money skeleton-rect flex align-baseline'>￥\r\n\t\t\t\t\t\t\t\t\t\t<text class='num font-44' v-if=\"attr.productSelect.vipPrice && attr.productSelect.vipPrice >= 0\" >\r\n\t\t\t\t\t\t\t\t\t\t{{attr.productSelect.vipPrice}}</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class='num font-44' v-else>{{attr.productSelect.price}}</text>\r\n\t\t\t\t\t\t\t\t\t\t<!-- <view class=\"flex  pl-2\" v-if=\"attr.productSelect.vipPrice && attr.productSelect.vipPrice > 0\">\r\n\t\t\t\t\t\t\t\t\t\t\t<image src=\"../../static/images/vip_badge.png\" class=\"vip_icon\"></image>\r\n\t\t\t\t\t\t\t\t\t\t\t<text class='vip_money skeleton-rect'>￥{{attr.productSelect.vipPrice}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class='iconfont icon-fenxiang' @click=\"listenerActionSheet\"></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class='introduce skeleton-rect'>{{productInfo.storeName}}</view>\r\n\t\t\t\t\t\t\t\t<view class='label acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t\t\t<view class=\"skeleton-rect\">原价:￥{{attr.productSelect.otPrice || 0}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"skeleton-rect\">\r\n\t\t\t\t\t\t\t\t\t\t库存:{{attr.productSelect.stock || 0}}{{productInfo.unitName || ''}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"skeleton-rect\">\r\n\t\t\t\t\t\t\t\t\t\t销量:{{Math.floor(productInfo.sales) + Math.floor(productInfo.ficti) || 0}}{{productInfo.unitName || ''}}\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view v-if=\"defaultCoupon.length>0 && type=='normal'\"\r\n\t\t\t\t\t\t\t\t\tclass='coupon acea-row row-between-wrapper' @click='couponTap'>\r\n\t\t\t\t\t\t\t\t\t<view class='hide line1 acea-row skeleton-rect'>优惠券：\r\n\t\t\t\t\t\t\t\t\t\t<view class='activity'>满{{defaultCoupon[0].minPrice}}减{{defaultCoupon[0].money}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class='iconfont icon-jiantou'></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"coupon acea-row row-between-wrapper\" v-if=\"activityH5.length\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"line1 acea-row\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"activityName skeleton-rect\">活&nbsp;&nbsp;&nbsp;动：</text>\r\n\t\t\t\t\t\t\t\t\t\t<view v-for='(item,index) in activityH5' :key='index' @click=\"goActivity(item)\"\r\n\t\t\t\t\t\t\t\t\t\t\tclass=\"activityBox\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view v-if=\"item.type === '1'\" class=\"skeleton-rect\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:class=\"index==0?'activity_pin':'' || index==1?'activity_miao':'' || index==2?'activity_kan':''\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"iconfonts iconfont icon-miaosha1\"></text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"activity_title\"> 参与秒杀</text>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"skeleton-rect\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:class=\"index==0?'activity_pin':'' || index==1?'activity_miao':'' || index==2?'activity_kan':''\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tv-if=\"item.type === '2'\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"iconfonts iconfont icon-kanjia\"></text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"activity_title\"> 参与砍价</text>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"skeleton-rect\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:class=\"index==0?'activity_pin':'' || index==1?'activity_miao':'' || index==2?'activity_kan':''\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tv-if=\"item.type === '3'\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"iconfonts iconfont icon-pintuan\"></text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"activity_title\"> 参与拼团</text>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class='attribute mb30 borRadius14' @click=\"selecAttr\">\r\n\t\t\t\t\t\t\t\t<view class=\"acea-row row-between-wrapper\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"line1 skeleton-rect\">{{attrTxt}}：\r\n\t\t\t\t\t\t\t\t\t\t<text class='atterTxt'>{{attrValue}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class='iconfont icon-jiantou'></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"acea-row row-between-wrapper\" style=\"margin-top:7px;padding-left:55px;\" v-if=\"skuArr.length > 1\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"flex\">\r\n\t\t\t\t\t\t\t\t\t\t<image :src=\"item.image\" v-for=\"(item,index) in skuArr.slice(0,4)\" :key=\"index\" class=\"attrImg\"></image>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"switchTxt\">共{{skuArr.length}}种规格可选</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class='userEvaluation' id=\"past1\">\r\n\t\t\t\t\t\t\t\t<view class='title acea-row row-between-wrapper'\r\n\t\t\t\t\t\t\t\t\t:style=\"replyCount==0?'border-bottom-left-radius:14rpx;border-bottom-right-radius:14rpx;':''\">\r\n\t\t\t\t\t\t\t\t\t<view>用户评价<i>({{replyCount}})</i></view>\r\n\t\t\t\t\t\t\t\t\t<navigator class='praise' hover-class='none'\r\n\t\t\t\t\t\t\t\t\t\t:url='\"/pages/users/goods_comment_list/index?productId=\"+id'>\r\n\t\t\t\t\t\t\t\t\t\t<i>好评</i>&nbsp;<text class='font_color px-12'>{{replyChance || 0}}%</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class='iconfont icon-jiantou'></text>\r\n\t\t\t\t\t\t\t\t\t</navigator>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<block v-if=\"replyCount\">\r\n\t\t\t\t\t\t\t\t\t<userEvaluation :reply=\"reply\"></userEvaluation>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- 优品推荐 -->\r\n\t\t\t\t\t\t\t<view class=\"superior borRadius14\" if='good_list.length' id=\"past2\">\r\n\t\t\t\t\t\t\t\t<view class=\"title acea-row row-center-wrapper\">\r\n\t\t\t\t\t\t\t\t\t<image src=\"../../static/images/xzuo.png\"></image>\r\n\t\t\t\t\t\t\t\t\t<view class=\"titleTxt\">优品推荐</view>\r\n\t\t\t\t\t\t\t\t\t<image src=\"../../static/images/xyou.png\"></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"slider-banner banner\">\r\n\t\t\t\t\t\t\t\t\t<swiper indicator-dots=\"true\" :autoplay=\"autoplay\" :circular=\"circular\"\r\n\t\t\t\t\t\t\t\t\t\t:interval=\"interval\" :duration=\"duration\" indicator-color=\"#999\"\r\n\t\t\t\t\t\t\t\t\t\t:indicator-active-color=\"indicatorBg\" :style=\"'height:'+clientHeight+'px'\">\r\n\t\t\t\t\t\t\t\t\t\t<swiper-item v-for=\"(item,indexw) in good_list\" :key=\"indexw\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"list acea-row row-middle\" :id=\"'list'+indexw\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"item\" v-for=\"(val,indexn) in item.list\" :key=\"indexn\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t@click=\"goDetail(val)\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"pictrue\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<image :src=\"val.image\"></image>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span class=\"pictrue_log pictrue_log_class\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-if=\"val.activityH5 && val.activityH5.type === '1'\">秒杀</span>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span class=\"pictrue_log pictrue_log_class\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-if=\"val.activityH5 && val.activityH5.type === '2'\">砍价</span>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span class=\"pictrue_log pictrue_log_class\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-if=\"val.activityH5 && val.activityH5.type === '3'\">拼团</span>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"name line1\">{{val.storeName}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"money theme_price\">¥{{val.price}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t\t\t\t\t\t<!-- <view class=\"swiper-pagination\" slot=\"pagination\"></view> -->\r\n\t\t\t\t\t\t\t\t\t</swiper>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='product-intro' id=\"past3\">\r\n\t\t\t\t\t\t<view class='title'>\r\n\t\t\t\t\t\t\t<image src=\"../../static/images/xzuo.png\"></image>\r\n\t\t\t\t\t\t\t<span class=\"sp\">产品详情</span>\r\n\t\t\t\t\t\t\t<image src=\"../../static/images/xyou.png\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='conter'>\r\n\t\t\t\t\t\t\t<jyf-parser :html=\"description\" ref=\"article\" :tag-style=\"tagStyle\"></jyf-parser>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view style='height:120rpx;'></view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\t\t\t<view class='footer acea-row row-between-wrapper'  >\r\n\t\t\t\t<!-- #ifdef MP -->\r\n\t\t\t\t<button hover-class='none' class='item skeleton-rect' @click=\"kefuClick\" v-if=\"chatConfig.telephone_service_switch === 'true'\">\r\n\t\t\t\t\t<view class='iconfont icon-kefu'></view>\r\n\t\t\t\t\t<view>客服</view>\r\n\t\t\t\t</button>\r\n\t\t\t\t<button open-type=\"contact\" hover-class='none' class='item skeleton-rect' v-else>\r\n\t\t\t\t\t<view class='iconfont icon-kefu'></view>\r\n\t\t\t\t\t<view>客服</view>\r\n\t\t\t\t</button>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<!-- #ifndef MP -->\r\n\t\t\t\t<view class=\"item skeleton-rect\" @click=\"kefuClick\">\r\n\t\t\t\t\t<view class=\"iconfont icon-kefu\"></view>\r\n\t\t\t\t\t<view>客服</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<block v-if=\"type === 'normal'\">\r\n\t\t\t\t\t<view @click=\"setCollect\" class='item skeleton-rect'>\r\n\t\t\t\t\t\t<view class='iconfont icon-shoucang1' v-if=\"userCollect\"></view>\r\n\t\t\t\t\t\t<view class='iconfont icon-shoucang' v-else></view>\r\n\t\t\t\t\t\t<view>收藏</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<navigator open-type='switchTab' class=\"animated item skeleton-rect\"\r\n\t\t\t\t\t\t:class=\"animated==true?'bounceIn':''\" url='/pages/order_addcart/order_addcart'\r\n\t\t\t\t\t\thover-class=\"none\">\r\n\t\t\t\t\t\t<view class='iconfont icon-gouwuche1'>\r\n\t\t\t\t\t\t\t<text v-if=\"Math.floor(CartCount)>0\" class='num bg_color'>{{CartCount}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view>购物车</view>\r\n\t\t\t\t\t</navigator>\r\n\t\t\t\t\t<view class=\"bnt acea-row skeleton-rect\" v-if=\"attr.productSelect.stock <= 0\">\r\n\t\t\t\t\t\t<form @submit=\"joinCart\" report-submit=\"true\"><button class=\"joinCart bnts\"\r\n\t\t\t\t\t\t\t\tform-type=\"submit\">加入购物车</button></form>\r\n\t\t\t\t\t\t<form report-submit=\"true\"><button class=\"bnts bg-color-hui\" form-type=\"submit\">已售罄</button>\r\n\t\t\t\t\t\t</form>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"bnt acea-row skeleton-rect\" v-else>\r\n\t\t\t\t\t\t<form @submit=\"joinCart\" report-submit=\"true\"><button class=\"joinCart bnts\"\r\n\t\t\t\t\t\t\t\tform-type=\"submit\">加入购物车</button></form>\r\n\t\t\t\t\t\t<form @submit=\"goBuy\" report-submit=\"true\"><button class=\"buy bnts\"\r\n\t\t\t\t\t\t\t\tform-type=\"submit\">立即购买</button>\r\n\t\t\t\t\t\t</form>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<view class=\"bnt bntVideo acea-row skeleton-rect\"\r\n\t\t\t\t\tv-if=\"attr.productSelect.stock <= 0 && type === 'video'\">\r\n\t\t\t\t\t<form report-submit=\"true\"><button class=\"bnts bg-color-hui\" form-type=\"submit\">已售罄</button>\r\n\t\t\t\t\t</form>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"bnt bntVideo acea-row skeleton-rect\"\r\n\t\t\t\t\tv-if=\"attr.productSelect.stock > 0 && type === 'video'\">\r\n\t\t\t\t\t<form @submit=\"goBuy\" report-submit=\"true\"><button class=\"buy bnts\" form-type=\"submit\">立即购买</button>\r\n\t\t\t\t\t</form>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<shareRedPackets :sharePacket=\"sharePacket\" @listenerActionSheet=\"listenerActionSheet\"\r\n\t\t\t\t @showShare=\"showShare\"></shareRedPackets>\r\n\t\t\t<!-- 组件 -->\r\n\t\t\t<productWindow :attr=\"attr\" :isShow='1' :iSplus='1' @myevent=\"onMyEvent\" @ChangeAttr=\"ChangeAttr\"\r\n\t\t\t\t@ChangeCartNum=\"ChangeCartNum\" @attrVal=\"attrVal\" @iptCartNum=\"iptCartNum\" id='product-window' @getImg=\"showImg\">\r\n\t\t\t</productWindow>\r\n\t\t\t<couponListWindow :coupon='coupon' :typeNum=\"couponDeaultType[0].useType\" @ChangCouponsClone=\"ChangCouponsClone\" @ChangCoupons=\"ChangCoupons\"\r\n\t\t\t\t@ChangCouponsUseState=\"ChangCouponsUseState\" @tabCouponType=\"tabCouponType\"></couponListWindow>\r\n\t\t\t<!-- 分享按钮 -->\r\n\t\t\t<view class=\"generate-posters\" :class=\"posters ? 'on' : ''\">\r\n\t\t\t\t<view class=\"generateCon acea-row row-middle\">\r\n\t\t\t\t\t<!-- #ifndef MP -->\r\n\t\t\t\t\t<button class=\"item\" hover-class=\"none\" v-if=\"weixinStatus === true\" @click=\"H5ShareBox = true\">\r\n\t\t\t\t\t\t<view class=\"pictrue\">\r\n\t\t\t\t\t\t\t<image src=\"../../static/images/weixin.png\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"\">分享给好友</view>\r\n\t\t\t\t\t</button>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<!-- #ifdef MP -->\r\n\t\t\t\t\t<button class=\"item\" open-type=\"share\" hover-class=\"none\">\r\n\t\t\t\t\t\t<view class=\"pictrue\">\r\n\t\t\t\t\t\t\t<image src=\"../../static/images/weixin.png\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"\">分享给好友</view>\r\n\t\t\t\t\t</button>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<!-- #ifdef APP-PLUS -->\r\n\t\t\t\t\t<view class=\"item\" @click=\"appShare('WXSceneSession')\">\r\n\t\t\t\t\t\t<view class=\"iconfont icon-weixin3\"></view>\r\n\t\t\t\t\t\t<view class=\"\">微信好友</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item\" @click=\"appShare('WXSenceTimeline')\">\r\n\t\t\t\t\t\t<view class=\"iconfont icon-pengyouquan\"></view>\r\n\t\t\t\t\t\t<view class=\"\">微信朋友圈</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<!-- #ifdef H5 || MP -->\r\n\t\t\t\t\t<view class=\"item\" @click=\"getpreviewImage\">\r\n\t\t\t\t\t\t<view class=\"pictrue\">\r\n\t\t\t\t\t\t\t<image src=\"../../static/images/changan.png\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"\">预览发图</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<!-- #ifdef MP  -->\r\n\t\t\t\t\t<button class=\"item\" hover-class=\"none\" @click=\"savePosterPath\">\r\n\t\t\t\t\t\t<view class=\"pictrue\">\r\n\t\t\t\t\t\t\t<image src=\"../../static/images/haibao.png\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"\">保存海报</view>\r\n\t\t\t\t\t</button>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"generateClose acea-row row-center-wrapper\" @click=\"posterImageClose\">取消</view>\r\n\t\t\t</view>\r\n\t\t\t<cus-previewImg ref=\"cusPreviewImg\" :list=\"skuArr\" @changeSwitch=\"changeSwitch\" @shareFriend=\"listenerActionSheet\"/>\r\n\t\t\t<view class=\"mask\" v-if=\"posters\" @click=\"closePosters\"></view>\r\n\t\t\t<view class=\"mask\" v-if=\"canvasStatus\"></view>\r\n\t\t\t<view class=\"mask_transparent\" v-if=\"currentPage\" @touchmove=\"hideNav\" @click=\"hideNav()\"></view>\r\n\t\t\t<!-- #ifdef MP -->\r\n\t\t\t<!-- <authorize @onLoadFun=\"onLoadFun\" :isAuto=\"isAuto\" :isShowAuth=\"isShowAuth\" @authColse=\"authColse\"></authorize> -->\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<!-- 海报展示 -->\r\n\t\t\t<view class='poster-pop' v-if=\"canvasStatus\">\r\n\t\t\t\t<image :src='imagePath'></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"canvas\" v-else>\r\n\t\t\t\t<canvas style=\"width:750px;height:1190px;\" canvas-id=\"firstCanvas\"></canvas>\r\n\t\t\t\t<canvas canvas-id=\"qrcode\" :style=\"{width: `${qrcodeSize}px`, height: `${qrcodeSize}px`}\" />\r\n\t\t\t</view>\r\n\t\t\t<!-- 发送给朋友图片 -->\r\n\t\t\t<view class=\"share-box\" v-if=\"H5ShareBox\">\r\n\t\t\t\t<image src=\"/static/images/share-info.png\" @click=\"H5ShareBox = false\"></image>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport uQRCode from '@/js_sdk/Sansnn-uQRCode/uqrcode.js'\r\n\t// import yzf_chat from '@/plugin/chat/yzf_chat.js'\r\n\timport store from '@/store';\r\n\timport {HTTP_H5_URL} from '@/config/app.js';\r\n\timport { spread } from \"@/api/user\";\r\n\timport {\r\n\t\tgetProductDetail,\r\n\t\tcollectAdd,\r\n\t\tcollectDel,\r\n\t\tpostCartAdd,\r\n\t\tgetReplyList,\r\n\t\tgetReplyConfig,\r\n\t\tgetProductGood,\r\n\t\tgetReplyProduct\r\n\t} from '@/api/store.js';\r\n\timport {getCoupons} from '@/api/api.js';\r\n\timport {getCartCounts} from '@/api/order.js';\r\n\timport {toLogin} from '@/libs/login.js';\r\n\timport {mapGetters} from \"vuex\";\r\n\timport {imageBase64} from \"@/api/public\";\r\n\timport productConSwiper from '@/components/productConSwiper';\r\n\timport couponListWindow from '@/components/couponListWindow';\r\n\timport productWindow from '@/components/productWindow';\r\n\timport userEvaluation from '@/components/userEvaluation';\r\n\timport shareRedPackets from '@/components/shareRedPackets';\r\n\timport cusPreviewImg from '@/components/cus-previewImg/cus-previewImg.vue'\r\n\timport {silenceBindingSpread} from \"@/utils\";\r\n\timport parser from \"@/components/jyf-parser/jyf-parser\";\r\n\t// import {computeUser} from \"@/api/user.js\";\r\n\t// #ifdef MP\r\n\timport {base64src} from '@/utils/base64src.js'\r\n\timport authorize from '@/components/Authorize';\r\n\timport {getQrcode} from '@/api/api.js';\r\n\t// #endif\r\n\tlet app = getApp();\r\n\timport {setThemeColor} from '@/utils/setTheme.js'\r\n\timport {Debounce} from '@/utils/validate.js'\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tproductConSwiper,\r\n\t\t\tcouponListWindow,\r\n\t\t\tproductWindow,\r\n\t\t\tuserEvaluation,\r\n\t\t\tshareRedPackets,\r\n\t\t\tcusPreviewImg ,\r\n\t\t\t\"jyf-parser\": parser,\r\n\t\t\t// #ifdef MP\r\n\t\t\tauthorize\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\tlet that = this;\r\n\t\t\treturn {\r\n\t\t\t\tshowSkeleton: true, //骨架屏显示隐藏\r\n\t\t\t\tisNodes: 0, //控制什么时候开始抓取元素节点,只要数值改变就重新抓取\r\n\t\t\t\t//属性是否打开\r\n\t\t\t\tcoupon: {\r\n\t\t\t\t\tcoupon: false,\r\n\t\t\t\t\ttype: 0,\r\n\t\t\t\t\tlist: [],\r\n\t\t\t\t\tcount: []\r\n\t\t\t\t},\r\n\t\t\t\tattrTxt: '请选择', //属性页面提示\r\n\t\t\t\tattrValue: '', //已选属性\r\n\t\t\t\tanimated: false, //购物车动画\r\n\t\t\t\tid: 0, //商品id\r\n\t\t\t\treplyCount: 0, //总评论数量\r\n\t\t\t\treply: [], //评论列表\r\n\t\t\t\tproductInfo: {}, //商品详情\r\n\t\t\t\tproductValue: [], //系统属性\r\n\t\t\t\tcouponList: [], //优惠券\r\n\t\t\t\tcart_num: 1, //购买数量\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false, //是否隐藏授权\r\n\t\t\t\tisOpen: false, //是否打开属性组件\r\n\t\t\t\tactionSheetHidden: true,\r\n\t\t\t\tstoreImage: '', //海报产品图\r\n\t\t\t\tPromotionCode: '', //二维码图片\r\n\t\t\t\tposterbackgd: '/static/images/posterbackgd.png',\r\n\t\t\t\tsharePacket: {\r\n\t\t\t\t\tisState: true, //默认不显示\r\n\t\t\t\t\ttouchstart:false\r\n\t\t\t\t}, //分销商详细\r\n\t\t\t\tcircular: false,\r\n\t\t\t\tautoplay: false,\r\n\t\t\t\tinterval: 3000,\r\n\t\t\t\tduration: 500,\r\n\t\t\t\tclientHeight: \"\",\r\n\t\t\t\tsystemStore: {}, //门店信息\r\n\t\t\t\tgood_list: [],\r\n\t\t\t\treplyChance: 0,\r\n\t\t\t\tCartCount: 0,\r\n\t\t\t\tisDown: true,\r\n\t\t\t\tposters: false,\r\n\t\t\t\tweixinStatus: false,\r\n\t\t\t\tattr: {\r\n\t\t\t\t\tcartAttr: false,\r\n\t\t\t\t\tproductAttr: [],\r\n\t\t\t\t\tproductSelect: {}\r\n\t\t\t\t},\r\n\t\t\t\tdescription: '',\r\n\t\t\t\tnavActive: 0,\r\n\t\t\t\tH5ShareBox: false, //公众号分享图片\r\n\t\t\t\tactivityH5: [],\r\n\t\t\t\tretunTop: true, //顶部返回\r\n\t\t\t\tnavH: \"\",\r\n\t\t\t\tnavList: [],\r\n\t\t\t\topacity: 0,\r\n\t\t\t\tscrollY: 0,\r\n\t\t\t\ttopArr: [],\r\n\t\t\t\ttoView: '',\r\n\t\t\t\theight: 0,\r\n\t\t\t\theightArr: [],\r\n\t\t\t\tlock: false,\r\n\t\t\t\tscrollTop: 0,\r\n\t\t\t\ttagStyle: {\r\n\t\t\t\t\timg: 'width:100%;display:block;',\r\n\t\t\t\t\ttable: 'width:100%',\r\n\t\t\t\t\tvideo: 'width:100%'\r\n\t\t\t\t},\r\n\t\t\t\tsliderImage: [],\r\n\t\t\t\tvideoLink:'',\r\n\t\t\t\tqrcodeSize: 600,\r\n\t\t\t\tcanvasStatus: false, //是否显示海报\r\n\t\t\t\timagePath: '', //海报路径\r\n\t\t\t\timgTop: '',\r\n\t\t\t\terrT: '',\r\n\t\t\t\thomeTop: 20,\r\n\t\t\t\tnavbarRight: 0,\r\n\t\t\t\tuserCollect: false,\r\n\t\t\t\toptions: null,\r\n\t\t\t\treturnShow: true, //判断顶部返回是否出现\r\n\t\t\t\ttype: \"\", //视频号普通商品类型\r\n\t\t\t\ttheme:app.globalData.theme,\r\n\t\t\t\tindicatorBg:'',\r\n\t\t\t\tshareStatus:true,\r\n\t\t\t\tskuArr:[],\r\n\t\t\t\tcurrentPage:false,\r\n\t\t\t\tselectSku:{},\r\n\t\t\t\tselectNavList:[\r\n\t\t\t\t\t{name:'首页',icon:'icon-shouye8',url:'/pages/index/index',after:'dialog_after'},\r\n\t\t\t\t\t{name:'搜索',icon:'icon-sousuo6',url:'/pages/goods_search/index',after:'dialog_after'},\r\n\t\t\t\t\t{name:'购物车',icon:'icon-gouwuche7',url:'/pages/order_addcart/order_addcart',after:'dialog_after'},\r\n\t\t\t\t\t{name:'我的收藏',icon:'icon-shoucang3',url:'/pages/users/user_goods_collection/index',after:'dialog_after'},\r\n\t\t\t\t\t{name:'个人中心',icon:'icon-gerenzhongxin1',url:'/pages/user/index'},\r\n\t\t\t\t],\r\n\t\t\t\tchatConfig:{\r\n\t\t\t\t\tconsumer_hotline:'',\r\n\t\t\t\t\ttelephone_service_switch:'false'\r\n\t\t\t\t}, //客服配置\r\n\t\t\t\tdefaultCoupon:[],\r\n\t\t\t\tcouponDeaultType:[\r\n\t\t\t\t\t{useType:1}\r\n\t\t\t\t]\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: mapGetters(['isLogin', 'uid', 'chatUrl']),\r\n\t\twatch: {\r\n\t\t\tisLogin: {\r\n\t\t\t\thandler: function(newV, oldV) {\r\n\t\t\t\t\tlet that = this;\r\n\t\t\t\t\tif (newV == true) {\r\n\t\t\t\t\t\tthat.getCouponList();\r\n\t\t\t\t\t\tthat.getCartCount();\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tdeep: true\r\n\t\t\t},\r\n\t\t\tproductInfo: {\r\n\t\t\t\thandler: function() {\r\n\t\t\t\t\tthis.$nextTick(() => {});\r\n\t\t\t\t},\r\n\t\t\t\timmediate: true\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\tthis.options = options;\r\n\t\t\tlet that = this;\r\n\t\t\tvar pages = getCurrentPages();\r\n\t\t\tthat.returnShow = pages.length === 1 ? false : true;\r\n\t\t\tif (pages.length <= 1) {\r\n\t\t\t\tthat.retunTop = false\r\n\t\t\t}\r\n\t\t\tthat.navH = app.globalData.navHeight;\r\n\t\t\tthat.$set(that,'chatConfig',that.$Cache.getItem('chatConfig'));\r\n\t\t\t// #ifdef H5\r\n\t\t\t// computeUser();\r\n\t\t\t// #endif\r\n\t\t\tthat.$set(that,'theme',that.$Cache.get('theme')); //用户从分享卡片进入的场景下获取主题色配置\r\n\t\t\tuni.getSystemInfo({\r\n\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\tthat.height = res.windowHeight\r\n\t\t\t\t\t//res.windowHeight:获取整个窗口高度为px，*2为rpx；98为头部占据的高度；\r\n\t\t\t\t\t// #ifndef APP-PLUS || H5 || MP-ALIPAY\r\n\t\t\t\t\t//that.navbarRight = res.windowWidth - uni.getMenuButtonBoundingClientRect().left;\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t},\r\n\t\t\t});\r\n\t\t\tif (!options.scene && !options.id) {\r\n\t\t\t\tthis.showSkeleton = false;\r\n\t\t\t\tthis.$util.Tips({\r\n\t\t\t\t\ttitle: '缺少参数无法查看商品'\r\n\t\t\t\t}, {\r\n\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tif (options.hasOwnProperty('id') || options.scene) {  \r\n\t\t\t\tif (options.scene) { // 仅仅小程序扫码进入\r\n\t\t\t\t\tlet qrCodeValue = this.$util.getUrlParams(decodeURIComponent(options.scene));\r\n\t\t\t\t\tlet mapeMpQrCodeValue = this.$util.formatMpQrCodeData(qrCodeValue);\r\n\t\t\t\t\tapp.globalData.spread = mapeMpQrCodeValue.spread;\r\n\t\t\t\t\tthis.id = mapeMpQrCodeValue.id;\r\n\t\t\t\t\t// setTimeout(()=>{\r\n\t\t\t\t\t// \tspread(mapeMpQrCodeValue.spread).then(res => {}).catch(res => {})\r\n\t\t\t\t\t// },2000)\r\n\t\t\t\t\t\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.id = options.id;\r\n\t\t\t\t}\r\n\t\t\t\toptions.type == undefined || options.type == null ? that.type = 'normal' : that.type = options.type;\r\n\t\t\t\tthat.$store.commit(\"PRODUCT_TYPE\", that.type);\r\n\t\t\t}\r\n            if(options.spread) app.globalData.spread = options.spread;\r\n\t\t\tthis.getGoodsDetails();\r\n\t\t\tthis.getCouponList();\r\n\t\t\tthis.getCouponType();\r\n\t\t\tthis.getProductReplyList();\r\n\t\t\tthis.getProductReplyCount();\r\n\t\t\tthis.getGoods();\r\n\t\t\tif(this.isLogin && app.globalData.spread){\r\n\t\t\t\tsilenceBindingSpread()\r\n\t\t\t}\r\n\t\t\tthis.indicatorBg = setThemeColor();\r\n\t\t},\r\n\t\tonReady() {\r\n\t\t\tthis.isNodes++;\r\n\t\t\tthis.$nextTick(function() {\r\n\t\t\t\t// #ifdef MP\r\n\t\t\t\tconst menuButton = uni.getMenuButtonBoundingClientRect();\r\n\t\t\t\tconst query = uni.createSelectorQuery().in(this);\r\n\t\t\t\tquery\r\n\t\t\t\t\t.select('#home')\r\n\t\t\t\t\t.boundingClientRect(data => {\r\n\t\t\t\t\t\tthis.homeTop = menuButton.top * 2 + menuButton.height - data.height;\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.exec();\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tthis.homeTop = 60;\r\n\t\t\t\t// #endif\r\n\t\t\t});\r\n\t\t},\r\n\t\t/**\r\n\t\t * 用户点击右上角分享\r\n\t\t */\r\n\t\t// #ifdef MP\r\n\t\tonShareAppMessage: function(res) {\r\n\t\t\tlet that = this;\r\n\t\t\tthat.$set(that, 'actionSheetHidden', !that.actionSheetHidden);\r\n\t\t\treturn {\r\n\t\t\t\ttitle: that.productInfo.storeName || '',\r\n\t\t\t\timageUrl: that.productInfo.image || '',\r\n\t\t\t\tpath: '/pages/goods_details/index?id=' + that.id + '&spread=' + that.uid,\r\n\t\t\t}\r\n\t\t},\r\n\t\t// #endif\r\n\t\tmethods: {\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\tappShare(scene) {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tlet routes = getCurrentPages(); // 获取当前打开过的页面路由数组\r\n\t\t\t\tlet curRoute = routes[routes.length - 1].$page.fullPath // 获取当前页面路由，也就是最后一个打开的页面路由\r\n\t\t\t\tuni.share({\r\n\t\t\t\t\tprovider: \"weixin\",\r\n\t\t\t\t\tscene: scene,\r\n\t\t\t\t\ttype: 0,\r\n\t\t\t\t\thref: `${HTTP_H5_URL}${curRoute}&spread=${that.uid}`,\r\n\t\t\t\t\ttitle: that.productInfo.storeName,\r\n\t\t\t\t\tsummary: that.productInfo.storeInfo,\r\n\t\t\t\t\timageUrl: that.productInfo.image,\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tthat.posters = false;\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '分享失败',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthat.posters = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\tkefuClick() {\r\n\t\t\t\tif(this.chatConfig.telephone_service_switch === 'true'){\r\n\t\t\t\t\tuni.makePhoneCall({\r\n\t\t\t\t\t    phoneNumber: this.chatConfig.consumer_hotline //仅为示例\r\n\t\t\t\t\t});\r\n\t\t\t\t}else{\r\n\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/users/web_page/index?webUel=' + this.chatUrl + '&title=客服'\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifndef APP-PLUS\r\n\t\t\t\t\tlocation.href = this.chatUrl;\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgoActivity: function(e) {\r\n\t\t\t\tlet item = e;\r\n\t\t\t\tif (item.type === \"1\") {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/pages/activity/goods_seckill_details/index?id=${item.id}`\r\n\t\t\t\t\t});\r\n\t\t\t\t} else if (item.type === \"2\") {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/pages/activity/goods_bargain_details/index?id=${item.id}&startBargainUid=${this.uid}`\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/pages/activity/goods_combination_details/index?id=${item.id}`\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 购物车手动填写\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tiptCartNum: function(e) {\r\n\t\t\t\tthis.$set(this.attr.productSelect, 'cart_num', e ? e : 1);\r\n\t\t\t},\r\n\t\t\t// 后退\r\n\t\t\treturns: function() {\r\n\t\t\t\tuni.navigateBack()\r\n\t\t\t},\r\n\t\t\tshowNav(){\r\n\t\t\t\tthis.currentPage = !this.currentPage;\r\n\t\t\t},\r\n\t\t\ttap: function(index) {\r\n\t\t\t\tvar id = \"past\" + index;\r\n\t\t\t\tvar index = index;\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthis.$set(this, 'toView', id);\r\n\t\t\t\tthis.$set(this, 'navActive', index);\r\n\t\t\t\tthis.$set(this, 'lock', true);\r\n\t\t\t\tthis.$set(this, 'scrollTop', index > 0 ? that.topArr[index] - (app.globalData.navHeight / 2) : that\r\n\t\t\t\t\t.topArr[index]);\r\n\t\t\t},\r\n\t\t\tscroll: function(e) {\r\n\t\t\t\tvar that = this,\r\n\t\t\t\t\tscrollY = e.detail.scrollTop;\r\n\t\t\t\tvar opacity = scrollY / 500;\r\n\t\t\t\topacity = opacity > 1 ? 1 : opacity;\r\n\t\t\t\tthat.$set(that, 'opacity', opacity);\r\n\t\t\t\tthat.$set(that, 'scrollY', scrollY);\r\n\t\t\t\tif (that.lock) {\r\n\t\t\t\t\tthat.$set(that, 'lock', false)\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tfor (var i = 0; i < that.topArr.length; i++) {\r\n\t\t\t\t\tif (scrollY < that.topArr[i] - (app.globalData.navHeight / 2) + that.heightArr[i]) {\r\n\t\t\t\t\t\tthat.$set(that, 'navActive', i)\r\n\t\t\t\t\t\tbreak\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthat.$set(that.sharePacket, 'touchstart', true); //滑动屏幕时让分享气泡缩回\r\n\t\t\t},\r\n\t\t\t/*\r\n\t\t\t *去商品详情页 \r\n\t\t\t */\r\n\t\t\tgoDetail(item) {\r\n\t\t\t\tif (!item.activityH5) {\r\n\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\turl: '/pages/goods_details/index?id=' + item.id\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tif (item.activityH5.length == 0) {\r\n\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\turl: '/pages/goods_details/index?id=' + item.id\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t// 砍价\r\n\t\t\t\tif (item.activityH5 && item.activityH5.type == 2) {\r\n\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\turl: `/pages/activity/goods_bargain_details/index?id=${item.activityH5.id}&bargain=${this.uid}`\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t// 拼团\r\n\t\t\t\tif (item.activityH5 && item.activityH5.type == 3) {\r\n\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\turl: `/pages/activity/goods_combination_details/index?id=${item.activityH5.id}`\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t// 秒杀\r\n\t\t\t\tif (item.activityH5 && item.activityH5.type == 1) {\r\n\t\t\t\t\tdebugger\r\n\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\turl: `/pages/activity/goods_seckill_details/index?id=${item.activityH5.id}`\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 微信登录回调\r\n\t\t\tonLoadFun: function(e) {\r\n\t\t\t\tthis.getCouponList();\r\n\t\t\t\tthis.getCartCount();\r\n\t\t\t},\r\n\t\t\tChangCouponsClone: function() {\r\n\t\t\t\tthis.$set(this.coupon, 'coupon', false)\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 购物车数量加和数量减\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tChangeCartNum: function(changeValue) {\r\n\t\t\t\t//changeValue:是否 加|减\r\n\t\t\t\t//获取当前变动属性\r\n\t\t\t\tlet productSelect = this.productValue[this.attrValue];\r\n\t\t\t\t//如果没有属性,赋值给商品默认库存\r\n\t\t\t\tif (productSelect === undefined && !this.attr.productAttr.length)\r\n\t\t\t\t\tproductSelect = this.attr.productSelect;\r\n\t\t\t\t//无属性值即库存为0；不存在加减；\r\n\t\t\t\tif (productSelect === undefined) return;\r\n\t\t\t\tlet stock = productSelect.stock || 0;\r\n\t\t\t\tlet num = this.attr.productSelect;\r\n\t\t\t\tif (changeValue) {\r\n\t\t\t\t\tnum.cart_num++;\r\n\t\t\t\t\tif (num.cart_num > stock) {\r\n\t\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", stock);\r\n\t\t\t\t\t\tthis.$set(this, \"cart_num\", stock);\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tnum.cart_num--;\r\n\t\t\t\t\tif (num.cart_num < 1) {\r\n\t\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\t\tthis.$set(this, \"cart_num\", 1);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tattrVal(val) {\r\n\t\t\t\tthis.$set(this.attr.productAttr[val.indexw], 'index', this.attr.productAttr[val.indexw].attrValues[val.indexn]);\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 属性变动赋值\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tChangeAttr: function(res) {\r\n\t\t\t\tlet productSelect = this.productValue[res];\r\n\t\t\t\tthis.$set(this, \"selectSku\", productSelect);\r\n\t\t\t\tif (productSelect) {\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"image\", productSelect.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"price\", productSelect.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"stock\", productSelect.stock);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"unique\", productSelect.id);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"vipPrice\", productSelect.vipPrice);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect,'otPrice',productSelect.otPrice);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", res);\r\n\t\t\t\t\tthis.$set(this, \"attrTxt\", \"已选择\");\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"image\", this.productInfo.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"price\", this.productInfo.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"stock\", 0);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"unique\", this.productInfo.id);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"vipPrice\", this.productInfo.vipPrice);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect,'otPrice',this.productInfo.otPrice);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", \"\");\r\n\t\t\t\t\tthis.$set(this, \"attrTxt\", \"请选择\");\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 领取完毕移除当前页面领取过的优惠券展示\r\n\t\t\t */\r\n\t\t\tChangCoupons: function(e) {\r\n\t\t\t\tlet coupon = e;\r\n\t\t\t\tlet couponList = this.$util.ArrayRemove(this.couponList, 'id', coupon.id);\r\n\t\t\t\tthis.$set(this, 'couponList', couponList);\r\n\t\t\t\tthis.getCouponList();\r\n\t\t\t},\r\n\r\n\t\t\tsetClientHeight: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (!that.good_list.length) return;\r\n\t\t\t\tlet view = uni.createSelectorQuery().in(this).select(\"#list0\");\r\n\t\t\t\tview.fields({\r\n\t\t\t\t\tsize: true,\r\n\t\t\t\t}, data => {\r\n\t\t\t\t\tthat.$set(that, 'clientHeight', data.height + 20)\r\n\t\t\t\t}).exec();\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 优品推荐\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tgetGoods() {\r\n\t\t\t\tgetProductGood().then(res => {\r\n\t\t\t\t\tlet good_list = res.data.list || [];\r\n\t\t\t\t\tlet count = Math.ceil(good_list.length / 6);\r\n\t\t\t\t\tlet goodArray = new Array();\r\n\t\t\t\t\tfor (let i = 0; i < count; i++) {\r\n\t\t\t\t\t\tlet list = good_list.slice(i * 6, i * 6 + 6);\r\n\t\t\t\t\t\tif (list.length) goodArray.push({\r\n\t\t\t\t\t\t\tlist: list\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.$set(this, 'good_list', goodArray);\r\n\t\t\t\t\tlet navList = ['商品', '评价', '详情'];\r\n\t\t\t\t\tif (goodArray.length) {\r\n\t\t\t\t\t\tnavList.splice(2, 0, '推荐')\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.$set(this, 'navList', navList);\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tif (good_list.length) {\r\n\t\t\t\t\t\t\t// #ifndef APP-PLUS\r\n\t\t\t\t\t\t\tthis.setClientHeight();\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\tthis.setClientHeight();\r\n\t\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t})\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 获取产品详情\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tgetGoodsDetails: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetProductDetail(that.id, that.type).then(res => {\r\n\t\t\t\t\tlet productInfo = res.data.productInfo;\r\n\t\t\t\t\t// 字符串数组转数组；\r\n\t\t\t\t\tlet arrayImg = productInfo.sliderImage;\r\n\t\t\t\t\tlet sliderImage = JSON.parse(arrayImg);\r\n\t\t\t\t\tif(that.getFileType(sliderImage[0]) == 'video'){\r\n\t\t\t\t\t\t//判断轮播图第一张是否是视频，如果是，就赋值给videoLink，并且将其在轮播图中删除\r\n\t\t\t\t\t\tthis.$set(this,'videoLink',sliderImage[0]);\r\n\t\t\t\t\t\tsliderImage.splice(0,1);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.$set(that, 'sliderImage', sliderImage);\r\n\t\t\t\t\tthat.$set(that, 'productInfo', productInfo);\r\n\t\t\t\t\tthat.$set(that, 'description', productInfo.content);\r\n\t\t\t\t\tthat.$set(that, 'userCollect', res.data.userCollect);\r\n\t\t\t\t\tthat.$set(that.attr, 'productAttr', res.data.productAttr);\r\n\t\t\t\t\tthat.$set(that, 'productValue', res.data.productValue);\r\n\t\t\t\t\tfor(let key in res.data.productValue){\r\n\t\t\t\t\t\tlet obj = res.data.productValue[key];\r\n\t\t\t\t\t\tthat.skuArr.push(obj)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.$set(this, \"selectSku\", that.skuArr[0]);\r\n\t\t\t\t\tthat.$set(that.sharePacket, 'priceName', res.data.priceName);\r\n\t\t\t\t\t//that.$set(that.sharePacket, 'isState', Math.floor(res.data.priceName) != 0 ?false : true);\r\n\t\t\t\t\tthat.$set(that.sharePacket, 'isState', (res.data.priceName != \"0\" && res.data.priceName!==null) ? false : true);\r\n\t\t\t\t\tthat.$set(that, 'activityH5', res.data.activityAllH5 ? res.data.activityAllH5 : []);\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: productInfo.storeName.substring(0, 7) + \"...\"\r\n\t\t\t\t\t})\r\n\t\t\t\t\t\r\n\t\t\t\t\tlet productAttr = this.attr.productAttr.map(item => {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tattrName : item.attrName,\r\n\t\t\t\t\t\tattrValues: item.attrValues.split(','),\r\n\t\t\t\t\t\tid:item.id,\r\n\t\t\t\t\t\tisDel:item.isDel,\r\n\t\t\t\t\t\tproductId:item.productId,\r\n\t\t\t\t\t\ttype:item.type\r\n\t\t\t\t\t }\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.$set(this.attr,'productAttr',productAttr);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// var navList = ['商品', '评价', '详情'];\r\n\t\t\t\t\t// if (goodArray.length) {\r\n\t\t\t\t\t// \tnavList.splice(2, 0, '推荐')\r\n\t\t\t\t\t// }\r\n\t\t\t\t\t//that.$set(that, 'navList', navList);\r\n\t\t\t\t\tif (that.isLogin) {\r\n\t\t\t\t\t\tthat.getCartCount();\r\n\t\t\t\t\t\t//#ifdef H5\r\n\t\t\t\t\t\tthat.make(that.uid);\r\n\t\t\t\t\t\tthat.ShareInfo();\r\n\t\t\t\t\t\tthis.getImageBase64(this.productInfo.image);\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t// #ifdef MP\r\n\t\t\t\t\t\tthat.getQrcode();\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t};\r\n\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\tthat.infoScroll();\r\n\t\t\t\t\t}, 500);\r\n\t\t\t\t\t// #ifdef MP\r\n\t\t\t\t\tthat.imgTop = res.data.productInfo.image\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifndef H5\r\n\t\t\t\t\tthat.downloadFilestoreImage();\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tthat.DefaultSelect();\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.showSkeleton = false\r\n\t\t\t\t\t\tthis.defaultCoupon = this.coupon.list;\r\n\t\t\t\t\t}, 1000)\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t//状态异常返回上级页面\r\n\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\ttitle: err.toString()\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\ttab: 3,\r\n\t\t\t\t\t\turl: 1\r\n\t\t\t\t\t});\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.showSkeleton = false\r\n\t\t\t\t\t}, 500)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetProductReplyList: function() {\r\n\t\t\t\tgetReplyProduct(this.id).then(res => {\r\n\t\t\t\t\tthis.reply = res.data.productReply ? [res.data.productReply] : [];\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetProductReplyCount: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetReplyConfig(that.id).then(res => {\r\n\t\t\t\t\tthat.$set(that, 'replyChance', res.data.replyChance * 100);\r\n\t\t\t\t\tthat.$set(that, 'replyCount', res.data.sumCount);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tinfoScroll: function() {\r\n\t\t\t\tvar that = this,\r\n\t\t\t\t\ttopArr = [],\r\n\t\t\t\t\theightArr = [];\r\n\t\t\t\tfor (var i = 0; i < that.navList.length; i++) { //productList\r\n\t\t\t\t\t//获取元素所在位置\r\n\t\t\t\t\tvar query = uni.createSelectorQuery().in(this);\r\n\t\t\t\t\tvar idView = \"#past\" + i;\r\n\t\t\t\t\t// if (!that.data.good_list.length && i == 2) {\r\n\t\t\t\t\t//   var idView = \"#past\" + 3;\r\n\t\t\t\t\t// }\r\n\t\t\t\t\tquery.select(idView).boundingClientRect();\r\n\t\t\t\t\tquery.exec(function(res) {\r\n\t\t\t\t\t\tvar top = res[0].top;\r\n\t\t\t\t\t\tvar height = res[0].height;\r\n\t\t\t\t\t\ttopArr.push(top);\r\n\t\t\t\t\t\theightArr.push(height);\r\n\t\t\t\t\t\tthat.$set(that, 'topArr', topArr);\r\n\t\t\t\t\t\tthat.$set(that, 'heightArr', heightArr);\r\n\t\t\t\t\t});\r\n\t\t\t\t};\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 默认选中属性\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tDefaultSelect: function() {\r\n\t\t\t\tlet productAttr = this.attr.productAttr;\r\n\t\t\t\tlet value = [];\r\n\t\t\t\t//默认选中每种规格的第一个\r\n\t\t\t\tproductAttr.forEach(item=>{\r\n\t\t\t\t\tvalue.push(item.attrValues[0]);\r\n\t\t\t\t})\r\n\t\t\t\tfor (let i = 0; i < value.length; i++) {\r\n\t\t\t\t\tthis.$set(productAttr[i], \"index\", value[i]);\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t//sort();排序函数:数字-英文-汉字；\r\n\t\t\t\tlet productSelect = this.productValue[value.join(\",\")];\r\n\t\t\t\tif (productSelect && productAttr.length) {\r\n\t\t\t\t\tthis.$set(this.attr.productSelect,\"storeName\",this.productInfo.storeName);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"image\", productSelect.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"price\", productSelect.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"stock\", productSelect.stock);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"unique\", productSelect.id);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"vipPrice\", productSelect.vipPrice); //attr.productSelect.otPrice\r\n\t\t\t\t\tthis.$set(this.attr.productSelect,'otPrice',productSelect.otPrice);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", value.join(\",\"));\r\n\t\t\t\t\tthis.$set(this, \"attrTxt\", \"已选择\");\r\n\t\t\t\t} else if (!productSelect && productAttr.length) {\r\n\t\t\t\t\tthis.$set(this.attr.productSelect,\"storeName\",this.productInfo.storeName);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"image\", this.productInfo.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"price\", this.productInfo.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"stock\", 0);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"unique\", this.productInfo.id);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"vipPrice\", this.productInfo.vipPrice);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect,'otPrice',this.productInfo.otPrice);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", \"\");\r\n\t\t\t\t\tthis.$set(this, \"attrTxt\", \"请选择\");\r\n\t\t\t\t} else if (!productSelect && !productAttr.length) {\r\n\t\t\t\t\tthis.$set(this.attr.productSelect,\"storeName\",this.productInfo.storeName);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"image\", this.productInfo.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"price\", this.productInfo.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"stock\", this.productInfo.stock);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect,\"unique\",this.productInfo.id || \"\");\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"vipPrice\", this.productInfo.vipPrice);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect,'otPrice',this.productInfo.otPrice);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", \"\");\r\n\t\t\t\t\tthis.$set(this, \"attrTxt\", \"请选择\");\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 获取优惠券\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tgetCouponList(type) {\r\n\t\t\t\tlet that = this,\r\n\t\t\t\t\tobj = {\r\n\t\t\t\t\t\tpage: 1,\r\n\t\t\t\t\t\tlimit: 20,\r\n\t\t\t\t\t\tproductId: that.id,\r\n\t\t\t\t\t\ttype: type\r\n\t\t\t\t\t};\r\n\t\t\t\tif (type != undefined || type != null) {\r\n\t\t\t\t\tobj.type = type;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tobj.type = \"\";\r\n\t\t\t\t}\r\n\t\t\t\tgetCoupons(obj).then(res => {\r\n\t\t\t\t\tthat.$set(that.coupon, 'list', res.data);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tasync getCouponType(){\r\n\t\t\t\t//在onLoad只调用一次，获取默认的类型作为打开优惠券列表的参数，不会随着切换变化\r\n\t\t\t\tlet dataList = await getCoupons({productId: this.id});\r\n\t\t\t\tif(dataList.length){\r\n\t\t\t\t\tthis.couponDeaultType = dataList.data;\r\n\t\t\t\t\tthis.$set(this.coupon,'type',dataList);\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\ttabCouponType(type) {\r\n\t\t\t\tthis.$set(this.coupon, 'type', type);\r\n\t\t\t\tthis.getCouponList(type);\r\n\t\t\t},\r\n\r\n\t\t\tChangCouponsUseState(index) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthat.coupon.list[index].isUse = true;\r\n\t\t\t\tthat.$set(that.coupon, 'list', that.coupon.list);\r\n\t\t\t\tthat.$set(that.coupon, 'coupon', false);\r\n\t\t\t},\r\n\t\t\t/** \r\n\t\t\t * \r\n\t\t\t * \r\n\t\t\t * 收藏商品\r\n\t\t\t */\r\n\t\t\tsetCollect: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (this.isLogin === false) {\r\n\t\t\t\t\ttoLogin();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (this.userCollect) {\r\n\t\t\t\t\t\tcollectDel(this.productInfo.id).then(res => {\r\n\t\t\t\t\t\t\tthat.$set(that, 'userCollect', !that.userCollect);\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tcollectAdd(this.productInfo.id).then(res => {\r\n\t\t\t\t\t\t\tthat.$set(that, 'userCollect', !that.userCollect);\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 打开属性插件\r\n\t\t\t */\r\n\t\t\tselecAttr: function() {\r\n\t\t\t\tthis.$set(this.attr, 'cartAttr', true);\r\n\t\t\t\tthis.$set(this, 'isOpen', true);\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 打开优惠券插件\r\n\t\t\t */\r\n\t\t\tcouponTap: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (that.isLogin === false) {\r\n\t\t\t\t\ttoLogin();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.getCouponList(this.couponDeaultType[0].useType); //打开弹框默认请求商品券\r\n\t\t\t\t\tthat.$set(that.coupon, 'coupon', true);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tonMyEvent: function() {\r\n\t\t\t\tthis.$set(this.attr, 'cartAttr', false);\r\n\t\t\t\tthis.$set(this, 'isOpen', false);\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 打开属性加入购物车\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tjoinCart: function(e) {\r\n\t\t\t\t//是否登录\r\n\t\t\t\tif (this.isLogin === false) {\r\n\t\t\t\t\ttoLogin();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.goCat(1);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/*\r\n\t\t\t * 加入购物车\r\n\t\t\t */\r\n\t\t\tgoCat: function(num) {\r\n\t\t\t\tlet that = this,\r\n\t\t\t\t\tproductSelect = that.productValue[this.attrValue];\r\n\t\t\t\t//打开属性\r\n\t\t\t\tif (that.attrValue) {\r\n\t\t\t\t\t//默认选中了属性，但是没有打开过属性弹窗还是自动打开让用户查看默认选中的属性\r\n\t\t\t\t\tthat.attr.cartAttr = !that.isOpen ? true : false;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (that.isOpen) that.attr.cartAttr = true;\r\n\t\t\t\t\telse that.attr.cartAttr = !that.attr.cartAttr;\r\n\t\t\t\t}\r\n\t\t\t\t//只有关闭属性弹窗时进行加入购物车\r\n\t\t\t\tif (that.attr.cartAttr === true && that.isOpen === false)\r\n\t\t\t\t\treturn (that.isOpen = true);\r\n\t\t\t\t//如果有属性,没有选择,提示用户选择\r\n\t\t\t\tif (\r\n\t\t\t\t\tthat.attr.productAttr.length &&\r\n\t\t\t\t\tproductSelect.stock === 0 &&\r\n\t\t\t\t\tthat.isOpen === true\r\n\t\t\t\t)\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: \"产品库存不足，请选择其它\"\r\n\t\t\t\t\t});\r\n\t\t\t\tif (num === 1) {\r\n\t\t\t\t\tlet q = {\r\n\t\t\t\t\t\tproductId: parseFloat(that.id),\r\n\t\t\t\t\t\tcartNum: parseFloat(that.attr.productSelect.cart_num),\r\n\t\t\t\t\t\tisNew: false,\r\n\t\t\t\t\t\tproductAttrUnique: that.attr.productSelect !== undefined ?\r\n\t\t\t\t\t\t\tthat.attr.productSelect.unique : that.productInfo.id\r\n\t\t\t\t\t};\r\n\t\t\t\t\tpostCartAdd(q).then(function(res) {\r\n\t\t\t\t\t\t\tthat.isOpen = false;\r\n\t\t\t\t\t\t\tthat.attr.cartAttr = false;\r\n\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: \"添加购物车成功\",\r\n\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\tthat.getCartCount(true);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(res => {\r\n\t\t\t\t\t\t\tthat.isOpen = false;\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: res\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.getPreOrder();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 获取购物车数量\r\n\t\t\t * @param boolean 是否展示购物车动画和重置属性\r\n\t\t\t */\r\n\t\t\tgetCartCount: function(isAnima) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tconst isLogin = that.isLogin;\r\n\t\t\t\tif (isLogin) {\r\n\t\t\t\t\tgetCartCounts(true, 'total').then(res => {\r\n\t\t\t\t\t\tthat.CartCount = res.data.count;\r\n\t\t\t\t\t\t//加入购物车后重置属性\r\n\t\t\t\t\t\tif (isAnima) {\r\n\t\t\t\t\t\t\tthat.animated = true;\r\n\t\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t\tthat.animated = false;\r\n\t\t\t\t\t\t\t}, 500);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 立即购买\r\n\t\t\t */\r\n\t\t\tgoBuy: Debounce(function(e) {\r\n\t\t\t\tif (this.isLogin === false) {\r\n\t\t\t\t\ttoLogin();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.goCat(0);\r\n\t\t\t\t}\r\n\t\t\t}),\r\n\t\t\t/**\r\n\t\t\t * 预下单\r\n\t\t\t */\r\n\t\t\tgetPreOrder: function() {\r\n\t\t\t\tthis.$Order.getPreOrder(this.type === 'normal' ? 'buyNow' : 'video', [{\r\n\t\t\t\t\t\"attrValueId\": parseFloat(this.attr.productSelect.unique),\r\n\t\t\t\t\t\"productId\": parseFloat(this.id),\r\n\t\t\t\t\t\"productNum\": parseFloat(this.attr.productSelect.cart_num)\r\n\t\t\t\t}]);\r\n\t\t\t},\r\n\t\t\t// 授权关闭\r\n\t\t\tauthColse: function(e) {\r\n\t\t\t\tthis.isShowAuth = e\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 分享打开\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tlistenerActionSheet: function() {\r\n\t\t\t\tif (this.isLogin === false) {\r\n\t\t\t\t\ttoLogin();\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\tif (this.$wechat.isWeixin() === true) {\r\n\t\t\t\t\t\tthis.weixinStatus = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tthis.goPoster() \r\n\t\t\t\t\tthis.posters = true; \r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tclosePosters: function() {\r\n\t\t\t\tthis.posters = false;\r\n\t\t\t\tthis.currentPage = false;\r\n\t\t\t},\r\n\t\t\t//隐藏海报\r\n\t\t\tposterImageClose: function() {\r\n\t\t\t\tthis.canvasStatus = false\r\n\t\t\t\tthis.posters = false;\r\n\t\t\t},\r\n\t\t\t//替换安全域名\r\n\t\t\tsetDomain: function(url) {\r\n\t\t\t\turl = url ? url.toString() : '';\r\n\t\t\t\t//本地调试打开,生产请注销\r\n\t\t\t\tif (url.indexOf(\"https://\") > -1) return url;\r\n\t\t\t\telse return url.replace('http://', 'https://');\r\n\t\t\t},\r\n\t\t\t//获取海报产品图（解决跨域问题，只适用于小程序）\r\n\t\t\tdownloadFilestoreImage: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\turl: that.setDomain(that.productInfo.image),\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tthat.storeImage = res.tempFilePath;\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function() {\r\n\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: ''\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthat.storeImage = '';\r\n\t\t\t\t\t},\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 小程序关闭分享弹窗；\r\n\t\t\tgoFriend: function() {\r\n\t\t\t\tthis.posters = false;\r\n\t\t\t},\r\n\t\t\t// 小程序二维码\r\n\t\t\tgetQrcode() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tpid: that.uid,\r\n\t\t\t\t\tid: that.id,\r\n\t\t\t\t\tpath: 'pages/goods_details/index'\r\n\t\t\t\t}\r\n\t\t\t\tgetQrcode(data).then(res => {\r\n\t\t\t\t\tbase64src(res.data.code, Date.now(), res => {\r\n\t\t\t\t\t\tthat.PromotionCode = res;\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tthat.errT = err;\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 生成二维码；\r\n\t\t\tmake(uid) {\r\n\t\t\t\tlet href = location.href.split('?')[0] + \"?id=\" + this.id + \"&spread=\" + this.uid;\r\n\t\t\t\tuQRCode.make({\r\n\t\t\t\t\tcanvasId: 'qrcode',\r\n\t\t\t\t\ttext: href,\r\n\t\t\t\t\tsize: this.qrcodeSize,\r\n\t\t\t\t\tmargin: 10,\r\n\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\tthis.PromotionCode = res;\r\n\t\t\t\t\t},\r\n\t\t\t\t\tcomplete: () => {},\r\n\t\t\t\t\tfail: res => {\r\n\t\t\t\t\t\tthis.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: '海报二维码生成失败！'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetImageBase64: function(images) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\timageBase64({\r\n\t\t\t\t\turl: images\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tthat.imgTop = res.data.code;\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 获取产品分销二维码\r\n\t\t\t * @param function successFn 下载完成回调\r\n\t\t\t *\r\n\t\t\t */\r\n\t\t\tdownloadFilePromotionCode: function(successFn) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetProductCode(that.id)\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\t\t\turl: that.setDomain(res.data.code),\r\n\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\tthat.$set(that, 'isDown', false);\r\n\t\t\t\t\t\t\t\tif (typeof successFn == 'function') successFn && successFn(res\r\n\t\t\t\t\t\t\t\t\t.tempFilePath);\r\n\t\t\t\t\t\t\t\telse that.$set(that, 'PromotionCode', res.tempFilePath);\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail: function() {\r\n\t\t\t\t\t\t\t\tthat.$set(that, 'isDown', false);\r\n\t\t\t\t\t\t\t\tthat.$set(that, 'PromotionCode', '');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tthat.$set(that, 'isDown', false);\r\n\t\t\t\t\t\tthat.$set(that, 'PromotionCode', '');\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 生成海报\r\n\t\t\t */\r\n\t\t\tgoPoster: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '海报生成中',\r\n\t\t\t\t\tmask: true\r\n\t\t\t\t});\r\n\t\t\t\tthat.posters = false;\r\n\t\t\t\tlet arrImagesUrl = '';\r\n\t\t\t\tlet arrImagesUrlTop = '';\r\n\t\t\t\tif (!that.PromotionCode) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\ttitle: that.errT\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tif (!that.imgTop) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: '无法生成商品海报！'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t}, 1000);\r\n\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\turl: that.imgTop, //仅为示例，并非真实的资源\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tarrImagesUrlTop = res.tempFilePath;\r\n\t\t\t\t\t\tlet arrImages = [that.posterbackgd, arrImagesUrlTop, that.PromotionCode];\r\n\t\t\t\t\t\tlet storeName = that.productInfo.storeName;\r\n\t\t\t\t\t\tlet price = that.productInfo.price;\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tthat.$util.PosterCanvas(arrImages, storeName, price, that.productInfo\r\n\t\t\t\t\t\t\t\t.otPrice,\r\n\t\t\t\t\t\t\t\tfunction(tempFilePath) {\r\n\t\t\t\t\t\t\t\t\tthat.imagePath = tempFilePath;\r\n\t\t\t\t\t\t\t\t\tthat.canvasStatus = true;\r\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}, 500);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 图片预览；\r\n\t\t\tgetpreviewImage: function() {\r\n\t\t\t\tif (this.imagePath) {\r\n\t\t\t\t\tlet photoList = [];\r\n\t\t\t\t\tphotoList.push(this.imagePath)\r\n\t\t\t\t\tuni.previewImage({\r\n\t\t\t\t\t\turls: photoList,\r\n\t\t\t\t\t\tcurrent: this.imagePath\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$util.Tips({\r\n\t\t\t\t\t\ttitle: '您的海报尚未生成'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/*\r\n\t\t\t * 保存到手机相册\r\n\t\t\t */\r\n\t\t\t// #ifdef MP\r\n\t\t\tsavePosterPath: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tuni.getSetting({\r\n\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\tif (!res.authSetting['scope.writePhotosAlbum']) {\r\n\t\t\t\t\t\t\tuni.authorize({\r\n\t\t\t\t\t\t\t\tscope: 'scope.writePhotosAlbum',\r\n\t\t\t\t\t\t\t\tsuccess() {\r\n\t\t\t\t\t\t\t\t\tuni.saveImageToPhotosAlbum({\r\n\t\t\t\t\t\t\t\t\t\tfilePath: that.imagePath,\r\n\t\t\t\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\t\t\t\tthat.posterImageClose();\r\n\t\t\t\t\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '保存成功',\r\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tfail: function(res) {\r\n\t\t\t\t\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '保存失败'\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.saveImageToPhotosAlbum({\r\n\t\t\t\t\t\t\t\tfilePath: that.imagePath,\r\n\t\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\t\tthat.posterImageClose();\r\n\t\t\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '保存成功',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfail: function(res) {\r\n\t\t\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '保存失败'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\tShareInfo() {\r\n\t\t\t\tlet data = this.productInfo;\r\n\t\t\t\tlet href = location.href;\r\n\t\t\t\tif (this.$wechat.isWeixin()) {\r\n\t\t\t\t\thref = href.indexOf(\"?\") === -1 ? href + \"?spread=\" + this.uid : href + \"&spread=\" + this.uid;\r\n\t\t\t\t\tlet configAppMessage = {\r\n\t\t\t\t\t\tdesc: data.storeInfo,\r\n\t\t\t\t\t\ttitle: data.storeName, \r\n\t\t\t\t\t\tlink: href,\r\n\t\t\t\t\t\timgUrl: data.image\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthis.$wechat.wechatEvevt([\r\n\t\t\t\t\t\t\"updateAppMessageShareData\",\r\n\t\t\t\t\t\t\"updateTimelineShareData\",\r\n\t\t\t\t\t\t\"onMenuShareAppMessage\",\r\n\t\t\t\t\t\t\"onMenuShareTimeline\"\r\n\t\t\t\t\t], configAppMessage).then(res => {\r\n\t\t\t\t\t\t// console.log(res);\r\n\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\tconsole.log(err);\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tshowShare(status){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthat.$set(that.sharePacket, 'touchstart', status);\r\n\t\t\t},\r\n\t\t\thideNav(){\r\n\t\t\t\tthis.currentPage = false;\r\n\t\t\t},\r\n\t\t\t//下拉导航页面跳转\r\n\t\t\tlinkPage(url){\r\n\t\t\t\tif(url == '/pages/index/index' || url == '/pages/order_addcart/order_addcart' || url == '/pages/user/index'){\r\n\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\turl\r\n\t\t\t\t\t})\r\n\t\t\t\t}else{\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tthis.currentPage = false\r\n\t\t\t},\r\n\t\t\t//点击sku图片打开轮播图\r\n\t\t\tshowImg(index){\r\n\t\t\t\tthis.$refs.cusPreviewImg.open(this.selectSku.suk)\r\n\t\t\t},\r\n\t\t\t//滑动轮播图选择商品\r\n\t\t\tchangeSwitch(e){\r\n\t\t\t\tlet productSelect = this.skuArr[e];\r\n\t\t\t\tthis.$set(this,'selectSku',productSelect);\r\n\t\t\t\tvar skuList = productSelect.suk.split(',');\r\n\t\t\t\tskuList.forEach((i,index)=>{\r\n\t\t\t\t\tthis.$set(this.attr.productAttr[index],'index',skuList[index]);\r\n\t\t\t\t})\r\n\t\t\t\tif (productSelect) {\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"image\", productSelect.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"price\", productSelect.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"stock\", productSelect.stock);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"unique\", productSelect.id);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"vipPrice\", productSelect.vipPrice);\r\n\t\t\t\t\tthis.$set(this, \"attrTxt\", \"已选择\");\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", productSelect.suk)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetFileType(fileName) {\r\n\t\t\t\t// 后缀获取\r\n\t\t\t\tlet suffix = '';\r\n\t\t\t\t// 获取类型结果\r\n\t\t\t\tlet result = '';\r\n\t\t\t\ttry {\r\n\t\t\t\tconst flieArr = fileName.split('.');\r\n\t\t\t\tsuffix = flieArr[flieArr.length - 1];\r\n\t\t\t\t} catch (err) {\r\n\t\t\t\tsuffix = '';\r\n\t\t\t\t}\r\n\t\t\t\t// fileName无后缀返回 false\r\n\t\t\t\tif (!suffix) { return false; }\r\n\t\t\t\tsuffix = suffix.toLocaleLowerCase();\r\n\t\t\t\t// 图片格式\r\n\t\t\t\tconst imglist = ['png', 'jpg', 'jpeg', 'bmp', 'gif'];\r\n\t\t\t\t// 进行图片匹配\r\n\t\t\t\tresult = imglist.find(item => item === suffix);\r\n\t\t\t\tif (result) {\r\n\t\t\t\treturn 'image';\r\n\t\t\t\t}\r\n\t\t\t\t// 匹配 视频\r\n\t\t\t\tconst videolist = ['mp4', 'm2v', 'mkv', 'rmvb', 'wmv', 'avi', 'flv', 'mov', 'm4v'];\r\n\t\t\t\tresult = videolist.find(item => item === suffix);\r\n\t\t\t\tif (result) {\r\n\t\t\t\treturn 'video';\r\n\t\t\t\t}\r\n\t\t\t\t// 其他 文件类型\r\n\t\t\t\treturn 'other';\r\n\t\t\t},\r\n\t\t\tvideoPause(){\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.product-con {\r\n\t\theight: 100%;\r\n\t}\r\n\t.x-money{\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 700;\r\n\t\t@include price_color(theme);\r\n\t\t\r\n\t}\r\n\t.bg-color-hui {\r\n\t\tbackground: #bbb !important;\r\n\t\tborder-radius: 0 25px 25px 0;\r\n\t}\r\n\t.select_nav{\r\n\t\twidth: 170rpx !important;\r\n\t\theight: 60rpx !important;\r\n\t\tborder-radius: 33rpx;\r\n\t\tbackground: rgba(255, 255, 255, 0.3);\r\n\t\tborder: 1px solid rgba(0,0,0,0.07);\r\n\t\tcolor: #000;\r\n\t\tposition: fixed;\r\n\t\tfont-size: 18px;\r\n\t\tline-height: 62rpx;\r\n\t\tz-index: 1000;\r\n\t\tleft: 14rpx;\r\n\t}\r\n\t.px-20{\r\n\t\tpadding: 0 20rpx 0;\r\n\t}\r\n\t.nav_line{\r\n\t\tcontent: '';\r\n\t\tdisplay: inline-block;\r\n\t\twidth: 1px;\r\n\t\theight: 34rpx;\r\n\t\tbackground: #b3b3b3;\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tmargin: auto;\r\n\t}\r\n\t.bgwhite{\r\n\t\tbackground: #fff;\r\n\t}\r\n\t.input {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\t/* #ifdef MP */\r\n\t\twidth: 300rpx;\r\n\t\t/* #endif */\r\n\t\t/* #ifndef MP */\r\n\t\twidth: 460rpx;\r\n\t\t/* #endif */\r\n\t\theight: 58rpx;\r\n\t\tpadding: 0 0 0 30rpx;\r\n\t\tborder: 1px solid rgba(0,0,0,0.07);\r\n\t\tborder-radius: 33rpx;\r\n\t\tcolor: #666;\r\n\t\tfont-size: 26rpx;\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tmargin: auto;\r\n\t\tbackground: rgba(255, 255, 255, 0.3);\r\n\t\t.iconfont {\r\n\t\t\tmargin-right: 20rpx;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: #666666;\r\n\t\t}\r\n\t}\r\n\t.container_detail{\r\n\t\t/* #ifdef MP */\r\n\t\tmargin-top:32rpx;\r\n\t\t/* #endif */\r\n\t}\r\n\t.tab_nav{\r\n\t\twidth: 100%;\r\n\t\theight: 48px;\r\n\t\tpadding:0 30rpx 0;\r\n\t}\r\n\t.right_select{\r\n\t\twidth: 58rpx;\r\n\t\theight: 58rpx;\r\n\t\tbackground: rgba(255, 255, 255, 0.3);\r\n\t\tborder: 1px solid rgba(0,0,0,0.1);\r\n\t\tborder-radius: 50%;\r\n\t\tposition: fixed;\r\n\t\tright: 20rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 58rpx;\r\n\t}\r\n\t.dialog_nav{\r\n\t\tposition: absolute;\r\n\t\t/* #ifdef MP */\r\n\t\tleft: 14rpx;\r\n\t\t/* #endif */\r\n\t\t/* #ifdef H5 || APP-PLUS*/\r\n\t\tright: 14rpx;\r\n\t\t/* #endif */\r\n\t\twidth: 240rpx;\r\n\t\tbackground: #FFFFFF;\r\n\t\tbox-shadow: 0px 0px 16rpx rgba(0, 0, 0, 0.08);\r\n\t\tz-index: 310;\r\n\t\tborder-radius: 14rpx;\r\n\t\t&::before{\r\n\t\t\tcontent: '';\r\n\t\t\twidth: 0;\r\n\t\t\theight: 0;\r\n\t\t\tposition: absolute;\r\n\t\t\t/* #ifdef MP */\r\n\t\t\tleft: 0;\r\n\t\t\tright: 0;\r\n\t\t\tmargin:auto;\r\n\t\t\t/* #endif */\r\n\t\t\t/* #ifdef H5 || APP-PLUS */\r\n\t\t\tright: 8px;\r\n\t\t\t/* #endif */\r\n\t\t\ttop:-9px;\r\n\t\t\tborder-bottom: 10px solid #F5F5F5;\r\n\t\t\tborder-left: 10px solid transparent;    /*transparent 表示透明*/\r\n\t\t\tborder-right: 10px solid transparent;\r\n\t\t}\r\n\t}\r\n\t.dialog_nav_item{\r\n\t\twidth: 100%;\r\n\t\theight: 84rpx;\r\n\t\tline-height: 84rpx;\r\n\t\tpadding: 0 20rpx 0;\r\n\t\tbox-sizing: border-box;\r\n\t\tborder-bottom: #eee;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333;\r\n\t\tposition: relative;\r\n\t\t.iconfont{\r\n\t\t\tfont-size: 32rpx;\r\n\t\t}\r\n\t}\r\n\t.dialog_after{\r\n\t\t::after{\r\n\t\t\tcontent: '';\r\n\t\t\tposition: absolute;\r\n\t\t\twidth:172rpx;\r\n\t\t\theight: 1px;\r\n\t\t\tbackground-color: #EEEEEE;\r\n\t\t\tbottom: 0;\r\n\t\t\tright: 0;\r\n\t\t}\r\n\t}\r\n\t.pl-20{\r\n\t\tpadding-left: 20rpx;\r\n\t}\r\n\t.activity {\r\n\t\tpadding: 0 20rpx;\r\n\t\t@include coupons_border_color(theme);\r\n\t\t@include main_color(theme);\r\n\t\tfont-size: 24rpx;\r\n\t\tline-height: 34rpx;\r\n\t\tposition: relative;\r\n\t\tmargin-left: 4rpx;\r\n\t}\r\n\t.product-con .wrapper .coupon .activity:before {\r\n\t\tcontent: ' ';\r\n\t\tposition: absolute;\r\n\t\twidth: 7rpx;\r\n\t\theight: 10rpx;\r\n\t\tborder-radius: 0 7rpx 7rpx 0;\r\n\t\t@include coupons_border_color(theme);\r\n\t\tbackground-color: #fff !important;\r\n\t\tbottom: 50%;\r\n\t\tleft: -3rpx;\r\n\t\tmargin-bottom: -6rpx;\r\n\t\t// border-left-color: #fff ;\r\n\t\t@include white_left_border;\r\n\t}\r\n\t\r\n\t.product-con .wrapper .coupon .activity:after {\r\n\t\tcontent: ' ';\r\n\t\tposition: absolute;\r\n\t\twidth: 7rpx;\r\n\t\theight: 10rpx;\r\n\t\tborder-radius: 7rpx 0 0 7rpx;\r\n\t\t@include coupons_border_color(theme);\r\n\t\tbackground-color: #fff;\r\n\t\tright: -3rpx;\r\n\t\tbottom: 50%;\r\n\t\tmargin-bottom: -6rpx;\r\n\t\t// border-right-color: #fff;\r\n\t\t@include white_right_border;\r\n\t}\r\n\t.justify-center{\r\n\t\tjustify-content: center;\r\n\t}\r\n\t.align-center {\r\n\t\talign-items: center;\r\n\t}\r\n\t.align-baseline{\r\n\t\talign-items: baseline;\r\n\t}\r\n\t.bg_color{\r\n\t\t@include main_bg_color(theme);\r\n\t}\r\n\t.vip_icon {\r\n\t\twidth: 44rpx;\r\n\t\theight: 28rpx;\r\n\t}\r\n\t.pl-2{\r\n\t\tpadding-left: 20rpx;\r\n\t}\r\n\t.vip_money {\r\n\t\tbackground: #FFE7B9;\r\n\t\tborder-radius: 4px;\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #333;\r\n\t\tline-height: 28rpx;\r\n\t\ttext-align: center;\r\n\t\tpadding: 0 6rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tmargin-left: -4rpx;\r\n\t}\r\n\t.theme_price{\r\n\t\t@include price_color(theme);\r\n\t}\r\n\t.activityName {\r\n\t\tline-height: 44rpx;\r\n\t}\r\n\r\n\t.userEvaluation {\r\n\t\ti {\r\n\t\t\tdisplay: inline-block;\r\n\t\t}\r\n\t}\r\n\r\n\t.bntVideo {\r\n\t\twidth: auto !important;\r\n\r\n\t\t.buy {\r\n\t\t\tborder-radius: 50rpx !important;\r\n\t\t}\r\n\t}\r\n\r\n\t.attribute {\r\n\t\t.line1 {\r\n\t\t\twidth: 600rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.chat-btn {\r\n\t\tbackground-color: antiquewhite !important;\r\n\t}\r\n\r\n\t.activity_pin {\r\n\t\twidth: auto;\r\n\t\theight: 44rpx;\r\n\t\tline-height: 44rpx;\r\n\t\t// background: linear-gradient(90deg, rgba(233, 51, 35, 1) 0%, rgba(250, 101, 20, 1) 100%);\r\n\t\t@include linear-gradient(theme);\r\n\t\topacity: 1;\r\n\t\tborder-radius: 22rpx;\r\n\t\tpadding: 0 15rpx;\r\n\t\t// margin-left: 19rpx;\r\n\t}\r\n\r\n\t.activity_miao {\r\n\t\twidth: auto;\r\n\t\theight: 44rpx;\r\n\t\tline-height: 44rpx;\r\n\t\tpadding: 0 15rpx;\r\n\t\t// background: linear-gradient(90deg, rgba(250, 102, 24, 1) 0%, rgba(254, 161, 15, 1) 100%);\r\n\t\t@include linear-gradient(theme);\r\n\t\topacity: 1;\r\n\t\tborder-radius: 22rpx;\r\n\t\tmargin-left: 19rpx;\r\n\t}\r\n\r\n\t.iconfonts {\r\n\t\tcolor: #fff !important;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t.activity_title {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #fff;\r\n\t}\r\n\r\n\t.activity_kan {\r\n\t\twidth: auto;\r\n\t\theight: 44rpx;\r\n\t\tline-height: 44rpx;\r\n\t\tpadding: 0 15rpx;\r\n\t\t@include linear-gradient(theme);\r\n\t\topacity: 1;\r\n\t\tborder-radius: 22rpx;\r\n\t\tmargin-left: 19rpx;\r\n\t}\r\n\r\n\t.mask {\r\n\t\tz-index: 300 !important;\r\n\t}\r\n\r\n\t.head-bar {\r\n\t\tbackground: #fff;\r\n\t}\r\n\r\n\t.generate-posters {\r\n\t\twidth: 100%;\r\n\t\theight: 318rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\tbottom: 0;\r\n\t\tz-index: 388;\r\n\t\ttransform: translate3d(0, 100%, 0);\r\n\t\ttransition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);\r\n\t\tborder-top: 1rpx solid #eee;\r\n\t\t\r\n\t\t.generateCon {\r\n\t\t\theight: 220rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.generateClose {\r\n\t\t\theight: 98rpx;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #333333;\r\n\t\t\tborder-top: 1px solid #eee;\r\n\t\t}\r\n\t\t\r\n\t\t.item {\r\n\t\t\t.pictrue {\r\n\t\t\t\twidth: 96rpx;\r\n\t\t\t\theight: 96rpx;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\tmargin: 0 auto 6rpx auto;\r\n\t\t\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.generate-posters.on {\r\n\t\ttransform: translate3d(0, 0, 0);\r\n\t}\r\n\t\r\n\t.generate-posters .item {\r\n\t\tflex: 1;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 30rpx;\r\n\t}\r\n\t\r\n\t.generate-posters .item .iconfont {\r\n\t\tfont-size: 80rpx;\r\n\t\tcolor: #5eae72;\r\n\t}\r\n\t\r\n\t.generate-posters .item .iconfont.icon-haibao {\r\n\t\tcolor: #5391f1;\r\n\t}\r\n\t\r\n\t.generate-posters .item .iconfont.icon-haowuquan1 {\r\n\t\tcolor: #ff954d;\r\n\t}\r\n\r\n\t.product-con .footer {\r\n\t\tpadding: 0 20rpx 0 30rpx;\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\t\twidth: 100%;\r\n\t\tbox-sizing: border-box;\r\n\t\tbackground-color: #fff;\r\n\t\tz-index: 277;\r\n\t\tborder-top: 1rpx solid #f0f0f0;\r\n\t\theight: 100rpx;\r\n\t\theight: calc(100rpx+ constant(safe-area-inset-bottom)); ///兼容 IOS<11.2/\r\n\t\theight: calc(100rpx + env(safe-area-inset-bottom)); ///兼容 IOS>11.2/\r\n\t}\r\n\r\n\t.product-con .footer .item {\r\n\t\tfont-size: 18rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.product-con .footer .item .iconfont {\r\n\t\ttext-align: center;\r\n\t\tfont-size: 40rpx;\r\n\t}\r\n\r\n\t.product-con .footer .item .iconfont.icon-shoucang1 {\r\n\t\t@include main_color(theme);\r\n\t}\r\n\r\n\t.product-con .footer .item .iconfont.icon-gouwuche1 {\r\n\t\tfont-size: 40rpx;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.product-con .footer .item .iconfont.icon-gouwuche1 .num {\r\n\t\tcolor: #fff;\r\n\t\tposition: absolute;\r\n\t\tfont-size: 18rpx;\r\n\t\tpadding: 2rpx 8rpx 3rpx;\r\n\t\tborder-radius: 200rpx;\r\n\t\ttop: -10rpx;\r\n\t\tright: -10rpx;\r\n\t}\r\n\r\n\t.product-con .footer .bnt {\r\n\t\twidth: 444rpx;\r\n\t\theight: 76rpx;\r\n\t}\r\n\r\n\t.product-con .footer .bnt .bnts {\r\n\t\twidth: 222rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 76rpx;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t.product-con .footer .bnt .joinCart {\r\n\t\tborder-radius: 50rpx 0 0 50rpx;\r\n\t\t@include left_color(theme);\r\n\t}\r\n\r\n\t.product-con .footer .bnt .buy {\r\n\t\tborder-radius: 0 50rpx 50rpx 0;\r\n\t\t@include main_bg_color(theme);\r\n\t}\r\n\r\n\t.product-con .store-info {\r\n\t\tmargin-top: 20rpx;\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\r\n\t.product-con .store-info .title {\r\n\t\tpadding: 0 30rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #282828;\r\n\t\theight: 80rpx;\r\n\t\tline-height: 80rpx;\r\n\t\tborder-bottom: 1px solid #f5f5f5;\r\n\t}\r\n\r\n\t.product-con .store-info .info {\r\n\t\tpadding: 0 30rpx;\r\n\t\theight: 126rpx;\r\n\t}\r\n\r\n\t.product-con .store-info .info .picTxt {\r\n\t\twidth: 615rpx;\r\n\t}\r\n\r\n\t.product-con .store-info .info .picTxt .pictrue {\r\n\t\twidth: 76rpx;\r\n\t\theight: 76rpx;\r\n\t}\r\n\r\n\t.product-con .store-info .info .picTxt .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 6rpx;\r\n\t}\r\n\r\n\t.product-con .store-info .info .picTxt .text {\r\n\t\twidth: 522rpx;\r\n\t}\r\n\r\n\t.product-con .store-info .info .picTxt .text .name {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #282828;\r\n\t}\r\n\r\n\t.product-con .store-info .info .picTxt .text .address {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666;\r\n\t\tmargin-top: 3rpx;\r\n\t}\r\n\r\n\t.product-con .store-info .info .picTxt .text .address .iconfont {\r\n\t\tcolor: #707070;\r\n\t\tfont-size: 18rpx;\r\n\t\tmargin-left: 10rpx;\r\n\t}\r\n\r\n\t.product-con .store-info .info .picTxt .text .address .addressTxt {\r\n\t\tmax-width: 480rpx;\r\n\t}\r\n\r\n\t.product-con .store-info .info .iconfont {\r\n\t\tfont-size: 40rpx;\r\n\t}\r\n\r\n\t.product-con .superior {\r\n\t\tbackground-color: #fff;\r\n\t\tmargin-top: 30rpx;\r\n\t\tpadding: 0 24rpx 30rpx 24rpx;\r\n\t}\r\n\r\n\t.product-con .superior .title {\r\n\t\theight: 98rpx;\r\n\t}\r\n\r\n\t.product-con .superior .title image {\r\n\t\twidth: 20rpx;\r\n\t\theight: 20rpx;\r\n\t}\r\n\r\n\t.product-con .superior .title .titleTxt {\r\n\t\tmargin: 0 10rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.product-con .superior .slider-banner {\r\n\t\twidth: 100%;\r\n\t\tmargin: 0 auto;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.product-con .superior .slider-banner swiper {\r\n\t\theight: 100%;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.product-con .superior .slider-banner swiper-item {\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.product-con .superior .slider-banner .list {\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.product-con .superior .slider-banner .list .item {\r\n\t\twidth: 198rpx;\r\n\t\tmargin: 0 22rpx 30rpx 0;\r\n\t\tfont-size: 26rpx;\r\n\t}\r\n\r\n\t.product-con .superior .slider-banner .list .item:nth-of-type(3n) {\r\n\t\tmargin-right: 0;\r\n\t}\r\n\r\n\t.product-con .superior .slider-banner .list .item .pictrue {\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\theight: 198rpx;\r\n\t}\r\n\r\n\t.product-con .superior .slider-banner .list .item .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 6rpx;\r\n\t}\r\n\r\n\t.product-con .superior .slider-banner .list .item .name {\r\n\t\tcolor: #282828;\r\n\t\tmargin-top: 12rpx;\r\n\t}\r\n\r\n\t.product-con .superior .slider-banner .swiper-pagination-bullet {\r\n\t\tbackground-color: #999;\r\n\t}\r\n\r\n\t.product-con .superior .slider-banner .swiper-pagination-bullet-active {\r\n\t\tbackground-color: $theme-color;\r\n\t}\r\n\r\n\tbutton {\r\n\t\tpadding: 0;\r\n\t\tmargin: 0;\r\n\t\tline-height: normal;\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\r\n\tbutton::after {\r\n\t\tborder: 0;\r\n\t}\r\n\r\n\taction-sheet-item {\r\n\t\tpadding: 0;\r\n\t\theight: 240rpx;\r\n\t\talign-items: center;\r\n\t\tdisplay: flex;\r\n\t}\r\n\r\n\t.contact {\r\n\t\tfont-size: 16px;\r\n\t\twidth: 50%;\r\n\t\tbackground-color: #fff;\r\n\t\tpadding: 8rpx 0;\r\n\t\tborder-radius: 0;\r\n\t\tmargin: 0;\r\n\t\tline-height: 2;\r\n\t}\r\n\r\n\t.contact::after {\r\n\t\tborder: none;\r\n\t}\r\n\r\n\t.action-sheet {\r\n\t\tfont-size: 17px;\r\n\t\tline-height: 1.8;\r\n\t\twidth: 50%;\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tright: 0;\r\n\t\tpadding: 25rpx 0;\r\n\t}\r\n\r\n\t.canvas {\r\n\t\tposition: fixed;\r\n\t\tz-index: -5;\r\n\t\topacity: 0;\r\n\t}\r\n\r\n\t.poster-pop {\r\n\t\tposition: fixed;\r\n\t\twidth: 450rpx;\r\n\t\theight: 714rpx;\r\n\t\ttop: 50%;\r\n\t\tleft: 50%;\r\n\t\ttransform: translateX(-50%);\r\n\t\tmargin-top: -432rpx;\r\n\t\tz-index: 399;\r\n\t}\r\n\r\n\t.poster-pop image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.poster-pop .close {\r\n\t\twidth: 46rpx;\r\n\t\theight: 75rpx;\r\n\t\tposition: fixed;\r\n\t\tright: 0;\r\n\t\ttop: -73rpx;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.poster-pop .save-poster {\r\n\t\tbackground-color: #df2d0a;\r\n\t\tfont-size: ：22rpx;\r\n\t\tcolor: #fff;\r\n\t\ttext-align: center;\r\n\t\theight: 76rpx;\r\n\t\tline-height: 76rpx;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.poster-pop .keep {\r\n\t\tcolor: #fff;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 25rpx;\r\n\t\tmargin-top: 10rpx;\r\n\t}\r\n\r\n\t.mask {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.6);\r\n\t}\r\n\r\n\t.pro-wrapper .iconn {\r\n\t\tbackground-image: url('data:image/png;base64,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');\r\n\t\twidth: 100rpx;\r\n\t\theight: 100rpx;\r\n\t\tbackground-repeat: no-repeat;\r\n\t\tbackground-size: 100% 100%;\r\n\t\tmargin: 0 auto;\r\n\t}\r\n\r\n\t.pro-wrapper .iconn.iconn1 {\r\n\t\tbackground-image: url('data:image/png;base64,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');\r\n\t}\r\n\r\n\t.pictrue_log {\r\n\t\twidth: 80upx;\r\n\t\theight: 40upx;\r\n\t\tborder-radius: 10upx 0 12upx 0;\r\n\t\tline-height: 40upx;\r\n\t\tfont-size: 24upx;\r\n\t}\r\n\r\n\t.pictrue_log_class {\r\n\t\tz-index: 3;\r\n\t\tbackground: -webkit-gradient(linear, left top, right top, from(rgba(246, 122, 56, 1)), to(rgba(241, 27, 9, 1)));\r\n\t\tbackground: linear-gradient(90deg, rgba(246, 122, 56, 1) 0%, rgba(241, 27, 9, 1) 100%);\r\n\t\topacity: 1;\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tcolor: #fff;\r\n\t\ttext-align: center;\r\n\r\n\t}\r\n\r\n\t.tab_nav .header {\r\n\t\twidth: 100%;\r\n\t\theight: 96rpx;\r\n\t\tpadding: 0 30rpx 0;\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #050505;\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\r\n\t.icon-xiangzuo {\r\n\t\t/* #ifdef H5 */\r\n\t\ttop: 20rpx !important;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.navbar .header .item {\r\n\t\tposition: relative;\r\n\t\tmargin: 0 25rpx;\r\n\t}\r\n\r\n\t.navbar .header .item.on:before {\r\n\t\tposition: absolute;\r\n\t\twidth: 60rpx;\r\n\t\theight: 5rpx;\r\n\t\tbackground-repeat: no-repeat;\r\n\t\tcontent: \"\";\r\n\t\t@include linear-gradient(theme);\r\n\t\tbottom: -10rpx;\r\n\t\tleft: 50%;\r\n\t\tmargin-left: -28rpx;\r\n\t}\r\n\r\n\t.navbar {\r\n\t\tposition: fixed;\r\n\t\t// background-color: #fff;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tz-index: 99;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.navbar .navbarH {\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.navbar .navbarH .navbarCon {\r\n\t\tposition: absolute;\r\n\t\tbottom: 0;\r\n\t\theight: 100rpx;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.h5_back {\r\n\t\tcolor: #000;\r\n\t\tposition: fixed;\r\n\t\tleft:20rpx;\r\n\t\tfont-size: 32rpx;\r\n\t\ttext-align: center;\r\n\t\twidth: 58rpx;\r\n\t\theight: 58rpx;\r\n\t\tbackground: rgba(255, 255, 255, 0.3);\r\n\t\tborder: 1px solid rgba(0,0,0,0.1);\r\n\t\tborder-radius: 50%;\r\n\t}\r\n\r\n\t.share-box {\r\n\t\tz-index: 1000;\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\r\n\t\timage {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t}\r\n\t}\r\n\t.mask_transparent{\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground:transparent;\r\n\t\tz-index: 300;\r\n\t}\r\n\t.px-12 {\r\n\t\tpadding-left: 12rpx;\r\n\t\tpadding-right: 12rpx;\r\n\t}\r\n\t.font-44{\r\n\t\tfont-size: 44rpx;\r\n\t}\r\n\t.font_color{\r\n\t\t@include main_color(theme);\r\n\t}\r\n\t.attrImg{\r\n\t\twidth: 66rpx;\r\n\t\theight: 66rpx;\r\n\t\tborder-radius: 6rpx;\r\n\t\tdisplay: block;\r\n\t\tmargin-right: 14rpx;\r\n\t}\r\n\t.switchTxt{\r\n\t\theight: 60rpx;\r\n\t\tflex: 1;\r\n\t\tline-height: 60rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tbackground: #EEEEEE;\r\n\t\tpadding-right: 0 24rpx 0;\r\n\t\tborder-radius: 8rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=78ee64b3&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=78ee64b3&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294178739\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}