{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_combination/index.vue?4182", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_combination/index.vue?bdf1", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_combination/index.vue?933d", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_combination/index.vue?c1f1", "uni-app:///pages/activity/goods_combination/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_combination/index.vue?b587", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_combination/index.vue?5efa", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_combination/index.vue?db0f", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_combination/index.vue?1b2c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "home", "data", "showSkeleton", "isNodes", "indicatorDots", "circular", "autoplay", "interval", "duration", "navH", "combinationList", "limit", "page", "loading", "loadend", "returnShow", "loadTitle", "avatarList", "bannerList", "totalPeople", "theme", "bgColor", "onLoad", "that", "uni", "frontColor", "backgroundColor", "setTimeout", "title", "methods", "goBack", "openSubcribe", "animationType", "animationDuration", "url", "getCombinationHeader", "value", "image", "otPrice", "price", "setShare", "desc", "link", "imgUrl", "console", "onReachBottom"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACc;AACwB;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnCA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACkFnnB;AAIA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AAAA,eACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACA;IACA;IACA;IACA;IACAH;MACAI;IACA;IAEA;IAMA;IACA;EACA;EACAC;IACAC;MACAN;IACA;IACAO;MACA;MASAP;QACAI;MACA;MACA;QACAJ;QACAA;UACAQ;UACAC;UACAC;QACA;MACA;QACAV;MACA;IAEA;IACAW;MAAA;MACA;QAAAC;MAAA;MACA;QACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;;MAEA;MAEA;MACA;MACAb;MACA;QACAX;QACAD;MACA;MACA;QAAA0B;QAAAC;QAAAC;QAAAX;MAAA;MACA;MACA;QACAL;QACA;QACA;QACA;QACAA;QACAA;QAIAA;QACAA;QACAA;QACAI;UACAJ;QACA;MACA;QACAA;QACAA;MACA;IACA;IACAiB;MACA,2BACA,0BACA,6BACA,2BACA,yBACA,sBACA;QACAC;QACAb;QACAc;QACAC;MACA,wBACA;QACAC;MACA;IACA;EACA;EACAC;IACA;;;;;;;;;;;;;;;;AC5OA;AAAA;AAAA;AAAA;AAA0oC,CAAgB,gnCAAG,EAAC,C;;;;;;;;;;;ACA9pC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/activity/goods_combination/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/activity/goods_combination/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=e6e4503c&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\nimport style1 from \"./index.vue?vue&type=style&index=1&id=e6e4503c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"e6e4503c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/activity/goods_combination/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=e6e4503c&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.bannerList.length\n  var g1 = _vm.avatarList.length\n  var l0 =\n    g1 > 0\n      ? _vm.__map(_vm.avatarList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = index === 6 && Number(_vm.avatarList.length) > 3\n          return {\n            $orig: $orig,\n            m0: m0,\n          }\n        })\n      : null\n  var g2 = _vm.combinationList.length\n  var g3 = _vm.combinationList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        l0: l0,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :data-theme=\"theme\">\r\n\t\t<view class=\"pageInfo\">\r\n\t\t\t<skeleton :show=\"showSkeleton\" :isNodes=\"isNodes\" ref=\"skeleton\" loading=\"chiaroscuro\" selector=\"skeleton\"></skeleton>\r\n\t\t\t<view class=\"skeleton\" :style=\"{visibility: showSkeleton ? 'hidden' : 'visible'}\">\r\n\t\t\t\t<view class=\"combinationBj\"></view>\r\n\t\t\t\t<view class=\"combinationList\">\r\n\t\t\t\t\t<view class='group-list'>\r\n\t\t\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t\t\t<view class='iconfont icon-xiangzuo' @tap='goBack' :style=\"'top:'+ (navH/2) +'rpx'\" v-if=\"returnShow\"></view>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t<!-- banner -->\r\n\t\t\t\t\t\t<view class=\"swiper skeleton-rect\" v-if=\"bannerList.length\">\r\n\t\t\t\t\t\t\t<swiper indicator-dots=\"true\" :autoplay=\"true\" :circular=\"circular\" :interval=\"interval\"\r\n\t\t\t\t\t\t\t\t:duration=\"duration\" indicator-color=\"rgba(255,255,255,0.6)\" indicator-active-color=\"#fff\">\r\n\t\t\t\t\t\t\t\t<block v-for=\"(item,index) in bannerList\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t<swiper-item>\r\n\t\t\t\t\t\t\t\t\t\t<navigator :url='item.value' class='slide-navigator acea-row row-between-wrapper'\r\n\t\t\t\t\t\t\t\t\t\t\thover-class='none'>\r\n\t\t\t\t\t\t\t\t\t\t\t<image :src=\"item.value\" class=\"slide-image\" lazy-load mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t\t\t\t\t</navigator>\r\n\t\t\t\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</swiper>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"nav acea-row row-between-wrapper\" v-if=\"avatarList.length > 0\">\r\n\t\t\t\t\t\t\t<image src=\"../static/zuo.png\"></image>\r\n\t\t\t\t\t\t\t<view class=\"title acea-row row-center\">\r\n\t\t\t\t\t\t\t\t<view class=\"spike-bd\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"activity_pic\">\r\n\t\t\t\t\t\t\t\t\t\t<view v-for=\"(item,index) in avatarList\" :key=\"index\" class=\"picture\"\r\n\t\t\t\t\t\t\t\t\t\t\t:style='index===6?\"position: relative\":\"position: static\"'>\r\n\t\t\t\t\t\t\t\t\t\t\t<span class=\"avatar\" :style='\"background-image: url(\"+item+\")\"'></span>\r\n\t\t\t\t\t\t\t\t\t\t\t<span v-if=\"index===6 && Number(avatarList.length) > 3\" class=\"mengceng\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<i>···</i>\r\n\t\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<text class=\"pic_count\">{{totalPeople}}人参与</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<image src=\"../static/you.png\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='list'>\r\n\t\t\t\t\t\t\t<block v-for=\"(item,index) in combinationList\" :key='index'>\r\n\t\t\t\t\t\t\t\t<view class='item acea-row row-between-wrapper' @tap=\"openSubcribe(item)\"\r\n\t\t\t\t\t\t\t\t data-url=''>\r\n\t\t\t\t\t\t\t\t\t<view class='pictrue skeleton-rect'>\r\n\t\t\t\t\t\t\t\t\t\t<image :src='item.image'></image>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class='text'>\r\n\t\t\t\t\t\t\t\t\t\t<view class='line2 skeleton-rect'>{{item.title}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<text class='y-money skeleton-rect'>￥{{item.otPrice}}</text>\r\n\t\t\t\t\t\t\t\t\t\t<view class='bottom acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class='money skeleton-rect'>￥<text class='num'>{{item.price}}</text></view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"bnt acea-row row-center-wrapper\" v-if=\"item.stock>0\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"light\"><image src=\"../static/shandian1.png\"></image></view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"num\">{{item.people}}人团</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"go\">去拼团</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"bnt gray acea-row row-center-wrapper\" v-else>已售罄</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<view class='loadingicon acea-row row-center-wrapper' v-if='combinationList.length > 0' style=\"color:#fff;\">\r\n\t\t\t\t\t\t\t\t<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>{{loadTitle}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view v-if=\"combinationList.length == 0\" class=\"no_shop flex-center\">\r\n\t\t\t\t\t\t\t<image src=\"../../../static/images/noShopper.png\" mode=\"aspectFit\" style=\"width: 400rpx;\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- <home></home> -->\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\t\r\n\t</view>\r\n\t\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tgetCombinationList,\r\n\t\tcombinationHeaderApi\r\n\t} from '@/api/activity.js';\r\n\timport {openPinkSubscribe} from '../../../utils/SubscribeMessage.js';\r\n\timport {setThemeColor} from '@/utils/setTheme.js'\r\n\timport home from '@/components/home/<USER>'\r\n\timport animationType from '@/utils/animationType.js'\r\n\tlet app = getApp();\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\thome\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tshowSkeleton: true, //骨架屏显示隐藏\r\n\t\t\t\tisNodes: 0, //控制什么时候开始抓取元素节点,只要数值改变就重新抓取\r\n\t\t\t\tindicatorDots: false,\r\n\t\t\t\tcircular: true,\r\n\t\t\t\tautoplay: true,\r\n\t\t\t\tinterval: 3000,\r\n\t\t\t\tduration: 500,\r\n\t\t\t\tnavH: '',\r\n\t\t\t\tcombinationList: [],\r\n\t\t\t\tlimit: 10,\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tloading: false,\r\n\t\t\t\tloadend: false,\r\n\t\t\t\treturnShow: true,\r\n\t\t\t\tloadTitle: '',\r\n\t\t\t\tavatarList: [],\r\n\t\t\t\tbannerList: [],\r\n\t\t\t\ttotalPeople: 0,\r\n\t\t\t\ttheme:app.globalData.theme,\r\n\t\t\t\tbgColor:'#e93323'\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tlet that = this;\r\n\t\t\tthat.bgColor = setThemeColor();\r\n\t\t\tuni.setNavigationBarColor({\r\n\t\t\t\tfrontColor: '#ffffff',\r\n\t\t\t\tbackgroundColor:that.bgColor,\r\n\t\t\t});\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.isNodes++;\r\n\t\t\t}, 500);\r\n\t\t\tvar pages = getCurrentPages();\r\n\t\t\tthis.returnShow = pages.length===1?false:true;\r\n\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\ttitle:\"拼团列表\"\r\n\t\t\t})\r\n\t\t\t// #ifdef MP\r\n\t\t\tthis.navH = app.globalData.navH;\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef H5\r\n\t\t\tthis.navH = app.globalData.navHeight;\r\n\t\t\t// #endif\r\n\t\t\t\r\n\t\t\tthis.getCombinationList();\r\n\t\t\tthis.getCombinationHeader();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgoBack: function() {\r\n\t\t\t\tuni.navigateBack();\r\n\t\t\t},\r\n\t\t\topenSubcribe: function(item) {\r\n\t\t\t\tlet page = item;\r\n\t\t\t\t// #ifndef MP\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\tanimationType: animationType.type,\r\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\turl: `/pages/activity/goods_combination_details/index?id=${item.id}`\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef MP\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '正在加载',\r\n\t\t\t\t})\r\n\t\t\t\topenPinkSubscribe().then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\tanimationType: animationType.type,\r\t\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\t\turl: `/pages/activity/goods_combination_details/index?id=${item.id}`\r\n\t\t\t\t\t});\r\n\t\t\t\t}).catch(() => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tgetCombinationHeader: function() {\r\n\t\t\t\tthis.bannerList=[{value:''}];\r\n\t\t\t\tcombinationHeaderApi().then(res => {\r\n\t\t\t\t\tthis.avatarList = res.data.avatarList || [];\r\n\t\t\t\t\tthis.bannerList = res.data.bannerList || [];\r\n\t\t\t\t\tthis.totalPeople = res.data.totalPeople;\r\n\t\t\t\t}).catch(() => {\r\n\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\tthis.loadTitle = '加载更多';\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetCombinationList: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\t\r\n\t\t\t\tif (that.loadend) return;\r\n\t\t\t\tif (that.loading) return;\r\n\t\t\t\tthat.loadTitle = '';\r\n\t\t\t\tvar data = {\r\n\t\t\t\t\tpage: that.page,\r\n\t\t\t\t\tlimit: that.limit\r\n\t\t\t\t};\r\n\t\t\t\tthis.combinationList = [{image:'',otPrice:'',price:'',title:''}];\r\n\t\t\t\tthis.loading = true\r\n\t\t\t\tgetCombinationList(data).then(function(res) {\r\n\t\t\t\t\tthat.combinationList = [];\r\n\t\t\t\t\tlet list = res.data.list;\r\n\t\t\t\t\tlet combinationList = that.$util.SplitArray(list, that.combinationList);\r\n\t\t\t\t\tlet loadend = list.length < that.limit;\r\n\t\t\t\t\tthat.loadend = loadend;\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\tthat.setShare();\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tthat.loadTitle = loadend ? '已全部加载' : '加载更多';\r\n\t\t\t\t\tthat.$set(that, 'combinationList', combinationList);\r\n\t\t\t\t\tthat.$set(that, 'page', that.page + 1);\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthat.showSkeleton = false\r\n\t\t\t\t\t}, 1000)\r\n\t\t\t\t}).catch(() => {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.loadTitle = '加载更多';\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tsetShare: function() {\r\n\t\t\t\tthis.$wechat.isWeixin() &&\r\n\t\t\t\t\tthis.$wechat.wechatEvevt([\r\n\t\t\t\t\t\t\"updateAppMessageShareData\",\r\n\t\t\t\t\t\t\"updateTimelineShareData\",\r\n\t\t\t\t\t\t\"onMenuShareAppMessage\",\r\n\t\t\t\t\t\t\"onMenuShareTimeline\"\r\n\t\t\t\t\t], {\r\n\t\t\t\t\t\tdesc: this.combinationList[0].title,\r\n\t\t\t\t\t\ttitle: this.combinationList[0].title,\r\n\t\t\t\t\t\tlink: location.href,\r\n\t\t\t\t\t\timgUrl:this.combinationList[0].image \r\n\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\tconsole.log(err);\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t},\r\n\t\tonReachBottom: function() {\r\n\t\t\tthis.getCombinationList();\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\tpage {\r\n\t\t@include main_bg_color(theme);\r\n\t\theight: 100vh;\r\n\t\toverflow: auto;\r\n\t}\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n\t.pageInfo{\r\n\t\t/* #ifdef MP || APP-PLUS */\r\n\t\t@include main_bg_color(theme);\r\n\t\theight: 100vh;\r\n\t\toverflow: auto;\r\n\t\t/* #endif */\r\n\t}\r\n\t.mengceng {\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t\tline-height: 36rpx;\r\n\t\tbackground: rgba(51, 51, 51, 0.6);\r\n\t\ttext-align: center;\r\n\t\tborder-radius: 50%;\r\n\t\topacity: 1;\r\n\t\tposition: absolute;\r\n\t\tleft: -2rpx;\r\n\t\tcolor: #FFF;\r\n\t\ttop: 2rpx;\r\n\t\ti{\r\n\t\t\tfont-style: normal;\r\n\t\t\tfont-size: 20rpx;\r\n\t\t}\r\n\t}\r\n\t.activity_pic {\r\n\t\r\n\t\t.picture {\r\n\t\t\tdisplay: inline-table;\r\n\t\t}\r\n\t\r\n\t\t.avatar {\r\n\t\t\twidth: 38rpx;\r\n\t\t\theight: 38rpx;\r\n\t\t\tdisplay: inline-table;\r\n\t\t\tvertical-align: middle;\r\n\t\t\t-webkit-user-select: none;\r\n\t\t\t-moz-user-select: none;\r\n\t\t\t-ms-user-select: none;\r\n\t\t\tuser-select: none;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tbackground-repeat: no-repeat;\r\n\t\t\tbackground-size: cover;\r\n\t\t\tbackground-position: 0 0;\r\n\t\t\tmargin-right: -10rpx;\r\n\t\t\tbox-shadow: 0 0 0 1px #fff;\r\n\t\t}\r\n\t}\r\n    .combinationList{\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tpadding: 25rpx 30rpx;\r\n\t\tbackground: url(../static/pinbei.png) no-repeat;\r\n\t\tbackground-size: 100% 990rpx;\r\n\t\t.swiper{\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 300rpx;\r\n\t\t\tborder-radius: 14rpx;\r\n\t\t\tmargin-bottom: 34rpx;\r\n\t\t\tswiper,\r\n\t\t\t.swiper-item,\r\n\t\t\timage {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 300rpx;\r\n\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.nav{\r\n\t\t\twidth: 100%;\r\n\t\t\tmargin-bottom: 34rpx;\r\n\t\t\timage{\r\n\t\t\t\twidth: 102rpx;\r\n\t\t\t\theight: 4rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.title {\r\n\t\t\twidth: 68%;\r\n\t\t\t.pic_count {\r\n\t\t\t\tmargin-left: 30rpx;\r\n\t\t\t\tcolor: $theme-color;\r\n\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\theight: auto;\r\n\t\t\t\tbackground-color: rgba(0,0,0,0.1);\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\tborder-radius: 19rpx;\r\n\t\t\t\tpadding: 4rpx 14rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.icon-xiangzuo {\r\n\t\tfont-size: 40rpx;\r\n\t\tcolor: #fff;\r\n\t\tposition: fixed;\r\n\t\tleft: 30rpx;\r\n\t\tz-index: 99;\r\n\t\ttransform: translateY(-20%);\r\n\t}\r\n\r\n\t.group-list .list .item {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 14rpx;\r\n\t\tpadding: 22rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tmargin: 0 auto 20rpx auto;\r\n\t}\r\n\r\n\t.group-list .list .item .pictrue {\r\n\t\twidth: 186rpx;\r\n\t\theight: 186rpx;\r\n\t}\r\n\r\n\t.group-list .list .item .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 6rpx;\r\n\t}\r\n\r\n\t.group-list .list .item .text {\r\n\t\twidth: 440rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #333333;\r\n\t\tmargin-left: 20rpx;\r\n\t\t.line2{\r\n\t\t\theight: 86rpx;\r\n\t\t}\r\n\t\t.bnt{\r\n\t\t\theight: 58rpx;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tposition: relative;\r\n\t\t\t@include main_bg_color(theme);\r\n\t\t\tborder-radius: 28rpx;\r\n\t\t\t.light{\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\twidth: 28rpx;\r\n\t\t\t\theight: 58rpx;\r\n\t\t\t\ttop:0;\r\n\t\t\t\tleft:50%;\r\n\t\t\t\tmargin-left: -8rpx;\r\n\t\t\t\timage{\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.num{\r\n\t\t\t\twidth: 120rpx;\r\n\t\t\t\tbackground-color: rgba(255,255,255,0.85);\r\n\t\t\t\t@include main_color(theme);\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tline-height: 58rpx;\r\n\t\t\t\tborder-radius: 28rpx 0 14rpx 28rpx;\r\n\t\t\t}\r\n\t\t\t.go{\r\n\t\t\t\twidth: 112rpx;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tline-height: 58rpx;\r\n\t\t\t\tborder-radius: 0 28rpx 28rpx 0;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t}\r\n\t\t\t&.gray{\r\n\t\t\t\twidth: 148rpx;\r\n\t\t\t\tbackground-color: #cccccc;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t}\r\n\t\t}\r\n\t    .nothing{\r\n\t\t\twidth: 148rpx;\r\n\t\t\theight: 58rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tline-height: 58rpx;\r\n\t\t\tbackground: #CCCCCC;\r\n\t\t\topacity: 1;\r\n\t\t\tcolor: #FFFFFF;\r\n\t\t\tborder-radius: 30rpx;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.group-list .list .item .text .team {\r\n\t\theight: 38rpx;\r\n\t\tborder-radius: 4rpx;\r\n\t\tfont-size: 22rpx;\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\r\n\t.group-list .list .item .text .team .iconfont {\r\n\t\twidth: 54rpx;\r\n\t\tbackground-color: #ffdcd9;\r\n\t\ttext-align: center;\r\n\t\tcolor: #dd3823;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.group-list .list .item .text .team .num {\r\n\t\ttext-align: center;\r\n\t\tpadding: 0 6rpx;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.group-list .list .item .text .bottom .money {\r\n\t\tfont-size: 24rpx;\r\n\t\tfont-weight: bold;\r\n\t\t@include price_color(theme);\r\n\t}\r\n\r\n\t.group-list .list .item .text .bottom .money .num {\r\n\t\tfont-size: 32rpx;\r\n\t}\r\n\r\n\t.group-list .list .item .text .y-money {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t\tfont-weight: normal;\r\n\t\ttext-decoration: line-through;\r\n\t}\r\n\r\n\t.group-list .list .item .text .bottom .groupBnt {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #fff;\r\n\t\twidth: 146rpx;\r\n\t\theight: 54rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 54rpx;\r\n\t\tborder-radius: 4rpx;\r\n\t}\r\n\r\n\t.group-list .list .item .text .bottom .groupBnt .iconfont {\r\n\t\tfont-size: 25rpx;\r\n\t\tvertical-align: 2rpx;\r\n\t\tmargin-left: 10rpx;\r\n\t}\r\n\t.flex-center{\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t}\r\n\t.no_shop{\r\n\t\twidth: 100%;\r\n\t\theight: 700rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tbackground-color: #fff;\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294179077\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=1&id=e6e4503c&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=1&id=e6e4503c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294179082\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}