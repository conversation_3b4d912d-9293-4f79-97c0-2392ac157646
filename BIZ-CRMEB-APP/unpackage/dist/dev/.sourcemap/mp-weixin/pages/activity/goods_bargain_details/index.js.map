{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_bargain_details/index.vue?ebc2", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_bargain_details/index.vue?c89d", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_bargain_details/index.vue?c7c9", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_bargain_details/index.vue?e079", "uni-app:///pages/activity/goods_bargain_details/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_bargain_details/index.vue?d513", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_bargain_details/index.vue?feab", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_bargain_details/index.vue?0ab5", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_bargain_details/index.vue?0005"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "countDown", "authorize", "home", "filters", "p<PERSON><PERSON><PERSON><PERSON>", "data", "showSkeleton", "isNodes", "bgColor", "countDownDay", "countDownHour", "countDownMinute", "countDownSecond", "active", "id", "bargainStatus", "startBargainUid", "bargainUserInfo", "storeBargainId", "bargainInfo", "page", "limit", "limitStatus", "bargainUserHelpList", "bargainUserHelpInfo", "bargainUserBargainPrice", "bargainCount", "retunTop", "bargainPartake", "isHelp", "interval", "productStock", "quota", "userBargainStatusHelp", "navH", "bargainPrice", "datatime", "offest", "tagStyle", "img", "table", "video", "H5ShareBox", "systemH", "isAuto", "isShowAuth", "pages", "couponsHidden", "loading", "loadend", "posters", "buyPrice", "qrcodeSize", "posterbackgd", "PromotionCode", "canvasStatus", "imgTop", "imagePath", "theme", "imgHost", "backBg", "backHead", "computed", "onLoad", "setTimeout", "that", "uni", "success", "app", "options", "title", "methods", "goConfirm", "url", "openTap", "goProduct", "userBargain", "goBack", "delta", "make", "window", "uid", "uQRCode", "canvasId", "text", "size", "margin", "complete", "fail", "getImageBase64", "gobargainUserInfo", "bargainId", "bargainUserId", "alreadyPrice", "tab", "goPay", "getBargainDetails", "currentBargainUser", "setBargain", "setBargainHelp", "bargainUserUid", "getBargainUserBargainPricePoster", "mask", "arrImagesUrlTop", "surplusPrice", "goBargainList", "close", "returns", "backList", "onReady", "onShow", "onHide", "onUnload", "onShareAppMessage", "share", "path", "imageUrl"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACc;AACwB;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChIA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACwUnnB;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AASA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AAAA,eAEA;EACAC;IACAC;IAEAC;IAEAC;IACA;EACA;EACAC;IACAC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;EACA;EACA;AACA;AACA;EAEAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;QACA;QACA;QACA;QACA;QACA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;IACA;EAEA;EACAC;EACA;AACA;AACA;EACAC;IAAA;IACAC;MACA;IACA;IACA;IACAC;IACA;MACAA;IACA;IACA;MACA;QACAA;QACAA;QACAA;QACA;MACA;QACAA;QACAA;QACAA;QACA;MACA;QACAA;QACAA;QACAA;QACA;MACA;QACAA;QACAA;QACAA;QACA;MACA;QACAA;QACAA;QACAA;QACA;IAAA;IAEAA;IAEAC;MACAC;QACAF;QACAA;MACA;IACA;;IAIA;IACA;IAGA;IACA;MACAA;IACA;IAEA;MACA;QAAA;QACA;QACA;QACAG;QACAA;QACA;MACA;QACA;MACA;IACA;IACAC,0GACArD;IAEA;MACA;QACAiD;MACA;MACA;MACA;IACA;MACA,iCACA,iEACA,kGACA;MACA;IACA;IACAC;MACAI;IACA;IACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACAN;QACAO;MACA;IACA;IACA;IAmEAC;MACA;IACA;IACA;IACAC;MACAT;QACAO;MACA;IACA;IACA;IACAG;MACA;QACA;MACA;IACA;IACAC;MACAX;QAEAY;MACA;IACA;IACA;IACAC;MAAA;MACA,sCACA,OACAC,uBACA,yFACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAnB;UACA;QACA;QACAoB;QACAC;UACA;YACAlB;UACA;QACA;MACA;IACA;IACA;IACAmB;MACA;MACA;QACAhB;MACA;QACAR;MACA;IACA;IACA;IACAyB;MAAA;MACA;QACAC;QACAC;MACA;QACA;QACA;QACA;QACA;QACA,kGACAC;QACA;MAKA;QACA;QACA;UACAvB;QACA;UACAwB;UACArB;QACA;MACA;IACA;IACAsB;MAAA;MACA;QACA;MACA;QACA;QACA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;QACA/B;QACAA;QACAA;QACAA;QACAA,2EACA,2BACAgB;QACAf;UACAI;QACA;QACAL;QACA;UACAA;QACA;UACAA;QACA;QAIAD;UACAC;QACA;MACA;QACAA;UACAK;QACA;UACAwB;UACArB;QACA;MACA;IACA;IACAwB;MAAA;MACA;MACA/B;QACAO,yFACA;MACA;IACA;IACAyB;MAAA;MAAA;MACA;MACA;QACA;UACA;QACA;QACA;QACAjC;QACAA;MAIA;QACA;QACAA;UACAK;QACA;MACA;IACA;IACA;IACA6B;MAAA;MACA;QACAR;QACAC;QACAQ;MACA;MACA;QACA;QACA;QACA;QACA;MACA;QACA;UACA9B;QACA;QACA;MACA;IACA;IACA+B;MAAA;MACA;MACA;MACAnC;QACAI;QACAgC;MACA;MACA;MACA;MACA;MACA;QACApC;QACA;UACAI;QACA;QACA;MACA;MACAN;QACA;UACAE;UACA;YACAI;UACA;UACA;QACA;MACA;MACAJ;QACAO;QACAN;UACAoC;UACA;UACAvC;YACA,wEACAb,+CACAqD,2BACA;cACA;cACA;cACAtC;YACA;UACA;QACA;MACA;IACA;IACAuC;MACAvC;QACAO;MACA;IACA;IACAiC;MACA;MACA;MACA;IACA;IACA;IACAC;MACAzC;QACAO;MACA;IACA;IACAmC;MACA1C;QACAO;MACA;IACA;EACA;EAEA;AACA;AACA;EACAoC,6BAEA;EACA;AACA;AACA;EACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;EACA;EAGA;AACA;AACA;EACAC;IACA;MACAC;QACA5C;QACA6C,+FACAnG;QACAoG;MACA;IACAnD;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;ACz4BA;AAAA;AAAA;AAAA;AAA0oC,CAAgB,gnCAAG,EAAC,C;;;;;;;;;;;ACA9pC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/activity/goods_bargain_details/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/activity/goods_bargain_details/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=07a662b0&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\nimport style1 from \"./index.vue?vue&type=style&index=1&id=07a662b0&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"07a662b0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/activity/goods_bargain_details/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=07a662b0&scoped=true&\"", "var components\ntry {\n  components = {\n    jyfParser: function () {\n      return import(\n        /* webpackChunkName: \"components/jyf-parser/jyf-parser\" */ \"@/components/jyf-parser/jyf-parser.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 =\n    new Date().getTime() - _vm.bargainInfo.stopTime < 0 &&\n    _vm.bargainInfo.quota > 0\n  var g1 = new Date().getTime()\n  var g2 =\n    (_vm.startBargainUid == _vm.uid &&\n      (_vm.bargainStatus == 1 || _vm.bargainStatus == 3)) ||\n    (_vm.startBargainUid != _vm.uid && _vm.bargainStatus == 5)\n      ? new Date().getTime()\n      : null\n  var g3 =\n    (_vm.startBargainUid == _vm.uid &&\n      (_vm.bargainStatus == 1 || _vm.bargainStatus == 3)) ||\n    (_vm.startBargainUid != _vm.uid && _vm.bargainStatus == 5)\n      ? new Date().getTime()\n      : null\n  var g4 =\n    (_vm.startBargainUid == _vm.uid &&\n      (_vm.bargainStatus == 1 || _vm.bargainStatus == 3)) ||\n    (_vm.startBargainUid != _vm.uid && _vm.bargainStatus == 5)\n      ? new Date().getTime()\n      : null\n  var g5 =\n    (_vm.startBargainUid == _vm.uid &&\n      (_vm.bargainStatus == 1 || _vm.bargainStatus == 3)) ||\n    (_vm.startBargainUid != _vm.uid && _vm.bargainStatus == 5)\n      ? new Date().getTime()\n      : null\n  var g6 =\n    (_vm.startBargainUid == _vm.uid &&\n      (_vm.bargainStatus == 1 || _vm.bargainStatus == 3)) ||\n    (_vm.startBargainUid != _vm.uid && _vm.bargainStatus == 5)\n      ? new Date().getTime()\n      : null\n  var m0 =\n    _vm.startBargainUid == _vm.uid &&\n    parseFloat(_vm.bargainUserInfo.surplusPrice) > 0 &&\n    _vm.bargainStatus == 1\n  var m1 =\n    _vm.startBargainUid == _vm.uid &&\n    parseFloat(_vm.bargainUserInfo.surplusPrice) > 0 &&\n    _vm.bargainStatus == 3\n  var m2 =\n    _vm.startBargainUid != _vm.uid &&\n    _vm.bargainStatus == 5 &&\n    parseFloat(_vm.bargainUserInfo.surplusPrice) > 0\n  var m3 =\n    _vm.startBargainUid != _vm.uid &&\n    _vm.bargainStatus == 4 &&\n    parseFloat(_vm.bargainUserInfo.surplusPrice) == 0\n  var m4 =\n    parseFloat(_vm.bargainUserInfo.surplusPrice) == 0 &&\n    _vm.startBargainUid == _vm.uid &&\n    (_vm.bargainStatus == 4 || _vm.bargainStatus == 8)\n  var m5 =\n    parseFloat(_vm.bargainUserInfo.surplusPrice) == 0 &&\n    _vm.startBargainUid == _vm.uid &&\n    _vm.bargainStatus == 9\n  var g7 = new Date().getTime()\n  var g8 =\n    new Date().getTime() - _vm.bargainInfo.stopTime >= 0 ||\n    _vm.bargainInfo.quota == 0\n  var g9 = _vm.bargainUserHelpList.length\n  var g10 = g9 > 0 ? _vm.bargainUserHelpList.length : null\n  var g11 = _vm.bargainUserHelpList.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.H5ShareBox = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n        g6: g6,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        g7: g7,\n        g8: g8,\n        g9: g9,\n        g10: g10,\n        g11: g11,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :data-theme=\"theme\">\r\n\t\t<skeleton :show=\"showSkeleton\" :isNodes=\"isNodes\" ref=\"skeleton\" loading=\"chiaroscuro\" selector=\"skeleton\"\r\n\t\t\t></skeleton>\r\n\t\t<view class='bargain skeleton' :style=\"{visibility: showSkeleton ? 'hidden' : 'visible'}\">\r\n\t\t\t<view class=\"header\" :class=\"startBargainUid != userInfo.uid ? 'on' : ''\" :style=\"{backgroundImage:'url('+imgHost+ '/' + backBg+')'}\" v-show=\"imgHost\">\r\n\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t<view class=\"select_nav flex justify-center align-center\">\r\n\t\t\t\t\t<text class=\"iconfont icon-fanhui2 px-20\" @tap=\"returns\"></text>\r\n\t\t\t\t\t<text class=\"iconfont icon-kanjialiebiao px-20\" @tap=\"backList\"></text>\r\n\t\t\t\t\t<text class=\"nav_line\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<!-- #ifndef H5 -->\r\n\t\t\t\t<navigator url=\"/pages/activity/goods_bargain/index\" hover-class=\"none\">\r\n\t\t\t\t\t<view class=\"back\">\r\n\t\t\t\t\t\t<text class=\"iconfont icon-xiangzuo\"></text> 返回砍价列表\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</navigator>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t</view>\r\n\t\t\t<view class=\"pad30\" style=\"padding-bottom: 30rpx;\">\r\n\t\t\t\t<view class='wrapper'>\r\n\t\t\t\t\t<view class='pictxt acea-row row-between' @tap=\"goProduct\">\r\n\t\t\t\t\t\t<view class='pictrue skeleton-rect'>\r\n\t\t\t\t\t\t\t<image :src='bargainInfo.image'></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='text acea-row row-column-around'>\r\n\t\t\t\t\t\t\t<view class='line1 skeleton-rect'>{{bargainInfo.title}}</view>\r\n\t\t\t\t\t\t\t<view class=\"surplus skeleton-rect\">最低价：￥{{bargainInfo.minPrice}}</view>\r\n\t\t\t\t\t\t\t<view class=\"surplus skeleton-rect\">剩余：{{bargainInfo.quota}}{{bargainInfo.unitName}}</view>\r\n\t\t\t\t\t\t\t<view class='money font-color-red skeleton-rect'>\r\n\t\t\t\t\t\t\t\t当前: ￥\r\n\t\t\t\t\t\t\t\t<text class='num'>{{buyPrice}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"acea-row skeleton-rect\"\r\n\t\t\t\t\t\t\t\tv-if=\"new Date().getTime()- bargainInfo.stopTime < 0 && bargainInfo.quota>0\">\r\n\t\t\t\t\t\t\t\t<countDown :tipText=\"' '\" :bgColor=\"bgColor\" :dayText=\"':'\" :hourText=\"':'\"\r\n\t\t\t\t\t\t\t\t\t:minuteText=\"':'\" :secondText=\"' '\" :datatime=\"datatime\" :isDay=\"true\"></countDown>\r\n\t\t\t\t\t\t\t\t<text style=\"font-size: 22rpx;margin-left: 4rpx;line-height: 36rpx;\">后结束</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view v-if=\"new Date().getTime()- bargainInfo.stopTime >=0\" class=\"skeleton-rect\">\r\n\t\t\t\t\t\t\t\t<view style=\"font-size: 22rpx;\" @tap='currentBargainUser'>已结束</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view v-if=\"bargainInfo.quota==0\" class=\"skeleton-rect\">\r\n\t\t\t\t\t\t\t\t<view style=\"font-size: 22rpx;\" @tap='currentBargainUser'>已售罄</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"iconfont icon-jiantou iconfonts\"></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<block\r\n\t\t\t\t\t\tv-if=\"(startBargainUid == uid && (bargainStatus==1 || bargainStatus==3)) || (startBargainUid != uid && bargainStatus==5)\">\r\n\t\t\t\t\t\t<view class='money acea-row row-center'\r\n\t\t\t\t\t\t\t:class=\"new Date().getTime()- bargainInfo.stopTime >=0 ? 'font_hui': ''\">\r\n\t\t\t\t\t\t\t<view style=\"margin-right: 40rpx;\" class=\"skeleton-rect\">已砍<text class=\"font-color-red\"\r\n\t\t\t\t\t\t\t\t\t:class=\"new Date().getTime()- bargainInfo.stopTime >=0 ? 'font_hui': ''\">￥{{bargainUserInfo.alreadyPrice}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"skeleton-rect\">还剩<text class=\"font-color-red\"\r\n\t\t\t\t\t\t\t\t\t:class=\"new Date().getTime()- bargainInfo.stopTime >=0 ? 'font_hui': ''\">￥{{bargainUserInfo.surplusPrice}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"cu-progress acea-row row-middle round margin-top skeleton-rect\"\r\n\t\t\t\t\t\t\t:class=\"new Date().getTime()- bargainInfo.stopTime >=0 ? 'bg_qian': ''\">\r\n\t\t\t\t\t\t\t<view class='acea-row row-middle bg-red'\r\n\t\t\t\t\t\t\t\t:class=\"new Date().getTime()- bargainInfo.stopTime >=0 ? 'bg-color-hui': ''\"\r\n\t\t\t\t\t\t\t\t:style=\"'width:'+ bargainUserInfo.bargainPercent +'%;'\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='tip skeleton-rect'>\r\n\t\t\t\t\t\t\t一 已有{{bargainInfo.sales}}位好友砍价成功 一\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\r\n\t\t\t\t\t<!-- 自己砍价 -->\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t\tv-if=\"startBargainUid == uid && parseFloat(bargainUserInfo.surplusPrice) > 0 && bargainStatus==1\">\r\n\t\t\t\t\t\t<view class='bargainBnt skeleton-rect' @tap='userBargain' v-if=\"quota>0\">\r\n\t\t\t\t\t\t\t立即参与砍价\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='bargainBnt grey' v-if=\"quota<=0\">商品暂无库存</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t\tv-if=\"startBargainUid == uid && parseFloat(bargainUserInfo.surplusPrice) > 0 && bargainStatus==3\">\r\n\t\t\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t\t\t<view class='bargainBnt' v-if=\"$wechat.isWeixin()\" @click=\"H5ShareBox = true\">邀请好友帮砍价</view>\r\n\t\t\t\t\t\t<view class='bargainBnt' v-else @tap='getBargainUserBargainPricePoster'>邀请好友帮砍价</view>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t<!-- #ifdef MP -->\r\n\t\t\t\t\t\t<button open-type='share' class='bargainBnt'>邀请好友帮砍价</button>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t<!-- #ifdef APP-PLUS -->\r\n\t\t\t\t\t\t<view class='bargainBnt' @click=\"posters = true\">邀请好友帮砍价</view>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t\tv-if=\"startBargainUid != uid && bargainStatus==5 && parseFloat(bargainUserInfo.surplusPrice) > 0\">\r\n\t\t\t\t\t\t<view class='bargainBnt' @tap='setBargainHelp'>帮好友砍一刀</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"startBargainUid != uid && bargainStatus==4 && parseFloat(bargainUserInfo.surplusPrice) == 0\" >\r\n\t\t\t\t\t\t<view class='bargainSuccess'>\r\n\t\t\t\t\t\t\t<image src=\"../static/cheng.png\"></image>\r\n\t\t\t\t\t\t\t好友已砍成功\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='bargainBnt' @tap='currentBargainUser'>我也要参与</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"startBargainUid != uid && bargainStatus==7\">\r\n\t\t\t\t\t\t<view class='bargainSuccess'>\r\n\t\t\t\t\t\t\t<image src=\"../static/cheng.png\"></image>\r\n\t\t\t\t\t\t\t您已帮其他好友砍过此商品\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='bargainBnt' @tap='currentBargainUser'>我也要参与</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"startBargainUid != uid && bargainStatus==6\">\r\n\t\t\t\t\t\t<view class='bargainSuccess'>\r\n\t\t\t\t\t\t\t<image src=\"../static/chengh.png\"></image>\r\n\t\t\t\t\t\t\t已成功帮助好友砍价\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='bargainBnt' @tap='currentBargainUser'>我也要参与</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t\tv-if=\"parseFloat(bargainUserInfo.surplusPrice) == 0 &&  startBargainUid == uid && (bargainStatus==4 || bargainStatus==8)\">\r\n\t\t\t\t\t\t<view class='bargainSuccess'>\r\n\t\t\t\t\t\t\t<image src=\"../static/chengh.png\"></image>\r\n\t\t\t\t\t\t\t恭喜您砍价成功，快去支付吧~\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"acea-row row-between buyBox pad30\">\r\n\t\t\t\t\t\t\t<view class='buyMore on' @tap='goBargainList'>继续选购</view>\r\n\t\t\t\t\t\t\t<view v-if=\"bargainStatus==4\" class='buyNow on' @tap='goPay'>立即支付</view>\r\n\t\t\t\t\t\t\t<view v-if=\"bargainStatus==8\" class='buyNow on' @tap='goConfirm'>砍价记录</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t\tv-if=\"parseFloat(bargainUserInfo.surplusPrice) == 0 &&  startBargainUid == uid && bargainStatus==9\">\r\n\t\t\t\t\t\t<view class='bargainSuccess'>\r\n\t\t\t\t\t\t\t<image src=\"../static/chengh.png\"></image>\r\n\t\t\t\t\t\t\t恭喜您砍价成功，去看看别的商品吧~\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"buyBox pad30\">\r\n\t\t\t\t\t\t\t<view class='buyMore on' @tap='goBargainList' style=\"margin: 40rpx auto 0 auto;\">继续选购</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"new Date().getTime()- bargainInfo.stopTime >=0\">\r\n\t\t\t\t\t\t<view class='huiBtn' @tap='currentBargainUser'>活动已结束</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-else-if=\"bargainInfo.quota==0\">\r\n\t\t\t\t\t\t<view class='huiBtn' @tap='currentBargainUser'>商品已售罄</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<navigator v-if=\"new Date().getTime()- bargainInfo.stopTime >=0 || bargainInfo.quota==0\"\r\n\t\t\t\t\t\turl=\"/pages/activity/goods_bargain/index\" hover-class=\"none\">\r\n\t\t\t\t\t\t<view class=\"go\">再去逛逛<text class=\"iconfont icon-jiantou\"></text></view>\r\n\t\t\t\t\t</navigator>\r\n\t\t\t\t\t<view v-if=\"bargainStatus==2\" class=\"contentNo\" style=\"padding: 0;\">\r\n\t\t\t\t\t\t<text class=\"iconfont icon-xiaolian mr8\"></text>\r\n\t\t\t\t\t\t您购买的商品数量已达上限\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"bargainStatus==10\" class=\"contentNo\" style=\"padding: 0;\">\r\n\t\t\t\t\t\t<view class=\"pb-25\">\r\n\t\t\t\t\t\t\t<text class=\"iconfont icon-xiaolian mr8 \"></text>\r\n\t\t\t\t\t\t\t您已参与\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='bargainBnt skeleton-rect'@tap='goConfirm'>\r\n\t\t\t\t\t\t\t查看记录\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 砍价记录 -->\r\n\t\t\t\t<view class='title font-color acea-row row-center-wrapper skeleton-rect'>\r\n\t\t\t\t\t<view class='pictrue'>\r\n\t\t\t\t\t\t<image src='../static/zuo2.png'></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tits\">\r\n\t\t\t\t\t\t<view class='titleCon'>砍价记录</view>\r\n\t\t\t\t\t\t<view class=\"line\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='pictrue on'>\r\n\t\t\t\t\t\t<image src='../static/you2.png'></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='bargainGang borRadius14 skeleton-rect'>\r\n\t\t\t\t\t<view class='list' v-if=\"bargainUserHelpList.length>0\">\r\n\t\t\t\t\t\t<block v-for=\"(item,index) in bargainUserHelpList\" :key='index'\r\n\t\t\t\t\t\t\tv-if=\"index<3 || !couponsHidden\">\r\n\t\t\t\t\t\t\t<view class='item acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t\t<view class='pictxt acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t\t\t<view class='pictrue'>\r\n\t\t\t\t\t\t\t\t\t\t<image :src='item.avatar'></image>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class='text'>\r\n\t\t\t\t\t\t\t\t\t\t<view class='name line1'>{{item.nickname}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class='line1'>{{item.addTimeStr }}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class='money'>\r\n\t\t\t\t\t\t\t\t\t已砍 <text class=\"font-color-red\">{{item.price}}</text>元\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<view class=\"open acea-row row-center-wrapper\" @click=\"openTap\"\r\n\t\t\t\t\t\t\tv-if=\"bargainUserHelpList.length>3\">{{couponsHidden?'展开更多':'关闭展开'}}<text class=\"iconfont\"\r\n\t\t\t\t\t\t\t\t:class='couponsHidden==true?\"icon-xiangxia\":\"icon-xiangshang\"'></text></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"bargainUserHelpList.length===0\" class=\"contentNo\">\r\n\t\t\t\t\t\t<text class=\"iconfont icon-xiaolian mr8\"></text>\r\n\t\t\t\t\t\t暂无助力记录\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- <view class='load font-color' v-if=\"!limitStatus\" @tap='getBargainUser'>点击加载更多</view> -->\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 商品详情 -->\r\n\t\t\t\t<view class='title font-color acea-row row-center-wrapper'>\r\n\t\t\t\t\t<view class='pictrue'>\r\n\t\t\t\t\t\t<image src='../static/zuo2.png'></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tits\">\r\n\t\t\t\t\t\t<view class='titleCon'>商品详情</view>\r\n\t\t\t\t\t\t<view class=\"line\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='pictrue on'>\r\n\t\t\t\t\t\t<image src='../static/you2.png'></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='goodsDetails borRadius14'>\r\n\t\t\t\t\t<view class='conter borRadius14'>\r\n\t\t\t\t\t\t<jyf-parser v-if=\"bargainInfo.content\" :html=\"bargainInfo.content\" ref=\"article\"\r\n\t\t\t\t\t\t\t:tag-style=\"tagStyle\"></jyf-parser>\r\n\t\t\t\t\t\t<view v-else class=\"contentNo\">\r\n\t\t\t\t\t\t\t<text class=\"iconfont icon-xiaolian mr8\"></text>\r\n\t\t\t\t\t\t\t暂无商品详情\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class='bargainTip' :class='active==true?\"on\":\"\"'>\r\n\t\t\t\t\t<view class='pictrue' :style=\"{backgroundImage:'url('+imgHost +'/'+backHead+')'}\" v-show=\"imgHost\"></view>\r\n\t\t\t\t\t<view v-if=\"startBargainUid == uid\">\r\n\t\t\t\t\t\t<view class='cutOff'>\r\n\t\t\t\t\t\t\t您已砍掉<text class='font_color'>{{bargainUserBargainPrice}}元</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"bubbleBox\">\r\n\t\t\t\t\t\t\t<view class=\"bubble\"\r\n\t\t\t\t\t\t\t\t:style=\"'left:'+ (bargainUserInfo.bargainPercent>0?bargainUserInfo.bargainPercent-9:0) +'%;'\">\r\n\t\t\t\t\t\t\t\t<text>{{bargainUserInfo.bargainPercent}}%</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"cu-progress acea-row row-middle round margin-top\">\r\n\t\t\t\t\t\t\t<view class='acea-row row-middle bg-red'\r\n\t\t\t\t\t\t\t\t:style=\"'width:'+ bargainUserInfo.bargainPercent +'%;'\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"t1\">分享次数越多，成功的机会越大哦！</view>\r\n\t\t\t\t\t\t<!-- #ifdef MP -->\r\n\t\t\t\t\t\t<button open-type='share' class='tipBnt'>邀请好友帮砍价</button>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t\t\t<view class='tipBnt' @tap='getBargainUserBargainPricePoster'>邀请好友帮砍价</view>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t<!-- #ifdef APP-PLUS -->\r\n\t\t\t\t\t\t<view class='tipBnt' @click=\"posters = true\">邀请好友帮砍价</view>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-else>\r\n\t\t\t\t\t\t<view class='cutOff'>\r\n\t\t\t\t\t\t\t帮好友砍掉<text class='font_color'>{{bargainUserBargainPrice}}元</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"bubbleBox\">\r\n\t\t\t\t\t\t\t<view class=\"bubble\"\r\n\t\t\t\t\t\t\t\t:style=\"'left:'+ (bargainUserInfo.bargainPercent>0?bargainUserInfo.bargainPercent-9:0) +'%;'\">\r\n\t\t\t\t\t\t\t\t<text>{{bargainUserInfo.bargainPercent}}%</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"cu-progress acea-row row-middle round margin-top\">\r\n\t\t\t\t\t\t\t<view class='acea-row row-middle bg-red'\r\n\t\t\t\t\t\t\t\t:style=\"'width:'+ bargainUserInfo.bargainPercent +'%;'\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"t1\">您也可以砍价低价拿哦，快去挑选吧~</view>\r\n\t\t\t\t\t\t<!-- <view class='help font-color'>成功帮砍{{bargainUserBargainPrice}}元</view> -->\r\n\t\t\t\t\t\t<!-- \t\t\t\t\t\t<view class='cutOff on'>您也可以砍价低价拿哦，快去挑选心仪的商品吧~</view> -->\r\n\t\t\t\t\t\t<view @tap='currentBargainUser' class='tipBnt'>我也要参与</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class='mask' catchtouchmove=\"true\" v-show='active==true || posters==true || canvasStatus'></view>\r\n\t\t</view>\r\n\t\t<!-- 分享按钮 -->\r\n\t\t<view class=\"generate-posters acea-row row-middle\" :class=\"posters ? 'on' : ''\">\r\n\t\t\t<!-- #ifdef APP-PLUS -->\r\n\t\t\t<view class=\"item\" @click=\"appShare('WXSceneSession')\">\r\n\t\t\t\t<view class=\"iconfont icon-weixin3\"></view>\r\n\t\t\t\t<view class=\"\">微信好友</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" @click=\"appShare('WXSenceTimeline')\">\r\n\t\t\t\t<view class=\"iconfont icon-pengyouquan\"></view>\r\n\t\t\t\t<view class=\"\">微信朋友圈</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- #endif -->\r\n\t\t</view>\r\n\r\n\t\t<!-- 发送给朋友图片 -->\r\n\t\t<view class=\"share-box\" v-if=\"H5ShareBox\">\r\n\t\t\t<image src=\"/static/images/share-info.png\" @click=\"H5ShareBox = false\"></image>\r\n\t\t</view>\r\n\r\n\t\t<!-- 海报展示 -->\r\n\t\t<view class='poster-pop' v-if=\"canvasStatus\">\r\n\t\t\t<image :src='imagePath'></image>\r\n\t\t\t<!-- #ifndef H5  -->\r\n\t\t\t<view class='save-poster' @click=\"savePosterPath\">保存到手机</view>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t<view class=\"keep\">长按图片保存至相册</view>\r\n\t\t\t<view class=\"close_box\">\r\n\t\t\t\t<text class='iconfont icon-cha2 close' @tap='close'></text>\r\n\t\t\t</view>\r\n\t\t\t<!-- #endif -->\r\n\t\t</view>\r\n\t\t<view class=\"canvas\">\r\n\t\t\t<canvas style=\"width:597px;height:850px;\" canvas-id=\"activityCanvas\"></canvas>\r\n\t\t\t<canvas canvas-id=\"qrcode\" :style=\"{width: `${qrcodeSize}px`, height: `${qrcodeSize}px`}\"\r\n\t\t\t\tstyle=\"opacity: 0;\" />\r\n\t\t</view>\r\n\t\t<!-- <home></home> -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tgetBargainDetail,\r\n\t\tpostBargainStartUser,\r\n\t\tpostBargainStart,\r\n\t\tpostBargainHelp,\r\n\t\tgetBargainUser\r\n\t} from '../../../api/activity.js';\r\n\timport {imageBase64} from \"@/api/public\";\r\n\timport uQRCode from '@/js_sdk/Sansnn-uQRCode/uqrcode.js';\r\n\timport {postCartAdd} from '../../../api/store.js';\r\n\timport util from '../../../utils/util.js';\r\n\timport {toLogin} from '@/libs/login.js';\r\n\timport {mapGetters} from \"vuex\";\r\n\timport { getImageDomain } from '@/api/api.js'\r\n\t// #ifdef MP\r\n\timport authorize from '@/components/Authorize';\r\n\t// #endif\r\n\timport countDown from '@/components/countDown';\r\n\timport home from '@/components/home';\r\n\timport parser from \"@/components/jyf-parser/jyf-parser\";\r\n\timport {\r\n\t\tsilenceBindingSpread\r\n\t} from \"@/utils\";\r\n\t// #ifdef APP-PLUS\r\n\timport {\r\n\t\tTOKENNAME,\r\n\t\tHTTP_H5_URL\r\n\t} from '@/config/app.js';\r\n\t// #endif\r\n\timport { BACK_URL} from '@/config/cache';\r\n\tconst app = getApp();\r\n\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tcountDown,\r\n\t\t\t// #ifdef MP\r\n\t\t\tauthorize,\r\n\t\t\t// #endif\r\n\t\t\thome,\r\n\t\t\t\"jyf-parser\": parser\r\n\t\t},\r\n\t\tfilters: {\r\n\t\t\tpicFilter(status) {\r\n\t\t\t\tconst statusMap = {\r\n\t\t\t\t\t'0': 'num1',\r\n\t\t\t\t\t'1': 'num2',\r\n\t\t\t\t\t'2': 'num3'\r\n\t\t\t\t}\r\n\t\t\t\treturn statusMap[status]\r\n\t\t\t}\r\n\t\t},\r\n\t\t/**\r\n\t\t * 页面的初始数据\r\n\t\t */\r\n\t\t\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tshowSkeleton: true, //骨架屏显示隐藏\r\n\t\t\t\tisNodes: 0, //控制什么时候开始抓取元素节点,只要数值改变就重新抓取\r\n\t\t\t\tbgColor: {\r\n\t\t\t\t\t'bgColor': '#333333',\r\n\t\t\t\t\t'Color': '#fff',\r\n\t\t\t\t\t'width': '44rpx',\r\n\t\t\t\t\t'timeTxtwidth': '16rpx',\r\n\t\t\t\t\t'isDay': true\r\n\t\t\t\t},\r\n\t\t\t\tcountDownDay: '00',\r\n\t\t\t\tcountDownHour: '00',\r\n\t\t\t\tcountDownMinute: '00',\r\n\t\t\t\tcountDownSecond: '00',\r\n\t\t\t\tactive: false,\r\n\t\t\t\tid: 0, //砍价商品id\r\n\t\t\t\tbargainStatus: 0, //当前用户砍价状态：1-可以参与砍价,2-参与次数已满，3-砍价中,4-已完成，5-可以帮砍，6-已帮砍,7-帮砍次数已满,8-已生成订单未支付，9-已支付\r\n\t\t\t\tstartBargainUid: 0, //开启砍价用户uid\r\n\t\t\t\tbargainUserInfo: {}, //开启砍价用户信息\r\n\t\t\t\t//bargainUserId: 0, //开启砍价编号\r\n\t\t\t\tstoreBargainId: 0, // 砍价活动id\r\n\t\t\t\tbargainInfo: {}, //砍价产品\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tlimit: 5,\r\n\t\t\t\tlimitStatus: false,\r\n\t\t\t\tbargainUserHelpList: [],\r\n\t\t\t\tbargainUserHelpInfo: [],\r\n\t\t\t\tbargainUserBargainPrice: 0, //砍了多少钱\r\n\t\t\t\tbargainCount: [], //分享人数  浏览人数 参与人数\r\n\t\t\t\tretunTop: true,\r\n\t\t\t\tbargainPartake: 0,\r\n\t\t\t\tisHelp: false,\r\n\t\t\t\tinterval: null,\r\n\t\t\t\tproductStock: 0, //判断是否售罄；\r\n\t\t\t\tquota: 0, //判断是否已限量；\r\n\t\t\t\tuserBargainStatusHelp: true,\r\n\t\t\t\tnavH: '',\r\n\t\t\t\tbargainPrice: 0,\r\n\t\t\t\tdatatime: 0,\r\n\t\t\t\toffest: '',\r\n\t\t\t\ttagStyle: {\r\n\t\t\t\t\timg: 'width:100%;display:block;',\r\n\t\t\t\t\ttable: 'width:100%',\r\n\t\t\t\t\tvideo: 'width:100%'\r\n\t\t\t\t},\r\n\t\t\t\tH5ShareBox: false, //公众号分享图片\r\n\t\t\t\tsystemH: 0,\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false, //是否隐藏授权\r\n\t\t\t\tpages: '',\r\n\t\t\t\tcouponsHidden: true,\r\n\t\t\t\tloading: false,\r\n\t\t\t\tloadend: false,\r\n\t\t\t\tposters: false,\r\n\t\t\t\tbuyPrice: '', //最后砍价购买价格\r\n\t\t\t\tqrcodeSize: 600,\r\n\t\t\t\tposterbackgd: '../../../static/images/bargain_post1.png',\r\n\t\t\t\tPromotionCode: '', //二维码\r\n\t\t\t\tcanvasStatus: false,\r\n\t\t\t\timgTop: '', //商品图base64位\r\n\t\t\t\timagePath: '', // 海报图片\r\n\t\t\t\ttheme:app.globalData.theme,\r\n\t\t\t\timgHost:'',\r\n\t\t\t\tbackBg:'crmebimage/change/bargain_header_bg/bargain_header_bg1.jpg',\r\n\t\t\t\tbackHead:'crmebimage/change/bargain_tip/bargain_tip1.png'\r\n\t\t\t}\r\n\r\n\t\t},\r\n\t\tcomputed: mapGetters(['isLogin', 'userInfo', 'uid']),\r\n\t\t/**\r\n\t\t * 生命周期函数--监听页面加载\r\n\t\t */\r\n\t\tonLoad: function(options) {\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.isNodes++;\r\n\t\t\t}, 200);\r\n\t\t\tlet that = this;\r\n\t\t\tthat.$set(that,'theme',that.$Cache.get('theme'));\r\n\t\t\tgetImageDomain().then(res=>{\r\n\t\t\t\tthat.$set(that,'imgHost',res.data);\r\n\t\t\t})\r\n\t\t\tswitch (that.theme) {\r\n\t\t\t\tcase 'theme1':\r\n\t\t\t\t\tthat.backBg = 'crmebimage/change/bargain_header_bg/bargain_header_bg1.jpg';\r\n\t\t\t\t\tthat.backHead = 'crmebimage/change/bargain_tip/bargain_tip1.png';\r\n\t\t\t\t\tthat.posterbackgd = '../../../static/images/bargain_post1.png' // 因为跨域不能使用网络图片，\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 'theme2':\r\n\t\t\t\t\tthat.backBg = 'crmebimage/change/bargain_header_bg/bargain_header_bg2.jpg';\r\n\t\t\t\t\tthat.backHead = 'crmebimage/change/bargain_tip/bargain_tip2.png';\r\n\t\t\t\t\tthat.posterbackgd = '../../../static/images/bargain_post2.png'\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 'theme3':\r\n\t\t\t\t\tthat.backBg = 'crmebimage/change/bargain_header_bg/bargain_header_bg3.jpg';\r\n\t\t\t\t\tthat.backHead = 'crmebimage/change/bargain_tip/bargain_tip3.png';\r\n\t\t\t\t\tthat.posterbackgd = '../../../static/images/bargain_post3.png'\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 'theme4':\r\n\t\t\t\t\tthat.backBg = 'crmebimage/change/bargain_header_bg/bargain_header_bg4.jpg';\r\n\t\t\t\t\tthat.backHead = 'crmebimage/change/bargain_tip/bargain_tip4.png';\r\n\t\t\t\t\tthat.posterbackgd = '../../../static/images/bargain_post4.png'\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 'theme5':\r\n\t\t\t\t\tthat.backBg = 'crmebimage/change/bargain_header_bg/bargain_header_bg5.jpg';\r\n\t\t\t\t\tthat.backHead = 'crmebimage/change/bargain_tip/bargain_tip5.png';\r\n\t\t\t\t\tthat.posterbackgd = '../../../static/images/bargain_post5.png'\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t\tthat.$store.commit(\"PRODUCT_TYPE\", 'normal');\r\n\t\t\t// #ifdef MP\r\n\t\t\tuni.getSystemInfo({\r\n\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\tthat.systemH = res.statusBarHeight\r\n\t\t\t\t\tthat.navH = that.systemH + 10\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t// #endif\r\n\t\t\t\r\n            // #ifdef MP || APP-NVUE\r\n            // 小程序链接进入获取绑定关系id\r\n            if(options.spread) app.globalData.spread = options.spread; \r\n\t\t\t// #endif\r\n\t\t\t\r\n\t\t\tvar pages = getCurrentPages();\r\n\t\t\tif (pages.length <= 1) {\r\n\t\t\t\tthat.retunTop = false\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (options.hasOwnProperty('id') || options.scene) {\r\n\t\t\t\tif (options.scene) { // 仅仅小程序扫码进入\r\n\t\t\t\t\tlet qrCodeValue = this.$util.getUrlParams(decodeURIComponent(options.scene));\r\n\t\t\t\t\tlet mapeMpQrCodeValue = this.$util.formatMpQrCodeData(qrCodeValue);\r\n\t\t\t        app.globalData = mapeMpQrCodeValue;\r\n\t\t\t\t\tapp.globalData.spread = mapeMpQrCodeValue.spread; \r\n\t\t\t\t\tthis.id = app.globalData.id\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.id = options.id;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\toptions.startBargainUid == 'undefined' ? that.startBargainUid = 0 : that.startBargainUid = Number(options\r\n\t\t\t\t.startBargainUid);\r\n\t\t\t\t\r\n\t\t\tif (this.isLogin) {\r\n\t\t\t\tif (that.startBargainUid == 0) {\r\n\t\t\t\t\tthat.startBargainUid = Number(that.$store.state.app.uid)\r\n\t\t\t\t}\r\n\t\t\t\tthis.storeBargainId = options.storeBargainId ? Number(options.storeBargainId) : 0;\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t} else {\r\n\t\t\t\tthis.$Cache.set(BACK_URL,\r\n\t\t\t\t\t'/pages/activity/goods_bargain_details/index?id=' + options.id +\r\n\t\t\t\t\t'&startBargainUid=' + this.uid + '&spread=' + this.uid + '&storeBargainId=' + this.storeBargainId\r\n\t\t\t\t);\r\n\t\t\t\ttoLogin();\r\n\t\t\t}\r\n\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\ttitle: '砍价详情'\r\n\t\t\t})\r\n\t\t\tif(this.isLogin && app.globalData.spread){\r\n\t\t\t\tsilenceBindingSpread()\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t//去支付\r\n\t\t\tgoConfirm() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/activity/bargain/index`\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// app分享\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\tappShare(scene) {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tlet routes = getCurrentPages(); // 获取当前打开过的页面路由数组\r\n\t\t\t\tlet curRoute = routes[routes.length - 1].$page.fullPath // 获取当前页面路由，也就是最后一个打开的页面路由\"\r\n\t\t\t\tlet href = HTTP_H5_URL + '/pages/activity/goods_bargain_details/index?id=' + this.id +\r\n\t\t\t\t\t'&startBargainUid=' + this\r\n\t\t\t\t\t.uid + '&spread=' + this.uid + '&storeBargainId=' + this.storeBargainId;\r\n\t\t\t\tuni.share({\r\n\t\t\t\t\tprovider: \"weixin\",\r\n\t\t\t\t\tscene: scene,\r\n\t\t\t\t\ttype: 0,\r\n\t\t\t\t\thref: href,\r\n\t\t\t\t\ttitle: '您的好友' + that.userInfo.nickname + '邀请您帮他砍' + that.bargainInfo.title + ' 快去帮忙吧！',\r\n\t\t\t\t\timageUrl: that.bargainInfo.image,\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tthat.posters = false;\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '分享失败',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthat.posters = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\t//#ifdef H5\r\n\t\t\tsetOpenShare() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet configTimeline = {\r\n\t\t\t\t\ttitle: \"您的好友\" +\r\n\t\t\t\t\t\tthat.userInfo.nickname +\r\n\t\t\t\t\t\t\"邀请您砍价\" +\r\n\t\t\t\t\t\tthat.bargainInfo.title,\r\n\t\t\t\t\tdesc: that.bargainInfo.title,\r\n\t\t\t\t\tlink: window.location.protocol +\r\n\t\t\t\t\t\t\"//\" +\r\n\t\t\t\t\t\twindow.location.host +\r\n\t\t\t\t\t\t'/pages/activity/goods_bargain_details/index?id=' + this.id + '&startBargainUid=' + this\r\n\t\t\t\t\t\t.uid + '&spread=' + this.uid + '&storeBargainId=' + this.storeBargainId,\r\n\t\t\t\t\timgUrl: that.bargainInfo.image\r\n\t\t\t\t};\r\n\t\t\t\tif (this.$wechat.isWeixin()) {\r\n\t\t\t\t\tthis.$wechat.wechatEvevt([\r\n\t\t\t\t\t\t\t\t\"updateAppMessageShareData\",\r\n\t\t\t\t\t\t\t\t\"updateTimelineShareData\",\r\n\t\t\t\t\t\t\t\t\"onMenuShareAppMessage\",\r\n\t\t\t\t\t\t\t\t\"onMenuShareTimeline\"\r\n\t\t\t\t\t\t\t],\r\n\t\t\t\t\t\t\tconfigTimeline\r\n\t\t\t\t\t\t)\r\n\t\t\t\t\t\t.then(res => {})\r\n\t\t\t\t\t\t.catch(res => {\r\n\t\t\t\t\t\t\tif (res.is_ready) {\r\n\t\t\t\t\t\t\t\tres.wx.updateAppMessageShareData(configTimeline);\r\n\t\t\t\t\t\t\t\tres.wx.updateTimelineShareData(configTimeline);\r\n\t\t\t\t\t\t\t\tres.wx.onMenuShareAppMessage(configTimeline);\r\n\t\t\t\t\t\t\t\tres.wx.onMenuShareTimeline(configTimeline);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//#endif\r\n\t\t\topenTap() {\r\n\t\t\t\tthis.$set(this, 'couponsHidden', !this.couponsHidden);\r\n\t\t\t},\r\n\t\t\t// 去商品页\r\n\t\t\tgoProduct() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/goods_details/index?id=${this.bargainInfo.productId}`\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 自己砍价；\r\n\t\t\tuserBargain: function() {\r\n\t\t\t\tif (this.uid == this.startBargainUid) {\r\n\t\t\t\t\tthis.setBargain();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgoBack: function() {\r\n\t\t\t\tuni.navigateBack({\r\n\r\n\t\t\t\t\tdelta: 1\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 生成二维码；\r\n\t\t\tmake() {\r\n\t\t\t\tlet href = window.location.protocol +\r\n\t\t\t\t\t\"//\" +\r\n\t\t\t\t\twindow.location.host +\r\n\t\t\t\t\t'/pages/activity/goods_bargain_details/index?id=' + this.id + '&startBargainUid=' + this\r\n\t\t\t\t\t.uid + '&spread=' + this.uid + '&storeBargainId=' + this.storeBargainId;\r\n\t\t\t\tuQRCode.make({\r\n\t\t\t\t\tcanvasId: 'qrcode',\r\n\t\t\t\t\ttext: href,\r\n\t\t\t\t\tsize: this.qrcodeSize,\r\n\t\t\t\t\tmargin: 10,\r\n\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\tthis.PromotionCode = res;\r\n\t\t\t\t\t},\r\n\t\t\t\t\tcomplete: () => {},\r\n\t\t\t\t\tfail: res => {\r\n\t\t\t\t\t\tthis.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: '海报二维码生成失败！'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 商品图片转base64\r\n\t\t\tgetImageBase64: function(images) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\timageBase64({\r\n\t\t\t\t\turl: images\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tthat.imgTop = res.data.code;\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//获取砍价用户信息\r\n\t\t\tgobargainUserInfo: function() {\r\n\t\t\t\tgetBargainUser({\r\n\t\t\t\t\tbargainId: this.id,\r\n\t\t\t\t\tbargainUserId: this.storeBargainId || 0\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tlet bargainUserInfo = res.data;\r\n\t\t\t\t\tthis.bargainUserInfo = bargainUserInfo;\r\n\t\t\t\t\tthis.bargainStatus = bargainUserInfo.bargainStatus;\r\n\t\t\t\t\tthis.storeBargainId = bargainUserInfo.storeBargainUserId;\r\n\t\t\t\t\tthis.buyPrice = this.$util.$h.Sub(parseFloat(this.bargainPrice), parseFloat(bargainUserInfo\r\n\t\t\t\t\t\t.alreadyPrice))\r\n\t\t\t\t\tthis.bargainUserHelpList = bargainUserInfo.userHelpList || [];\r\n\t\t\t\t\t//#ifdef H5\r\n\t\t\t\t\tif (bargainUserInfo.storeBargainUserId) this.make();\r\n\t\t\t\t\tthis.setOpenShare();\r\n\t\t\t\t\t//#endif\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t//状态异常返回上级页面\r\n\t\t\t\t\treturn this.$util.Tips({\r\n\t\t\t\t\t\ttitle: err.toString()\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\ttab: 3,\r\n\t\t\t\t\t\turl: 1\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgoPay: function() { //立即支付\r\n\t\t\t\tif (this.isLogin === false) {\r\n\t\t\t\t\ttoLogin();\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 预下单\r\n\t\t\t\t\tthis.$Order.getPreOrder(\"buyNow\", [{\r\n\t\t\t\t\t\t\"attrValueId\": parseFloat(this.bargainInfo.attrValueId),\r\n\t\t\t\t\t\t\"bargainId\": parseFloat(this.id),\r\n\t\t\t\t\t\t\"productNum\": 1,\r\n\t\t\t\t\t\t\"productId\": parseFloat(this.bargainInfo.productId),\r\n\t\t\t\t\t\t\"bargainUserId\": parseFloat(this.storeBargainId)\r\n\t\t\t\t\t}]);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//获取砍价产品详情\r\n\t\t\tgetBargainDetails: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tgetBargainDetail(that.id).then(function(res) {\r\n\t\t\t\t\tlet bargainInfo = res.data;\r\n\t\t\t\t\tthat.bargainInfo = bargainInfo;\r\n\t\t\t\t\tthat.bargainPrice = bargainInfo.price;\r\n\t\t\t\t\tthat.quota = bargainInfo.quota;\r\n\t\t\t\t\tthat.datatime = bargainInfo.stopTime / 1000;\r\n\t\t\t\t\tthat.pages = '/pages/activity/goods_bargain_details/index?id=' + that.id +\r\n\t\t\t\t\t\t'&startBargainUid=' + that\r\n\t\t\t\t\t\t.uid + '&spread=' + that.uid + '&storeBargainId=' + that.storeBargainId;\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: bargainInfo.title.substring(0, 13) + '...'\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthat.bargainUserHelpList = []\r\n\t\t\t\t\tif (that.isLogin && that.quota > 0 && new Date().getTime() - bargainInfo.stopTime < 0) {\r\n\t\t\t\t\t\tthat.gobargainUserInfo();\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthat.buyPrice = that.bargainPrice;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t//#ifdef H5\r\n\t\t\t\t\tthat.getImageBase64(bargainInfo.image);\r\n\t\t\t\t\t//#endif\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthat.showSkeleton = false\r\n\t\t\t\t\t}, 1000)\r\n\t\t\t\t}).catch(function(err) {\r\n\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\ttab: 2,\r\n\t\t\t\t\t\turl: '/pages/activity/goods_bargain/index'\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tcurrentBargainUser: function() { //当前用户砍价\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/activity/goods_bargain_details/index?id=' + this.id + '&startBargainUid=' +\r\n\t\t\t\t\t\tthis.uid\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tsetBargain: function() { //参与砍价\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tpostBargainStart(that.id).then(res => {\r\n\t\t\t\t\tif (res.code === 'subscribe') {\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.storeBargainId = res.data.storeBargainUserId;\r\n\t\t\t\t\tthat.setBargainHelp();\r\n\t\t\t\t\tthat.userBargainStatus = 1;\r\n\t\t\t\t\t//#ifdef H5\r\n\t\t\t\t\tthat.make();\r\n\t\t\t\t\t//#endif\r\n\t\t\t\t}, error => {\r\n\t\t\t\t\tthis.startBargainUid = 0;\r\n\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\ttitle: error\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//帮好友砍价\r\n\t\t\tsetBargainHelp: function() {\r\n\t\t\t\tvar data = {\r\n\t\t\t\t\tbargainId: this.id,\r\n\t\t\t\t\tbargainUserId: this.storeBargainId,\r\n\t\t\t\t\tbargainUserUid: this.startBargainUid\r\n\t\t\t\t};\r\n\t\t\t\tpostBargainHelp(data).then(res => {\r\n\t\t\t\t\tthis.$set(this, 'bargainUserHelpList', []);\r\n\t\t\t\t\tthis.$set(this, 'bargainUserBargainPrice', res.data.bargainPrice);\r\n\t\t\t\t\tthis.$set(this, 'active', true);\r\n\t\t\t\t\tthis.gobargainUserInfo();\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tthis.$util.Tips({\r\n\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.$set(this, 'bargainUserHelpList', []);\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetBargainUserBargainPricePoster: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthis.active = false\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '海报生成中',\r\n\t\t\t\t\tmask: true\r\n\t\t\t\t});\r\n\t\t\t\tthis.posters = false;\r\n\t\t\t\tlet arrImagesUrl = '';\r\n\t\t\t\tlet arrImagesUrlTop = '';\r\n\t\t\t\tif (!this.PromotionCode) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthis.$util.Tips({\r\n\t\t\t\t\t\ttitle: this.errT\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tif (!this.imgTop) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tthis.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: '无法生成商品海报！'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t}, 1000);\r\n\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\turl: this.imgTop,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tarrImagesUrlTop = res.tempFilePath;\r\n\t\t\t\t\t\tlet arrImages = [this.posterbackgd, arrImagesUrlTop, this.PromotionCode];\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tthis.$util.activityCanvas(arrImages, this.bargainInfo.title, this\r\n\t\t\t\t\t\t\t\t.buyPrice, '已砍至', '还剩' + this.bargainUserInfo\r\n\t\t\t\t\t\t\t\t.surplusPrice + '元砍价成功', 0,\r\n\t\t\t\t\t\t\t\t(tempFilePath) => {\r\n\t\t\t\t\t\t\t\t\tthis.imagePath = tempFilePath;\r\n\t\t\t\t\t\t\t\t\tthis.canvasStatus = true;\r\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}, 500);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgoBargainList: function() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/activity/goods_bargain/index',\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tclose: function() {\r\n\t\t\t\tthis.$set(this, 'active', false);\r\n\t\t\t\tthis.$set(this, 'posters', false);\r\n\t\t\t\tthis.$set(this, 'canvasStatus', false);\r\n\t\t\t},\r\n\t\t\t// 返回\r\n\t\t\treturns() {\r\n\t\t\t\tuni.switchTab({\r\n\t\t\t\t\turl:'/pages/index/index'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tbackList(){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:'/pages/activity/goods_bargain/index'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 生命周期函数--监听页面初次渲染完成\r\n\t\t */\r\n\t\tonReady: function() {\r\n\r\n\t\t},\r\n\t\t/**\r\n\t\t * 生命周期函数--监听页面显示\r\n\t\t */\r\n\t\tonShow: function() {\r\n\t\t\tif (this.isLogin) this.getBargainDetails();\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 生命周期函数--监听页面隐藏\r\n\t\t */\r\n\t\tonHide: function() {\r\n\t\t\tif (this.interval !== null) clearInterval(this.interval);\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 生命周期函数--监听页面卸载\r\n\t\t */\r\n\t\tonUnload: function() {\r\n\t\t\tif (this.interval !== null) clearInterval(this.interval);\r\n\t\t},\r\n\r\n\t\t//#ifdef MP\r\n\t\t/**\r\n\t\t * 用户点击右上角分享\r\n\t\t */\r\n\t\tonShareAppMessage: function() {\r\n\t\t\tlet that = this,\r\n\t\t\t\tshare = {\r\n\t\t\t\t\ttitle: '您的好友' + that.userInfo.nickname + '邀请您帮他砍' + that.bargainInfo.title + ' 快去帮忙吧！',\r\n\t\t\t\t\tpath: '/pages/activity/goods_bargain_details/index?id=' + this.id + '&startBargainUid=' + this\r\n\t\t\t\t\t\t.startBargainUid + '&spread=' + this.uid + '&storeBargainId=' + this.storeBargainId,\r\n\t\t\t\t\timageUrl: that.bargainInfo.image,\r\n\t\t\t\t};\r\n\t\t\tthat.close();\r\n\t\t\treturn share;\r\n\t\t},\r\n\t\t//#endif\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\tpage {\r\n\t\t@include main_bg_color(theme);\r\n\t}\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n\t.home_back{\r\n\t\tdisplay: block;\r\n\t\tcolor: #000;\r\n\t\tfont-size: 32px;\r\n\t\ttext-align: center;\r\n\t\twidth: 58rpx;\r\n\t\theight: 58rpx;\r\n\t\tline-height: 58rpx;\r\n\t\tbackground: rgba(255, 255, 255, 0.3);\r\n\t\tborder: 1px solid rgba(0, 0, 0, 0.1);\r\n\t\tborder-radius: 50%;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tmargin-left: 20rpx;\r\n\t}\r\n\t.select_nav{\r\n\t\twidth: 170rpx !important;\r\n\t\theight: 60rpx !important;\r\n\t\tborder-radius: 33rpx;\r\n\t\tbackground: rgba(0, 0, 0, 0.4);\r\n\t\tborder: 1px solid rgba(0,0,0,0.1);\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 18px;\r\n\t\tline-height: 58rpx;\r\n\t\tposition: relative;\r\n\t\tleft: 20rpx;\r\n\t}\r\n\t.px-20{\r\n\t\tpadding: 0 20rpx 0;\r\n\t}\r\n\t.justify-center{\r\n\t\tjustify-content: center;\r\n\t}\r\n\t.align-center {\r\n\t\talign-items: center;\r\n\t}\r\n\t.nav_line{\r\n\t\tcontent: '';\r\n\t\tdisplay: inline-block;\r\n\t\twidth: 1px;\r\n\t\theight: 34rpx;\r\n\t\tbackground: rgba(255, 255, 255, 0.1);\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tmargin: auto;\r\n\t}\r\n\t\r\n\t\r\n\t.userEvaluation {\r\n\t\ti {\r\n\t\t\tdisplay: inline-block;\r\n\t\t}\r\n\t}\r\n\t.bargain{\r\n\t\t/* #ifdef MP || APP-PLUS */\r\n\t\t@include main_bg_color(theme);\r\n\t\t/* #endif */\r\n\t\theight: 100vh;\r\n\t\toverflow: auto;\r\n\t}\r\n\t.go {\r\n\t\tcolor: #E93323;\r\n        text-align: center;\r\n\t\tfont-size: 28rpx;\r\n\t\tmargin-top: 26rpx;\r\n\t\t.iconfont {\r\n\t\t\tfont-size: 11px\r\n\t\t}\r\n\t}\r\n\r\n\t.poster-pop {\r\n\t\twidth: 594rpx;\r\n\t\theight: 850rpx;\r\n\t\tposition: fixed;\r\n\t\tleft: 50%;\r\n\t\ttransform: translateX(-50%);\r\n\t\tz-index: 999;\r\n\t\ttop: 50%;\r\n\t\tmargin-top: -466rpx;\r\n\r\n\t\timage {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tdisplay: block;\r\n\t\t\tborder-radius: 10rpx;\r\n\t\t}\r\n\t\t.close_box{\r\n\t\t\ttext-align: center;\r\n\t\t\tmargin-top: 55rpx;\r\n\t\t}\r\n\t\t.close {\r\n\t\t\tcolor: #fff;\r\n\t\t\tfont-size: 52rpx;\r\n\t\t}\r\n\r\n\t\t.save-poster {\r\n\t\t\tbackground-color: #df2d0a;\r\n\t\t\tfont-size: ：22rpx;\r\n\t\t\tcolor: #fff;\r\n\t\t\ttext-align: center;\r\n\t\t\theight: 76rpx;\r\n\t\t\tline-height: 76rpx;\r\n\t\t\twidth: 100%;\r\n\t\t}\r\n\r\n\t\t.keep {\r\n\t\t\tcolor: #fff;\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-size: 25rpx;\r\n\t\t\tmargin-top: 25rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.canvas {\r\n\t\tposition: fixed;\r\n\t\topacity: 0;\r\n\t}\r\n\r\n\t.font_hui {\r\n\t\tcolor: #CCCCCC !important;\r\n\t}\r\n\r\n\t.bg_qian {\r\n\t\tbackground-color: #F5F5F5 !important;\r\n\t}\r\n\r\n\t.font-color-red {\r\n\t\tfont-weight: 800;\r\n\t\t@include price_color(theme);\r\n\t}\r\n\r\n\t.huiBtn {\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #fff;\r\n\t\twidth: 630rpx;\r\n\t\theight: 80rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 80rpx;\r\n\t\tmargin: 0 auto;\r\n\t\tbackground-color: #CCCCCC;\r\n\t\t// box-shadow: 0 7rpx 0 #AAAAAA;\r\n\t\tborder-radius: 40rpx;\r\n\t}\r\n\r\n\t.huifont {\r\n\t\tcolor: #CCCCCC;\r\n\t}\r\n\r\n\t.buyBox {\r\n\t\tmargin-top: 40rpx;\r\n\r\n\t\t.on {\r\n\t\t\twidth: 300rpx;\r\n\t\t\theight: 80rpx;\r\n\t\t\tborder-radius: 40rpx;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tcolor: #fff;\r\n\t\t\ttext-align: center;\r\n\t\t\tline-height: 80rpx;\r\n\t\t}\r\n\r\n\t\t.buyNow {\r\n\t\t\t@include linear-gradient(theme);\r\n\t\t\t// box-shadow: 0 7rpx 0 #C11100;\r\n\t\t}\r\n\r\n\t\t.buyMore {\r\n\t\t\tbackground: linear-gradient(180deg, #FFCA52 0%, #FE960F 100%);\r\n\t\t\t// box-shadow: 0 7rpx 0 #D87A00;\r\n\t\t}\r\n\t}\r\n\r\n\r\n\t.mr8 {\r\n\t\tmargin-right: 8rpx;\r\n\t}\r\n\r\n\t.contentNo {\r\n\t\twidth: 100%;\r\n\t\tpadding: 50rpx 15rpx;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #AAAAAA;\r\n\t}\r\n\r\n\t.generate-posters {\r\n\t\twidth: 100%;\r\n\t\theight: 170rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\tbottom: 0;\r\n\t\tz-index: 388;\r\n\t\ttransform: translate3d(0, 100%, 0);\r\n\t\ttransition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);\r\n\t\tborder-top: 1rpx solid #eee;\r\n\t}\r\n\r\n\t.generate-posters.on {\r\n\t\ttransform: translate3d(0, 0, 0);\r\n\t}\r\n\r\n\t.generate-posters .item {\r\n\t\tflex: 1;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 30rpx;\r\n\t}\r\n\r\n\t.generate-posters .item .iconfont {\r\n\t\tfont-size: 80rpx;\r\n\t\tcolor: #5eae72;\r\n\t}\r\n\r\n\t.generate-posters .item .iconfont.icon-haibao {\r\n\t\tcolor: #5391f1;\r\n\t}\r\n\r\n\t.bargain .bargainGang {\r\n\t\tbackground-color: #fff;\r\n\r\n\t\t.list {\r\n\t\t\tpadding: 50rpx 30rpx 0 30rpx;\r\n\t\t}\r\n\r\n\t\t.open {\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: #999;\r\n\t\t\tmargin-top: 30rpx;\r\n\t\t}\r\n\r\n\t\t.helpNo {\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: #AAAAAA;\r\n\t\t}\r\n\t}\r\n\r\n\t.bargain .bargainGang .open .iconfont {\r\n\t\tfont-size: 25rpx;\r\n\t\tmargin: 5rpx 0 0 10rpx;\r\n\t}\r\n\r\n\t.bargain .icon-xiangzuo {\r\n\t\tfont-size: 24rpx;\r\n\t\tmargin-right: 6px;\r\n\t}\r\n\r\n\t.bargain .header {\r\n\t\t// @include bargain-header-bg(theme);\r\n\t\tbackground-repeat: no-repeat;\r\n\t\tbackground-size: 100% 100%;\r\n\t\twidth: 100%;\r\n\t\theight: 340rpx;\r\n\t\tmargin: 0 auto;\r\n\t\tpadding-top: 20rpx;\r\n\r\n\t\t.back {\r\n\t\t\twidth: 235rpx;\r\n\t\t\theight: 54rpx;\r\n\t\t\t@include second-gradient(theme);\r\n\t\t\topacity: 1;\r\n\t\t\tborder-radius: 0px 27rpx 27rpx 0px;\r\n\t\t\tline-height: 54rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tcolor: #6E3F00;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t.bargain .header .pictxt {\r\n\t\tmargin: 330rpx auto 0 auto;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #fff;\r\n\t}\r\n\r\n\t.bargain .header .pictxt .pictrue {\r\n\t\twidth: 56rpx;\r\n\t\theight: 56rpx;\r\n\t\tmargin-right: 30rpx;\r\n\t}\r\n\r\n\t.bargain .header .pictxt .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 50%;\r\n\t\tborder: 2rpx solid #fff;\r\n\t}\r\n\r\n\t.bargain .header .pictxt .text {\r\n\t\tmargin-left: 20rpx;\r\n\t}\r\n\r\n\t.bargain .header .pictxt .text text {\r\n\t\tmargin-left: 20rpx;\r\n\t}\r\n\r\n\t.bargain .header .time {\r\n\t\tbackground-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAbgAAACmCAMAAACfv2reAAAAk1BMVEUAAAD/nSL/oCj/oCj/nSH/nSH/oCj/mhr/mhr/oCj/oCj/oCj/nSH/niT/mhr/mhr/oCj/mhr/oCj/oCj/mhr/niP/oCj/oCj/mhr/////oCj/mhr/nyT/w3f/rkf/rEL/pjT/tlr/xXv/uF3//vz/vmv/wXP/vWr/79r/1aD/05r/wXL/nB//5MH/+/X/t1r/5MP28hXaAAAAGXRSTlMABvndiVtUVPjkOC8Dk+DarKyQC+QH86amYv5b+wAAA6JJREFUeNrs3Nlu4kAQheEyGOywBkImcR8Sh7Avycz7P920G8l3MYtccko63wXc969GXuiS20XJ6ygF1SQdvyaRiL5kDKrZOBFtgxjID7tVRjVZ7Q45EA9EV4z5ntVqttqfEIuqBKevjGr3dUIiiqIZ9hkp2GMWiZ4Ocv5Oqljl6IieCQ4ZqThgInpG2GWkYoeR6EnBX0olK6SiB8hICSAlhjOE4YxiOKMYziiGM4rhjGI4o5TCIWA4PQhqzzcPGE4PMA9q3m0fAcPpKdfYk3o47z1gOD3Ae+A8qUfuLby/C4bTA5zXOPekHktvu11v12uG0wOc13jpybWi7ku75X6yOR43bwHD6QHegmK13U/+tF+6kZS6bVfls8RweoDPkqvS7pZ/lXTVNpvNkTtOW7nj/Gq7avFACrG7YOlt196W4fQA5zVeeu6CWLyu81rT3lP1VWWBV5WKgEVQeVX51Bu2nNcViR7890Of93EKVO7j+iFYJJ1iv/XlEj450QZ8BHJJv9hzHXn0n1M+q2ze9c8qpz7Zo7T9Z4+vdZoHyJV6Pllbin0XMVzzrg/37JO1xHl8kfoLAHIt5zHcb8FwRjGcUQxnFMMZdU84HrNq3jdSxXBjHmzUssPo9nA8Sty8Aya3h+Ph/caFw/s3h+O4jMbtMXtWDCcJTv8yqqQ/oKYMx5FQDSpHQumGGww5hK1e32EI23BwTziOPWzYOBFRDydRZ8JBo/VJR5NOJHeGI1MYziiGM4rhjGI4oxjOKIYziuGMYjijGM4ohjOK4YxiOKMYziiGM4rhjGI4oxjOKIYziuH+s0cHJAAAAACC/r9uR6AXnBI3JW5K3JS4KXFT4qbETYmbEjclbkrclLgpcVPipsRNiZsSNyVuStyUuClxU+KmxE2JmxI3JW5K3JS4KXFT4qbETYmbEjclbkrclLgpcVPipsRNiZsSNyVuStyUuClxU+KmxE2JmxI3JW5K3JS4KXFT4qbETYmbEjclbkrclLgpcVPipsRNiZsSNyVuStyUuClxtUdHJwzDUAxFZTs2xInBxLQf2n/QvrTQGSLQ+ZAGuKIcTpTDiXI4UQ4nyuFEOZwohxPlcKIcTpTDiXI4UQ4nyuFEOZwohxPlcKIcThQD3jEJJmSRzCixHSakkyyYsRdMyCA5UWPzAZNxZJIVaY/bXU7G8Q32AhpDvvqCPd7qIzM0hI0mZsPtHDQp48RPKzQZpeEv1Vky7fFymTXh9gGY1gZJcqJI8QAAAABJRU5ErkJggg==');\r\n\t\tbackground-repeat: no-repeat;\r\n\t\tbackground-size: 100% 100%;\r\n\t\twidth: 440rpx;\r\n\t\theight: 166rpx;\r\n\t\tmargin: 0 auto;\r\n\r\n\t\tfont-size: 22rpx;\r\n\t\ttext-align: center;\r\n\t\tpadding-top: 11rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.bargain .header .time .red {\r\n\t\tcolor: #fc4141;\r\n\t}\r\n\r\n\t.bargain .header .people {\r\n\t\ttext-align: center;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 20rpx;\r\n\t\tposition: absolute;\r\n\t\twidth: 85%;\r\n\t\t/* #ifdef MP || APP-PLUS */\r\n\t\theight: 44px;\r\n\t\tline-height: 44px;\r\n\t\ttop: 40rpx;\r\n\t\t/* #endif */\r\n\t\t/* #ifdef H5 */\r\n\t\ttop: 58rpx;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.bargain .header .time text {\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.bargain {\r\n\t\t.wrapper {\r\n\t\t\tmargin-top: -100rpx;\r\n\r\n\t\t\t.pictxt {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 260rpx;\r\n\t\t\t\tbackground-color: #FFF5E6;\r\n\t\t\t\tpadding: 20rpx;\r\n\t\t\t\tborder-radius: 14rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.surplus {\r\n\t\t\t\tcolor: #999999;\r\n\t\t\t\tfont-size: 22rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.content {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: auto;\r\n\t\t\t// background-image: url('../static/zhuangshi.png');\r\n\t\t\tbackground-size: 100% 100%;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-bottom-left-radius: 14rpx;\r\n\t\t\tborder-bottom-right-radius: 14rpx;\r\n\t\t\tpadding: 40rpx 0 70rpx 0;\r\n\r\n\t\t\t.money {\r\n\t\t\t\tcolor: #333333;\r\n\t\t\t\tfont-size: 36rpx;\r\n\r\n\t\t\t\t.price {}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\r\n\t.bargain .wrapper,\r\n\t\t{\r\n\t\t// background-image: url('data:image/png;base64,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');\r\n\t\t// babackground-repeat: no-repeat;\r\n\t\t// background-size: 100% 100%;\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #fff;\r\n\t\tpadding: 0 24rpx 10rpx 24rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.bargain .pictxt .pictrue {\r\n\t\twidth: 220rpx;\r\n\t\theight: 220rpx;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.bargain .pictxt .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 6rpx;\r\n\t}\r\n\r\n\t.bargain .pictxt .text {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #333333;\r\n\r\n\t\t.line1 {\r\n\t\t\twidth: 324rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.bargain .pictxt .text .money {\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.bargain .pictxt .text .money .num {\r\n\t\tfont-size: 36rpx;\r\n\t}\r\n\r\n\t.bargain .pictxt .text .successNum {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.bargain .cu-progress {\r\n\t\toverflow: hidden;\r\n\t\theight: 12rpx;\r\n\t\tbackground-color: #eee;\r\n\t\twidth: 560rpx;\r\n\t\theight: 20rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tmargin: 20rpx auto;\r\n\t}\r\n\r\n\t.bargain .cu-progress .bg-red {\r\n\t\twidth: 0;\r\n\t\theight: 100%;\r\n\t\ttransition: width 0.6s ease;\r\n\t\tborder-radius: 20rpx;\r\n\t\t@include linear-gradient(theme);\r\n\t}\r\n\r\n\t.bargain .money {\r\n\t\t// font-size: 22rpx;\r\n\t\t// color: #999;\r\n\t\t// margin-top: 40rpx;\r\n\r\n\t}\r\n\r\n\t.bargain .bargainSuccess {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #333333;\r\n\t\ttext-align: center;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n        margin-bottom: 40rpx;\r\n\t\timage {\r\n\t\t\twidth: 48rpx;\r\n\t\t\theight: 62rpx;\r\n\t\t\tmargin-right: 18rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.bargain .bargainSuccess .iconfont {\r\n\t\tfont-size: 45rpx;\r\n\t\tcolor: #54c762;\r\n\t\tpadding-right: 18rpx;\r\n\t\tvertical-align: -5rpx;\r\n\t}\r\n\r\n\t.bargain .bargainBnt {\r\n\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #fff;\r\n\t\twidth: 630rpx;\r\n\t\theight: 80rpx;\r\n\t\t// background-image: url('../static/btn.png');\r\n\t\tbackground-size: 100% 100%;\r\n\t\tborder-radius: 40rpx;\r\n\t\t@include bargain-btn(theme);\r\n\t\ttext-align: center;\r\n\t\tline-height: 80rpx;\r\n\t\tmargin: 0 auto;\r\n\t}\r\n\t.pb-25{\r\n\t\tpadding-bottom: 25rpx;\r\n\t}\r\n\t.bargain .bargainBnt.on {\r\n\t\tborder: 2rpx solid $theme-color;\r\n\t\tcolor: $theme-color;\r\n\t\tbackground-image: linear-gradient(to right, #fff 0%, #fff 100%);\r\n\t\twidth: 596rpx;\r\n\t\theight: 76rpx;\r\n\t}\r\n\r\n\t.bargain .bargainBnt.grey {\r\n\t\tcolor: #fff;\r\n\t\tbackground-image: linear-gradient(to right, #bbbbbb 0%, #bbbbbb 100%);\r\n\t}\r\n\r\n\t.bargain .tip {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #999;\r\n\t\ttext-align: center;\r\n\t\tmargin-top: 20rpx;\r\n\t\tmargin-bottom: 50rpx;\r\n\t}\r\n\r\n\t.bargain .lock,\r\n\t.bargain .bargainGang .lock,\r\n\t.bargain .goodsDetails .lock {\r\n\t\tbackground-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAiQAAABCCAYAAABnwc0eAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA3ZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTQyIDc5LjE2MDkyNCwgMjAxNy8wNy8xMy0wMTowNjozOSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDo4YmQzMTQ1Ny01MGY2LWFmNDMtYmY4Yi1kNWRjZTMxZDg5MTUiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6M0RCMkU3QUEzQzBCMTFFOUI2N0VEOEJBMDUwMTU2ODMiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6M0RCMkU3QTkzQzBCMTFFOUI2N0VEOEJBMDUwMTU2ODMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTggKFdpbmRvd3MpIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6NDRmMWQxNmItZTIxMC1lYzQwLWJmODYtYzE4OWRiYzNmOGYyIiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjhiZDMxNDU3LTUwZjYtYWY0My1iZjhiLWQ1ZGNlMzFkODkxNSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PoRfwVwAAAlWSURBVHja7J3NqhxFHEer+uOaGL2ECG5U1IBgEkgeQBcqLiLJ3hcIJAs34k7XZhkRFBSSjbs8QCCLEF3oA0iuBjfBr+xcRWPCzHSXVdVVM9XV1X1nrvbC6XPg78ztnuqBDh5+9THV8sfTL4qIk7ou6HpH18vu2D1dN3Vd1fVj3ODE9z8LANhO7p55KXUYTwDAf+qJLHi/o+szXXd0va/rVV1PuDrhjplzX+g6zO0HmCR4AgBG8UQRNL6h6+19vsQEmIu6XnGJZ8Z9B5iUZPAEAIziCT9C8skajUPe0nWF+w4wKfAEAIzmCRNITrmUsiSXUhzJM3G0yMWxsrBl3ptj5pzjkmsLANsPngCAUT1hAolZcJL7o77hoaz5sPm4dBc1x/yFZNPmAvcfYBLgCQAY1RMmkJz1jXddw/0wn3m6sN95lvsPMAnwBACM6gnz6eM+yZSr4ZN9KZthmOPcf4BJgCcAYFRP2Pjih082RbeZ5Rt8KQD8f8ETADCmJ8zPfu8dyuSr4YmdFyqx+8ZcZE8q+3f9txQPvinF7Lc8vsYvui3/AgDbD54AgFE9YWLMzXhoZffNSmRH9DGpT8vcvjfHEtwq6fkATIEBT+TWFXgCAE+kPZG5PJHZ932eMIHkWi5l62zTuNBvciebvDnWxrS5ylAswCToeuIp7YbMeML5QheeAMATnTyRNYMb1hculKQ8YY7u6fqydcoEERtGitX7rDO8YtrsKf4BAKZA1xNGMCL0hAsmeAIATyzzRNEKI8u/E56wMaVSyuwrf6t1AV+251M271fcFs1e9KJWqAZgCiQ9kQeeyPAEAJ6IPGFnWZwbvCPyMukJG0jmSplnTZzT9blxh23khmCtcHIXSsy5JsmcF+75FHNEAzAJOp7wjpBOML4TgycA8IT3RB6FEf8+4QkbSB7XVhbmwHu6TjcNy0Yy0lVzgTOi2eL1kf9y1xYAtpykJ/JyFUZk6Xs+eAIATzSe8F5YzraUfmq34wk/ZaMvUtsDz35w7IelaELh6NLn9tpfXNu2ALD9dDzhHRF2YDLjiWfwBACeaHsib+eKVJ6QKhbF9dfMf5Uwq+LtOb863n5Oine/444DTJ3rrztPqKUamvfSeeJb7hEAnhAuSLgsoYLXbp7oLIkX+c4qf/gn4Zjr2Vd+ugcAIliU5js0uAEAIjLnCd9ZaY1vdEdNu4Ek2xHtRBI0wjkA4EUT6aE7qgoAdFzCAKG63hgOJGWrbQs8AwBWNNFIKgDAkCeiXJLyRpFONOF8sBL9CQUAJklWivb4K6kEABKeaEWHYU+k15D4xuHurmQRAFiKZqfd48ERANDnCRm4YmDZWTHc8xFRIqEXBAAi3mmxbRuCCQD4PCHXn13pX0PiBbP6KR8AgAskO5Fj6LAAQF/HJbX8Q24SSIIwIpENAIR9lbLxgu/9KOZtACDOE37K5j8ZIRHNOhIcAwAtLxT988H0XQDAeyLZoVk7kBRrNwaAqYqmFMs9BQAANgkkYpNAstwTTUULUrAPABhP5M1rPJvL7C4AdPJEMK0rNwkkMncNfBgBAOjxBJsnAsB+nhBirbWoiUCSid7JYcliEgCIRCNcB2ZoC0YAmKYnQj+ED+NcP5D0NUA0ACBWo6dSJgIJAIAmy4LnWwXPsulxRTqQqCjRmFc6PgCwMk0kF//z34xbAwBRpvCucH6wf2drBJK4B0QKAYCOHxJuCB8vDgAg4pFUMZgruoFEqf5V8+zYCgChGPxwrBQ80wYAunnC548wP8i0KIpkoFGp+WDmhwEgDB110/NRKpjmBQCIYoPNFMEPZdS6W8fXVXQ1L6CanwEDwMoH4WtLQngCAHyeCBazqmVCSXqi6BVN61iwsBUAQFWid1c0PAEAcZ6In62X8ERihGThhmEFC9QAoL/nk9qHRLGhIgAMdFz89O5agcRcwA+rtJaQ0PMBAO+JRb8P8AQA2I7LonusNXWzbyBZpLeDrrm3ABCJhrXvALBOx2UNN3QDSTVvN26FEYZiAcA4YZ7wAQ/hBIBEngi3jd9ohKQjGv87YqZsACD0hEi7AgDAemKR6LT0d1gGAklqnIWeDwCEgSR2BJ4AgMgT4YarAzs6F/0XiNzCr24AIBVI4gf9MlACANYTs64jBvotiTUkM5F+jDiWAQDviXm3p2LmiGt6LQAQ5wmRyBLrbIyWWtQKABCLpveZV9weABDtGZfObMtaP/udMQQLAGuIhlFUABjquMxFZ55mYCR1YMomSjO4BgBCT/if8fmn/eIKAOjkieTwSDqQ3D3zUuvAiY+ebxop/7vh9oXiz9s23//MjQfYUpL/z3/4XKvTs+rx4AkAPBF6IprL9U8Il902WeeiH/9+0g6zmNWx/tXWXNy9rM8BAPK5fP+knbYxhScAoNcTKzc0vnCe+Ph+xxNhINnR9ZmuO41gomoWu97R9YWuw9xqgEnS9UQVBRM8AYAnvCe8G+JckfBEETS+oett+5fZf772G5nIcFMTE2Au6npF1zu6Ztx3gElJJvDEfLVepHaLR/AEAJ4IPVHP+55n0/GEHyH5ZNl4eQEdSqqFSzOLeAvYt3Rd4b4DTIrIE84LoScqPAGAJwJPVC5P+Eyh5r15wgSSUy6liFYgqcMLNOI5kmcil8sVspdcWwDYfhKeWKw84V8VngDAEwGqilxR2VCS8oQJJBd05f6o+VD9V925QP1XJQ5lmTha5PYzsmlzgfsPMAkSnqhWjhDNa/1wgScA8ETgicUqT1hfVDZjpDxhAslZ33hXnzQfevC11GLRoUQ1ZcRjjnnMZ54u7Hee5f4DTIIeT6imx1NrT/xZiwe3MzwBgCcCT2RBntBh5M/+PGEWtR73SaZ0wyezXzPxx1c7g99qPqvbHH9Y1fwTAGw/PZ7I8AQA7OOJ9fKEtYmZxzEpZVN0m1kwBwQAWwyeAIAxPWFa3TuUHVgWv/yLtgDw/wFPAMConjCB5GZ58N7LrZKeD8AUwBMAMKonTCC5lktZHaCxaXOVoViASYAnAGBUT5hAsqfrywNcwLTZ48GeAJMATwDAqJ6wK08qpd7XL7c2aHxbl2kjaoVqAKYAngCAMT1hA8lcKfOsiXO6PjfuGGhYuyRzXrjnU8wRDcAkwBMAMKYnbCB5XFtZmAPv6Tqt61NdP5lr63qo6wd37Ixotnh95K/o2gLAloMnAGBMT/wjwAC10O4qfVDGDQAAAABJRU5ErkJggg==');\r\n\t\tbackground-repeat: no-repeat;\r\n\t\tbackground-size: 100% 100%;\r\n\t\twidth: 548rpx;\r\n\t\theight: 66rpx;\r\n\t\tposition: absolute;\r\n\t\tleft: 50%;\r\n\t\ttransform: translateX(-50%);\r\n\t\tbottom: -43rpx;\r\n\t\tz-index: 5;\r\n\t}\r\n\r\n\t.bargain .title {\r\n\t\tfont-size: 40rpx;\r\n\t\tfont-weight: 600;\r\n\t\t// height: 80rpx;\r\n\t\t// margin-top: 30rpx;\r\n\t}\r\n\r\n\t.bargain .title .pictrue {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t}\r\n\r\n\t.bargain .title .pictrue.on,\r\n\t.bargain .title .pictrue.on {\r\n\t\ttransform: rotate(180deg);\r\n\t}\r\n\r\n\t.bargain .title .pictrue image,\r\n\t.bargain .title .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.bargain .title .tits {\r\n\t\tmargin: 50rpx 15rpx 40rpx 15rpx;\r\n\t}\r\n\r\n\t.bargain .title .titleCon {\r\n\t\tmargin: 0 10rpx -14rpx 10rpx;\r\n\t\tcolor: #fff;\r\n\t\tborder: 16rpx solid linear-gradient(0deg, #FFD136 0%, rgba(255, 213, 72, 0.12) 100%);\r\n\t\t;\r\n\t}\r\n\r\n\t.bargain .title .line {\r\n\t\t// width: 216rpx;\r\n\t\theight: 16rpx;\r\n\t\tbackground: linear-gradient(0deg, #FFD136 0%, rgba(255, 213, 72, 0.12) 100%);\r\n\t\topacity: 0.6;\r\n\t}\r\n\r\n\t.bargain .bargainGang .list .item {\r\n\t\tpadding-bottom: 50rpx;\r\n\t}\r\n\r\n\t.bargain .bargainGang .list .item .pictxt {\r\n\r\n\t\t// width: 310rpx;\r\n\t\t.num {\r\n\t\t\twidth: 28rpx;\r\n\t\t\theight: 36rpx;\r\n\r\n\t\t\t// image{\r\n\t\t\t// \twidth: 100%;\r\n\t\t\t// \theight: 100%;\r\n\t\t\t// }\r\n\t\t}\r\n\r\n\t\t.num1 {\r\n\t\t\tbackground-image: url(../static/n1.png);\r\n\t\t\tbackground-repeat: no-repeat;\r\n\t\t\tbackground-size: 100% 100%;\r\n\t\t}\r\n\r\n\t\t.num2 {\r\n\t\t\tbackground-image: url(../static/n2.png);\r\n\t\t\tbackground-repeat: no-repeat;\r\n\t\t\tbackground-size: 100% 100%;\r\n\t\t}\r\n\r\n\t\t.num3 {\r\n\t\t\tbackground-image: url(../static/n3.png);\r\n\t\t\tbackground-repeat: no-repeat;\r\n\t\t\tbackground-size: 100% 100%;\r\n\t\t}\r\n\t}\r\n\r\n\t.bargain .bargainGang .list .item .pictxt .pictrue {\r\n\t\twidth: 70rpx;\r\n\t\theight: 70rpx;\r\n\t\tmargin-right: 14rpx;\r\n\t}\r\n\r\n\t.bargain .bargainGang .list .item .pictxt .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 50%;\r\n\t}\r\n\r\n\t.bargain .bargainGang .list .item .pictxt .text {\r\n\t\twidth: 225rpx;\r\n\t\tfont-size: 20rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.bargain .bargainGang .list .item .pictxt .text .name {\r\n\t\tfont-size: 25rpx;\r\n\t\tcolor: #333333;\r\n\t\tmargin-bottom: 7rpx;\r\n\t}\r\n\r\n\t.bargain .bargainGang .list .item .money {\r\n\t\tfont-size: 25rpx;\r\n\t\tcolor: #999999;\r\n\t}\r\n\r\n\t.bargain .bargainGang .list .item .money .iconfont {\r\n\t\tfont-size: 35rpx;\r\n\t\tvertical-align: middle;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\r\n\t.bargain .bargainGang .load {\r\n\t\tfont-size: 24rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 80rpx;\r\n\t\theight: 80rpx;\r\n\t}\r\n\r\n\t.bargain .goodsDetails {\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #fff;\r\n\t\t//margin: 13rpx auto 0 auto;\r\n\t}\r\n\r\n\t.bargain .goodsDetails~.goodsDetails {\r\n\t\tmargin-bottom: 50rpx;\r\n\t}\r\n\r\n\t.bargain .goodsDetails .conter {\r\n\t\t// margin-top: 20rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.bargain .goodsDetails .conter image {\r\n\t\twidth: 100% !important;\r\n\t\tdisplay: block !important;\r\n\t\theight: unset !important;\r\n\t}\r\n\r\n\t.bargain .bargainTip {\r\n\t\tposition: fixed;\r\n\t\ttop: 50%;\r\n\t\tleft: 50%;\r\n\t\twidth: 500rpx;\r\n\t\theight: 469rpx;\r\n\t\tmargin-left: -246rpx;\r\n\t\tz-index: 111;\r\n\t\tborder-radius: 14rpx;\r\n\t\tbackground-color: #fff;\r\n\t\ttransition: all 0.3s ease-in-out 0s;\r\n\t\topacity: 0;\r\n\t\ttransform: scale(0);\r\n\t\tpadding-bottom: 60rpx;\r\n\t\tmargin-top: -330rpx;\r\n\t\tbackground: linear-gradient(180deg, #FFFFFF 0%, #FFEEEB 100%);\r\n\t\t\r\n\t\t.cu-progress {\r\n\t\t\twidth: 410rpx;\r\n\t\t\tmargin: 0 auto;\r\n\t\t}\r\n\r\n\t\t.t1 {\r\n\t\t\ttext-align: center;\r\n\t\t\tcolor: #666666;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tmargin: 20rpx 0 54rpx 0;\r\n\t\t}\r\n\r\n\t\t.bubbleBox {\r\n\t\t\twidth: 410rpx;\r\n\t\t\tmargin: 16rpx auto;\r\n\r\n\t\t\t.bubble {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tdisplay: inline-block;\r\n\r\n\t\t\t\ttext {\r\n\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\tpadding: 2rpx 8rpx;\r\n\t\t\t\t\tborder-radius: 30rpx;\r\n\t\t\t\t\t@include main_bg_color(theme);\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t}\r\n\r\n\t\t\t\ttext::before {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\twidth: 0;\r\n\t\t\t\t\theight: 32rpx;\r\n\t\t\t\t\tborder: 14rpx solid;\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tbottom: -54rpx;\r\n\t\t\t\t\tleft: 14rpx;\r\n\t\t\t\t\t@include bragin-border-rate(theme);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t}\r\n\r\n\t}\r\n\t.pictrue{\r\n\t\t// @include bargain-tip-header(theme);\r\n\t\tbackground-size: 100%;\r\n\t}\r\n\t.bargain .bargainTip.on {\r\n\t\topacity: 1;\r\n\t\ttransform: scale(1);\r\n\t}\r\n\r\n\t.bargain .bargainTip .pictrue {\r\n\t\twidth: 500rpx;\r\n\t\theight: 200rpx;\r\n\t\tmargin-top: -118rpx;\r\n\t}\r\n\r\n\t.bargain .bargainTip .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.bargain .bargainTip .cutOff {\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #333;\r\n\t\ttext-align: center;\r\n\t\tmargin: 12rpx 0 0rpx 0;\r\n\t\tfont-weight: 600;\r\n\r\n\t\ttext {\r\n\t\t\tfont-weight: 600;\r\n\t\t\tfont-size: 44rpx;\r\n\t\t}\r\n\t}\r\n\t.font_color{\r\n\t\t@include price_color(theme);\r\n\t}\r\n\t.bargain .bargainTip .cutOff.on {\r\n\t\tmargin-top: 26rpx;\r\n\t}\r\n\r\n\t.bargain .bargainTip .help {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\ttext-align: center;\r\n\t\tmargin-top: 40rpx;\r\n\t}\r\n\r\n\t.bargain .bargainTip .tipBnt {\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #fff;\r\n\t\twidth: 360rpx;\r\n\t\theight: 82rpx;\r\n\t\tborder-radius: 41rpx;\r\n\t\t// background-image: linear-gradient(to right, #f67a38 0%, #f11b09 100%);\r\n\t\t@include linear-gradient(theme);\r\n\t\ttext-align: center;\r\n\t\tline-height: 82rpx;\r\n\t\tmargin: 0 auto;\r\n\t}\r\n\r\n\t.bargain_view {\r\n\t\twidth: 180rpx;\r\n\t\theight: 48rpx;\r\n\t\tbackground: rgba(0, 0, 0, 0.5);\r\n\t\topacity: 1;\r\n\t\tborder-radius: 0 0 6rpx 6rpx;\r\n\t\tposition: absolute;\r\n\t\tbottom: 0;\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #fff;\r\n\t\ttext-align: center;\r\n\t\tline-height: 48rpx;\r\n\t}\r\n\r\n\t.iconfonts {\r\n\t\tfont-size: 22rpx !important;\r\n\t\tline-height: 220rpx;\r\n\t}\r\n\r\n\t.wxParse-div {\r\n\t\twidth: auto !important;\r\n\t\theight: auto !important;\r\n\t}\r\n\r\n\t.bargain .mask {\r\n\t\tz-index: 100;\r\n\t}\r\n\r\n\t.share-box {\r\n\t\tz-index: 1000;\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\r\n\t\timage {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294178992\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=1&id=07a662b0&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=1&id=07a662b0&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294178994\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}