{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/promotionList/index.vue?96f7", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/promotionList/index.vue?cb91", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/promotionList/index.vue?4078", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/promotionList/index.vue?68fd", "uni-app:///pages/activity/promotionList/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/promotionList/index.vue?ced3", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/promotionList/index.vue?de10"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "components", "GoodList", "emptyPage", "Loading", "data", "circular", "theme", "typeInfo", "loading", "params", "page", "limit", "onLoad", "uni", "title", "methods", "getGroomList", "getProductRank", "toDetail", "animationType", "animationDuration", "url", "onReachBottom"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACiDnnB;AACA;AACA;AAEA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AACA;AAAA,eACA;EACAC;EACAC;IACAC;IACAC;IACAC;EACA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QAAA;QACAC;QACAC;MACA;IAAA,kDACA,0DACA,sDACA;EAEA;EACAC;IACA;IACAC;MACAC;IACA;IACA;MACA;IACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;MACA;MACA;QAAA;QACA;QACA;QACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;MACA;IACA;IACAC;MACAL;QACAM;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrHA;AAAA;AAAA;AAAA;AAA0oC,CAAgB,gnCAAG,EAAC,C;;;;;;;;;;;ACA9pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/activity/promotionList/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/activity/promotionList/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=1e598b1d&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/activity/promotionList/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=1e598b1d&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.tempArr.length\n  var g1 = _vm.tempArr.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<div class=\"quality-recommend\" :data-theme=\"theme\">\r\n\t\t<view class=\"saleBox\" v-if=\"typeInfo.pic\"></view>\r\n\t\t<view class=\"header skeleton-rect\" v-if=\"typeInfo.pic\">\r\n\t\t\t<swiper indicator-dots=\"true\" autoplay=\"true\" :circular=\"circular\" interval=\"3000\" duration=\"1500\"\r\n\t\t\t\tindicator-color=\"rgba(255,255,255,0.6)\" indicator-active-color=\"#fff\">\r\n\t\t\t\t<block>\r\n\t\t\t\t\t<swiper-item class=\"borRadius14\">\r\n\t\t\t\t\t\t<image :src=\"typeInfo.pic\" class=\"slide-image borRadius14\" lazy-load></image>\r\n\t\t\t\t\t</swiper-item>\r\n\t\t\t\t</block>\r\n\t\t\t</swiper>\r\n\t\t</view>\r\n\t\t<div class=\"title acea-row row-center-wrapper\">\r\n\t\t\t<div class=\"line\"></div>\r\n\t\t\t<div class=\"name\">\r\n\t\t\t\t<span class=\"iconfont icon-jingpintuijian\" ></span> {{typeInfo.name}}\r\n\t\t\t</div>\r\n\t\t\t<div class=\"line\"></div>\r\n\t\t</div>\r\n\t\t<view class=\"wrapper\">\r\n\t\t\t<!-- <GoodList :bastList=\"tempArr\" :status=\"1\"></GoodList> -->\r\n\t\t\t<view class=\"list\">\r\n\t\t\t\t<view class=\"item acea-row row-middle\" v-for=\"(item,index) in tempArr\" :key=\"index\" @click=\"toDetail(item.id)\">\r\n\t\t\t\t\t<view class=\"img_box\">\r\n\t\t\t\t\t\t<image class=\"pictrue\" :src=\"item.image\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"ml_11 flex-column justify-between\">\r\n\t\t\t\t\t\t<view class=\"goods_name\">{{item.storeName}}</view>\r\n\t\t\t\t\t\t<view class=\"price flex justify-between\">\r\n\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t<text class=\"price_bdg\">￥</text>{{item.price}}\r\n\t\t\t\t\t\t\t\t<text class=\"otPrice\">￥{{item.otPrice}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class='loadingicon acea-row row-center-wrapper' v-if=\"goodScroll\">\r\n\t\t\t\t<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"txt-bar\"  v-if=\"tempArr.length > 0\">😕人家是有底线的~~</view>\r\n\t\t\t<emptyPage title=\"暂无数据~\" v-if=\"tempArr.length == 0\"></emptyPage>\r\n\t\t</view>\r\n\t</div>\r\n</template>\r\n<script>\r\n\timport emptyPage from '@/components/emptyPage.vue';\r\n\timport GoodList from '@/components/goodList/index';\r\n\timport {getGroomList} from '@/api/store';\r\n\timport {goPage} from '@/libs/order.js';\r\n\timport {productRank} from '@/api/api.js'\r\n\timport Loading from '@/components/Loading/index.vue';\r\n\timport animationType from '@/utils/animationType.js'\r\n\tlet app = getApp()\r\n\texport default {\r\n\t\tname: 'HotNewGoods',\r\n\t\tcomponents: {\r\n\t\t\tGoodList,\r\n\t\t\temptyPage,\r\n\t\t\tLoading\r\n\t\t},\r\n\t\tdata: function() {\r\n\t\t\treturn {\r\n\t\t\t\tcircular:true,\r\n\t\t\t\ttheme:app.globalData.theme,\r\n\t\t\t\ttypeInfo:{},\r\n\t\t\t\tloading:false,\r\n\t\t\t\tparams: { //精品推荐分页\r\n\t\t\t\t\tpage: 1,\r\n\t\t\t\t\tlimit: 10,\r\n\t\t\t\t},\r\n\t\t\t\tloading: false,\r\n\t\t\t\tgoodScroll: true, //精品推荐开关\r\n\t\t\t\ttempArr:[],\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad: function(e) {\r\n\t\t\tthis.typeInfo = JSON.parse(e.type)\r\n\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\ttitle: this.typeInfo.name\r\n\t\t\t});\r\n\t\t\tif(this.typeInfo.name == '商品排行'){\r\n\t\t\t\tthis.getProductRank();\r\n\t\t\t}else{\r\n\t\t\t\tthis.getGroomList();\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetGroomList() {\r\n\t\t\t\tthis.loading = true\r\n\t\t\t\tif (!this.goodScroll) return\r\n\t\t\t\tgetGroomList(this.typeInfo.type, this.params).then(({data}) => {\r\n\t\t\t\t\tthis.goodScroll = data.list.length >= this.params.limit\r\n\t\t\t\t\tthis.loading = false\r\n\t\t\t\t\tthis.params.page++\r\n\t\t\t\t\tthis.tempArr = this.tempArr.concat(data.list)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetProductRank(){\r\n\t\t\t\tproductRank().then(res=>{\r\n\t\t\t\t\tthis.tempArr = res.data;\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttoDetail(id){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\tanimationType: animationType.type,\r\n\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\turl:'/pages/goods_details/index?id=' + id\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\tonReachBottom() {\r\n\t\t\tif (this.params.page != 1) {\r\n\t\t\t\tthis.getGroomList();\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n</script>\r\n<style lang=\"scss\">\r\n\t/deep/ .quality-recommend {\r\n\t\tbackground-color: #f5f5f5;\r\n\t}\r\n\t.saleBox{\r\n\t\twidth: 100%;\r\n\t\theight: 298rpx;\r\n\t\t/* #ifdef MP */\r\n\t\theight: 300rpx;\r\n\t\t/* #endif */\r\n\t\t@include main_bg_color(theme);\r\n\t\tborder-radius: 0 0 50rpx 50rpx;\r\n\t}\r\n\t.quality-recommend .header {\r\n\t\twidth: 710rpx;\r\n\t\theight: 330rpx;\r\n\t\tmargin: -276rpx auto 0 auto;\r\n\t\tborder-radius: 14rpx;\r\n\t\toverflow: hidden;\r\n\t\tswiper{\r\n\t\t\theight: 330rpx !important;\r\n\t\t\tborder-radius: 14rpx;\r\n\t\t\toverflow: hidden;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.quality-recommend .header image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 14rpx;\r\n\t\toverflow: hidden;\r\n\t\timg{\r\n\t\t\tborder-radius: 14rpx;\r\n\t\t}\r\n\t}\r\n\t.quality-recommend {\r\n\t\t.wrapper {\r\n\t\t\t// background: #fff;\r\n\t\t\t.list {\r\n\t\t\t\twidth: 690rpx;\r\n\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tmargin: 0rpx auto 0 auto;\r\n\t\t\t\tpadding: 20rpx 20rpx 30rpx;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\t.item {\r\n\t\t\t\t\tbackground: #fff;\r\n\t\t\t\t\tmargin-top: 26rpx;\r\n\t\t\t\t\t.img_box{\r\n\t\t\t\t\t\twidth: 180rpx;\r\n\t\t\t\t\t\theight: 180rpx;\r\n\t\t\t\t\t\tbackground: #F3F3F3;\r\n\t\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\t.pictrue{\r\n\t\t\t\t\t\t\twidth:100%;\r\n\t\t\t\t\t\t\theight:100%;\r\n\t\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t.rank_bdg{\r\n\t\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\t\theight: 46rpx;\r\n\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\t\t\tleft: 0;\r\n\t\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\t\tmargin: auto;\r\n\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\tline-height: 46rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.ml_11{\r\n\t\t\t\t\t\tmargin-left: 22rpx;\r\n\t\t\t\t\t\tborder-bottom: 1px solid #eee;\r\n\t\t\t\t\t\tpadding-bottom: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.goods_name{\r\n\t\t\t\t\t\twidth: 420rpx;\r\n\t\t\t\t\t\theight: 80rpx;\r\n\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t\t\tline-height: 40rpx;\r\n\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\ttext-overflow:ellipsis;\r\n\t\t\t\t\t\twhite-space: wrap;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.title {\r\n\t\t\theight: 120rpx;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tcolor: #282828;\r\n\t\t\tbackground-color: #f5f5f5;\r\n\r\n\t\t\t.name {\r\n\t\t\t\tmargin: 0 20rpx;\r\n\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.line {\r\n\t\t\t\twidth: 230rpx;\r\n\t\t\t\theight: 2rpx;\r\n\t\t\t\tbackground-color: #e9e9e9;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.price{\r\n\t\tmargin-top: 60rpx;\r\n\t\tfont-size: 34rpx;\r\n\t\tfont-weight: 600;\r\n\t\t@include price_color(theme);\r\n\t\t.price_bdg{\r\n\t\t\tfont-size: 26rpx;\r\n\t\t}\r\n\t\t.otPrice{\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: #999999;\r\n\t\t\tfont-weight: 400;\r\n\t\t\tpadding-left: 12rpx;\r\n\t\t\ttext-decoration: line-through;\r\n\t\t}\r\n\t\t.cart_icon{\r\n\t\t\twidth: 48rpx;\r\n\t\t\theight: 48rpx;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\t@include main_bg_color(theme);\r\n\t\t\ttext-align: center;\r\n\t\t\tline-height: 40rpx;\r\n\t\t\t.iconfont{\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.txt-bar {\r\n\t\tpadding: 20rpx 0;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #666;\r\n\t\tbackground-color: #f5f5f5;\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294179223\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}