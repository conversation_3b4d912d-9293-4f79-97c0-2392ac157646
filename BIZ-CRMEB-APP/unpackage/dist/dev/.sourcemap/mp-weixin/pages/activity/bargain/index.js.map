{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/bargain/index.vue?d77d", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/bargain/index.vue?c582", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/bargain/index.vue?8f0d", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/bargain/index.vue?95b6", "uni-app:///pages/activity/bargain/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/bargain/index.vue?1ed5", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/bargain/index.vue?af44", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/bargain/index.vue?cf33", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/bargain/index.vue?d412"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "components", "CountDown", "Loading", "emptyPage", "home", "payment", "props", "computed", "data", "bgColor", "bargain", "status", "loadingList", "page", "limit", "payMode", "icon", "value", "title", "number", "pay_close", "pay_order_id", "totalPrice", "theme", "onShow", "<PERSON><PERSON><PERSON><PERSON>", "methods", "goPay", "onChangeFun", "action", "payClose", "pay_complete", "pay_fail", "goConfirm", "uni", "animationType", "animationDuration", "url", "goDetail", "goList", "getBargainUserList", "then", "that", "catch", "getBargainUserCancel", "bargainId", "onReachBottom"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACc;AACwB;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACqDnnB;AAOA;AACA;AACA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AACA;AAAA,eACA;EACAC;EACAC;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;EACAC;EACAC;IACA;MACAC;QACA;QACA;QACA;QACA;QACA;MACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;QACAhB;QACAiB;QACAC;QACAC;MACA,GACA;QACAnB;QACAiB;QACAC;QACAC;QACAC;MACA,EACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACA;MACA;MACA;MACA;IACA;MACAC;IACA;EACA;EACAC;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACAC;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;QACAR;MACA;QACAS;UACAC;UACAC;UACAC;QACA;MACA;IACA;IACAC;MACAJ;QACAC;QACAC;QACAC;MACA;IACA;IACA;IACAE;MACAL;QACAC;QACAC;QACAC;MACA;IACA;IACAG;MACA;MACA;MACA;MACA;QACA3B;QACAC;MACA,GACA2B;QACAC;QACAA;QACAA;QACAA;MACA,GACAC;QACAD;MACA;IACA;IACAE;MACA;MACA;QACAC;MACA,GACAJ;QACAC;QACAA;QACAA;QACAA;QACAA;QACAA;UACAxB;QACA;MACA,GACAyB;QACAD;UACAxB;QACA;MACA;IACA;EACA;EACA4B;;;;;;;;;;;;;;;;;ACtOA;AAAA;AAAA;AAAA;AAA0oC,CAAgB,gnCAAG,EAAC,C;;;;;;;;;;;ACA9pC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/activity/bargain/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/activity/bargain/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=7af2e664&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\nimport style1 from \"./index.vue?vue&type=style&index=1&id=7af2e664&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7af2e664\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/activity/bargain/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=7af2e664&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.bargain.length\n  var g1 = _vm.bargain.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :data-theme=\"theme\">\r\n\t\t<block v-if=\"bargain.length>0\">\r\n\t\t\t<div class=\"bargain-record\" ref=\"container\">\r\n\t\t\t\t<div class=\"item borRadius14\" v-for=\"(item, index) in bargain\" :key=\"index\">\r\n\t\t\t\t\t<div class=\"picTxt acea-row row-between-wrapper\">\r\n\t\t\t\t\t\t<div class=\"pictrue\">\r\n\t\t\t\t\t\t\t<image :src=\"item.image\" />\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"text acea-row row-column-around\">\r\n\t\t\t\t\t\t\t<div class=\"line1\" style=\"width: 100%;\">{{ item.title }}</div>\r\n\t\t\t\t\t\t\t<count-down :justify-left=\"'justify-content:left'\" :bgColor=\"bgColor\" :is-day=\"true\" :tip-text=\"'倒计时 '\" :day-text=\"'天'\"\r\n\t\t\t\t\t\t\t :hour-text=\"' 时 '\" :minute-text=\"' 分 '\"\r\n\t\t\t\t\t\t\t :second-text=\"' 秒 '\" :datatime=\"item.stopTime/1000\" v-if=\"item.status === 1\"></count-down>\r\n\t\t\t\t\t\t\t<div class=\"successTxt font_color\" v-else-if=\"item.status === 3 && item.isDel === false\">砍价成功</div>\r\n\t\t\t\t\t\t\t<div class=\"successTxt \" v-else-if=\"item.status === 3 && item.isDel === true && item.isPay === false\">砍价失败</div>\r\n\t\t\t\t\t\t\t<div class=\"endTxt\" v-else>活动已结束</div>\r\n\t\t\t\t\t\t\t<div class=\"money\">\r\n\t\t\t\t\t\t\t\t已砍至<span class=\"symbol font_color\">￥</span><span class=\"num font_color\">{{ item.surplusPrice }}</span>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div class=\"bottom acea-row row-between-wrapper\">\r\n\t\t\t\t\t\t<div class=\"purple\" v-if=\"item.status === 1\">活动进行中</div>\r\n\t\t\t\t\t\t<div class=\"success\" v-else-if=\"item.status === 3 && item.isDel === false\">砍价成功</div>\r\n\t\t\t\t\t\t<div class=\"end\" v-else-if=\"item.status === 3 && item.isDel === true && item.isPay === false\">砍价失败</div>\r\n\t\t\t\t\t\t<div class=\"end\" v-else>活动已结束</div>\r\n\t\t\t\t\t\t<div class=\"acea-row row-middle row-right\">\r\n\t\t\t\t\t\t\t<div class=\"bnt bg_color\" v-if=\"item.status === 3 && !item.isOrder\" @click=\"goConfirm(item)\">\r\n\t\t\t\t\t\t\t\t去付款\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"bnt bg_color\" v-if=\"item.status === 3 && !item.isDel && item.isOrder && !item.isPay\" @click=\"goPay(item.surplusPrice,item.orderNo)\">\r\n\t\t\t\t\t\t\t\t立即付款\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"bnt bg_color\" v-if=\"item.status === 1\" @click=\"goDetail(item.id)\">\r\n\t\t\t\t\t\t\t\t继续砍价\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"bnt bg_color\" v-if=\"item.status === 2\" @click=\"goList\">重开一个</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<Loading :loaded=\"status\" :loading=\"loadingList\"></Loading>\r\n\t\t\t</div>\r\n\t\t</block>\r\n\t\t<block v-if=\"bargain.length == 0\">\r\n\t\t\t<emptyPage title=\"暂无砍价记录～\"></emptyPage>\r\n\t\t</block>\r\n\t\t<payment :payMode='payMode' :pay_close=\"pay_close\" @onChangeFun='onChangeFun' :order_id=\"pay_order_id\" :totalPrice='totalPrice'></payment>\r\n\t</view>\r\n</template>\r\n<script>\r\n\timport CountDown from \"@/components/countDown\";\r\n\timport emptyPage from '@/components/emptyPage.vue'\r\n\timport {\r\n\t\tgetBargainUserList,\r\n\t\tgetBargainUserCancel\r\n\t} from \"@/api/activity\";\r\n\timport Loading from \"@/components/Loading\";\r\n\timport home from '@/components/home';\r\n\timport payment from '@/components/payment';\r\n\timport {mapGetters} from \"vuex\";\r\n\timport {setThemeColor} from '@/utils/setTheme.js'\r\n\timport animationType from '@/utils/animationType.js'\r\n\tlet app = getApp();\r\n\texport default {\r\n\t\tname: \"BargainRecord\",\r\n\t\tcomponents: {\r\n\t\t\tCountDown,\r\n\t\t\tLoading,\r\n\t\t\temptyPage,\r\n\t\t\thome,\r\n\t\t\tpayment\r\n\t\t},\r\n\t\tprops: {},\r\n\t\tcomputed: mapGetters(['isLogin', 'userInfo', 'uid']),\r\n\t\tdata: function() {\r\n\t\t\treturn {\r\n\t\t\t\tbgColor:{\r\n\t\t\t\t\t'bgColor': '',\r\n\t\t\t\t\t'Color': '#E93323',\r\n\t\t\t\t\t'width': '40rpx',\r\n\t\t\t\t\t'timeTxtwidth': '28rpx',\r\n\t\t\t\t\t'isDay': false\r\n\t\t\t\t},\r\n\t\t\t\tbargain: [],\r\n\t\t\t\tstatus: false, //砍价列表是否获取完成 false 未完成 true 完成\r\n\t\t\t\tloadingList: false, //当前接口是否请求完成 false 完成 true 未完成\r\n\t\t\t\tpage: 1, //页码\r\n\t\t\t\tlimit: 20, //数量\r\n\t\t\t\tpayMode: [{\r\n\t\t\t\t\t\tname: \"微信支付\",\r\n\t\t\t\t\t\ticon: \"icon-weixinzhifu\",\r\n\t\t\t\t\t\tvalue: 'weixin',\r\n\t\t\t\t\t\ttitle: '微信快捷支付'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: \"余额支付\",\r\n\t\t\t\t\t\ticon: \"icon-yuezhifu\",\r\n\t\t\t\t\t\tvalue: 'yue',\r\n\t\t\t\t\t\ttitle: '可用余额:',\r\n\t\t\t\t\t\tnumber: 0\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tpay_close: false,\r\n\t\t\t\tpay_order_id: '',\r\n\t\t\t\ttotalPrice: '0',\r\n\t\t\t\ttheme:app.globalData.theme\r\n\t\t\t};\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tif (this.isLogin) {\r\n\t\t\t\tthis.payMode[1].number = this.userInfo.nowMoney;\r\n\t\t\t\tthis.$set(this, 'payMode', this.payMode);\r\n\t\t\t\tthis.getBargainUserList();\r\n\t\t\t\tthis.bgColor.Color = setThemeColor();\r\n\t\t\t} else {\r\n\t\t\t\ttoLogin();\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t/**\r\n\t\t\t * 打开支付组件\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tgoPay(pay_price, order_id) {\r\n\t\t\t\tthis.$set(this, 'pay_close', true);\r\n\t\t\t\tthis.$set(this, 'pay_order_id', order_id);\r\n\t\t\t\tthis.$set(this, 'totalPrice', pay_price);\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 事件回调\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tonChangeFun: function(e) {\r\n\t\t\t\tlet opt = e;\r\n\t\t\t\tlet action = opt.action || null;\r\n\t\t\t\tlet value = opt.value != undefined ? opt.value : null;\r\n\t\t\t\t(action && this[action]) && this[action](value);\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 关闭支付组件\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tpayClose: function() {\r\n\t\t\t\tthis.pay_close = false;\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 支付成功回调\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tpay_complete: function() {\r\n\t\t\t\tthis.status = false;\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.$set(this, 'bargain', []);\r\n\t\t\t\tthis.$set(this, 'pay_close', false);\r\n\t\t\t\tthis.getBargainUserList();\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 支付失败回调\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tpay_fail: function() {\r\n\t\t\t\tthis.pay_close = false;\r\n\t\t\t},\r\n\t\t\tgoConfirm: function(item) { //立即支付\r\n\t\t\t\tif (this.isLogin === false) {\r\n\t\t\t\t\ttoLogin();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\tanimationType: animationType.type,\r\t\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\t\turl: `/pages/activity/goods_bargain_details/index?id=${item.id}&startBargainUid=${this.uid}&storeBargainId=${item.bargainUserId}`\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgoDetail: function(id) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\tanimationType: animationType.type,\r\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\turl: `/pages/activity/goods_bargain_details/index?id=${id}&startBargainUid=${this.uid}`\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 砍价列表\r\n\t\t\tgoList: function() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\tanimationType: animationType.type,\r\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\turl: '/pages/activity/goods_bargain/index'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetBargainUserList: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tif (that.loadingList) return;\r\n\t\t\t\tif (that.status) return;\r\n\t\t\t\tgetBargainUserList({\r\n\t\t\t\t\t\tpage: that.page,\r\n\t\t\t\t\t\tlimit: that.limit\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tthat.status = res.data.list.length < that.limit;\r\n\t\t\t\t\t\tthat.bargain.push.apply(that.bargain, res.data.list);\r\n\t\t\t\t\t\tthat.page++;\r\n\t\t\t\t\t\tthat.loadingList = false;\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(res => {\r\n\t\t\t\t\t\tthat.$dialog.error(res);\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetBargainUserCancel: function(bargainId) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tgetBargainUserCancel({\r\n\t\t\t\t\t\tbargainId: bargainId\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tthat.status = false;\r\n\t\t\t\t\t\tthat.loadingList = false;\r\n\t\t\t\t\t\tthat.page = 1;\r\n\t\t\t\t\t\tthat.bargain = [];\r\n\t\t\t\t\t\tthat.getBargainUserList();\r\n\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: res\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(res => {\r\n\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: res\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\tonReachBottom() {\r\n\t\t\tthis.getBargainUserList();\r\n\t\t}\r\n\t};\r\n</script>\r\n<style lang=\"scss\">\r\n\tpage {\r\n\t\t@include main_bg_color(theme);\r\n\t\theight: 100vh;\r\n\t\toverflow: auto;\r\n\t}\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n\t/*砍价记录*/\r\n\t.bargain-record{\r\n\t\tpadding: 0 30rpx 15rpx;\r\n\t}\r\n\t.bargain-record .item .picTxt .text .time {\r\n\t\theight: 36rpx;\r\n\t\tline-height: 36rpx;\r\n\t\t.styleAll {\r\n\t\t\tcolor: #fc4141;\r\n\t\t\tfont-size:24rpx;\r\n\t\t}\r\n\t}\r\n\t.bargain-record .item .picTxt .text .time .red {\r\n\t\tcolor: #999;\r\n\t\tfont-size:24rpx;\r\n\t}\r\n\t.bargain-record .item {\r\n\t\tbackground-color: #fff;\r\n\t\tmargin-top: 15rpx;\r\n\t\tpadding: 30rpx 24rpx 0 24rpx;\r\n\t}\r\n\r\n\t.bargain-record .item .picTxt {\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t\tpadding-bottom: 30rpx;\r\n\t}\r\n\r\n\t.bargain-record .item .picTxt .pictrue {\r\n\t\twidth: 150upx;\r\n\t\theight: 150upx;\r\n\t}\r\n\r\n\t.bargain-record .item .picTxt .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 6upx;\r\n\t}\r\n\r\n\t.bargain-record .item .picTxt .text {\r\n\t\twidth: 470rpx;\r\n\t\tfont-size: 30upx;\r\n\t\tcolor: #333333;\r\n\t\theight: 160rpx;\r\n\t}\r\n\r\n\t.bargain-record .item .picTxt .text .time {\r\n\t\tfont-size: 24upx;\r\n\t\tcolor: #868686;\r\n\t\tjustify-content: left !important;\r\n\t}\r\n\t\r\n\t.bargain-record .item .picTxt .text .successTxt{\r\n\t\tfont-size:24rpx;\r\n\t}\r\n\t\r\n\t.bargain-record .item .picTxt .text .endTxt{\r\n\t\tfont-size:24rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\t.bargain-record .item .picTxt .text .money {\r\n\t\tfont-size: 24upx;\r\n\t\tcolor: #999999;\r\n\t}\r\n\r\n\t.bargain-record .item .picTxt .text .money .num {\r\n\t\tfont-size: 32upx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.bargain-record .item .picTxt .text .money .symbol {\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.bargain-record .item .bottom {\r\n\t\theight: 100upx;\r\n\t\tfont-size: 27upx;\r\n\t}\r\n\r\n\t.bargain-record .item .bottom .purple {\r\n\t\tcolor: #f78513;\r\n\t}\r\n\r\n\t.bargain-record .item .bottom .end {\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.bargain-record .item .bottom .success {\r\n\t\tcolor: $theme-color;\r\n\t}\r\n\r\n\t.bargain-record .item .bottom .bnt {\r\n\t\tfont-size: 27upx;\r\n\t\tcolor: #fff;\r\n\t\twidth: 176upx;\r\n\t\theight: 60upx;\r\n\t\tborder-radius: 32upx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 60upx;\r\n\t}\r\n\t.bg_color{\r\n\t\t@include main_bg_color(theme);\r\n\t}\r\n\t.font_color{\r\n\t\t@include price_color(theme);\r\n\t}\r\n\t.bargain-record .item .bottom .bnt.cancel {\r\n\t\tcolor: #aaa;\r\n\t\tborder: 1px solid #ddd;\r\n\t}\r\n\r\n\t.bargain-record .item .bottom .bnt~.bnt {\r\n\t\tmargin-left: 18upx;\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294179209\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=1&id=7af2e664&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=1&id=7af2e664&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294179203\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}