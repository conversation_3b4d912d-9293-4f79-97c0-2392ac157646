{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_bargain/index.vue?1ed1", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_bargain/index.vue?c8c6", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_bargain/index.vue?5db5", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_bargain/index.vue?f650", "uni-app:///pages/activity/goods_bargain/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_bargain/index.vue?79a5", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_bargain/index.vue?e6c7", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_bargain/index.vue?54b4", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_bargain/index.vue?e852"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "countDown", "home", "authorize", "data", "showSkeleton", "isNodes", "bgColor", "bargainList", "page", "limit", "loading", "loadend", "navH", "isAuto", "isShowAuth", "returnShow", "loadTitle", "bargainSuccessList", "bargainTotal", "indicatorDots", "autoplay", "theme", "imgHost", "backBg", "navBgColor", "computed", "watch", "is<PERSON>ogin", "handler", "deep", "onLoad", "that", "uni", "frontColor", "backgroundColor", "setTimeout", "title", "methods", "getBargain<PERSON><PERSON>er", "auth<PERSON><PERSON><PERSON>", "goBack", "url", "onLoadFun", "openSubscribe", "getBargainList", "image", "minPrice", "setShare", "desc", "link", "imgUrl", "console", "onReachBottom"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACc;AACwB;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvCA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC2EnnB;AAIA;AAKA;AAGA;AAMA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAnBA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAoBA;EACAC;IACAC;IACAC;IAEAC;EAEA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;QACA;QACA;QACA;QACA;QACA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;EACAC;IACAC;MACAC;QACA;UACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACAC;IACA;MACA;QACAA;QACAA;QACAA;QACA;MACA;QACAA;QACAA;QACAA;QACA;MACA;QACAA;QACAA;QACAA;QACA;MACA;QACAA;QACAA;QACAA;QACA;MACA;QACAA;QACAA;QACAA;QACA;IAAA;IAEAC;MACAC;MACAC;IACA;IACAC;MACA;IACA;IACA;IACA;IACAH;MACAI;IACA;IACA;IACA;MACA;MACA;IACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;QACA;MACA;QACA;UACAF;QACA;MACA;IACA;IACA;IACAG;MACA;IACA;IACAC;MACAR;QACAS;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MASAX;QACAI;MACA;MACA;QACAJ;QACAA;UACAS;QACA;MACA;QACAT;MACA;IAEA;IACAY;MACA;MACA;MACA;MACAb;MACAA;MACAA;QAAAc;QAAAT;QAAAU;MAAA;MACA;QACAtC;QACAC;MACA;QACAsB;QACA;QACA;QACA;QACAA;QACAA;QAIAA;QACAA;;QAGAI;UACAJ;QACA;MACA;QACAA;QACAA;MACA;IACA;IACAgB;MACA,2BACA,0BACA,6BACA,2BACA,yBACA,sBACA;QACAC;QACAZ;QACAa;QACAC;MACA,wBACA;QACAC;MACA;IACA;EACA;EACAC;IACA;EACA;;;;;;;;;;;;;;;ACrSA;AAAA;AAAA;AAAA;AAA0oC,CAAgB,gnCAAG,EAAC,C;;;;;;;;;;;ACA9pC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/activity/goods_bargain/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/activity/goods_bargain/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=51d94765&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\nimport style1 from \"./index.vue?vue&type=style&index=1&id=51d94765&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"51d94765\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/activity/goods_bargain/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=51d94765&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.bargainSuccessList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = _vm.$util.formatName(item.nickName)\n    return {\n      $orig: $orig,\n      g0: g0,\n    }\n  })\n  var g1 = _vm.bargainList.length\n  var l1 = g1\n    ? _vm.__map(_vm.bargainList, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g2 = new Date().getTime()\n        return {\n          $orig: $orig,\n          g2: g2,\n        }\n      })\n    : null\n  var g3 = g1 ? _vm.bargainList.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g1: g1,\n        l1: l1,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :data-theme=\"theme\">\r\n\t\t<view class=\"pageInfo\">\r\n\t\t\t<skeleton :show=\"showSkeleton\" :isNodes=\"isNodes\" ref=\"skeleton\" loading=\"chiaroscuro\" selector=\"skeleton\"\r\n\t\t\t\t></skeleton>\r\n\t\t\t<view class='bargain-list skeleton' :style=\"{visibility: showSkeleton ? 'hidden' : 'visible'}\">\r\n\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t<view class='iconfont icon-xiangzuo' @tap='goBack' :style=\"'top:'+ (navH/2) +'rpx'\" v-if=\"returnShow\">\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<view class='header skeleton-rect' :style=\"{backgroundImage:'url('+imgHost+backBg+')'}\" v-show=\"imgHost\">\r\n\t\t\t\t\t<view class=\"pic\">\r\n\t\t\t\t\t\t<view class='swipers skeleton-rect'>\r\n\t\t\t\t\t\t\t<swiper :indicator-dots=\"indicatorDots\" :autoplay=\"autoplay\" interval=\"2500\" duration=\"500\" vertical=\"true\"\r\n\t\t\t\t\t\t\t circular=\"true\">\r\n\t\t\t\t\t\t\t\t<block v-for=\"(item,index) in bargainSuccessList\" :key='index'>\r\n\t\t\t\t\t\t\t\t\t<swiper-item >\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"acea-row row-middle\">\r\n\t\t\t\t\t\t\t\t\t\t\t<image :src=\"item.avatar\" class=\"mr9\"></image>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class='mr9 nickName'>{{$util.formatName(item.nickName)}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<text class='mr9'>拿了</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class='line1'>{{item.title}}</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</swiper>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tit\" v-show=\"bargainTotal > 0\">已有{{bargainTotal}}人砍成功</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='list' v-if=\"bargainList.length\">\r\n\t\t\t\t\t<block v-for=\"(item,index) in bargainList\" :key=\"index\">\r\n\t\t\t\t\t\t<view class='item acea-row row-between-wrapper'\r\n\t\t\t\t\t\t\t@tap=\"openSubscribe('/pages/activity/goods_bargain_details/index?id='+ item.id +'&startBargainUid='+ uid)\">\r\n\t\t\t\t\t\t\t<view class='pictrue skeleton-rect'>\r\n\t\t\t\t\t\t\t\t<image :src='item.image'></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class='text acea-row row-column-around'>\r\n\t\t\t\t\t\t\t\t<view class='name line2 skeleton-rect'>{{item.title}}</view>\r\n\t\t\t\t\t\t\t\t<view v-if=\"item.quota>0\" class=\"acea-row skeleton-rect\" style=\"margin-bottom: 14rpx;\">\r\n\t\t\t\t\t\t\t\t\t<countDown :tipText=\"' '\" :bgColor=\"bgColor\" :dayText=\"':'\" :hourText=\"':'\"\r\n\t\t\t\t\t\t\t\t\t\t:minuteText=\"':'\" :secondText=\"' '\" :datatime=\"item.stopTime/1000\" :isDay=\"true\"\r\n\t\t\t\t\t\t\t\t\t\t></countDown>\r\n\t\t\t\t\t\t\t\t\t<text class=\"txt\">后结束</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view v-if=\"new Date().getTime()- item.stopTime >=0\">\r\n\t\t\t\t\t\t\t\t\t<view style=\"font-size: 22rpx;\" @tap='currentBargainUser'>已结束</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view v-if=\"item.quota==0\">\r\n\t\t\t\t\t\t\t\t\t<view style=\"font-size: 22rpx;\" @tap='currentBargainUser'>已售罄</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<!-- <view class='num'><text class='iconfont icon-pintuan'></text>{{item.countPeopleAll}}人正在参与\r\n\t\t\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t\t\t<view class='money skeleton-rect'>最低: ￥<text class='price'>{{item.minPrice}}</text></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view v-if=\"item.quota>0\" class='cutBnt bg_color'>参与砍价</view>\r\n\t\t\t\t\t\t\t<view  v-if=\"item.quota==0\" class='cutBnt bg-color-hui'>已售罄</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<view class='loadingicon acea-row row-center-wrapper' v-if='bargainList.length > 0' style=\"color: #fff;\">\r\n\t\t\t\t\t\t<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>{{loadTitle}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-else class=\"flex-center no_shop\">\r\n\t\t\t\t\t<image src=\"../../../static/images/noShopper.png\" mode=\"aspectFit\" style=\"width: 400rpx;\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- #ifdef MP -->\r\n\t\t\t<!-- <authorize @onLoadFun=\"onLoadFun\" :isAuto=\"isAuto\" :isShowAuth=\"isShowAuth\" @authColse=\"authColse\"></authorize> -->\r\n\t\t\t<!-- #endif -->\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n<script>\r\n\tlet app = getApp();\r\n\timport {\r\n\t\tgetBargainList,\r\n\t\tbargainHeaderApi\r\n\t} from '@/api/activity.js';\r\n\timport {\r\n\t\topenBargainSubscribe\r\n\t} from '@/utils/SubscribeMessage.js';\r\n\timport home from '@/components/home';\r\n\timport countDown from '@/components/countDown';\r\n\timport {\r\n\t\ttoLogin\r\n\t} from '@/libs/login.js';\r\n\timport {\r\n\t\tmapGetters\r\n\t} from \"vuex\";\r\n\t// #ifdef MP\r\n\timport authorize from '@/components/Authorize';\r\n\t// #endif\r\n\timport animationType from '@/utils/animationType.js'\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tcountDown,\r\n\t\t\thome,\r\n\t\t\t// #ifdef MP\r\n\t\t\tauthorize\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tshowSkeleton: true, //骨架屏显示隐藏\r\n\t\t\t\tisNodes: 0, //控制什么时候开始抓取元素节点,只要数值改变就重新抓取\r\n\t\t\t\tbgColor: {\r\n\t\t\t\t\t'bgColor': '#E93323',\r\n\t\t\t\t\t'Color': '#fff',\r\n\t\t\t\t\t'width': '44rpx',\r\n\t\t\t\t\t'timeTxtwidth': '16rpx',\r\n\t\t\t\t\t'isDay': true\r\n\t\t\t\t},\r\n\t\t\t\tbargainList: [],\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tlimit: 10,\r\n\t\t\t\tloading: false,\r\n\t\t\t\tloadend: false,\r\n\t\t\t\tnavH: '',\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false, //是否隐藏授权\r\n\t\t\t\treturnShow: true,\r\n\t\t\t\tloadTitle: '加载更多',\r\n\t\t\t\tbargainSuccessList: [],\r\n\t\t\t\tbargainTotal: 0,\r\n\t\t\t\tindicatorDots: false,\r\n\t\t\t\tautoplay: true,\r\n\t\t\t\ttheme:app.globalData.theme,\r\n\t\t\t\timgHost:'',\r\n\t\t\t\tbackBg:'crmebimage/change/bargain_header/bargain_header1.jpg',\r\n\t\t\t\tnavBgColor:'#e93323'\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: mapGetters(['isLogin', 'uid']),\r\n\t\twatch: {\r\n\t\t\tisLogin: {\r\n\t\t\t\thandler: function(newV, oldV) {\r\n\t\t\t\t\tif (newV) {\r\n\t\t\t\t\t\tthis.getBargainList();\r\n\t\t\t\t\t\tthis.getBargainHeader();\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tdeep: true\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad: function(options) {\r\n\t\t\tlet that = this;\r\n\t\t\tthat.$set(that,'imgHost',that.$Cache.get('imgHost'));\r\n\t\t\tswitch (app.globalData.theme) {\r\n\t\t\t\tcase 'theme1':\r\n\t\t\t\t\tthat.backBg = 'crmebimage/change/bargain_header/bargain_header1.jpg';\r\n\t\t\t\t\tthat.bgColor.bgColor = '#e93323';\r\n\t\t\t\t\tthat.navBgColor = '#e93323';\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 'theme2':\r\n\t\t\t\t\tthat.backBg = 'crmebimage/change/bargain_header/bargain_header2.jpg';\r\n\t\t\t\t\tthat.bgColor.bgColor = '#FE5C2D';\r\n\t\t\t\t\tthat.navBgColor = '#FE5C2D';\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 'theme3':\r\n\t\t\t\t\tthat.backBg = 'crmebimage/change/bargain_header/bargain_header3.jpg';\r\n\t\t\t\t\tthat.bgColor.bgColor = '#42CA4D';\r\n\t\t\t\t\tthat.navBgColor = '#42CA4D';\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 'theme4':\r\n\t\t\t\t\tthat.backBg = 'crmebimage/change/bargain_header/bargain_header4.jpg';\r\n\t\t\t\t\tthat.bgColor.bgColor = '#1DB0FC';\r\n\t\t\t\t\tthat.navBgColor = '#1DB0FC';\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 'theme5':\r\n\t\t\t\t\tthat.backBg = 'crmebimage/change/bargain_header/bargain_header5.jpg';\r\n\t\t\t\t\tthat.bgColor.bgColor = '#FF448F';\r\n\t\t\t\t\tthat.navBgColor = '#FF448F';\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t\tuni.setNavigationBarColor({\r\n\t\t\t\tfrontColor: '#ffffff',\r\n\t\t\t\tbackgroundColor:that.navBgColor,\r\n\t\t\t});\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.isNodes++;\r\n\t\t\t}, 500);\r\n\t\t\tvar pages = getCurrentPages();\r\n\t\t\tthis.returnShow = pages.length === 1 ? false : true;\r\n\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\ttitle: \"砍价列表\"\r\n\t\t\t})\r\n\t\t\tthis.navH = app.globalData.navHeight;\r\n\t\t\tif (this.isLogin) {\r\n\t\t\t\tthis.getBargainList();\r\n\t\t\t\tthis.getBargainHeader();\r\n\t\t\t} else {\r\n\t\t\t\ttoLogin();\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetBargainHeader: function() {\r\n\t\t\t\tbargainHeaderApi().then(res => {\r\n\t\t\t\t\tthis.bargainTotal = res.data.bargainTotal;\r\n\t\t\t\t\tthis.bargainSuccessList = res.data.bargainSuccessList;\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\treturn this.$util.Tips({\r\n\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 授权关闭\r\n\t\t\tauthColse: function(e) {\r\n\t\t\t\tthis.isShowAuth = e\r\n\t\t\t},\r\n\t\t\tgoBack: function() {\r\n\t\t\t\tuni.switchTab({\r\n\t\t\t\t\turl:'/pages/index/index'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tonLoadFun: function(e) {\r\n\t\t\t\tthis.getBargainList();\r\n\t\t\t},\r\n\t\t\topenSubscribe: function(e) {\r\n\t\t\t\tlet page = e;\r\n\t\t\t\t// #ifndef MP\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\tanimationType: animationType.type,\r\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\turl: page\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef MP\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '正在加载',\r\n\t\t\t\t})\r\n\t\t\t\topenBargainSubscribe().then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: page,\r\n\t\t\t\t\t});\r\n\t\t\t\t}).catch((err) => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tgetBargainList: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (that.loadend) return;\r\n\t\t\t\tif (that.loading) return;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tthat.loadTitle = '';\r\n\t\t\t\tthat.bargainList = [{image:'',title:'',minPrice:''}];\r\n\t\t\t\tgetBargainList({\r\n\t\t\t\t\tpage: that.page,\r\n\t\t\t\t\tlimit: that.limit\r\n\t\t\t\t}).then(function(res) {\r\n\t\t\t\t\tthat.bargainList = [];\r\n\t\t\t\t\tlet list = res.data.list;\r\n\t\t\t\t\tlet bargainList = that.$util.SplitArray(list, that.bargainList);\r\n\t\t\t\t\tlet loadend = list.length < that.limit;\r\n\t\t\t\t\tthat.loadend = loadend;\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\tthat.setShare();\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tthat.loadTitle = loadend ? '已全部加载' : '加载更多';\r\n\t\t\t\t\tthat.$set(that, 'bargainList', bargainList);\r\n\t\t\t\t\tthat.$set(that, 'page', that.page + 1);\r\n\t\t\t\t\t\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthat.showSkeleton = false\r\n\t\t\t\t\t}, 1000)\r\n\t\t\t\t}).catch(res => {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.loadTitle = '加载更多';\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tsetShare: function() {\r\n\t\t\t\tthis.$wechat.isWeixin() &&\r\n\t\t\t\t\tthis.$wechat.wechatEvevt([\r\n\t\t\t\t\t\t\"updateAppMessageShareData\",\r\n\t\t\t\t\t\t\"updateTimelineShareData\",\r\n\t\t\t\t\t\t\"onMenuShareAppMessage\",\r\n\t\t\t\t\t\t\"onMenuShareTimeline\"\r\n\t\t\t\t\t], {\r\n\t\t\t\t\t\tdesc: this.bargainList[0].title,\r\n\t\t\t\t\t\ttitle: this.bargainList[0].title,\r\n\t\t\t\t\t\tlink: location.href,\r\n\t\t\t\t\t\timgUrl:this.bargainList[0].image \r\n\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\tconsole.log(err);\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t},\r\n\t\tonReachBottom: function() {\r\n\t\t\tthis.getBargainList();\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\tpage {\r\n\t\t@include main_bg_color(theme);\r\n\t\theight: 100%;\r\n\t}\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n\t.pageInfo{\r\n\t\t/* #ifdef MP || APP-PLUS */\r\n\t\t@include main_bg_color(theme);\r\n\t\t/* #endif */\r\n\t\theight: 100vh;\r\n\t\toverflow: auto;\r\n\t}\r\n     .mr9{\r\n\t\t margin-right: 9rpx;\r\n\t }\r\n\t.swipers {\r\n\t\theight: 100%;\r\n\t\twidth: 76%;\r\n\t\tmargin: auto;\r\n\t\toverflow: hidden;\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #fff;\r\n        image{\r\n\t\t\twidth: 24rpx;\r\n\t\t\theight: 24rpx;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\toverflow: hidden;\r\n\t\t}\r\n\t\tswiper {\r\n\t\t\theight: 100%;\r\n\t\t\twidth: 100%;\r\n\t\t\toverflow: hidden;\r\n\t\t}\r\n\t\t.line1{\r\n\t\t\twidth: 195rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.bargain-list .icon-xiangzuo {\r\n\t\tfont-size: 40rpx;\r\n\t\tcolor: #fff;\r\n\t\tposition: fixed;\r\n\t\tleft: 30rpx;\r\n\t\tz-index: 99;\r\n\t\ttransform: translateY(-20%);\r\n\t}\r\n\r\n\t.bargain-list .header {\r\n\t\t// @include bargain-list-header(theme);\r\n\t\tbabackground-repeat: no-repeat;\r\n\t\tbackground-size: 100% 100%;\r\n\t\twidth: 750rpx;\r\n\t\theight: 420rpx;\r\n        .acea-row{\r\n        \theight: 50rpx;\r\n\t\t\tline-height: 50rpx;\r\n\t\t\tleft: 50rpx;\r\n\t\t\t.nickName{\r\n\t\t\t\twidth: 65rpx;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\twhite-space: nowrap;\r\n\t\t\t}\r\n        }\r\n\t\t.pic {\r\n\t\t\twidth: 478rpx;\r\n\t\t\theight: 50rpx;\r\n\t\t\tmargin: 0 auto;\r\n\t\t}\r\n\r\n\t\t.tit {\r\n\t\t\tcolor: #FFFFFF;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tfont-weight: 400;\r\n\t\t\ttext-align: center;\r\n\t\t\tmargin-top: 304rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.bargain-list .list {\r\n\t\tpadding: 0 30rpx;\r\n\t}\r\n\r\n\t.bargain-list .list .item {\r\n\t\tposition: relative;\r\n\t\theight: 250rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 14rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tpadding: 30rpx 25rpx;\r\n\t}\r\n\r\n\t.bargain-list .list .item .pictrue {\r\n\t\twidth: 190rpx;\r\n\t\theight: 190rpx;\r\n\t}\r\n\r\n\t.bargain-list .list .item .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 14rpx;\r\n\t}\r\n\r\n\t.bargain-list .list .item .text {\r\n\t\twidth: 432rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333333;\r\n\t\t\r\n\t\t.txt{\r\n\t\t\tfont-size: 22rpx;\r\n\t\t\tmargin-left: 4rpx;\r\n\t\t\tcolor: #666666;\r\n\t\t\tline-height: 36rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.bargain-list .list .item .text .name {\r\n\t\twidth: 100%;\r\n\t\theight: 68rpx;\r\n\t\tline-height: 36rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tmargin-bottom: 26rpx;\r\n\t}\r\n\r\n\t.bargain-list .list .item .text .num {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.bargain-list .list .item .text .num .iconfont {\r\n\t\tfont-size: 35rpx;\r\n\t\tmargin-right: 7rpx;\r\n\t}\r\n\r\n\t.bargain-list .list .item .text .money {\r\n\t\tfont-size: 24rpx;\r\n\t\tfont-weight: bold;\r\n\t\t@include price_color(theme);\r\n\t}\r\n\r\n\t.bargain-list .list .item .text .money .price {\r\n\t\tfont-size: 38rpx;\r\n\t}\r\n\r\n\t.bargain-list .list .item .cutBnt {\r\n\t\tposition: absolute;\r\n\t\twidth: 162rpx;\r\n\t\theight: 52rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #fff;\r\n\t\ttext-align: center;\r\n\t\tline-height: 52rpx;\r\n\t\tright: 24rpx;\r\n\t\tbottom: 30rpx;\r\n\t}\r\n\t.bg_color{\r\n\t\t@include linear-gradient(theme);\r\n\t}\r\n\t.bg-color-hui{\r\n\t\tbackground-color: #CDCBCC;\r\n\t}\r\n\t.bargain-list .list .item .cutBnt .iconfont {\r\n\t\tmargin-right: 8rpx;\r\n\t\tfont-size: 30rpx;\r\n\t}\r\n\r\n\t.bargain-list .list .load {\r\n\t\tfont-size: 24rpx;\r\n\t\theight: 85rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 85rpx;\r\n\t}\r\n\t.flex-center{\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t}\r\n\t.no_shop{\r\n\t\tmargin: 0 30rpx 0;\r\n\t\theight: 700rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tbackground-color: #fff;\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294179020\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=1&id=51d94765&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=1&id=51d94765&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294179009\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}