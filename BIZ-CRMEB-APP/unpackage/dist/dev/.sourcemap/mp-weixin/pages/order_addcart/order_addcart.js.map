{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/order_addcart/order_addcart.vue?04cc", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/order_addcart/order_addcart.vue?daae", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/order_addcart/order_addcart.vue?648b", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/order_addcart/order_addcart.vue?22c7", "uni-app:///pages/order_addcart/order_addcart.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/order_addcart/order_addcart.vue?dc31", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/order_addcart/order_addcart.vue?b5d2"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "recommend", "productWindow", "authorize", "data", "cartCount", "goodsHidden", "footerswitch", "hostProduct", "cartList", "valid", "invalid", "isAllSelect", "selectValue", "selectCountPrice", "isAuto", "isShowAuth", "hotScroll", "hotPage", "hotLimit", "loading", "loadend", "loadTitle", "page", "limit", "loadingInvalid", "loadendInvalid", "loadTitleInvalid", "pageInvalid", "limitInvalid", "attr", "cartAttr", "productAttr", "productSelect", "productValue", "productInfo", "attrValue", "attrTxt", "cartId", "product_id", "sysHeight", "canShow", "config<PERSON>pi", "theme", "navH", "homeTop", "currentPage", "selectNavList", "name", "icon", "url", "computed", "onLoad", "console", "that", "onReady", "query", "select", "boundingClientRect", "exec", "onShow", "uni", "methods", "auth<PERSON><PERSON><PERSON>", "reGoCat", "title", "id", "productId", "num", "unique", "then", "success", "catch", "onMyEvent", "reElection", "getGoodsDetails", "mask", "attrName", "attrV<PERSON>ues", "isDel", "type", "ChangeAttr", "DefaultSelect", "value", "attrVal", "indexn", "ChangeCartNum", "iptCartNum", "subDel", "getSelectValueProductId", "subCollect", "subOrder", "getPreOrder", "checkboxAllChange", "setAllSelectValue", "item", "checkboxChange", "arr1", "arr2", "arr3", "inArray", "switchSelect", "cartNum", "success<PERSON>allback", "resolve", "<PERSON><PERSON><PERSON><PERSON>", "i", "validList", "numSub", "numAdd", "stock", "newArr", "ids", "desc", "link", "imgUrl", "configAppMessage", "animationType", "animationDuration", "onReachBottom"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjDA;AAAA;AAAA;AAAA;AAAumB,CAAgB,ioBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACwL3nB;AAOA;AAKA;AACA;AAMA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AArBA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAsBA;AAAA,eACA;EACAC;IACAC;IACAC;IAEAC;EAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;MACA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC,gBACA;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA;IAEA;EACA;EAEAC;EACAC;IACAC;IACA;IAEAC;EAQA;EACAC;IACA;MAAA;MAEA;MACA;MACAC,MACAC,gBACAC;QACA;MACA,GACAC;IAEA;EACA;EACAC;IACA;IACA;MACA;MACA,uBACA,wBACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAlD;QACAC;MACA,GACA;MACA;MACA;MACA;MACA;IACA;IAAA;IACAkD;EACA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;QACA/B;MACA;MACA,IACAqB,gCACArB,6BAEA;QACAgC;MACA;MAEA;QACAC;QACAC;QACAC;QACAC,gDACAf;MACA;MACA,4BACAgB;QACAhB;QACAA;UACAW;UACAM;YACAjB;YACAA;YACAA;YACAA;YACAA;UACA;QACA;MACA,GACAkB;QACA;UACAP;QACA;MACA;IACA;IACAQ;MACA;IACA;IACAC;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MAAA;MACAd;QACAI;QACAW;MACA;MACA;MACAtB;MACAA;MACA;QACAO;QACAP;QACA;QACAA;QACA;QACAA;QACA;UACA;YACAuB;YACAC;YACAZ;YACAa;YACAZ;YACAa;UACA;QACA;QACA;QACA1B;MACA;QACAO;MACA;IACA;IACA;AACA;AACA;AACA;IACAoB;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;QACA;UACAC;UACA;QACA;MACA;MACA;QACA;MACA;MACA;MACA;MACA;QACA,UACA,yBACA,aACA,2BACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA,UACA,yBACA,aACA,2BACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA,UACA,yBACA,aACA,2BACA;QACA;QACA;QACA;QACA,UACA,yBACA,UACA,0BACA;QACA;QACA;QACA;MACA;IACA;IACAC;MACA,uGACAC;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;MACA,kEACArD;MACA;MACA;MACA;MACA;MACA;QACAmC;QACA;UACA;UACA;QACA;MACA;QACAA;QACA;UACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAmB;MACA;IACA;IACAC;MACA;QACA3E;MACA,4BACA;QACAyC;QACAA;QACAA;QACAA;QACAA;MACA,QAEA;QACAW;MACA;IACA;IACAwB;MACA;MACA;MACA;MACA;MACA;QACA;UACA;YACAtB;UACA;QACA;MACA;MAAA;MACA;IACA;IACAuB;MACA;QACA7E;MACA;QACA;QACA;UACA;YACAoD;YACAhB;UACA;QACA;UACA;YACAgB;UACA;QACA;MACA;QACA;UACAA;QACA;MACA;IACA;IACA;IACA0B;MAEA;QACA9E;MACA;QACAyC;MACA;QACA;UACAW;QACA;MACA;IACA;IACA;AACA;AACA;IACA2B;MACA;QACA;UACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;UACA;YACA;cACA;gBACAC;gBACAlF;cACA;gBACAkF;cACA;YACA;cACAA;cACAlF;YACA;YACAyC;UACA;YACAyC;YACAzC;UACA;UACA;QACA;QACAA;QACAA;QACAA;MACA;IACA;IACA0C;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACA;YACA;cACAD;cACAE;YACA;cACAF;YACA;UACA;YACAA;YACAE;UACA;QACA;UACAF;UACAG;QACA;QACA;MACA;MACA;QACAC;UAAA;QAAA;MACA;MACA7C;MACA;MACAA;MACAA;MACAA;IACA;IACA8C;MACA;QACA;UACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;QACA/C;MACA;QACA;UACA;YACAxC,0FACAwF;UACA;QACA;QACAhD;MACA;IACA;EAAA,yDAKA;IACA;IACA;MACA;IACA;IACA;EACA,yDACA;IACA;IACA;MACAyC;MACA;IACA;EACA,uDACA;IACA;IACA;IACA;IACAA;IACA;IACA;MACAA;MACAA;IACA;MACAA;MACAA;IACA;IACA;MACAzC;QACAA;QACAA;QACAA;MACA;IACA;EACA,uDACA;IACA;IACA;IACAyC;IACA;IACA;MACAA;MACAA;MACAA;IACA;MACAA;MACAA;IACA;IACAzC;MACAA;MACAA;MACAA;IACA;EACA,8EACAhB;IACA;IACA;MACAiE;IACA;EACA,0DACA;IACA;IACA;MACAjD;IACA;EACA,gFACAlD;IACA;MACA;QACAoG;MACA;QACA;QACA;QACA;UACAvC;QACA;MACA;IACA;EACA,kFACA;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACAJ;gBACAI;gBACAW;cACA;cACAtB;cACAlD;gBACAmB;gBACAC;gBACAiF;cACA;cACA;gBAAA;kBAAA;kBAAA;oBAAA;sBAAA;wBAAA;0BACAnD;0BACA;0BACAoD;wBAAA;0BAAA;4BAAA;4BAAA;0BAAA;0BAAA;0BAAA,OACA;wBAAA;0BAAAjG;0BACAC;0BACAiG;0BACAC;4BACAA;0BACA;4BACAA;0BACA;0BACAC;4BACAA;0BACA;4BACAA;0BACA,IACAhG;0BACA;4BACA;8BACA;gCACA8F;8BACA;gCACAA;8BACA;8BACAxE;8BACA2E;8BACA;gCACAH;8BACA;gCACAA;8BACA;gCACAA;8BACA;8BACA;gCACAA;gCACA9F;8BACA;gCACA8F;8BACA;4BACA;0BACA;0BACArD;0BACAlD;0BACAkD;0BACAyD;4BAAA;0BAAA;0BACAzD;0BACAA;wBAAA;0BA5CAoD;0BAAA;0BAAA;wBAAA;0BA+CApD;0BACAA;0BACAO;wBAAA;wBAAA;0BAAA;sBAAA;oBAAA;kBAAA;gBAAA,CACA;gBAAA;kBAAA;gBAAA;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA,8DACA;IACA;IACA;IACA;IACA;MACAtC;MACAC;MACAiF;IACA;IACA;MACA;QACA/E;MACA;MACA4B;MACAA;MACAA;MACAA;MACAA;MACA;IACA;MACAA;MACAA;IACA;EAEA,8DACA;IACA;IACA;IACA,0BACAA,cACAA,cACA;MACAA;MACAA;MACAA;IACA;EACA,yDACA;IACA;IACAA;EACA,sDACA;IACA;IACAA;IACA;IACA;IACA;MACA;QACA;UACA;YACA2C;UACA;QACA;UACAF;UACAG;QACA;MACA;QACA;UACAD;QACA;MACA;MACA;IACA;IACA3C;IACA;MACAA;IACA;MACAA;IACA;IACAA;IACAA;EACA,yDACA;IACA;MACA0D;IACA;MACAA;IACA;IACA;MACA1D;QACAW;MACA;MACAX;MACAA;IACA,yBAEA;EACA,wDACA;IAAA;IACA;MACA;IAIA;EACA,4DAEA;IACA;IACA;MACA;QACA2D;QACAhD;QACAiD;QACAC;MACA;MACA7D,mFACA8D;IACA;EACA,uDACA;IACAvD;MACAX;IACA;EACA,0EACA;IACA;EACA,0EAEAA;IACA;MACAW;QACAX;MACA;IACA;MACAW;QACAwD;QACAC;QACApE;MACA;IACA;IACA;EACA,gFACA;IACA;EACA,aACA;EACAqE;IACA;IACA;MACAjE;IACA;IACA;MACAA;IACA;EACA;;;;;;;;;;;;;;;AC/+BA;AAAA;AAAA;AAAA;AAA0qC,CAAgB,gpCAAG,EAAC,C;;;;;;;;;;;ACA9rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/order_addcart/order_addcart.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/order_addcart/order_addcart.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./order_addcart.vue?vue&type=template&id=a5aa7f30&scoped=true&\"\nvar renderjs\nimport script from \"./order_addcart.vue?vue&type=script&lang=js&\"\nexport * from \"./order_addcart.vue?vue&type=script&lang=js&\"\nimport style0 from \"./order_addcart.vue?vue&type=style&index=0&id=a5aa7f30&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"a5aa7f30\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/order_addcart/order_addcart.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_addcart.vue?vue&type=template&id=a5aa7f30&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 =\n    (_vm.cartList.valid.length === 0 && _vm.cartList.invalid.length === 0) ||\n    _vm.cartList.valid.length > 0\n  var g1 = g0\n    ? _vm.cartList.valid.length > 0 || _vm.cartList.invalid.length > 0\n    : null\n  var g2 = _vm.cartList.valid.length > 0 || _vm.cartList.invalid.length > 0\n  var g3 = g2 ? _vm.cartList.invalid.length : null\n  var g4 =\n    g2 && g3 > 0\n      ? _vm.cartList.valid.length === 0 && _vm.cartList.invalid.length > 0\n      : null\n  var g5 =\n    g2 && g3 > 0\n      ? _vm.cartList.invalid.length > 1 || _vm.cartList.valid.length > 0\n      : null\n  var g6 = g2 ? _vm.cartList.invalid.length && _vm.loadend : null\n  var g7 =\n    (_vm.cartList.valid.length == 0 &&\n      _vm.cartList.invalid.length == 0 &&\n      _vm.canShow) ||\n    !_vm.isLogin\n  var g8 = _vm.cartList.valid.length\n  var g9 = g8 > 0 ? _vm.selectValue.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n        g6: g6,\n        g7: g7,\n        g8: g8,\n        g9: g9,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_addcart.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_addcart.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :data-theme=\"theme\">\r\n\t\t<view class=\"cart_nav\" :style='\"height:\"+navH+\"rpx;\"'>\r\n\t\t\t<view class='navbarCon acea-row'>\r\n\t\t\t\t<!-- #ifdef MP -->\r\n\t\t\t\t<view class=\"select_nav flex justify-center align-center\" id=\"home\" :style=\"{ top: homeTop + 'rpx' }\">\r\n\t\t\t\t\t<text class=\"iconfont icon-fanhui2 px-20\" @tap=\"returns\"></text>\r\n\t\t\t\t\t<text class=\"iconfont icon-gengduo5 px-20\" @tap.stop=\"showNav\"></text>\r\n\t\t\t\t\t<text class=\"nav_line\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t<view id=\"home\" class=\"home acea-row row-center-wrapper iconfont icon-shouye4 h5_back\"\r\n\t\t\t\t\t:style=\"{ top: homeTop + 'rpx' }\" @tap=\"returns\">\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<!-- #ifndef APP-PLUS -->\r\n\t\t\t\t<view class=\"nav_title\" :style=\"{ top: homeTop + 'rpx' }\">购物车</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<!-- #ifdef H5 || APP-PLUS -->\r\n\t\t\t\t<view class=\"right_select\" :style=\"{ top: homeTop + 'rpx' }\" @tap=\"showNav\">\r\n\t\t\t\t\t<text class=\"iconfont icon-gengduo2\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"dialog_nav\" :style='\"top:\"+navH+\"rpx;\"' v-show=\"currentPage\">\r\n\t\t\t<view class=\"dialog_nav_item\" v-for=\"(item,index) in selectNavList\" :key=\"index\" @click=\"linkPage(item.url)\">\r\n\t\t\t\t<text class=\"iconfont\" :class=\"item.icon\"></text>\r\n\t\t\t\t<text class=\"pl-20\">{{item.name}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class='shoppingCart copy-data' :style='\"top:\"+navH+\"rpx;\"' @touchstart=\"touchStart\">\r\n\t\t\t<view class='labelNav acea-row row-around'>\r\n\t\t\t\t<view class='item'><text class='iconfont icon-xuanzhong'></text>100%正品保证</view>\r\n\t\t\t\t<view class='item'><text class='iconfont icon-xuanzhong'></text>所有商品精挑细选</view>\r\n\t\t\t\t<view class='item'><text class='iconfont icon-xuanzhong'></text>售后无忧</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"borRadius14 cartBox\">\r\n\t\t\t\t<view\r\n\t\t\t\t\tv-if=\"(cartList.valid.length === 0 && cartList.invalid.length === 0) || (cartList.valid.length > 0)\"\r\n\t\t\t\t\tclass='nav acea-row row-between-wrapper'>\r\n\t\t\t\t\t<view>购物数量 <text class='num font_color'>{{cartCount}}</text></view>\r\n\t\t\t\t\t<view v-if=\"cartList.valid.length > 0 || cartList.invalid.length > 0\"\r\n\t\t\t\t\t\tclass='administrate acea-row row-center-wrapper' @click='manage'>{{ footerswitch ? '管理' : '取消'}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-if=\"cartList.valid.length > 0 || cartList.invalid.length > 0\" class=\"pad30\">\r\n\t\t\t\t\t<view class='list'>\r\n\t\t\t\t\t\t<checkbox-group @change=\"checkboxChange\">\r\n\t\t\t\t\t\t\t<block v-for=\"(item,index) in cartList.valid\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<view class='item acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t\t\t<!-- #ifndef MP -->\r\n\t\t\t\t\t\t\t\t\t<checkbox :value=\"(item.id).toString()\" :checked=\"item.checked\"\r\n\t\t\t\t\t\t\t\t\t\t:disabled=\"!item.attrStatus && footerswitch\" style=\"margin-right: 10rpx;\" />\r\n\t\t\t\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t\t\t\t<!-- #ifdef MP -->\r\n\t\t\t\t\t\t\t\t\t<checkbox :value=\"item.id\" :checked=\"item.checked\"\r\n\t\t\t\t\t\t\t\t\t\t:disabled=\"!item.attrStatus && footerswitch\" />\r\n\t\t\t\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t\t\t\t<navigator :url='\"/pages/goods_details/index?id=\"+item.productId' hover-class='none'\r\n\t\t\t\t\t\t\t\t\t\tclass='picTxt acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t\t\t\t<view class='pictrue'>\r\n\t\t\t\t\t\t\t\t\t\t\t<image :src='item.image'></image>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class='text'>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class='line1' :class=\"item.attrStatus?'':'reColor'\">{{item.storeName}}\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class='infor line1' v-if=\"item.suk\">属性：{{item.suk}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class='money mt-28' v-if=\"item.attrStatus\">￥{{item.vipPrice ? item.vipPrice :item.price}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"reElection acea-row row-between-wrapper\" v-else>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"title\">请重新选择商品规格</view>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"reBnt cart-color acea-row row-center-wrapper\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\************=\"reElection(item)\">重选</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class='carnum acea-row row-center-wrapper' v-if=\"item.attrStatus\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"reduce\" :class=\"item.numSub ? 'on' : ''\"\r\n\t\t\t\t\t\t\t\t\t\t\t\************='subCart(index)'>-</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class='num'>{{item.cartNum}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"plus\" :class=\"item.numAdd ? 'on' : ''\"\r\n\t\t\t\t\t\t\t\t\t\t\t\************='addCart(index)'>+</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</navigator>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"cartList.invalid.length > 0\" class='invalidGoods borRadius14'\r\n\t\t\t\t\t\t:style=\"cartList.valid.length===0 && cartList.invalid.length > 0 ? 'position: relative;z-index: 111;top: -120rpx;':'position: static;'\">\r\n\t\t\t\t\t\t<view class='goodsNav acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t<view v-if=\"cartList.invalid.length > 1 || cartList.valid.length > 0\" @click='goodsOpen'>\r\n\t\t\t\t\t\t\t\t<text class='iconfont'\r\n\t\t\t\t\t\t\t\t\t:class='goodsHidden==true?\"icon-xiangxia\":\"icon-xiangshang\"'></text>失效商品\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view v-else>\r\n\t\t\t\t\t\t\t\t失效商品\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class='del' @click='unsetCart'><text class='iconfont icon-shanchu1'></text>清空</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='goodsList' :hidden='goodsHidden'>\r\n\t\t\t\t\t\t\t<block v-for=\"(item,index) in cartList.invalid\" :key='index'>\r\n\t\t\t\t\t\t\t\t<view class='item acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t\t\t<view class='invalid'>失效</view>\r\n\t\t\t\t\t\t\t\t\t<view class='picTxt acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t\t\t\t<view class='pictrue'>\r\n\t\t\t\t\t\t\t\t\t\t\t<image :src='item.image'></image>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class='text acea-row row-column-between'>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class='line1 name'>{{item.storeName}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class='infor line1' v-if=\"item.suk\">属性：{{item.suk}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class='acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<view class='end'>该商品已失效</view>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t\t<view style=\"height:240rpx;\"></view>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<!-- #ifdef MP || APP-PLUS -->\r\n\t\t\t\t\t<view style=\"height:120rpx;\"></view>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<view class='loadingicon acea-row row-center-wrapper' v-if=\"cartList.invalid.length&&loadend\">\r\n\t\t\t\t\t\t<text class='loading iconfont icon-jiazai'\r\n\t\t\t\t\t\t\t:hidden='loadingInvalid==false'></text>{{loadTitleInvalid}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='noCart' v-if=\"(cartList.valid.length == 0 && cartList.invalid.length == 0 && canShow) || !isLogin\">\r\n\t\t\t\t\t<view class='pictrue'>\r\n\t\t\t\t\t\t<image src='../../static/images/noCart.png'></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<recommend :hostProduct='hostProduct'></recommend>\r\n\t\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t\t<view style=\"height:120rpx;\"></view>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- <view style=\"height:260rpx;\"></view> -->\r\n\t\t<view class='footer acea-row row-between-wrapper' v-if=\"cartList.valid.length > 0\">\r\n\t\t\t<view>\r\n\t\t\t\t<checkbox-group @change=\"checkboxAllChange\">\r\n\t\t\t\t\t<checkbox value=\"all\" :checked=\"!!isAllSelect\" />\r\n\t\t\t\t\t<text class='checkAll'>全选({{selectValue.length}})</text>\r\n\t\t\t\t</checkbox-group>\r\n\t\t\t</view>\r\n\t\t\t<view class='money acea-row row-middle' v-if=\"footerswitch==true\">\r\n\t\t\t\t<text class='price-color'>￥{{selectCountPrice}}</text>\r\n\t\t\t\t<form @submit=\"subOrder\" report-submit='true'>\r\n\t\t\t\t\t<button class='placeOrder bg_color' formType=\"submit\">立即下单</button>\r\n\t\t\t\t</form>\r\n\t\t\t</view>\r\n\t\t\t<view class='button acea-row row-middle' v-else>\r\n\t\t\t\t<form @submit=\"subCollect\" report-submit='true'>\r\n\t\t\t\t\t<button class='btn_cart_color' formType=\"submit\">收藏</button>\r\n\t\t\t\t</form>\r\n\t\t\t\t<form @submit=\"subDel\" report-submit='true'>\r\n\t\t\t\t\t<button class='bnt' formType=\"submit\">删除</button>\r\n\t\t\t\t</form>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<productWindow :attr=\"attr\" :isShow='1' :iSplus='1' :iScart='1' @myevent=\"onMyEvent\" @ChangeAttr=\"ChangeAttr\"\r\n\t\t\t@ChangeCartNum=\"ChangeCartNum\" @attrVal=\"attrVal\" @iptCartNum=\"iptCartNum\" @goCat=\"reGoCat\"\r\n\t\t\tid='product-window'></productWindow>\r\n\t\t<view class=\"uni-p-b-96\"></view>\r\n\t\t<view class=\"uni-p-b-98\"></view>\r\n\t\t<!-- #ifdef MP -->\r\n\t\t<!-- <authorize :isAuto=\"isAuto\" :isShowAuth=\"isShowAuth\" @authColse=\"authColse\"></authorize> -->\r\n\t\t<!-- #endif -->\r\n\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// #ifdef APP-PLUS\r\n\tlet sysHeight = uni.getSystemInfoSync().statusBarHeight + 'px';\r\n\t// #endif\r\n\t// #ifndef APP-PLUS\r\n\tlet sysHeight = 0\r\n\t// #endif\r\n\timport {\r\n\t\tgetCartList,\r\n\t\tgetCartCounts,\r\n\t\tchangeCartNum,\r\n\t\tcartDel,\r\n\t\tgetResetCart\r\n\t} from '@/api/order.js';\r\n\timport {\r\n\t\tgetProductHot,\r\n\t\tcollectAll,\r\n\t\tgetProductDetail\r\n\t} from '@/api/store.js';\r\n\timport {getShare} from '@/api/public.js';\r\n\timport {mapGetters} from \"vuex\";\r\n\timport recommend from '@/components/recommend';\r\n\timport productWindow from '@/components/productWindow';\r\n\t// #ifdef MP\r\n\timport authorize from '@/components/Authorize';\r\n\t// #endif\r\n\timport animationType from '@/utils/animationType.js'\r\n\tlet app = getApp();\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\trecommend,\r\n\t\t\tproductWindow,\r\n\t\t\t// #ifdef MP\r\n\t\t\tauthorize\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcartCount: 0,\r\n\t\t\t\tgoodsHidden: false,\r\n\t\t\t\tfooterswitch: true,\r\n\t\t\t\thostProduct: [],\r\n\t\t\t\tcartList: {\r\n\t\t\t\t\tvalid: [],\r\n\t\t\t\t\tinvalid: []\r\n\t\t\t\t},\r\n\t\t\t\tisAllSelect: false, //全选\r\n\t\t\t\tselectValue: [], //选中的数据\r\n\t\t\t\tselectCountPrice: 0.00,\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false, //是否隐藏授权\r\n\t\t\t\thotScroll: false,\r\n\t\t\t\thotPage: 1,\r\n\t\t\t\thotLimit: 10,\r\n\t\t\t\tloading: false,\r\n\t\t\t\tloadend: false,\r\n\t\t\t\tloadTitle: '加载更多', //提示语\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tlimit: 20,\r\n\t\t\t\tloadingInvalid: false,\r\n\t\t\t\tloadendInvalid: false,\r\n\t\t\t\tloadTitleInvalid: '加载更多', //提示语\r\n\t\t\t\tpageInvalid: 1,\r\n\t\t\t\tlimitInvalid: 20,\r\n\t\t\t\tattr: {\r\n\t\t\t\t\tcartAttr: false,\r\n\t\t\t\t\tproductAttr: [],\r\n\t\t\t\t\tproductSelect: {}\r\n\t\t\t\t},\r\n\t\t\t\tproductValue: [], //系统属性\r\n\t\t\t\tproductInfo: {},\r\n\t\t\t\tattrValue: '', //已选属性\r\n\t\t\t\tattrTxt: '请选择', //属性页面提示\r\n\t\t\t\tcartId: 0,\r\n\t\t\t\tproduct_id: 0,\r\n\t\t\t\tsysHeight: sysHeight,\r\n\t\t\t\tcanShow: false,\r\n\t\t\t\tconfigApi: {}, //分享类容配置\r\n\t\t\t\ttheme:app.globalData.theme,\r\n\t\t\t\tnavH:\"\",\r\n\t\t\t\thomeTop: 20,\r\n\t\t\t\tcurrentPage:false,\r\n\t\t\t\tselectNavList:[\r\n\t\t\t\t\t{name:'首页',icon:'icon-shouye8',url:'/pages/index/index'},\r\n\t\t\t\t\t{name:'搜索',icon:'icon-sousuo6',url:'/pages/goods_search/index'},\r\n\t\t\t\t\t{name:'我的收藏',icon:'icon-shoucang3',url:'/pages/users/user_goods_collection/index'},\r\n\t\t\t\t\t{name:'个人中心',icon:'icon-gerenzhongxin1',url:'/pages/user/index'},\r\n\t\t\t\t]\r\n\t\t\t};\r\n\t\t},\r\n\t\t\r\n\t\tcomputed: mapGetters(['isLogin']),\r\n\t\tonLoad: function(options) {\r\n\t\t\tconsole.log(app.globalData)\r\n\t\t\tlet that = this;\r\n\t\t\t// #ifdef MP\r\n\t\t\tthat.navH = app.globalData.navHeight;\r\n\t\t\t// #endif\r\n\t\t\t// #ifndef MP\r\n\t\t\tthat.navH = 96;\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef H5\r\n\t\t\tthat.shareApi();\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tonReady() {\r\n\t\t\tthis.$nextTick(function() {\r\n\t\t\t\t// #ifdef MP\r\n\t\t\t\tconst menuButton = uni.getMenuButtonBoundingClientRect();\r\n\t\t\t\tconst query = uni.createSelectorQuery().in(this);\r\n\t\t\t\tquery\r\n\t\t\t\t\t.select('#home')\r\n\t\t\t\t\t.boundingClientRect(data => {\r\n\t\t\t\t\t\tthis.homeTop = menuButton.top * 2 + menuButton.height - data.height;\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.exec();\r\n\t\t\t\t// #endif\r\n\t\t\t});\r\n\t\t},\r\n\t\tonShow: function() {\r\n\t\t\tthis.canShow = false\r\n\t\t\tif (this.isLogin == true) {\r\n\t\t\t\tthis.hotPage = 1;\r\n\t\t\t\tthis.hostProduct = [],\r\n\t\t\t\t\tthis.hotScroll = false,\r\n\t\t\t\t\tthis.loadend = false;\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.cartList.valid = [];\r\n\t\t\t\tthis.getCartList();\r\n\t\t\t\tthis.loadendInvalid = false;\r\n\t\t\t\tthis.pageInvalid = 1;\r\n\t\t\t\tthis.cartList.invalid = [];\r\n\t\t\t\tthis.getInvalidList();\r\n\t\t\t\t//this.getCartNum();\r\n\t\t\t\tthis.footerswitch = true;\r\n\t\t\t\tthis.hotScroll = false;\r\n\t\t\t\tthis.hotPage = 1;\r\n\t\t\t\tthis.hotLimit = 10;\r\n\t\t\t\tthis.cartList = {\r\n\t\t\t\t\t\tvalid: [],\r\n\t\t\t\t\t\tinvalid: []\r\n\t\t\t\t\t},\r\n\t\t\t\t\tthis.isAllSelect = false; //全选\r\n\t\t\t\tthis.selectValue = []; //选中的数据\r\n\t\t\t\tthis.selectCountPrice = 0.00;\r\n\t\t\t\tthis.cartCount = 0;\r\n\t\t\t\tthis.isShowAuth = false;\r\n\t\t\t};\r\n\t\t\tuni.showTabBar();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 授权关闭\r\n\t\t\tauthColse: function(e) {\r\n\t\t\t\tthis.isShowAuth = e;\r\n\t\t\t},\r\n\t\t\t// 修改购物车\r\n\t\t\treGoCat: function() {\r\n\t\t\t\tlet that = this,\r\n\t\t\t\t\tproductSelect = that.productValue[this.attrValue];\r\n\t\t\t\t//如果有属性,没有选择,提示用户选择\r\n\t\t\t\tif (\r\n\t\t\t\t\tthat.attr.productAttr.length &&\r\n\t\t\t\t\tproductSelect === undefined\r\n\t\t\t\t)\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: \"产品库存不足，请选择其它\"\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\tlet q = {\r\n\t\t\t\t\tid: that.cartId,\r\n\t\t\t\t\tproductId: that.product_id,\r\n\t\t\t\t\tnum: that.attr.productSelect.cart_num,\r\n\t\t\t\t\tunique: that.attr.productSelect !== undefined ?\r\n\t\t\t\t\t\tthat.attr.productSelect.unique : that.productInfo.id\r\n\t\t\t\t};\r\n\t\t\t\tgetResetCart(q)\r\n\t\t\t\t\t.then(function(res) {\r\n\t\t\t\t\t\tthat.attr.cartAttr = false;\r\n\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: \"添加购物车成功\",\r\n\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\tthat.loadend = false;\r\n\t\t\t\t\t\t\t\tthat.page = 1;\r\n\t\t\t\t\t\t\t\tthat.cartList.valid = [];\r\n\t\t\t\t\t\t\t\tthat.getCartList();\r\n\t\t\t\t\t\t\t\tthat.getCartNum();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(res => {\r\n\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: res\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tonMyEvent: function() {\r\n\t\t\t\tthis.$set(this.attr, 'cartAttr', false);\r\n\t\t\t},\r\n\t\t\treElection: function(item) {\r\n\t\t\t\tthis.getGoodsDetails(item)\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 获取产品详情\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tgetGoodsDetails: function(item) {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '加载中',\r\n\t\t\t\t\tmask: true\r\n\t\t\t\t});\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthat.cartId = item.id;\r\n\t\t\t\tthat.product_id = item.productId;\r\n\t\t\t\tgetProductDetail(item.productId).then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthat.attr.cartAttr = true;\r\n\t\t\t\t\tlet productInfo = res.data.productInfo;\r\n\t\t\t\t\tthat.$set(that, 'productInfo', productInfo);\r\n\t\t\t\t\t// that.$set(that.attr, 'productAttr', res.data.productAttr);\r\n\t\t\t\t\tthat.$set(that, 'productValue', res.data.productValue);\r\n\t\t\t\t\tlet productAttr = res.data.productAttr.map(item => {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tattrName : item.attrName,\r\n\t\t\t\t\t\tattrValues: item.attrValues.split(','),\r\n\t\t\t\t\t\tid:item.id,\r\n\t\t\t\t\t\tisDel:item.isDel,\r\n\t\t\t\t\t\tproductId:item.productId,\r\n\t\t\t\t\t\ttype:item.type\r\n\t\t\t\t\t }\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.$set(that.attr,'productAttr',productAttr);\r\n\t\t\t\t\tthat.DefaultSelect();\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 属性变动赋值\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tChangeAttr: function(res) {\r\n\t\t\t\tlet productSelect = this.productValue[res];\r\n\t\t\t\tif (productSelect && productSelect.stock > 0) {\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"image\", productSelect.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"price\", productSelect.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"stock\", productSelect.stock);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"unique\", productSelect.id);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", res);\r\n\t\t\t\t\tthis.$set(this, \"attrTxt\", \"已选择\");\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"image\", this.productInfo.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"price\", this.productInfo.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"stock\", 0);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"unique\", this.productInfo.id);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 0);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", \"\");\r\n\t\t\t\t\tthis.$set(this, \"attrTxt\", \"请选择\");\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 默认选中属性\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tDefaultSelect: function() {\r\n\t\t\t\tlet productAttr = this.attr.productAttr;\r\n\t\t\t\tlet value = [];\r\n\t\t\t\tfor (let key in this.productValue) {\r\n\t\t\t\t\tif (this.productValue[key].stock > 0) {\r\n\t\t\t\t\t\tvalue = this.attr.productAttr.length ? key.split(\",\") : [];\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tfor (let i = 0; i < productAttr.length; i++) {\r\n\t\t\t\t\tthis.$set(productAttr[i], \"index\", value[i]);\r\n\t\t\t\t}\r\n\t\t\t\t//sort();排序函数:数字-英文-汉字；\r\n\t\t\t\tlet productSelect = this.productValue[value.sort().join(\",\")];\r\n\t\t\t\tif (productSelect && productAttr.length) {\r\n\t\t\t\t\tthis.$set(\r\n\t\t\t\t\t\tthis.attr.productSelect,\r\n\t\t\t\t\t\t\"storeName\",\r\n\t\t\t\t\t\tthis.productInfo.storeName\r\n\t\t\t\t\t);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"image\", productSelect.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"price\", productSelect.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"stock\", productSelect.stock);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"unique\", productSelect.id);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", value.sort().join(\",\"));\r\n\t\t\t\t\tthis.$set(this, \"attrTxt\", \"已选择\");\r\n\t\t\t\t} else if (!productSelect && productAttr.length) {\r\n\t\t\t\t\tthis.$set(\r\n\t\t\t\t\t\tthis.attr.productSelect,\r\n\t\t\t\t\t\t\"storeName\",\r\n\t\t\t\t\t\tthis.productInfo.storeName\r\n\t\t\t\t\t);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"image\", this.productInfo.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"price\", this.productInfo.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"stock\", 0);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"unique\", this.productInfo.id);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 0);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", \"\");\r\n\t\t\t\t\tthis.$set(this, \"attrTxt\", \"请选择\");\r\n\t\t\t\t} else if (!productSelect && !productAttr.length) {\r\n\t\t\t\t\tthis.$set(\r\n\t\t\t\t\t\tthis.attr.productSelect,\r\n\t\t\t\t\t\t\"storeName\",\r\n\t\t\t\t\t\tthis.productInfo.storeName\r\n\t\t\t\t\t);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"image\", this.productInfo.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"price\", this.productInfo.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"stock\", this.productInfo.stock);\r\n\t\t\t\t\tthis.$set(\r\n\t\t\t\t\t\tthis.attr.productSelect,\r\n\t\t\t\t\t\t\"unique\",\r\n\t\t\t\t\t\tthis.productInfo.id || \"\"\r\n\t\t\t\t\t);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", \"\");\r\n\t\t\t\t\tthis.$set(this, \"attrTxt\", \"请选择\");\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tattrVal(val) {\r\n\t\t\t\tthis.$set(this.attr.productAttr[val.indexw], 'index', this.attr.productAttr[val.indexw].attrValues[val\r\n\t\t\t\t\t.indexn]);\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 购物车数量加和数量减\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tChangeCartNum: function(changeValue) {\r\n\t\t\t\t//changeValue:是否 加|减\r\n\t\t\t\t//获取当前变动属性\r\n\t\t\t\tlet productSelect = this.productValue[this.attrValue];\r\n\t\t\t\t//如果没有属性,赋值给商品默认库存\r\n\t\t\t\tif (productSelect === undefined && !this.attr.productAttr.length)\r\n\t\t\t\t\tproductSelect = this.attr.productSelect;\r\n\t\t\t\t//无属性值即库存为0；不存在加减；\r\n\t\t\t\tif (productSelect === undefined) return;\r\n\t\t\t\tlet stock = productSelect.stock || 0;\r\n\t\t\t\tlet num = this.attr.productSelect;\r\n\t\t\t\tif (changeValue) {\r\n\t\t\t\t\tnum.cart_num++;\r\n\t\t\t\t\tif (num.cart_num > stock) {\r\n\t\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", stock ? stock : 1);\r\n\t\t\t\t\t\tthis.$set(this, \"cart_num\", stock ? stock : 1);\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tnum.cart_num--;\r\n\t\t\t\t\tif (num.cart_num < 1) {\r\n\t\t\t\t\t\tthis.$set(this.attr.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\t\tthis.$set(this, \"cart_num\", 1);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 购物车手动填写\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tiptCartNum: function(e) {\r\n\t\t\t\tthis.$set(this.attr.productSelect, 'cart_num', e);\r\n\t\t\t},\r\n\t\t\tsubDel: function(event) {\r\n\t\t\t\tlet that = this,\r\n\t\t\t\t\tselectValue = that.selectValue;\r\n\t\t\t\tif (selectValue.length > 0)\r\n\t\t\t\t\tcartDel(selectValue).then(res => {\r\n\t\t\t\t\t\tthat.loadend = false;\r\n\t\t\t\t\t\tthat.page = 1;\r\n\t\t\t\t\t\tthat.cartList.valid = [];\r\n\t\t\t\t\t\tthat.getCartList();\r\n\t\t\t\t\t\tthat.getCartNum();\r\n\t\t\t\t\t});\r\n\t\t\t\telse\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: '请选择产品'\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetSelectValueProductId: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet validList = that.cartList.valid;\r\n\t\t\t\tlet selectValue = that.selectValue;\r\n\t\t\t\tlet productId = [];\r\n\t\t\t\tif (selectValue.length > 0) {\r\n\t\t\t\t\tfor (let index in validList) {\r\n\t\t\t\t\t\tif (that.inArray(validList[index].id, selectValue)) {\r\n\t\t\t\t\t\t\tproductId.push(validList[index].productId);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t};\r\n\t\t\t\treturn productId;\r\n\t\t\t},\r\n\t\t\tsubCollect: function(event) {\r\n\t\t\t\tlet that = this,\r\n\t\t\t\t\tselectValue = that.selectValue;\r\n\t\t\t\tif (selectValue.length > 0) {\r\n\t\t\t\t\tlet selectValueProductId = that.getSelectValueProductId();\r\n\t\t\t\t\tcollectAll(that.getSelectValueProductId()).then(res => {\r\n\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: '收藏成功',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: '请选择产品'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 立即下单\r\n\t\t\tsubOrder: function(event) {\r\n\r\n\t\t\t\tlet that = this,\r\n\t\t\t\t\tselectValue = that.selectValue;\r\n\t\t\t\tif (selectValue.length > 0) {\r\n\t\t\t\t\tthat.getPreOrder();\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: '请选择产品'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 预下单\r\n\t\t\t */\r\n\t\t\tgetPreOrder: function() {\r\n\t\t\t\tlet shoppingCartId = this.selectValue.map(item => {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\"shoppingCartId\": Number(item)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tthis.$Order.getPreOrder(\"shoppingCart\", shoppingCartId);\r\n\t\t\t},\r\n\t\t\tcheckboxAllChange: function(event) {\r\n\t\t\t\tlet value = event.detail.value;\r\n\t\t\t\tif (value.length > 0) {\r\n\t\t\t\t\tthis.setAllSelectValue(1)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.setAllSelectValue(0)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsetAllSelectValue: function(status) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet selectValue = [];\r\n\t\t\t\tlet valid = that.cartList.valid;\r\n\t\t\t\tif (valid.length > 0) {\r\n\t\t\t\t\tlet newValid = valid.map(item => {\r\n\t\t\t\t\t\tif (status) {\r\n\t\t\t\t\t\t\tif (that.footerswitch) {\r\n\t\t\t\t\t\t\t\tif (item.attrStatus) {\r\n\t\t\t\t\t\t\t\t\titem.checked = true;\r\n\t\t\t\t\t\t\t\t\tselectValue.push(item.id);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\titem.checked = false;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\titem.checked = true;\r\n\t\t\t\t\t\t\t\tselectValue.push(item.id);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthat.isAllSelect = true;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\titem.checked = false;\r\n\t\t\t\t\t\t\tthat.isAllSelect = false;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn item;\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthat.$set(that.cartList, 'valid', newValid);\r\n\t\t\t\t\tthat.selectValue = selectValue;\r\n\t\t\t\t\tthat.switchSelect();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tcheckboxChange: function(event) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet value = event.detail.value;\r\n\t\t\t\tlet valid = that.cartList.valid;\r\n\t\t\t\tlet arr1 = [];\r\n\t\t\t\tlet arr2 = [];\r\n\t\t\t\tlet arr3 = [];\r\n\t\t\t\tlet newValid = valid.map(item => {\r\n\t\t\t\t\tif (that.inArray(item.id, value)) {\r\n\t\t\t\t\t\tif (that.footerswitch) {\r\n\t\t\t\t\t\t\tif (item.attrStatus) {\r\n\t\t\t\t\t\t\t\titem.checked = true;\r\n\t\t\t\t\t\t\t\tarr1.push(item);\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\titem.checked = false;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\titem.checked = true;\r\n\t\t\t\t\t\t\tarr1.push(item);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\titem.checked = false;\r\n\t\t\t\t\t\tarr2.push(item);\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn item;\r\n\t\t\t\t});\r\n\t\t\t\tif (that.footerswitch) {\r\n\t\t\t\t\tarr3 = arr2.filter(item => !item.attrStatus);\r\n\t\t\t\t}\r\n\t\t\t\tthat.$set(that.cartList, 'valid', newValid);\r\n\t\t\t\t// let newArr = that.cartList.valid.filter(item => item.attrStatus);\r\n\t\t\t\tthat.isAllSelect = newValid.length === arr1.length + arr3.length;\r\n\t\t\t\tthat.selectValue = value;\r\n\t\t\t\tthat.switchSelect();\r\n\t\t\t},\r\n\t\t\tinArray: function(search, array) {\r\n\t\t\t\tfor (let i in array) {\r\n\t\t\t\t\tif (array[i] == search) {\r\n\t\t\t\t\t\treturn true;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn false;\r\n\t\t\t},\r\n\t\t\tswitchSelect: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet validList = that.cartList.valid;\r\n\t\t\t\tlet selectValue = that.selectValue;\r\n\t\t\t\tlet selectCountPrice = 0.00;\r\n\t\t\t\tif (selectValue.length < 1) {\r\n\t\t\t\t\tthat.selectCountPrice = selectCountPrice;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tfor (let index in validList) {\r\n\t\t\t\t\t\tif (that.inArray(validList[index].id, selectValue)) {\r\n\t\t\t\t\t\t\tselectCountPrice = that.$util.$h.Add(selectCountPrice, that.$util.$h.Mul(validList[index]\r\n\t\t\t\t\t\t\t\t.cartNum, validList[index].vipPrice ? validList[index].vipPrice : validList[index].price))\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.selectCountPrice = selectCountPrice;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 购物车手动填写\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tiptCartNum: function(index) {\r\n\t\t\t\tlet item = this.cartList.valid[index];\r\n\t\t\t\tif (item.cartNum) {\r\n\t\t\t\t\tthis.setCartNum(item.id, item.cartNum);\r\n\t\t\t\t}\r\n\t\t\t\tthis.switchSelect();\r\n\t\t\t},\r\n\t\t\tblurInput: function(index) {\r\n\t\t\t\tlet item = this.cartList.valid[index];\r\n\t\t\t\tif (!item.cartNum) {\r\n\t\t\t\t\titem.cartNum = 1;\r\n\t\t\t\t\tthis.$set(this.cartList, 'valid', this.cartList.valid)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsubCart: function(index) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet status = false;\r\n\t\t\t\tlet item = that.cartList.valid[index];\r\n\t\t\t\titem.cartNum = Number(item.cartNum) - 1;\r\n\t\t\t\tif (item.cartNum < 1) status = true;\r\n\t\t\t\tif (item.cartNum <= 1) {\r\n\t\t\t\t\titem.cartNum = 1;\r\n\t\t\t\t\titem.numSub = true;\r\n\t\t\t\t} else {\r\n\t\t\t\t\titem.numSub = false;\r\n\t\t\t\t\titem.numAdd = false;\r\n\t\t\t\t}\r\n\t\t\t\tif (false == status) {\r\n\t\t\t\t\tthat.setCartNum(item.id, item.cartNum, function(data) {\r\n\t\t\t\t\t\tthat.cartList.valid[index] = item;\r\n\t\t\t\t\t\tthat.switchSelect();\r\n\t\t\t\t\t\tthat.getCartNum();\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\taddCart: function(index) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet item = that.cartList.valid[index];\r\n\t\t\t\titem.cartNum = Number(item.cartNum) + 1;\r\n\t\t\t\tlet productInfo = item;\r\n\t\t\t\tif (item.cartNum >= item.stock) {\r\n\t\t\t\t\titem.cartNum = item.stock;\r\n\t\t\t\t\titem.numAdd = true;\r\n\t\t\t\t\titem.numSub = false;\r\n\t\t\t\t} else {\r\n\t\t\t\t\titem.numAdd = false;\r\n\t\t\t\t\titem.numSub = false;\r\n\t\t\t\t}\r\n\t\t\t\tthat.setCartNum(item.id, item.cartNum, function(data) {\r\n\t\t\t\t\tthat.cartList.valid[index] = item;\r\n\t\t\t\t\tthat.switchSelect();\r\n\t\t\t\t\tthat.getCartNum();\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tsetCartNum(cartId, cartNum, successCallback) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tchangeCartNum(cartId, cartNum).then(res => {\r\n\t\t\t\t\tsuccessCallback && successCallback(res.data);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetCartNum: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetCartCounts(true, 'sum').then(res => {\r\n\t\t\t\t\tthat.cartCount = res.data.count;\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetCartData(data) {\r\n\t\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\t\tgetCartList(data).then((res) => {\r\n\t\t\t\t\t\tresolve(res.data);\r\n\t\t\t\t\t}).catch(function(err) {\r\n\t\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\t\tthis.canShow = true;\r\n\t\t\t\t\t\tthis.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t})\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tasync getCartList() {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '加载中',\r\n\t\t\t\t\tmask: true\r\n\t\t\t\t});\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tpage: that.page,\r\n\t\t\t\t\tlimit: that.limit,\r\n\t\t\t\t\tisValid: true\r\n\t\t\t\t}\r\n\t\t\t\tgetCartCounts(true, 'sum').then(async c => {\r\n\t\t\t\t\tthat.cartCount = c.data.count;\r\n\t\t\t\t\tif (c.data.count === 0) that.getHostProduct();\r\n\t\t\t\t\tfor (let i = 0; i < Math.ceil(that.cartCount / that.limit); i++) {\r\n\t\t\t\t\t\tlet cartList = await this.getCartData(data);\r\n\t\t\t\t\t\tlet valid = cartList.list;\r\n\t\t\t\t\t\tlet validList = that.$util.SplitArray(valid, that.cartList.valid);\r\n\t\t\t\t\t\tlet numSub = [{\r\n\t\t\t\t\t\t\tnumSub: true\r\n\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\tnumSub: false\r\n\t\t\t\t\t\t}];\r\n\t\t\t\t\t\tlet numAdd = [{\r\n\t\t\t\t\t\t\t\tnumAdd: true\r\n\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\tnumAdd: false\r\n\t\t\t\t\t\t\t}],\r\n\t\t\t\t\t\t\tselectValue = [];\r\n\t\t\t\t\t\tif (validList.length > 0) {\r\n\t\t\t\t\t\t\tfor (let index in validList) {\r\n\t\t\t\t\t\t\t\tif (validList[index].cartNum == 1) {\r\n\t\t\t\t\t\t\t\t\tvalidList[index].numSub = true;\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tvalidList[index].numSub = false;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tlet productInfo = validList[index];\r\n\t\t\t\t\t\t\t\tlet stock = validList[index].stock ? validList[index].stock : 0;\r\n\t\t\t\t\t\t\t\tif (validList[index].cartNum == stock) {\r\n\t\t\t\t\t\t\t\t\tvalidList[index].numAdd = true;\r\n\t\t\t\t\t\t\t\t} else if (validList[index].cartNum == validList[index].stock) {\r\n\t\t\t\t\t\t\t\t\tvalidList[index].numAdd = true;\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tvalidList[index].numAdd = false;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tif (validList[index].attrStatus) {\r\n\t\t\t\t\t\t\t\t\tvalidList[index].checked = true;\r\n\t\t\t\t\t\t\t\t\tselectValue.push(validList[index].id);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tvalidList[index].checked = false;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthat.$set(that.cartList, 'valid', validList);\r\n\t\t\t\t\t\tdata.page +=1;\r\n\t\t\t\t\t\tthat.selectValue = selectValue;\r\n\t\t\t\t\t\tlet newArr = validList.filter(item => item.attrStatus);\r\n\t\t\t\t\t\tthat.isAllSelect = newArr.length == selectValue.length && newArr.length;\r\n\t\t\t\t\t\tthat.switchSelect();\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.canShow = true;\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetInvalidList: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (this.loadendInvalid) return false;\r\n\t\t\t\tif (this.loadingInvalid) return false;\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tpage: that.pageInvalid,\r\n\t\t\t\t\tlimit: that.limitInvalid,\r\n\t\t\t\t\tisValid: false\r\n\t\t\t\t}\r\n\t\t\t\tgetCartList(data).then(res => {\r\n\t\t\t\t\tlet invalid = res.data.list,\r\n\t\t\t\t\t\tloadendInvalid = invalid.length < that.limitInvalid;\r\n\t\t\t\t\tlet invalidList = that.$util.SplitArray(invalid, that.cartList.invalid);\r\n\t\t\t\t\tthat.$set(that.cartList, 'invalid', invalidList);\r\n\t\t\t\t\tthat.loadendInvalid = loadendInvalid;\r\n\t\t\t\t\tthat.loadTitleInvalid = loadendInvalid ? '我也是有底线的' : '加载更多';\r\n\t\t\t\t\tthat.pageInvalid = that.pageInvalid + 1;\r\n\t\t\t\t\tthat.loadingInvalid = false;\r\n\t\t\t\t\t//if(invalid.length===0) that.getHostProduct();\r\n\t\t\t\t}).catch(res => {\r\n\t\t\t\t\tthat.loadingInvalid = false;\r\n\t\t\t\t\tthat.loadTitleInvalid = '加载更多';\r\n\t\t\t\t})\r\n\r\n\t\t\t},\r\n\t\t\tgetHostProduct: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (that.hotScroll) return\r\n\t\t\t\tgetProductHot(\r\n\t\t\t\t\tthat.hotPage,\r\n\t\t\t\t\tthat.hotLimit,\r\n\t\t\t\t).then(res => {\r\n\t\t\t\t\tthat.hotPage++\r\n\t\t\t\t\tthat.hotScroll = res.data.list.length < that.hotLimit\r\n\t\t\t\t\tthat.hostProduct = that.hostProduct.concat(res.data.list)\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgoodsOpen: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthat.goodsHidden = !that.goodsHidden;\r\n\t\t\t},\r\n\t\t\tmanage: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthat.footerswitch = !that.footerswitch;\r\n\t\t\t\tlet arr1 = [];\r\n\t\t\t\tlet arr2 = [];\r\n\t\t\t\tlet newValid = that.cartList.valid.map(item => {\r\n\t\t\t\t\tif (that.footerswitch) {\r\n\t\t\t\t\t\tif (item.attrStatus) {\r\n\t\t\t\t\t\t\tif (item.checked) {\r\n\t\t\t\t\t\t\t\tarr1.push(item.id);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\titem.checked = false;\r\n\t\t\t\t\t\t\tarr2.push(item);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tif (item.checked) {\r\n\t\t\t\t\t\t\tarr1.push(item.id);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn item;\r\n\t\t\t\t});\r\n\t\t\t\tthat.cartList.valid = newValid;\r\n\t\t\t\tif (that.footerswitch) {\r\n\t\t\t\t\tthat.isAllSelect = newValid.length === arr1.length + arr2.length;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.isAllSelect = newValid.length === arr1.length;\r\n\t\t\t\t}\r\n\t\t\t\tthat.selectValue = arr1;\r\n\t\t\t\tthat.switchSelect();\r\n\t\t\t},\r\n\t\t\tunsetCart: function() {\r\n\t\t\t\tlet that = this,\r\n\t\t\t\t\tids = [];\r\n\t\t\t\tfor (let i = 0, len = that.cartList.invalid.length; i < len; i++) {\r\n\t\t\t\t\tids.push(that.cartList.invalid[i].id);\r\n\t\t\t\t}\r\n\t\t\t\tcartDel(ids).then(res => {\r\n\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\ttitle: '清除成功'\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthat.$set(that.cartList, 'invalid', []);\r\n\t\t\t\t\tthat.getHostProduct();\r\n\t\t\t\t}).catch(res => {\r\n\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tshareApi: function() {\r\n\t\t\t\tgetShare().then(res => {\r\n\t\t\t\t\tthis.$set(this, 'configApi', res.data);\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\tthis.setOpenShare(res.data);\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 微信分享；\r\n\t\t\tsetOpenShare: function(data) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (that.$wechat.isWeixin()) {\r\n\t\t\t\t\tlet configAppMessage = {\r\n\t\t\t\t\t\tdesc: data.synopsis,\r\n\t\t\t\t\t\ttitle: data.title,\r\n\t\t\t\t\t\tlink: location.href,\r\n\t\t\t\t\t\timgUrl: data.img\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthat.$wechat.wechatEvevt([\"updateAppMessageShareData\", \"updateTimelineShareData\"],\r\n\t\t\t\t\t\tconfigAppMessage);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\treturns: function() {\r\n\t\t\t\tuni.switchTab({\r\n\t\t\t\t\turl:'/pages/index/index'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tshowNav(){\r\n\t\t\t\tthis.currentPage = !this.currentPage;\r\n\t\t\t},\r\n\t\t\t//下拉导航页面跳转\r\n\t\t\tlinkPage(url){\r\n\t\t\t\tif(url == '/pages/index/index' || url == '/pages/user/index'){\r\n\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\turl\r\n\t\t\t\t\t})\r\n\t\t\t\t}else{\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\tanimationType: animationType.type,\r\t\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\t\turl\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tthis.currentPage = false\r\n\t\t\t},\r\n\t\t\ttouchStart(){\r\n\t\t\t\tthis.currentPage = false;\r\n\t\t\t}\r\n\t\t},\r\n\t\tonReachBottom() {\r\n\t\t\tlet that = this;\r\n\t\t\tif (that.loadend) {\r\n\t\t\t\tthat.getInvalidList();\r\n\t\t\t}\r\n\t\t\tif (that.cartList.valid.length == 0 && that.cartList.invalid.length == 0 && this.hotPage != 1) {\r\n\t\t\t\tthat.getHostProduct();\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.invalidClas {\r\n\t\tposition: relative;\r\n\t\tz-index: 111;\r\n\t\ttop: -120rpx;\r\n\t}\r\n\r\n\t.invalidClasNO {\r\n\t\tposition: static;\r\n\t\tmargin-top: 15px;\r\n\t}\r\n\r\n\t.cartBox {\r\n\t\t// background-color: #fff;\r\n\t}\r\n\t.cart_nav{\r\n\t\tposition: fixed;\r\n\t\t@include main_bg_color(theme);\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tz-index: 99;\r\n\t\twidth: 100%;\r\n\t}\r\n\t.navbarCon {\r\n\t\tposition: absolute;\r\n\t\tbottom: 0;\r\n\t\theight: 100rpx;\r\n\t\twidth: 100%;\r\n\t}\r\n\t.h5_back {\r\n\t\tcolor: #fff;\r\n\t\tposition: fixed;\r\n\t\tleft:20rpx;\r\n\t\tfont-size: 32rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 58rpx;\r\n\t}\r\n\t.select_nav{\r\n\t\twidth: 170rpx !important;\r\n\t\theight: 60rpx !important;\r\n\t\tborder-radius: 33rpx;\r\n\t\tbackground: rgba(255, 255, 255, 0.6);\r\n\t\tcolor: #000;\r\n\t\tposition: fixed;\r\n\t\tfont-size: 18px;\r\n\t\tline-height: 58rpx;\r\n\t\tz-index: 1000;\r\n\t\tleft: 14rpx;\r\n\t}\r\n\t.px-20{\r\n\t\tpadding: 0 20rpx 0;\r\n\t}\r\n\t.nav_line{\r\n\t\tcontent: '';\r\n\t\tdisplay: inline-block;\r\n\t\twidth: 1px;\r\n\t\theight: 34rpx;\r\n\t\tbackground: #fff;\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tmargin: auto;\r\n\t}\r\n\t.container_detail{\r\n\t\t/* #ifdef MP */\r\n\t\tmargin-top:32rpx;\r\n\t\t/* #endif */\r\n\t}\r\n\t.tab_nav{\r\n\t\twidth: 100%;\r\n\t\theight: 48px;\r\n\t\tpadding:0 30rpx 0;\r\n\t}\r\n\t.nav_title{\r\n\t\twidth: 200rpx;\r\n\t\theight: 58rpx;\r\n\t\tline-height: 58rpx;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 36rpx;\r\n\t\tposition: fixed;\r\n\t\ttext-align: center;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tmargin: auto;\r\n\t}\r\n\t.right_select{\r\n\t\tposition: fixed;\r\n\t\tright: 20rpx;\r\n\t\tcolor: #fff;\r\n\t\ttext-align: center;\r\n\t\tline-height: 58rpx;\r\n\t}\r\n\t.dialog_nav{\r\n\t\tposition: fixed;\r\n\t\t/* #ifdef MP */\r\n\t\tleft: 14rpx;\r\n\t\t/* #endif */\r\n\t\t/* #ifdef H5 || APP-PLUS*/\r\n\t\tright: 14rpx;\r\n\t\t/* #endif */\r\n\t\twidth: 240rpx;\r\n\t\tbackground: #FFFFFF;\r\n\t\tbox-shadow: 0px 0px 16rpx rgba(0, 0, 0, 0.08);\r\n\t\tz-index: 999;\r\n\t\tborder-radius: 14rpx;\r\n\t\t&::before{\r\n\t\t\tcontent: '';\r\n\t\t\twidth: 0;\r\n\t\t\theight: 0;\r\n\t\t\tposition: absolute;\r\n\t\t\t/* #ifdef MP */\r\n\t\t\tleft: 0;\r\n\t\t\tright: 0;\r\n\t\t\tmargin:auto;\r\n\t\t\t/* #endif */\r\n\t\t\t/* #ifdef H5 || APP-PLUS */\r\n\t\t\tright: 8px;\r\n\t\t\t/* #endif */\r\n\t\t\ttop:-9px;\r\n\t\t\tborder-bottom: 10px solid #fff;\r\n\t\t\tborder-left: 10px solid transparent;    /*transparent 表示透明*/\r\n\t\t\tborder-right: 10px solid transparent;\r\n\t\t}\r\n\t}\r\n\t.dialog_nav_item{\r\n\t\twidth: 100%;\r\n\t\theight: 84rpx;\r\n\t\tline-height: 84rpx;\r\n\t\tpadding: 0 20rpx 0;\r\n\t\tbox-sizing: border-box;\r\n\t\tborder-bottom: #eee;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333;\r\n\t\tposition: relative;\r\n\t\t.iconfont{\r\n\t\t\tfont-size: 32rpx;\r\n\t\t}\r\n\t\t&::after{\r\n\t\t\tcontent: '';\r\n\t\t\tposition: absolute;\r\n\t\t\twidth:86px;\r\n\t\t\theight: 1px;\r\n\t\t\tbackground-color: #EEEEEE;\r\n\t\t\tbottom: 0;\r\n\t\t\tright: 0;\r\n\t\t}\r\n\t}\r\n\t.pl-20{\r\n\t\tpadding-left: 20rpx;\r\n\t}\r\n\t.px-20{\r\n\t\tpadding: 0 20rpx 0;\r\n\t}\r\n\t.justify-center{\r\n\t\tjustify-content: center;\r\n\t}\r\n\t.align-center {\r\n\t\talign-items: center;\r\n\t}\r\n\t.shoppingCart {\r\n\t\t/* #ifdef H5 */\r\n\t\t// padding-bottom: 0;\r\n\t\t// padding-bottom: constant(safe-area-inset-bottom);  \r\n\t\t// padding-bottom: env(safe-area-inset-bottom);\r\n\t\t/* #endif */\r\n\t\tposition: absolute;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.shoppingCart .labelNav {\r\n\t\theight: 178rpx;\r\n\t\tpadding: 30rpx 30rpx 0 ;\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #fff;\r\n\t\twidth: 100%;\r\n\t\tbox-sizing: border-box;\r\n\t\t@include main_bg_color(theme);\r\n\t\tz-index: 5;\r\n\t}\r\n\r\n\t.shoppingCart .labelNav .item .iconfont {\r\n\t\tfont-size: 25rpx;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\r\n\t.shoppingCart .nav {\r\n\t\twidth: 92%;\r\n\t\theight: 90rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tpadding: 0 24rpx;\r\n\t\t-webkit-box-sizing: border-box;\r\n\t\tbox-sizing: border-box;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #282828;\r\n\t\tmargin: -90rpx auto 0 ;\r\n\t\tz-index: 6;\r\n\t\tborder-top-left-radius: 14rpx;\r\n\t\tborder-top-right-radius: 14rpx;\r\n\t}\r\n\r\n\t.shoppingCart .nav .num {\r\n\t\tmargin-left: 12rpx;\r\n\t}\r\n\r\n\t.shoppingCart .nav .administrate {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.shoppingCart .noCart {\r\n\t\t// margin-top: 171rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tpadding-top: 0.1rpx;\r\n\t}\r\n\r\n\t.shoppingCart .noCart .pictrue {\r\n\t\twidth: 414rpx;\r\n\t\theight: 336rpx;\r\n\t\tmargin: 78rpx auto 56rpx auto;\r\n\t}\r\n\r\n\t.shoppingCart .noCart .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.shoppingCart .list {\r\n\t\twidth: 100%;\r\n\t\t// margin-top: 178rpx;\r\n\t\t/* #ifdef MP */\r\n\t\t// margin-bottom:120rpx;\r\n\t\t/* #endif */\r\n\t\t/* #ifndef MP */\r\n\t\t// margin-bottom:240rpx;\r\n\t\t/* #endif */\r\n\t\toverflow: hidden;\r\n\t\tborder-bottom-left-radius: 14rpx;\r\n\t\tborder-bottom-right-radius: 14rpx;\r\n\t}\r\n\t\r\n\r\n\t.shoppingCart .list .item {\r\n\t\tpadding: 24rpx;\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\r\n\t.shoppingCart .list .item .picTxt {\r\n\t\twidth: 582rpx;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.shoppingCart .list .item .picTxt .pictrue {\r\n\t\twidth: 160rpx;\r\n\t\theight: 160rpx;\r\n\t}\r\n\r\n\t.shoppingCart .list .item .picTxt .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 6rpx;\r\n\t}\r\n\r\n\t.shoppingCart .list .item .picTxt .text {\r\n\t\twidth: 396rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #282828;\r\n\t}\r\n\r\n\t.shoppingCart .list .item .picTxt .text .reColor {\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.shoppingCart .list .item .picTxt .text .reElection {\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\r\n\t.shoppingCart .list .item .picTxt .text .reElection .title {\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.shoppingCart .list .item .picTxt .text .reElection .reBnt {\r\n\t\twidth: 120rpx;\r\n\t\theight: 46rpx;\r\n\t\tborder-radius: 23rpx;\r\n\t\tfont-size: 26rpx;\r\n\t}\r\n\r\n\t.shoppingCart .list .item .picTxt .text .infor {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999999;\r\n\t\tmargin-top: 16rpx;\r\n\t}\r\n\r\n\t.money {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 600;\r\n\t\t@include price_color(theme);\r\n\t\t.price-color{\r\n\t\t\t@include price_color(theme);\r\n\t\t}\r\n\t}\r\n\t.mt-28{\r\n\t\tmargin-top: 28rpx;\r\n\t}\r\n\t.bg_color{\r\n\t\t@include main_bg_color(theme);\r\n\t}\r\n\t.font_color{\r\n\t\t@include main_color(theme);\r\n\t}\r\n\t.shoppingCart .list .item .picTxt .carnum {\r\n\t\theight: 47rpx;\r\n\t\tposition: absolute;\r\n\t\tbottom: 7rpx;\r\n\t\tright: 0;\r\n\t}\r\n\r\n\t.shoppingCart .list .item .picTxt .carnum view {\r\n\t\tborder: 1rpx solid #a4a4a4;\r\n\t\twidth: 66rpx;\r\n\t\ttext-align: center;\r\n\t\theight: 100%;\r\n\t\tline-height: 44rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #a4a4a4;\r\n\t}\r\n\r\n\t.shoppingCart .list .item .picTxt .carnum .reduce {\r\n\t\tborder-right: 0;\r\n\t\tborder-radius: 3rpx 0 0 3rpx;\r\n\t\tborder-radius: 22rpx 0rpx 0rpx 22rpx;\r\n\t\tfont-size: 34rpx;\r\n\t\tline-height: 40rpx;\r\n\t}\r\n\r\n\t.shoppingCart .list .item .picTxt .carnum .reduce.on {\r\n\t\tborder-color: #e3e3e3;\r\n\t\tcolor: #dedede;\r\n\t}\r\n\r\n\t.shoppingCart .list .item .picTxt .carnum .plus {\r\n\t\tborder-left: 0;\r\n\t\tborder-radius: 0 3rpx 3rpx 0;\r\n\t\tborder-radius: 0rpx 22rpx 22rpx 0rpx;\r\n\t\tfont-size: 34rpx;\r\n\t\tline-height: 40rpx;\r\n\t}\r\n\r\n\t.shoppingCart .list .item .picTxt .carnum .num {\r\n\t\tcolor: #282828;\r\n\t}\r\n\r\n\t.shoppingCart .invalidGoods {\r\n\t\tbackground-color: #fff;\r\n\t\tmargin-top: 30rpx;\r\n\t\t/* #ifdef MP */\r\n\t\tmargin-top: 140rpx;\r\n\t\t/* #endif */\r\n\r\n\t}\r\n\r\n\t.shoppingCart .invalidGoods .goodsNav {\r\n\t\twidth: 100%;\r\n\t\theight: 90rpx;\r\n\t\tpadding: 0 24rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.shoppingCart .invalidGoods .goodsNav .iconfont {\r\n\t\tcolor: #424242;\r\n\t\tfont-size: 28rpx;\r\n\t\tmargin-right: 17rpx;\r\n\t}\r\n\r\n\t.shoppingCart .invalidGoods .goodsNav .del {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.shoppingCart .invalidGoods .goodsNav .del .icon-shanchu1 {\r\n\t\tcolor: #333;\r\n\t\tfont-size: 33rpx;\r\n\t\tvertical-align: -2rpx;\r\n\t\tmargin-right: 8rpx;\r\n\t}\r\n\r\n\t.shoppingCart .invalidGoods .goodsList .item {\r\n\t\tpadding: 24rpx;\r\n\t}\r\n\r\n\t.shoppingCart .invalidGoods .goodsList .picTxt {\r\n\t\twidth: 576rpx;\r\n\t}\r\n\r\n\t.shoppingCart .invalidGoods .goodsList .item .invalid {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #CCCCCC;\r\n\t\theight: 36rpx;\r\n\t\tborder-radius: 3rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 36rpx;\r\n\t}\r\n\r\n\t.shoppingCart .invalidGoods .goodsList .item .pictrue {\r\n\t\twidth: 160rpx;\r\n\t\theight: 160rpx;\r\n\t}\r\n\r\n\t.shoppingCart .invalidGoods .goodsList .item .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 6rpx;\r\n\t}\r\n\r\n\t.shoppingCart .invalidGoods .goodsList .item .text {\r\n\t\twidth: 396rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #999;\r\n\t\theight: 140rpx;\r\n\t}\r\n\r\n\t.shoppingCart .invalidGoods .goodsList .item .text .name {\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.shoppingCart .invalidGoods .goodsList .item .text .infor {\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.shoppingCart .invalidGoods .goodsList .item .text .end {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #bbb;\r\n\t}\r\n\r\n\t.footer {\r\n\t\tz-index: 999;\r\n\t\twidth: 100%;\r\n\t\theight: 100rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tposition: fixed;\r\n\t\tpadding: 0 24rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tborder-top: 1rpx solid #eee;\r\n\t\tbottom: var(--window-bottom);\r\n\t\t\r\n\t}\r\n\r\n\t.footer .checkAll {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #282828;\r\n\t\tmargin-left: 14rpx;\r\n\t}\r\n\r\n\t.footer .money {\r\n\t\tfont-size: 30rpx;\r\n\r\n\t\t.font-color {\r\n\t\t\tfont-weight: 600;\r\n\t\t}\r\n\t}\r\n\r\n\t.footer .placeOrder {\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 30rpx;\r\n\t\twidth: 226rpx;\r\n\t\theight: 70rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 70rpx;\r\n\t\tmargin-left: 22rpx;\r\n\t}\r\n\r\n\t.footer .button .bnt {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #999;\r\n\t\tborder-radius: 50rpx;\r\n\t\tborder: 1px solid #999;\r\n\t\twidth: 160rpx;\r\n\t\theight: 60rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 60rpx;\r\n\t}\r\n\t.btn_cart_color{\r\n\t\tfont-size: 14px;\r\n\t\tborder-radius: 25px;\r\n\t\twidth: 80px;\r\n\t\theight: 30px;\r\n\t\ttext-align: center;\r\n\t\tline-height: 30px;\r\n\t\t@include coupons_border_color(theme);\r\n\t\t@include main_color(theme);\r\n\t}\r\n\t.footer .button form~form {\r\n\t\tmargin-left: 17rpx;\r\n\t}\r\n\r\n\t.uni-p-b-96 {\r\n\t\theight: 96rpx;\r\n\t}\r\n\t/deep/ checkbox .uni-checkbox-input.uni-checkbox-input-checked {\r\n\t\t@include main_bg_color(theme);\r\n\t\tborder: none !important;\r\n\t\tcolor: #fff!important\r\n\t}\r\n\t\r\n\t/deep/ checkbox .wx-checkbox-input.wx-checkbox-input-checked {\r\n\t\t@include main_bg_color(theme);\r\n\t\tborder: none !important;\r\n\t\tcolor: #fff!important;\r\n\t\tmargin-right: 0 !important;\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_addcart.vue?vue&type=style&index=0&id=a5aa7f30&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_addcart.vue?vue&type=style&index=0&id=a5aa7f30&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294178838\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}