{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_goods_collection/index.vue?69bf", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_goods_collection/index.vue?6c5d", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_goods_collection/index.vue?8c9c", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_goods_collection/index.vue?5194", "uni-app:///pages/users/user_goods_collection/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_goods_collection/index.vue?4fa0", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_goods_collection/index.vue?76ee"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "recommend", "authorize", "home", "data", "footerswitch", "hostProduct", "loadTitle", "loading", "loadend", "collectProductList", "limit", "page", "isAuto", "isShowAuth", "hotScroll", "hotPage", "hotLimit", "isAllSelect", "selectValue", "delBtnWidth", "totals", "theme", "computed", "onLoad", "onShow", "methods", "manage", "checkboxChange", "values", "checkboxAllChange", "setAllSelectValue", "onLoadFun", "auth<PERSON><PERSON><PERSON>", "get_user_collect_product", "that", "res", "delCollection", "ids", "delCollectionAll", "title", "del", "icon", "get_host_product", "onReachBottom"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9BA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC4EnnB;AAKA;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA;AAAA,eACA;EACAC;IACAC;IAEAC;IAEAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;EACAC;IACA;IACA;MACA;MACA;MACA;MACA;IACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACAC;MACA;IACA;IAEAC;MACA;QACAC;MACA;QACA;QACA;UACA;QACA;UACA;QACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;UACA;YACA;YACAZ;YACA;UACA;YACA;YACA;UACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAa;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACAC;MACAA;MACA;QACAvB;QACAD;MACA;QACAyB;UACAD;QACA;QACAA;QACA;QACA;QACAA,yEACAzB;QACAyB;QACA;QACAA;QACAA;QACAA;QACAA;MACA;QACAA;QACAA;MACA;IACA;IACA;AACA;AACA;IACAE;MACA;MACA;QACAC;MACA;IACA;IACAC;MACA;QACAC;MACA;MACA;QACAF;MACA;IACA;IACAG;MAAA;MACA;QACA;UACAD;UACAE;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;UACAF;QACA;MACA;IACA;IACA;AACA;AACA;IACAG;MACA;MACA;MACA,0BACAR,cACAA,cACA;QACAA;QACAA;QACAA;MACA;IACA;EACA;EACA;AACA;AACA;EACAS;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC5RA;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/users/user_goods_collection/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/users/user_goods_collection/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=59b202a4&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=59b202a4&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"59b202a4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/users/user_goods_collection/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=59b202a4&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.collectProductList.length\n  var l0 = g0\n    ? _vm.__map(_vm.collectProductList, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g1 = !_vm.footerswitch ? item.id.toString() : null\n        return {\n          $orig: $orig,\n          g1: g1,\n        }\n      })\n    : null\n  var g2 = !g0 ? !_vm.collectProductList.length && _vm.page > 1 : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :data-theme=\"theme\">\r\n\t\t<view class='collectionGoods' v-if=\"collectProductList.length\">\r\n\t\t\t<!-- #ifdef  H5 || MP-->\r\n\t\t\t<view class='nav acea-row row-between-wrapper'>\r\n\t\t\t\t<view>当前共 <text class='num font_color'>{{ totals }}</text>件商品</view>\r\n\t\t\t\t<view class='administrate acea-row row-center-wrapper' @click='manage'>{{ footerswitch ? '管理' : '取消'}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<view class=\"list\">\r\n\t\t\t\t<checkbox-group @change=\"checkboxChange\" class=\"centent\">\r\n\t\t\t\t\t<!-- #ifndef APP-PLUS-->\r\n\t\t\t\t\t<view v-for=\"(item,index) in collectProductList\" :key=\"index\" class='item acea-row row-middle'>\r\n\t\t\t\t\t\t<checkbox :value=\"item.id.toString()\" :checked=\"item.checked\" v-if=\"!footerswitch\"\r\n\t\t\t\t\t\t\tstyle=\"margin-right: 10rpx;\" />\r\n\t\t\t\t\t\t<navigator :url='\"/pages/goods_details/index?id=\"+item.productId' hover-class='none'\r\n\t\t\t\t\t\t\tclass=\"acea-row\">\r\n\t\t\t\t\t\t\t<view class='pictrue'>\r\n\t\t\t\t\t\t\t\t<image :src=\"item.image\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t<view class='name line1'>{{item.storeName}}</view>\r\n\t\t\t\t\t\t\t\t<view class='money'>￥{{item.price}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</navigator>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<!-- #ifdef APP-PLUS -->\r\n\t\t\t\t\t<view v-for=\"(item,index) in collectProductList\" :key=\"index\" :data-index=\"index\"\r\n\t\t\t\t\t\tclass='item acea-row row-middle order-item'>\r\n\t\t\t\t\t\t<navigator :url='\"/pages/goods_details/index?id=\"+item.productId' hover-class='none' class=\"acea-row\">\r\n\t\t\t\t\t\t\t<view class='pictrue'>\r\n\t\t\t\t\t\t\t\t<image :src=\"item.image\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t<view class='name line1'>{{item.storeName}}</view>\r\n\t\t\t\t\t\t\t\t<view class='money'>￥{{item.price}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</navigator>\r\n\t\t\t\t\t\t<view class=\"remove borRadius14\" @tap=\"delCollection(item.id)\">删除</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t</checkbox-group>\r\n\t\t\t</view>\r\n\t\t\t<view class='loadingicon acea-row row-center-wrapper'>\r\n\t\t\t\t<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>{{loadTitle}}\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"!footerswitch\" class='footer acea-row row-between-wrapper'>\r\n\t\t\t\t<view>\r\n\t\t\t\t\t<checkbox-group @change=\"checkboxAllChange\">\r\n\t\t\t\t\t\t<checkbox value=\"all\" :checked=\"!!isAllSelect\" />\r\n\t\t\t\t\t\t<text class='checkAll'>全选</text>\r\n\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='button acea-row row-middle'>\r\n\t\t\t\t\t<form @submit=\"delCollectionAll\" report-submit='true'>\r\n\t\t\t\t\t\t<button class='bnt cart-color' formType=\"submit\">取消收藏</button>\r\n\t\t\t\t\t</form>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class='noCommodity' v-else-if=\"!collectProductList.length && page > 1\">\r\n\t\t\t<view class='pictrue'>\r\n\t\t\t\t<image src='../static/noCollection.png'></image>\r\n\t\t\t</view>\r\n\t\t\t<recommend :hostProduct=\"hostProduct\"></recommend>\r\n\t\t</view>\r\n\t\t<!-- #ifdef MP -->\r\n\t\t<!-- <authorize @onLoadFun=\"onLoadFun\" :isAuto=\"isAuto\" :isShowAuth=\"isShowAuth\" @authColse=\"authColse\"></authorize> -->\r\n\t\t<!-- #endif -->\r\n\t\t<!-- <home></home> -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tgetCollectUserList,\r\n\t\tgetProductHot,\r\n\t\tcollectDelete\r\n\t} from '@/api/store.js';\r\n\timport {\r\n\t\tmapGetters\r\n\t} from \"vuex\";\r\n\timport {\r\n\t\ttoLogin\r\n\t} from '@/libs/login.js';\r\n\timport recommend from '@/components/recommend';\r\n\t// #ifdef MP\r\n\timport authorize from '@/components/Authorize';\r\n\t// #endif\r\n\timport home from '@/components/home';\r\n\tlet app = getApp();\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\trecommend,\r\n\t\t\t// #ifdef MP\r\n\t\t\tauthorize,\r\n\t\t\t// #endif\r\n\t\t\thome\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tfooterswitch: true,\r\n\t\t\t\thostProduct: [],\r\n\t\t\t\tloadTitle: '加载更多',\r\n\t\t\t\tloading: false,\r\n\t\t\t\tloadend: false,\r\n\t\t\t\tcollectProductList: [],\r\n\t\t\t\tlimit: 8,\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false, //是否隐藏授权\r\n\t\t\t\thotScroll: false,\r\n\t\t\t\thotPage: 1,\r\n\t\t\t\thotLimit: 10,\r\n\t\t\t\tisAllSelect: false, //全选\r\n\t\t\t\tselectValue: [], //选中的数据\r\n\t\t\t\tdelBtnWidth: 80, //左滑默认宽度\r\n\t\t\t\ttotals: 0,\r\n\t\t\t\ttheme:app.globalData.theme,\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: mapGetters(['isLogin']),\r\n\t\tonLoad() {\r\n\t\t\tlet that = this;\r\n\t\t\tif (this.isLogin) {\r\n\t\t\t\tthis.loadend = false;\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.collectProductList = [];\r\n\t\t\t\tthis.get_user_collect_product();\r\n\t\t\t} else {\r\n\t\t\t\ttoLogin();\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tthis.loadend = false;\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.collectProductList = [];\r\n\t\t\tthis.get_user_collect_product();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tmanage: function() {\r\n\t\t\t\tthis.footerswitch = !this.footerswitch;\r\n\t\t\t},\r\n\r\n\t\t\tcheckboxChange: function(event) {\r\n\t\t\t\tvar items = this.collectProductList,\r\n\t\t\t\t\tvalues = event.detail.value;\r\n\t\t\t\tfor (var i = 0, lenI = items.length; i < lenI; ++i) {\r\n\t\t\t\t\tconst item = items[i]\r\n\t\t\t\t\tif (values.includes(item.id.toString())) {\r\n\t\t\t\t\t\tthis.$set(item, 'checked', true)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.$set(item, 'checked', false)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthis.selectValue = values.toString();\r\n\t\t\t\tthis.isAllSelect = items.length === values.length;\r\n\t\t\t},\r\n\t\t\tcheckboxAllChange: function(event) {\r\n\t\t\t\tlet value = event.detail.value;\r\n\t\t\t\tif (value.length > 0) {\r\n\t\t\t\t\tthis.setAllSelectValue(1)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.setAllSelectValue(0)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsetAllSelectValue: function(status) {\r\n\t\t\t\tlet selectValue = [];\r\n\t\t\t\tif (this.collectProductList.length > 0) {\r\n\t\t\t\t\tthis.collectProductList.map(item => {\r\n\t\t\t\t\t\tif (status) {\r\n\t\t\t\t\t\t\tthis.$set(item, 'checked', true)\r\n\t\t\t\t\t\t\tselectValue.push(item.id);\r\n\t\t\t\t\t\t\tthis.isAllSelect = true;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.$set(item, 'checked', false)\r\n\t\t\t\t\t\t\tthis.isAllSelect = false;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.selectValue = selectValue.toString();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 授权回调\r\n\t\t\t */\r\n\t\t\tonLoadFun: function() {\r\n\t\t\t\tthis.get_user_collect_product();\r\n\t\t\t\tthis.get_host_product();\r\n\t\t\t},\r\n\t\t\t// 授权关闭\r\n\t\t\tauthColse: function(e) {\r\n\t\t\t\tthis.isShowAuth = e\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 获取收藏产品\r\n\t\t\t */\r\n\t\t\tget_user_collect_product: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (this.loading) return;\r\n\t\t\t\tif (this.loadend) return;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tthat.loadTitle = \"\";\r\n\t\t\t\tgetCollectUserList({\r\n\t\t\t\t\tpage: that.page,\r\n\t\t\t\t\tlimit: that.limit\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tres.data.list.map(item => {\r\n\t\t\t\t\t\tthat.$set(item, 'right', 0);\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthat.totals = res.data.total;\r\n\t\t\t\t\tlet collectProductList = res.data.list;\r\n\t\t\t\t\tlet loadend = collectProductList.length < that.limit;\r\n\t\t\t\t\tthat.collectProductList = that.$util.SplitArray(collectProductList, that\r\n\t\t\t\t\t\t.collectProductList);\r\n\t\t\t\t\tthat.$set(that, 'collectProductList', that.collectProductList);\r\n\t\t\t\t\tif (that.collectProductList.length === 0) that.get_host_product();\r\n\t\t\t\t\tthat.loadend = loadend;\r\n\t\t\t\t\tthat.loadTitle = loadend ? '我也是有底线的' : '加载更多';\r\n\t\t\t\t\tthat.page = that.page + 1;\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.loadTitle = \"加载更多\";\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 取消收藏\r\n\t\t\t */\r\n\t\t\tdelCollection: function(id, index) {\r\n\t\t\t\tthis.selectValue = id;\r\n\t\t\t\tthis.del({\r\n\t\t\t\t\tids: this.selectValue.toString()\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tdelCollectionAll: function() {\r\n\t\t\t\tif (!this.selectValue || this.selectValue.length == 0) return this.$util.Tips({\r\n\t\t\t\t\ttitle: '请选择商品'\r\n\t\t\t\t});\r\n\t\t\t\tthis.del({\r\n\t\t\t\t\tids: this.selectValue\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tdel: function(data) {\r\n\t\t\t\tcollectDelete(data).then(res => {\r\n\t\t\t\t\tthis.$util.Tips({\r\n\t\t\t\t\t\ttitle: '取消收藏成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// this.collectProductList = this.collectProductList.filter(item=>item!==this.selectValue)\r\n\t\t\t\t\tthis.collectProductList = [];\r\n\t\t\t\t\tthis.loadend = false;\r\n\t\t\t\t\tthis.page = 1;\r\n\t\t\t\t\tthis.get_user_collect_product();\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\treturn this.$util.Tips({\r\n\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t})\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 获取我的推荐\r\n\t\t\t */\r\n\t\t\tget_host_product: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (that.hotScroll) return\r\n\t\t\t\tgetProductHot(\r\n\t\t\t\t\tthat.hotPage,\r\n\t\t\t\t\tthat.hotLimit,\r\n\t\t\t\t).then(res => {\r\n\t\t\t\t\tthat.hotPage++\r\n\t\t\t\t\tthat.hotScroll = res.data.list.length < that.hotLimit\r\n\t\t\t\t\tthat.hostProduct = that.hostProduct.concat(res.data.list)\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t/**\r\n\t\t * 页面上拉触底事件的处理函数\r\n\t\t */\r\n\t\tonReachBottom() {\r\n\t\t\tthis.get_user_collect_product();\r\n\t\t\tthis.get_host_product();\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.money{\r\n\t\tfont-size: 26rpx;\r\n\t\t@include price_color(theme);\r\n\t}\r\n\t.order-item {\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t\tposition: relative;\r\n\t\talign-items: right;\r\n\t\tflex-direction: row;\r\n\t}\r\n\r\n\t.remove {\r\n\t\twidth: 120rpx;\r\n\t\theight: 40rpx;\r\n\t\t@include main_bg_color(theme);\r\n\t\tcolor: #fff;\r\n\t\tposition: absolute;\r\n\t\tbottom: 30rpx;\r\n\t\tright: 60rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.collectionGoods {\r\n\r\n\t\t.nav {\r\n\t\t\twidth: 92%;\r\n\t\t\theight: 90rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tpadding: 0 24rpx;\r\n\t\t\t-webkit-box-sizing: border-box;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #282828;\r\n\t\t\tposition: fixed;\r\n\t\t\tleft: 30rpx;\r\n\t\t\tz-index: 5;\r\n\t\t\ttop: 30rpx;\r\n\t\t\tborder-bottom: 1px solid #EEEEEE;\r\n\t\t\tborder-top-left-radius: 14rpx;\r\n\t\t\tborder-top-right-radius: 14rpx;\r\n\t\t}\r\n\r\n\t\t.list {\r\n\t\t\tpadding: 30rpx;\r\n\t\t\t/* #ifndef APP-PLUS*/\r\n\t\t\tmargin-top: 90rpx;\r\n\t\t\t/* #endif */\r\n\t\t\t/* #ifdef MP  */\r\n\t\t\t//margin-top: 0rpx;\r\n\t\t\t/* #endif */\r\n\r\n\t\t\t.name {\r\n\t\t\t\twidth: 434rpx;\r\n\t\t\t\t/* #ifdef APP-PLUS */\r\n\t\t\t\twidth: 486rpx;\r\n\t\t\t\t/* #endif */\r\n\t\t\t\tmargin-bottom: 56rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.centent {\r\n\t\t\t/* #ifdef H5 || MP */\r\n\t\t\tbackground-color: #fff;\r\n\t\t\t/* #endif */\r\n\t\t\tborder-bottom-left-radius: 14rpx;\r\n\t\t\tborder-bottom-right-radius: 14rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.collectionGoods .item {\r\n\t\tbackground-color: #fff;\r\n\t\tpadding-left: 24rpx;\r\n\t\theight: 180rpx;\r\n\t\tmargin-bottom: 15rpx;\r\n\t\tborder-radius: 14rpx;\r\n\r\n\t}\r\n\r\n\t.collectionGoods .item .pictrue {\r\n\t\twidth: 130rpx;\r\n\t\theight: 130rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t}\r\n\r\n\t.collectionGoods .item .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 14rpx;\r\n\t}\r\n\r\n\t.collectionGoods .item .text {\r\n\t\twidth: 535rpx;\r\n\t\theight: 130rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #282828;\r\n\t}\r\n\r\n\t.collectionGoods .item .text .name {\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.collectionGoods .item .text .delete {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #282828;\r\n\t\twidth: 144rpx;\r\n\t\theight: 46rpx;\r\n\t\tborder: 1px solid #bbb;\r\n\t\tborder-radius: 4rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 46rpx;\r\n\t}\r\n\r\n\t.noCommodity {\r\n\t\tbackground-color: #fff;\r\n\t\tpadding-top: 1rpx;\r\n\t\tborder-top: 0;\r\n\t}\r\n\r\n\t.footer {\r\n\t\tz-index: 9;\r\n\t\twidth: 100%;\r\n\t\theight: 96rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tposition: fixed;\r\n\t\tpadding: 0 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tborder-top: 1rpx solid #eee;\r\n\t\tborder-bottom: 1px solid #EEEEEE;\r\n\t\t/* #ifdef H5 || MP */\r\n\t\tbottom: 0rpx;\r\n\t\t/* #endif */\r\n\t\t/* #ifdef APP-PLUS */\r\n\t\tbottom: 0;\r\n\r\n\t\t/* #endif */\r\n\t\t/* #ifndef MP || APP-PLUS */\r\n\t\t// bottom: 98rpx;\r\n\t\t// bottom: calc(98rpx+ constant(safe-area-inset-bottom)); ///兼容 IOS<11.2/\r\n\t\t// bottom: calc(98rpx + env(safe-area-inset-bottom)); ///兼容 IOS>11.2/\r\n\t\t/* #endif */\r\n\t\t.checkAll {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #282828;\r\n\t\t\tmargin-left: 16rpx;\r\n\t\t}\r\n\r\n\t\t.button .bnt {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #999;\r\n\t\t\tborder-radius: 30rpx;\r\n\t\t\tborder: 1px solid #999;\r\n\t\t\twidth: 160rpx;\r\n\t\t\theight: 60rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tline-height: 60rpx;\r\n\t\t}\r\n\t}\r\n\t.font_color{\r\n\t\t@include main_color(theme);\r\n\t}\r\n\t/deep/ checkbox .uni-checkbox-input.uni-checkbox-input-checked {\r\n\t\t@include main_bg_color(theme);\r\n\t\t@include coupons_border_color(theme);\r\n\t\tcolor: #fff!important\r\n\t}\r\n\t\r\n\t/deep/ checkbox .wx-checkbox-input.wx-checkbox-input-checked {\r\n\t\t@include main_bg_color(theme);\r\n\t\t@include coupons_border_color(theme);\r\n\t\tcolor: #fff!important;\r\n\t\tmargin-right: 0 !important;\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=59b202a4&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=59b202a4&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294180201\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}