{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/promoter-list/index.vue?36b2", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/promoter-list/index.vue?c197", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/promoter-list/index.vue?225b", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/promoter-list/index.vue?d495", "uni-app:///pages/users/promoter-list/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/promoter-list/index.vue?1622", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/promoter-list/index.vue?df91"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "Loading", "emptyPage", "authorize", "home", "data", "page", "limit", "keyword", "sort", "isAsc", "sortKey", "grade", "status", "loadingList", "recordList", "peopleData", "isShow", "isAuto", "isShowAuth", "theme", "bgColor", "computed", "onLoad", "that", "uni", "frontColor", "backgroundColor", "onShow", "onHide", "methods", "onLoadFun", "auth<PERSON><PERSON><PERSON>", "setSort", "submitForm", "setType", "spreadPeoCount", "userSpreadNewList", "recordListNew", "onReachBottom"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1BA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC0FnnB;AAIA;AACA;AAOA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AAAA,eACA;EACAC;IACAC;IACAC;IAEAC;IAEAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;EACAC;IACA;MACA;MACA;IACA;MACA;IACA;IACA;IACAC;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAT;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;IACA;IACAU;MACA;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA/B;QACAC;QACAC;QACAI;QACAD;QACAD;MACA;QACA;QACA;QACA4B;QACAd;QACAA;QACAA;QACAA;QACA;MACA;IACA;EACA;EACAe;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3OA;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/users/promoter-list/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/users/promoter-list/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=bfc5ae28&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=bfc5ae28&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"bfc5ae28\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/users/promoter-list/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=bfc5ae28&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.recordList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = item.time ? item.time.split(\" \") : null\n    return {\n      $orig: $orig,\n      g0: g0,\n    }\n  })\n  var g1 = _vm.recordList.length == 0 && _vm.isShow\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :data-theme=\"theme\">\r\n\t\t<view class=\"promoter-list\">\r\n\t\t\t<view class='promoterHeader'>\r\n\t\t\t\t<view class='headerCon acea-row row-between'>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<view class='name'>推广人数</view>\r\n\t\t\t\t\t\t<view><text class='num'>{{peopleData.count}}</text>人</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='iconfont icon-tuandui'></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"pad30\">\r\n\t\t\t\t<view class='nav acea-row row-around'>\r\n\t\t\t\t\t<view :class=\"grade == 0 ? 'item on' : 'item'\" @click='setType(0)'>一级({{peopleData.total}})</view>\r\n\t\t\t\t\t<view :class=\"grade == 1 ? 'item on' : 'item'\" @click='setType(1)'>二级({{peopleData.totalLevel}})\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='search acea-row row-between-wrapper'>\r\n\t\t\t\t\t<view class='input'>\r\n\t\t\t\t\t\t<input placeholder='点击搜索会员名称' placeholder-class='placeholder' v-model=\"keyword\"\r\n\t\t\t\t\t\t\t@confirm=\"submitForm\" confirm-type='search' name=\"search\" maxlength=\"10\"></input>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t<button class='iconfont icon-sousuo2' @click=\"submitForm\"></button>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='list'>\r\n\t\t\t\t\t<view class=\"sortNav acea-row row-middle\">\r\n\t\t\t\t\t\t<view class=\"sortItem\" @click='setSort(\"childCount\",\"ASC\")' v-if=\"sort == 'childCountDESC'\">团队排序\r\n\t\t\t\t\t\t\t<image src='/static/images/sort1.png'></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"sortItem\" @click='setSort(\"childCount\")' v-else-if=\"sort == 'childCountASC'\">团队排序\r\n\t\t\t\t\t\t\t<image src='/static/images/sort3.png'></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"sortItem\" @click='setSort(\"childCount\",\"DESC\")' v-else>团队排序\r\n\t\t\t\t\t\t\t<image src='/static/images/sort2.png'></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"sortItem\" @click='setSort(\"numberCount\",\"ASC\")' v-if=\"sort == 'numberCountDESC'\">\r\n\t\t\t\t\t\t\t金额排序\r\n\t\t\t\t\t\t\t<image src='/static/images/sort1.png'></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"sortItem\" @click='setSort(\"numberCount\")' v-else-if=\"sort == 'numberCountASC'\">金额排序\r\n\t\t\t\t\t\t\t<image src='/static/images/sort3.png'></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"sortItem\" @click='setSort(\"numberCount\",\"DESC\")' v-else>金额排序\r\n\t\t\t\t\t\t\t<image src='/static/images/sort2.png'></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"sortItem\" @click='setSort(\"orderCount\",\"ASC\")' v-if=\"sort == 'orderCountDESC'\">订单排序\r\n\t\t\t\t\t\t\t<image src='/static/images/sort1.png'></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"sortItem\" @click='setSort(\"orderCount\")' v-else-if=\"sort == 'orderCountASC'\">订单排序\r\n\t\t\t\t\t\t\t<image src='/static/images/sort3.png'></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"sortItem\" @click='setSort(\"orderCount\",\"DESC\")' v-else>订单排序\r\n\t\t\t\t\t\t\t<image src='/static/images/sort2.png'></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<block v-for=\"(item,index) in recordList\" :key=\"index\">\r\n\t\t\t\t\t\t<view class='item acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t<view class=\"picTxt acea-row row-between-wrapper\">\r\n\t\t\t\t\t\t\t\t<view class='pictrue'>\r\n\t\t\t\t\t\t\t\t\t<image :src='item.avatar'></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class='text'>\r\n\t\t\t\t\t\t\t\t\t<view class='name line1'>{{item.nickname}}</view>\r\n\t\t\t\t\t\t\t\t\t<view>加入时间: {{item.time ? item.time.split(' ')[0] : ''}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"right\">\r\n\t\t\t\t\t\t\t\t<view><text class='num font-color'>{{item.childCount ? item.childCount : 0}}</text>人\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view><text class=\"num\">{{item.orderCount ? item.orderCount : 0}}</text>单</view>\r\n\t\t\t\t\t\t\t\t<view><text class=\"num\">{{item.numberCount ? item.numberCount : 0}}</text>元</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<Loading :loaded=\"status\" :loading=\"loadingList\"></Loading>\r\n\t\t\t\t\t<block v-if=\"recordList.length == 0 && isShow\">\r\n\t\t\t\t\t\t<emptyPage title=\"暂无推广人数～\"></emptyPage>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- #ifdef MP -->\r\n\t\t<!-- <authorize @onLoadFun=\"onLoadFun\" :isAuto=\"isAuto\" :isShowAuth=\"isShowAuth\" @authColse=\"authColse\"></authorize> -->\r\n\t\t<!-- #endif -->\r\n\t\t<!-- <home></home> -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tspreadPeople,\r\n\t\tspreadPeoCount\r\n\t} from '@/api/user.js';\r\n\timport {toLogin} from '@/libs/login.js';\r\n\timport {mapGetters} from \"vuex\";\r\n\timport emptyPage from '@/components/emptyPage.vue'\r\n\timport Loading from \"@/components/Loading\";\r\n\t// #ifdef MP\r\n\timport authorize from '@/components/Authorize';\r\n\t// #endif\r\n\timport home from '@/components/home';\r\n\timport {setThemeColor} from '@/utils/setTheme.js'\r\n\tconst app = getApp();\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tLoading,\r\n\t\t\temptyPage,\r\n\t\t\t// #ifdef MP\r\n\t\t\tauthorize,\r\n\t\t\t// #endif\r\n\t\t\thome\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tlimit: 20,\r\n\t\t\t\tkeyword: '',\r\n\t\t\t\tsort: '',\r\n\t\t\t\tisAsc: '',\r\n\t\t\t\tsortKey: '',\r\n\t\t\t\tgrade: 0,\r\n\t\t\t\tstatus: false,\r\n\t\t\t\tloadingList: false,\r\n\t\t\t\trecordList: [],\r\n\t\t\t\tpeopleData: {},\r\n\t\t\t\tisShow: false,\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false, //是否隐藏授权\r\n\t\t\t\ttheme:app.globalData.theme,\r\n\t\t\t\tbgColor:'#e93323'\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: mapGetters(['isLogin']),\r\n\t\tonLoad() {\r\n\t\t\tif (this.isLogin) {\r\n\t\t\t\tthis.userSpreadNewList();\r\n\t\t\t\tthis.spreadPeoCount();\r\n\t\t\t} else {\r\n\t\t\t\ttoLogin();\r\n\t\t\t}\r\n\t\t\tlet that = this;\r\n\t\t\tthat.bgColor = setThemeColor();\r\n\t\t\tuni.setNavigationBarColor({\r\n\t\t\t\tfrontColor: '#ffffff',\r\n\t\t\t\tbackgroundColor:that.bgColor,\r\n\t\t\t});\r\n\t\t},\r\n\t\tonShow: function() {\r\n\t\t\tif (this.is_show) this.userSpreadNewList();\r\n\t\t},\r\n\t\tonHide: function() {\r\n\t\t\tthis.is_show = true;\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tonLoadFun: function(e) {\r\n\t\t\t\tthis.userSpreadNewList();\r\n\t\t\t},\r\n\t\t\t// 授权关闭\r\n\t\t\tauthColse: function(e) {\r\n\t\t\t\tthis.isShowAuth = e\r\n\t\t\t},\r\n\t\t\tsetSort: function(sortKey, isAsc) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthat.isAsc = isAsc;\r\n\t\t\t\tthat.sort = sortKey + isAsc;\r\n\t\t\t\tthat.sortKey = sortKey;\r\n\t\t\t\tthat.page = 1;\r\n\t\t\t\tthat.limit = 20;\r\n\t\t\t\tthat.status = false;\r\n\t\t\t\tthat.$set(that, 'recordList', []);\r\n\t\t\t\tthat.userSpreadNewList();\r\n\t\t\t},\r\n\t\t\tsubmitForm: function() {\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.limit = 20;\r\n\t\t\t\tthis.status = false;\r\n\t\t\t\tthis.$set(this, 'recordList', []);\r\n\t\t\t\tthis.userSpreadNewList();\r\n\t\t\t},\r\n\r\n\t\t\tsetType: function(grade) {\r\n\t\t\t\tif (this.grade != grade) {\r\n\t\t\t\t\tthis.grade = grade;\r\n\t\t\t\t\tthis.page = 1;\r\n\t\t\t\t\tthis.limit = 20;\r\n\t\t\t\t\tthis.keyword = '';\r\n\t\t\t\t\tthis.sort = '';\r\n\t\t\t\t\tthis.isAsc = '';\r\n\t\t\t\t\tthis.status = false;\r\n\t\t\t\t\tthis.loadingList = false;\r\n\t\t\t\t\tthis.$set(this, 'recordList', []);\r\n\t\t\t\t\tthis.userSpreadNewList();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tspreadPeoCount() {\r\n\t\t\t\tspreadPeoCount().then(res => {\r\n\t\t\t\t\tthis.peopleData = res.data;\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tuserSpreadNewList: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet page = that.page;\r\n\t\t\t\tlet limit = that.limit;\r\n\t\t\t\tlet status = that.status;\r\n\t\t\t\tlet keyword = that.keyword;\r\n\t\t\t\tlet isAsc = that.isAsc;\r\n\t\t\t\tlet sortKey = that.sortKey;\r\n\t\t\t\tlet grade = that.grade;\r\n\t\t\t\tlet recordList = that.recordList;\r\n\t\t\t\tlet recordListNew = [];\r\n\t\t\t\tif (that.loadingList) return;\r\n\t\t\t\tif (status == true) return;\r\n\t\t\t\tspreadPeople({\r\n\t\t\t\t\tpage: page,\r\n\t\t\t\t\tlimit: limit,\r\n\t\t\t\t\tkeyword: keyword,\r\n\t\t\t\t\tgrade: grade,\r\n\t\t\t\t\tsortKey: sortKey,\r\n\t\t\t\t\tisAsc: isAsc\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tlet recordListData = res.data.list ? res.data.list : [];\r\n\t\t\t\t\tlet len = recordListData.length;\r\n\t\t\t\t\trecordListNew = recordList.concat(recordListData);\r\n\t\t\t\t\tthat.status = limit > len;\r\n\t\t\t\t\tthat.page = page + 1;\r\n\t\t\t\t\tthat.$set(that, 'recordList', recordListNew || []);\r\n\t\t\t\t\tthat.loadingList = false;\r\n\t\t\t\t\tif(that.recordList.length===0) that.isShow = true;\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\tonReachBottom: function() {\r\n\t\t\tthis.userSpreadNewList();\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.promoter-list .nav {\r\n\t\tbackground-color: #fff;\r\n\t\theight: 86rpx;\r\n\t\tline-height: 86rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #282828;\r\n\t\tborder-bottom: 1rpx solid #eee;\r\n\t\tborder-top-left-radius: 14rpx;\r\n\t\tborder-top-right-radius: 14rpx;\r\n\t\tmargin-top: -30rpx;\r\n\t}\r\n\t.promoterHeader{\r\n\t\t@include main_bg_color(theme);\r\n\t}\r\n\t.promoter-list .nav .item.on {\r\n\t\t@include main_color(theme);\r\n\t\t@include tab_border_bottom(theme);\r\n\t}\r\n\t.promoter-list .search {\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #fff;\r\n\t\theight: 100rpx;\r\n\t\tpadding: 0 24rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tborder-bottom-left-radius: 14rpx;\r\n\t\tborder-bottom-right-radius: 14rpx;\r\n\t}\r\n\r\n\t.promoter-list .search .input {\r\n\t\twidth: 592rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\ttext-align: center;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.promoter-list .search .input input {\r\n\t\theight: 100%;\r\n\t\tfont-size: 26rpx;\r\n\t\twidth: 610rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.promoter-list .search .input .placeholder {\r\n\t\tcolor: #bbb;\r\n\t}\r\n\r\n\t.promoter-list .search .input .iconfont {\r\n\t\tposition: absolute;\r\n\t\tright: 28rpx;\r\n\t\tcolor: #999;\r\n\t\tfont-size: 28rpx;\r\n\t\ttop: 50%;\r\n\t\ttransform: translateY(-50%);\r\n\t}\r\n\r\n\t.promoter-list .search .iconfont {\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #515151;\r\n\t\theight: 60rpx;\r\n\t\tline-height: 60rpx;\r\n\t}\r\n\r\n\t.promoter-list .list {\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\r\n\t.promoter-list .list .sortNav {\r\n\t\tbackground-color: #fff;\r\n\t\theight: 76rpx;\r\n\t\tborder-bottom: 1rpx solid #eee;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 28rpx;\r\n\t\tborder-top-left-radius: 14rpx;\r\n\t\tborder-top-right-radius: 14rpx;\r\n\t}\r\n\r\n\t.promoter-list .list .sortNav .sortItem {\r\n\t\ttext-align: center;\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.promoter-list .list .sortNav .sortItem image {\r\n\t\twidth: 24rpx;\r\n\t\theight: 24rpx;\r\n\t\tmargin-left: 6rpx;\r\n\t\tvertical-align: -3rpx;\r\n\t}\r\n\r\n\t.promoter-list .list .item {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-bottom: 1rpx solid #eee;\r\n\t\theight: 152rpx;\r\n\t\tpadding: 0 24rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.promoter-list .list .item .picTxt .pictrue {\r\n\t\twidth: 106rpx;\r\n\t\theight: 106rpx;\r\n\t\tborder-radius: 50%;\r\n\t}\r\n\r\n\t.promoter-list .list .item .picTxt .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 50%;\r\n\t\tborder: 3rpx solid #fff;\r\n\t\tbox-shadow: 0 0 10rpx #aaa;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.promoter-list .list .item .picTxt .text {\r\n\t\t// width: 304rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666;\r\n\t\tmargin-left: 14rpx;\r\n\t}\r\n\r\n\t.promoter-list .list .item .picTxt .text .name {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 13rpx;\r\n\t}\r\n\r\n\t.promoter-list .list .item .right {\r\n\t\ttext-align: right;\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.promoter-list .list .item .right .num {\r\n\t\tmargin-right: 7rpx;\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=bfc5ae28&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=bfc5ae28&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294179650\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}