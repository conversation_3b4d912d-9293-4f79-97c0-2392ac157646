{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/login/index.vue?383a", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/login/index.vue?618f", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/login/index.vue?d446", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/login/index.vue?ff7f", "uni-app:///pages/users/login/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/login/index.vue?fff5", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/login/index.vue?ae7b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "mixins", "data", "navList", "current", "account", "password", "<PERSON><PERSON>a", "formItem", "type", "logoUrl", "keyCode", "codeUrl", "codeVal", "isShowCode", "platform", "appLoginStatus", "appUserInfo", "appleLoginStatus", "appleUserInfo", "appleShow", "theme", "watch", "mounted", "onLoad", "uni", "success", "self", "methods", "appleLogin", "title", "provider", "timeout", "fail", "icon", "duration", "complete", "console", "appleLoginApi", "openId", "email", "identityToken", "content", "wxL<PERSON>in", "wxLoginGo", "url", "again", "VUE_APP_API_URL", "Date", "getCode", "getLogoImage", "that", "loginMobile", "phone", "spread_spid", "then", "catch", "register", "code", "navTap", "submit", "getUserInfo", "backUrl"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChBA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACoFnnB;AACA;AACA;AASA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AANA;AAOA;AAAA,eAEA;EACAC;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACAd;MACA;QACA;MACA;QACA;MACA;IACA;EACA;EACAe;IACA;IACA;EACA;EACAC;IACA;IACAC;MACAC;QACA;UACAC;QACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;MACAJ;QACAK;MACA;MACAL;QACAM;QACAC;QACAN;UACAD;YACAM;YACAL;cACAC;cACAA;YACA;YACAM;cACAR;cACAA;gBACAK;gBACAI;gBACAC;cACA;YACA;YACAC;cACAX;YACA;UACA;QACA;QACAQ;UACAR;UACAY;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACAC;QACAC;QACAC;MACA;QACA;UACA;QACA;QACA;MACA;QACAhB;QACAA;UACAK;UACAY;UACAhB;YACA;cACAW;YACA;cACAA;YACA;UACA;QACA;MACA;IACA;IACA;IACAM;MACA;MACA;MACA;MACAlB;QACAK;MACA;MACAL;QACAM;QACAL;UACA;UACAD;YACAM;YACAL;cACAD;cACAE;cACAA;cACAA;YACA;YACAM;cACAR;cACAA;gBACAK;gBACAI;gBACAC;cACA;YACA;YACAC;cACAX;YACA;UACA;QACA;QACAQ;UACAR;UACAA;YACAK;YACAI;YACAC;UACA;QACA;MACA;IACA;IACAS;MAAA;MACA;QACA;UACAnB;YACAoB;UACA;QACA;QACA;UACA;YACA;UACA;UACA;QACA;MACA;QACA;UACAf;QACA;MACA;IACA;IACAgB;MACA,eACAC,yBACA,kBACA,SACA,eACAC;IACA;IACAC;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACA;kBACAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MACA;MACA;QACAtB;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;MACA;QACAuB;QACA9C;QACA+C;QACA;MACA,GACAC;QACA;QACA;QACA;UACA;QACA;QACAJ;MACA,GACAK;QACAL;UACArB;QACA;MACA;IACA;IACA2B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAN;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA;kBACArB;gBACA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;kBACAA;gBACA;cAAA;gBAAA,IACAqB;kBAAA;kBAAA;gBAAA;gBAAA;kBACArB;gBACA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;kBACAA;gBACA;cAAA;gBAAA,IACAqB;kBAAA;kBAAA;gBAAA;gBAAA;kBACArB;gBACA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;kBACAA;gBACA;cAAA;gBACA;kBACAzB;kBACAE;kBACAD;kBACAgD;kBACA;gBACA,GACAC;kBACAJ;oBACArB;kBACA;kBACAqB;gBACA,GACAK;kBACAL;oBACArB;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA4B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAP;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA;kBACArB;gBACA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;kBACAA;gBACA;cAAA;gBACA;gBAAA;gBAAA,OACA,wCACAyB;kBACAJ;oBAAArB;kBAAA;kBACAqB;gBACA,GACAK;kBACA;oBACA1B;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA6B;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA9B;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;MACA;QACAzB;QACAC;QACAgD;MACA;QAAA;QACA;UACA;QACA;QACAH;MACA,GACAK;QACAL;UACArB;QACA;MACA;IACA;IACA+B;MAAA;MACA;MACA;QACA;QACA;QACA;UACAC;QACA;QACArC;UACAoB;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjbA;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/users/login/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/users/login/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=3ae7937c&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=3ae7937c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3ae7937c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/users/login/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=3ae7937c&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.current = 1\n    }\n    _vm.e1 = function ($event) {\n      _vm.current = 0\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<div class=\"login-wrapper\" :data-theme=\"theme\">\r\n\t\t<div class=\"shading\">\r\n\t\t\t<!-- <image :src=\"logoUrl\"/> -->\r\n\t\t\t<image :src=\"logoUrl\"/>\r\n\t\t\t<!-- <image src=\"/static/images/logo2.png\" v-if=\"!logoUrl\" /> -->\r\n\t\t</div>\r\n\t\t<div class=\"whiteBg\" v-if=\"formItem === 1\">\r\n\t\t\t<div class=\"list\" v-if=\"current !== 1\">\r\n\t\t\t\t<form @submit.prevent=\"submit\">\r\n\t\t\t\t\t<div class=\"item\">\r\n\t\t\t\t\t\t<div class=\"acea-row row-middle\">\r\n\t\t\t\t\t\t\t<image src=\"/static/images/phone_1.png\"  style=\"width: 24rpx; height: 34rpx;\"></image>\r\n\t\t\t\t\t\t\t<input type=\"number\" class=\"texts\" placeholder=\"输入手机号码\" v-model=\"account\" maxlength=\"11\" required/>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div class=\"item\">\r\n\t\t\t\t\t\t<div class=\"acea-row row-middle\">\r\n\t\t\t\t\t\t\t<image src=\"/static/images/code_2.png\" style=\"width: 28rpx; height: 32rpx;\"></image>\r\n\t\t\t\t\t\t\t<input type=\"password\" class=\"texts\" placeholder=\"填写登录密码\" maxlength=\"18\" v-model=\"password\" required />\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</form>\r\n\t\t\t</div>\r\n\t\t\t<div class=\"list\" v-if=\"current !== 0 || appLoginStatus || appleLoginStatus\">\r\n\t\t\t\t<div class=\"item\">\r\n\t\t\t\t\t<div class=\"acea-row row-middle\">\r\n\t\t\t\t\t\t<image src=\"/static/images/phone_1.png\" style=\"width: 24rpx; height: 34rpx;\"></image>\r\n\t\t\t\t\t\t<input type=\"number\" class=\"texts\" placeholder=\"输入手机号码\" v-model=\"account\" maxlength=\"11\"/>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"item\">\r\n\t\t\t\t\t<div class=\"acea-row row-middle\">\r\n\t\t\t\t\t\t<image src=\"/static/images/code_2.png\" style=\"width: 28rpx; height: 32rpx;\"></image>\r\n\t\t\t\t\t\t<input type=\"number\" placeholder=\"填写验证码\" class=\"codeIput\" v-model=\"captcha\" maxlength=\"6\" />\r\n\t\t\t\t\t\t<button class=\"code main_color\" :disabled=\"disabled\" :class=\"disabled === true ? 'on' : ''\" @click=\"code\">\r\n\t\t\t\t\t\t\t{{ text }}\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"item\" v-if=\"isShowCode\">\r\n\t\t\t\t\t<div class=\"acea-row row-middle\">\r\n\t\t\t\t\t\t<image src=\"/static/images/code_2.png\" style=\"width: 28rpx; height: 32rpx;\"></image>\r\n\t\t\t\t\t\t<input type=\"number\" placeholder=\"填写验证码\" class=\"codeIput\" v-model=\"codeVal\" maxlength=\"6\"/>\r\n\t\t\t\t\t\t<div class=\"code\" @click=\"again\"><img :src=\"codeUrl\" /></div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t<div class=\"logon bg_color\" @click=\"loginMobile\" v-if=\"current !== 0\">登录</div>\r\n\t\t\t<div class=\"logon bg_color\" @click=\"submit\" v-if=\"current === 0\">登录</div>\r\n\t\t\t<!-- #ifndef APP-PLUS -->\r\n\t\t\t<div class=\"tips\">\r\n\t\t\t\t<div v-if=\"current==0\" @click=\"current = 1\">快速登录</div>\r\n\t\t\t\t<div v-if=\"current==1\" @click=\"current = 0\">账号登录</div>\r\n\t\t\t</div>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<!-- #ifdef APP-PLUS -->\r\n\t\t\t<view class=\"appLogin\" v-if=\"!appLoginStatus && !appleLoginStatus\">\r\n\t\t\t\t<view class=\"hds\">\r\n\t\t\t\t\t<span class=\"line\"></span>\r\n\t\t\t\t\t<p>其他方式登录</p>\r\n\t\t\t\t\t<span class=\"line\"></span>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"btn-wrapper\">\r\n\t\t\t\t\t<view class=\"btn wx\" @click=\"wxLogin\">\r\n\t\t\t\t\t\t<span class=\"iconfont icon-s-weixindenglu1\"></span>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"btn mima\" v-if=\"current == 1\" @click=\"current =0\">\r\n\t\t\t\t\t\t<span class=\"iconfont icon-s-mimadenglu1\"></span>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"btn yanzheng\" v-if=\"current == 0\" @click=\"current =1\">\r\n\t\t\t\t\t\t<span class=\"iconfont icon-s-yanzhengmadenglu1\"></span>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"btn apple-btn\" @click=\"appleLogin\" v-if=\"appleShow\">\r\n\t\t\t\t\t\t<view class=\"iconfont icon-s-pingguo\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- #endif -->\r\n\t\t</div>\r\n\t\t<div class=\"bottom\"></div>\r\n\t</div>\r\n</template>\r\n<script>\r\n\timport dayjs from \"@/plugin/dayjs/dayjs.min.js\";\r\n\timport sendVerifyCode from \"@/mixins/SendVerifyCode\";\r\n\timport {\r\n\t\tloginH5,\r\n\t\tloginMobile,\r\n\t\tregisterVerify,\r\n\t\tregister,\r\n\t\t// getCodeApi,\r\n\t\tgetUserInfo\r\n\t} from \"@/api/user\";\r\n\tlet app = getApp();\r\n\timport attrs, {required,alpha_num,chs_phone} from \"@/utils/validate\";\r\n\timport {validatorDefaultCatch} from \"@/utils/dialog\";\r\n\timport {getLogo, appAuth, appleLogin} from \"@/api/public\";\r\n\timport {VUE_APP_API_URL} from \"@/utils\";\r\n\timport Routine from '@/libs/routine';\r\n\timport {Debounce} from '@/utils/validate.js'\r\n\tconst BACK_URL = \"login_back_url\";\r\n\r\n\texport default {\r\n\t\tname: \"Login\",\r\n\t\tmixins: [sendVerifyCode],\r\n\t\tdata: function() {\r\n\t\t\treturn {\r\n\t\t\t\tnavList: [\"快速登录\", \"账号登录\"],\r\n\t\t\t\tcurrent: 1,\r\n\t\t\t\taccount: \"\",\r\n\t\t\t\tpassword: \"\",\r\n\t\t\t\tcaptcha: \"\",\r\n\t\t\t\tformItem: 1,\r\n\t\t\t\ttype: \"login\",\r\n\t\t\t\tlogoUrl: \"\",\r\n\t\t\t\tkeyCode: \"\",\r\n\t\t\t\tcodeUrl: \"\",\r\n\t\t\t\tcodeVal: \"\",\r\n\t\t\t\tisShowCode: false,\r\n\t\t\t\tplatform: '',\r\n\t\t\t\tappLoginStatus: false, // 微信登录强制绑定手机号码状态\r\n\t\t\t\tappUserInfo: null, // 微信登录保存的用户信息\r\n\t\t\t\tappleLoginStatus: false, // 苹果登录强制绑定手机号码状态\r\n\t\t\t\tappleUserInfo: null,\r\n\t\t\t\tappleShow: false ,// 苹果登录版本必须要求ios13以上的\r\n\t\t\t\ttheme:app.globalData.theme,\r\n\t\t\t};\r\n\t\t},\r\n\t\twatch:{\r\n\t\t\tformItem:function(nval,oVal){\r\n\t\t\t\tif(nval == 1){\r\n\t\t\t\t\tthis.type = 'login'\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.type = 'register'\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted: function() {\r\n\t\t\tthis.getCode();\r\n\t\t\tthis.getLogoImage();\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tlet self = this\r\n\t\t\tuni.getSystemInfo({\r\n\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\tif (res.platform.toLowerCase() == 'ios' && res.system.split(' ')[1] >= 13) {\r\n\t\t\t\t\t\tself.appleShow = true\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 苹果登录\r\n\t\t\tappleLogin() {\r\n\t\t\t\tlet self = this\r\n\t\t\t\tthis.account = ''\r\n\t\t\t\tthis.captcha = ''\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '登录中'\r\n\t\t\t\t})\r\n\t\t\t\tuni.login({\r\n\t\t\t\t\tprovider: 'apple',\r\n\t\t\t\t\ttimeout: 10000,\r\n\t\t\t\t\tsuccess(loginRes) {\r\n\t\t\t\t\t\tuni.getUserInfo({\r\n\t\t\t\t\t\t\tprovider: 'apple',\r\n\t\t\t\t\t\t\tsuccess: function(infoRes) {\r\n\t\t\t\t\t\t\t\tself.appleUserInfo = infoRes.userInfo\r\n\t\t\t\t\t\t\t\tself.appleLoginApi()\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail() {\r\n\t\t\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '获取用户信息失败',\r\n\t\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tcomplete() {\r\n\t\t\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail(error) {\r\n\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\tconsole.log(error)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 苹果登录Api\r\n\t\t\tappleLoginApi() {\r\n\t\t\t\tlet self = this\r\n\t\t\t\tappleLogin({\r\n\t\t\t\t\topenId: self.appleUserInfo.openId,\r\n\t\t\t\t\temail: self.appleUserInfo.email == undefined ? '' :self.appleUserInfo.email,\r\n\t\t\t\t\tidentityToken: self.appleUserInfo.identityToken || ''\r\n\t\t\t\t}).then((res) => {\r\n\t\t\t\t\tthis.$store.commit(\"LOGIN\", {\r\n\t\t\t\t\t\t'token': res.data.token\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.getUserInfo(res.data);\r\n\t\t\t\t}).catch(error => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\tcontent: `错误信息${error}`,\r\n\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\tconsole.log('用户点击确定');\r\n\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// App微信登录\r\n\t\t\twxLogin:Debounce(function() {\r\n\t\t\t\tlet self = this\r\n\t\t\t\tthis.account = ''\r\n\t\t\t\tthis.captcha = ''\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '登录中'\r\n\t\t\t\t}) \r\n\t\t\t\tuni.login({\r\n\t\t\t\t\tprovider: 'weixin',\r\n\t\t\t\t\tsuccess: function(loginRes) {\r\n\t\t\t\t\t\t// 获取用户信息\r\n\t\t\t\t\t\tuni.getUserInfo({\r\n\t\t\t\t\t\t\tprovider: 'weixin',\r\n\t\t\t\t\t\t\tsuccess: function(infoRes) {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tself.appUserInfo = infoRes.userInfo\r\n\t\t\t\t\t\t\t\tself.appUserInfo.type = self.platform === 'ios' ? 'iosWx' : 'androidWx'\r\n\t\t\t\t\t\t\t\tself.wxLoginGo(self.appUserInfo)\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail() {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '获取用户信息失败',\r\n\t\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tcomplete() {\r\n\t\t\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail() {\r\n\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '登录失败',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}),\r\n\t\t\twxLoginGo(userInfo) {\r\n\t\t\t\tappAuth(userInfo).then(res => {\r\n\t\t\t\t\tif (res.data.type === 'register') {\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: '/pages/users/app_login/index?authKey='+res.data.key\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (res.data.type === 'login') {\r\n\t\t\t\t\t\tthis.$store.commit(\"LOGIN\", {\r\n\t\t\t\t\t\t\t'token': res.data.token\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthis.getUserInfo(res.data);\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch(res => {\r\n\t\t\t\t\tthis.$util.Tips({\r\n\t\t\t\t\t\ttitle: res\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tagain() {\r\n\t\t\t\tthis.codeUrl =\r\n\t\t\t\t\tVUE_APP_API_URL +\r\n\t\t\t\t\t\"/sms_captcha?\" +\r\n\t\t\t\t\t\"key=\" +\r\n\t\t\t\t\tthis.keyCode +\r\n\t\t\t\t\tDate.parse(new Date());\r\n\t\t\t},\r\n\t\t\tgetCode() {\r\n\t\t\t\tlet that = this\r\n\t\t\t},\r\n\t\t\tasync getLogoImage() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetLogo().then(res => {\r\n\t\t\t\t\tthat.logoUrl = res.data.logoUrl?res.data.logoUrl:'/static/images/logo2.png';\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tloginMobile:Debounce(function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (!that.account) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '请填写手机号码'\r\n\t\t\t\t});\r\n\t\t\t\tif (!/^1(3|4|5|7|8|9|6)\\d{9}$/i.test(that.account)) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '请输入正确的手机号码'\r\n\t\t\t\t});\r\n\t\t\t\tif (!that.captcha) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '请填写验证码'\r\n\t\t\t\t});\r\n\t\t\t\tif (!/^[\\w\\d]+$/i.test(that.captcha)) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '请输入正确的验证码'\r\n\t\t\t\t});\r\n\t\t\t\tloginMobile({\r\n\t\t\t\t\t\tphone: that.account,\r\n\t\t\t\t\t\tcaptcha: that.captcha,\r\n\t\t\t\t\t\tspread_spid: that.$Cache.get(\"spread\")\r\n\t\t\t\t\t\t// spread_spid: uni.getStorageSync('spid')\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tlet data = res.data;\r\n\t\t\t\t\t\tlet newTime = Math.round(new Date() / 1000);\r\n\t\t\t\t\t\tthis.$store.commit(\"LOGIN\", {\r\n\t\t\t\t\t\t\t'token': res.data.token\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthat.getUserInfo(data);\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(res => {\r\n\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: res\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t}),\r\n\t\t\tasync register() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (!that.account) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '请填写手机号码'\r\n\t\t\t\t});\r\n\t\t\t\tif (!/^1(3|4|5|7|8|9|6)\\d{9}$/i.test(that.account)) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '请输入正确的手机号码'\r\n\t\t\t\t});\r\n\t\t\t\tif (!that.captcha) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '请填写验证码'\r\n\t\t\t\t});\r\n\t\t\t\tif (!/^[\\w\\d]+$/i.test(that.captcha)) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '请输入正确的验证码'\r\n\t\t\t\t});\r\n\t\t\t\tif (!that.password) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '请填写密码'\r\n\t\t\t\t});\r\n\t\t\t\tif (!/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,16}$/i.test(that.password)) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '您输入的密码过于简单'\r\n\t\t\t\t});\r\n\t\t\t\tregister({\r\n\t\t\t\t\t\taccount: that.account,\r\n\t\t\t\t\t\tcaptcha: that.captcha,\r\n\t\t\t\t\t\tpassword: that.password,\r\n\t\t\t\t\t\tspread_spid: that.$Cache.get(\"spread\")\r\n\t\t\t\t\t\t// spread_spid: uni.getStorageSync('spid') || 0\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: res\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthat.formItem = 1;\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(res => {\r\n\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: res\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tasync code() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (!that.account) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '请填写手机号码'\r\n\t\t\t\t});\r\n\t\t\t\tif (!/^1(3|4|5|7|8|9|6)\\d{9}$/i.test(that.account)) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '请输入正确的手机号码'\r\n\t\t\t\t});\r\n\t\t\t\tif (that.formItem == 2) that.type = \"register\";\r\n\t\t\t\tawait registerVerify(that.account)\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tthat.$util.Tips({title:res.message});\r\n\t\t\t\t\t\tthat.sendCode();\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tnavTap: function(index) {\r\n\t\t\t\tthis.current = index;\r\n\t\t\t},\r\n\t\t\tsubmit:Debounce(function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (!that.account) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '请填写账号'\r\n\t\t\t\t});\r\n\t\t\t\tif (!/^[\\w\\d]{5,16}$/i.test(that.account)) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '请输入正确的账号'\r\n\t\t\t\t});\r\n\t\t\t\tif (!that.password) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '请填写密码'\r\n\t\t\t\t});\r\n\t\t\t\tloginH5({\r\n\t\t\t\t\t\taccount: that.account,\r\n\t\t\t\t\t\tpassword: that.password,\r\n\t\t\t\t\t\tspread_spid: that.$Cache.get(\"spread\")\r\n\t\t\t\t\t}).then(({data}) => {\r\n\t\t\t\t\t\tthis.$store.commit(\"LOGIN\", {\r\n\t\t\t\t\t\t\t'token': data.token\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthat.getUserInfo(data);\t\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(e => {\r\n\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: e\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t}),\r\n\t\t\tgetUserInfo(data){\r\n\t\t\t\tthis.$store.commit(\"SETUID\", data.uid); \r\n\t\t\t\tgetUserInfo().then(res => {\r\n\t\t\t\t\tthis.$store.commit(\"UPDATE_USERINFO\", res.data);\r\n\t\t\t\t\tlet backUrl = this.$Cache.get(BACK_URL) || \"/pages/index/index\";\r\n\t\t\t\t\tif (backUrl.indexOf('/pages/users/login/index') !== -1) { \r\n\t\t\t\t\t\tbackUrl = '/pages/index/index';\r\n\t\t\t\t\t}\r\n\t\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\t\turl: backUrl\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\tpage {\r\n\t\tbackground: #fff;\r\n\t}\r\n\t.appLogin {\r\n\t\tmargin-top: 60rpx;\r\n\t\r\n\t\t.hds {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: #B4B4B4;\r\n\t\r\n\t\t\t.line {\r\n\t\t\t\twidth: 68rpx;\r\n\t\t\t\theight: 1rpx;\r\n\t\t\t\tbackground: #CCCCCC;\r\n\t\t\t}\r\n\t\r\n\t\t\tp {\r\n\t\t\t\tmargin: 0 20rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\r\n\t\t.btn-wrapper {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tmargin-top: 30rpx;\r\n\t\r\n\t\t\t.btn {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\twidth: 68rpx;\r\n\t\t\t\theight: 68rpx;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t}\r\n\t\r\n\t\t\t.apple-btn {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tmargin-left: 30rpx;\r\n\t\t\t\tbackground: #000;\r\n\t\t\t\tborder-radius: 34rpx;\r\n\t\t\t\tfont-size: 40rpx;\r\n\t\r\n\t\t\t\t.icon-s-pingguo {\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\tfont-size: 40rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\r\n\t\t\t.iconfont {\r\n\t\t\t\tfont-size: 40rpx;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t}\r\n\t\r\n\t\t\t.wx {\r\n\t\t\t\tmargin-right: 30rpx;\r\n\t\t\t\tbackground-color: #61C64F;\r\n\t\t\t}\r\n\t\r\n\t\t\t.mima {\r\n\t\t\t\tbackground-color: #28B3E9;\r\n\t\t\t}\r\n\t\r\n\t\t\t.yanzheng {\r\n\t\t\t\tbackground-color: #F89C23;\r\n\t\t\t}\r\n\t\r\n\t\t}\r\n\t}\r\n\t\r\n\t.main_color{\r\n\t\t@include main_color(theme);\r\n\t}\r\n\t.bg_color{\r\n\t\t@include main_bg_color(theme);\r\n\t}\r\n\t.code img {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\t\r\n\t.acea-row.row-middle {\r\n\t\tinput {\r\n\t\t\tmargin-left: 20rpx;\r\n\t\t\tdisplay: block;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.login-wrapper {\r\n\t\tpadding: 30rpx;\r\n\t\r\n\t\t.shading {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\twidth: 100%;\r\n\t\r\n\t\t\t/* #ifdef APP-VUE */\r\n\t\t\tmargin-top: 50rpx;\r\n\t\t\t/* #endif */\r\n\t\t\t/* #ifndef APP-VUE */\r\n\t\r\n\t\t\tmargin-top: 200rpx;\r\n\t\t\t/* #endif */\r\n\t\r\n\t\r\n\t\t\timage {\r\n\t\t\t\twidth: 180rpx;\r\n\t\t\t\theight: 180rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\r\n\t\t.whiteBg {\r\n\t\t\tmargin-top: 100rpx;\r\n\t\r\n\t\t\t.list {\r\n\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\toverflow: hidden;\r\n\t\r\n\t\t\t\t.item {\r\n\t\t\t\t\tborder-bottom: 1px solid #F0F0F0;\r\n\t\t\t\t\tbackground: #fff;\r\n\t\r\n\t\t\t\t\t.row-middle {\r\n\t\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\tpadding: 16rpx 45rpx;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.texts{\r\n\t\t\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\t\theight: 80rpx;\r\n\t\t\t\t\t\t\tline-height: 80rpx;\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t\tinput {\r\n\t\t\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\t\theight: 80rpx;\r\n\t\t\t\t\t\t\tline-height: 80rpx;\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t}\r\n\t\r\n\t\t\t\t\t\t.code {\r\n\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\tright: 30rpx;\r\n\t\t\t\t\t\t\ttop: 50%;\r\n\t\t\t\t\t\t\tcolor: $theme-color;\r\n\t\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\t\ttransform: translateY(-50%);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\r\n\t\t\t.logon {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 86rpx;\r\n\t\t\t\tmargin-top: 80rpx;\r\n\t\t\t\tbackground-color: $theme-color;\r\n\t\t\t\tborder-radius: 120rpx;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t}\r\n\t\r\n\t\t\t.tips {\r\n\t\t\t\tmargin: 30rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tcolor: #999;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=3ae7937c&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=3ae7937c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294179305\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}