{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/promoter-order/index.vue?2a4a", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/promoter-order/index.vue?a5b1", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/promoter-order/index.vue?6ecf", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/promoter-order/index.vue?d51d", "uni-app:///pages/users/promoter-order/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/promoter-order/index.vue?4720", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/promoter-order/index.vue?5e5e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "authorize", "emptyPage", "home", "data", "page", "limit", "status", "recordList", "recordCount", "isAuto", "isShowAuth", "time", "theme", "bgColor", "computed", "onLoad", "that", "uni", "frontColor", "backgroundColor", "methods", "stringToDate", "onLoadFun", "auth<PERSON><PERSON><PERSON>", "getRecordOrderList", "i", "len", "recordListData", "newList", "onReachBottom"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACuDnnB;AACA;AACA;AAMA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AAAA,eACA;EACAC;IAEAC;IAEAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;EACAC;IACA;MACA;IACA;MACA;IACA;IACA;IACAC;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACApB;QACAC;MACA;QACA;QACA;QACA;QAAA,2BACAoB;UACAC;UACA;UACA;UACA;YACAV;UACA;YACAW;cACA;gBACAC;cACA;YACA;YACAZ;UACA;UACAA;QAAA;QAdA;UAAA;QAeA;QAAA;QACAA;QACAA;QACAA;MACA;IACA;EACA;EACAa;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1JA;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/users/promoter-order/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/users/promoter-order/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=6c1cbc96&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=6c1cbc96&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6c1cbc96\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/users/promoter-order/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=6c1cbc96&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.recordList.length\n  var g1 = _vm.recordList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :data-theme=\"theme\">\r\n\t\t<view class=\"promoter-order\">\r\n\t\t\t<view class='promoterHeader'>\r\n\t\t\t\t<view class='headerCon acea-row row-between-wrapper'>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<view class='name'>累积推广订单</view>\r\n\t\t\t\t\t\t<view><text class='num'>{{recordCount || 0}}</text>单</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='iconfont icon-2'></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class='list pad30' v-if=\"recordList.length>0\">\r\n\t\t\t\t<block v-for=\"(item,index) in recordList\" :key=\"index\">\r\n\t\t\t\t\t<view class='item'>\r\n\t\t\t\t\t\t<view class='title acea-row row-column row-center'>\r\n\t\t\t\t\t\t\t<view class='data'>{{item.time}}</view>\r\n\t\t\t\t\t\t\t<view>本月累计推广订单：{{item.count || 0}}单</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='listn'>\r\n\t\t\t\t\t\t\t<block v-for=\"(child,indexn) in item.child\" :key=\"indexn\">\r\n\t\t\t\t\t\t\t\t<view class='itenm borRadius14'>\r\n\t\t\t\t\t\t\t\t\t<view class='top acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t\t\t\t<view class='pictxt acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class='pictrue'>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<image :src='child.avatar'></image>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class='text line1'>{{child.nickname}}</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class='money'>返佣：<text class='font-color'>￥{{child.number}}</text></view>\r\n\t\t\t\t\t\t\t\t\t<!-- \t<view class='money' v-if=\"child.type == 'brokerage'\">返佣：<text class='font-color'>￥{{child.number}}</text></view>\r\n\t\t\t\t\t\t\t\t\t\t<view class='money' v-else>暂未返佣：<text class='font-color'>￥{{child.number}}</text></view> -->\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class='bottom'>\r\n\t\t\t\t\t\t\t\t\t\t<view><text class='name'>订单编号：</text>{{child.orderId}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view><text class='name'>下单时间：</text>{{child.time}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"recordList.length == 0\">\r\n\t\t\t\t<emptyPage title=\"暂无推广订单～\"></emptyPage>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- #ifdef MP -->\r\n\t\t<!-- <authorize @onLoadFun=\"onLoadFun\" :isAuto=\"isAuto\" :isShowAuth=\"isShowAuth\" @authColse=\"authColse\"></authorize> -->\r\n\t\t<!-- #endif -->\r\n\t\t<!-- <home></home> -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {spreadOrder} from '@/api/user.js';\r\n\timport {toLogin} from '@/libs/login.js';\r\n\timport {mapGetters} from \"vuex\";\r\n\t// #ifdef MP\r\n\timport authorize from '@/components/Authorize';\r\n\t// #endif\r\n\timport emptyPage from '@/components/emptyPage.vue'\r\n\timport home from '@/components/home';\r\n\timport {setThemeColor} from '@/utils/setTheme.js'\r\n\tconst app = getApp();\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\t// #ifdef MP\r\n\t\t\tauthorize,\r\n\t\t\t// #endif\r\n\t\t\temptyPage,\r\n\t\t\thome\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tlimit: 20,\r\n\t\t\t\tstatus: false,\r\n\t\t\t\trecordList: [],\r\n\t\t\t\trecordCount: 0,\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false,//是否隐藏授权\r\n\t\t\t\ttime: 0,\r\n\t\t\t\ttheme:app.globalData.theme,\r\n\t\t\t\tbgColor:'#e93323'\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: mapGetters(['isLogin']),\r\n\t\tonLoad() {\r\n\t\t\tif (this.isLogin) {\r\n\t\t\t\tthis.getRecordOrderList();\r\n\t\t\t} else {\r\n\t\t\t\ttoLogin();\r\n\t\t\t}\r\n\t\t\tlet that = this;\r\n\t\t\tthat.bgColor = setThemeColor();\r\n\t\t\tuni.setNavigationBarColor({\r\n\t\t\t\tfrontColor: '#ffffff',\r\n\t\t\t\tbackgroundColor:that.bgColor,\r\n\t\t\t});\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tstringToDate : function(data){\r\n\t\t\t\tlet str = data.replace(/-/g,'/');\r\n\t\t\t\tlet date = new Date(str);\r\n\t\t\t\treturn data;\r\n\t\t\t },\r\n\t\t\tonLoadFun() {\r\n\t\t\t\tthis.getRecordOrderList();\r\n\t\t\t},\r\n\t\t\t// 授权关闭\r\n\t\t\tauthColse: function(e) {\r\n\t\t\t\tthis.isShowAuth = e\r\n\t\t\t},\r\n\t\t\tgetRecordOrderList: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet page = that.page;\r\n\t\t\t\tlet limit = that.limit;\r\n\t\t\t\tlet status = that.status;\r\n\t\t\t\tlet recordList = that.recordList;\r\n\t\t\t\tlet newList = [];\r\n\t\t\t\tif (status == true) return;\r\n\t\t\t\tspreadOrder({\r\n\t\t\t\t\tpage: page,\r\n\t\t\t\t\tlimit: limit\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tlet recordListData = res.data.list ? res.data.list : [];\r\n\t\t\t\t\t// 每页返回的总条数；\r\n\t\t\t\t\tlet len = 0;\r\n\t\t\t\t\tfor(let i = 0;i<recordListData.length;i++) {\r\n\t\t\t\t\t\tlen = len + recordListData[i].child.length;\r\n\t\t\t\t\t\tlet str = recordListData[i].time.replace(/-/g,'/');\r\n\t\t\t\t\t\tlet date = new Date(str).getTime();\r\n\t\t\t\t\t\tif(that.time === date){\r\n\t\t\t\t\t\t\tthat.$set(that.recordList[i],'child',that.recordList[i].child.concat(recordListData[i].child));\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\trecordListData.forEach((item,index)=>{\r\n\t\t\t\t\t\t\t\tif(recordListData[i]==item){\r\n\t\t\t\t\t\t\t\t\tnewList.push(item);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tthat.$set(that, 'recordList', recordList.concat(newList));\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthat.time = date;\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthat.recordCount = res.data.count || 0;\r\n\t\t\t\t\tthat.status = limit > len;\r\n\t\t\t\t\tthat.page = page + 1;\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\tonReachBottom() {\r\n\t\t\tthis.getRecordOrderList()\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.promoter-order .list .item .title {\r\n\t\theight: 133rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\t.promoterHeader{\r\n\t\t@include main_bg_color(theme);\r\n\t}\r\n\t.promoter-order .list .item .title .data {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #282828;\r\n\t\tmargin-bottom: 5rpx;\r\n\t}\r\n\r\n\t.promoter-order .list .item .listn .itenm {\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\r\n\t.promoter-order .list .item .listn .itenm~.itenm {\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\r\n\t.promoter-order .list .item .listn .itenm .top {\r\n\t\tpadding: 0 24rpx;\r\n\t\tborder-bottom: 1rpx solid #eee;\r\n\t\theight: 100rpx;\r\n\t}\r\n\r\n\t.promoter-order .list .item .listn .itenm .top .pictxt {\r\n\t\twidth: 320rpx;\r\n\t}\r\n\r\n\t.promoter-order .list .item .listn .itenm .top .pictxt .text {\r\n\t\twidth: 230rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #282828;\r\n\t}\r\n\r\n\t.promoter-order .list .item .listn .itenm .top .pictxt .pictrue {\r\n\t\twidth: 66rpx;\r\n\t\theight: 66rpx;\r\n\t}\r\n\r\n\t.promoter-order .list .item .listn .itenm .top .pictxt .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 50%;\r\n\t\tborder: 3rpx solid #fff;\r\n\t\tbox-sizing: border-box;\r\n\t\tbox-shadow: 0 0 15rpx #aaa;\r\n\t}\r\n\r\n\t.promoter-order .list .item .listn .itenm .top .money {\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t.promoter-order .list .item .listn .itenm .bottom {\r\n\t\tpadding: 20rpx 24rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666;\r\n\t\tline-height: 1.6;\r\n\t}\r\n\r\n\t.promoter-order .list .item .listn .itenm .bottom .name {\r\n\t\tcolor: #999;\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=6c1cbc96&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=6c1cbc96&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294179625\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}