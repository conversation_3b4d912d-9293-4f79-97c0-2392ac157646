{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/wechat_login/index.vue?f3ba", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/wechat_login/index.vue?9bca", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/wechat_login/index.vue?8658", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/wechat_login/index.vue?3d20", "uni-app:///pages/users/wechat_login/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/wechat_login/index.vue?6b1a", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/wechat_login/index.vue?4f54"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isUp", "phone", "statusBarHeight", "isHome", "isPhoneBox", "logoUrl", "code", "auth<PERSON><PERSON>", "options", "userInfo", "codeNum", "theme", "getPhoneContent", "components", "mobileLogin", "atModel", "onLoad", "methods", "back", "uni", "home", "url", "modelCancel", "maskClose", "bindPhoneClose", "title", "icon", "tab", "getUserInfo", "that", "getUserProfile", "Routine", "then", "self", "catch", "getWxUser", "token", "duration"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC8CnnB;AACA;AACA;AAMA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAdA;AACA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAcA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;IACAC;EACA;EACAC;IAAA;IACA;MACA;IACA;IACA;IAsCA;IACA;IACA;IACA;IACA;IACA;IACA;EAEA;;EACAC;IACAC;MACAC;IACA;IACAC;MACAD;QACAE;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IAAA,CACA;IACAC;MACA;QACA;QACA;UACAC;UACAC;QACA;UACAC;QACA;MACA;QACA;MACA;IAEA;IAEA;AACA;AACA;IACAC;MACA;MACA;QACAT;QACAU;QACAA;QACAA;UACAJ;UACAC;QACA;UACAC;QACA;MACA;IACA;IACAG;MACA;MACAX;QACAM;MACA;MACAM;QACAA,2BACAC;UACAC;QAEA,GACAC;UACAf;QACA;MACA,GACAe;QACAf;MACA;IACA;IAEAgB;MACA;MACA;MACA1B;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAsB,uDACAC;QACAC;QACA;UACAd;UACAc;QACA;QACA;UACAd;UACAc;YACAG;UACA;UACAH;UACAA;UACA;YACA;UACA;;UACAA;YACAR;YACAC;UACA;YACAC;UACA;QACA;MACA,GACAO;QACAf;QACAA;UACAM;UACAC;UACAW;QACA;MACA;IACA;EAqDA;AACA;AAAA,2B;;;;;;;;;;;;;ACzSA;AAAA;AAAA;AAAA;AAA0oC,CAAgB,gnCAAG,EAAC,C;;;;;;;;;;;ACA9pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/users/wechat_login/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/users/wechat_login/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=653c60f3&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/users/wechat_login/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=653c60f3&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\" :data-theme=\"theme\">\r\n\t\t<view class=\"system-height\" :style=\"{height:statusBarHeight}\"></view>\r\n\t\t<!-- #ifdef MP -->\r\n\t\t<view class=\"title-bar\" style=\"height: 43px;\">\r\n\t\t\t<view class=\"icon\" @click=\"back\" v-if=\"!isHome\">\r\n\t\t\t\t<image src=\"../static/left.png\"></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"icon\" @click=\"home\" v-else>\r\n\t\t\t\t<image src=\"../static/home.png\"></image>\r\n\t\t\t</view>\r\n\t\t\t账户登录\r\n\t\t</view>\r\n\t\t<!-- #endif -->\r\n\t\t<view class=\"wechat_login\">\r\n\t\t\t<view class=\"img\">\r\n\t\t\t\t<image src=\"../static/wechat_login.png\" mode=\"widthFix\"></image> \r\n\t\t\t</view>\r\n\t\t\t<view class=\"btn-wrapper\">\r\n\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t<button hover-class=\"none\" @click=\"wechatLogin\" class=\"bg-green btn1\">登录</button>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<!-- #ifdef MP -->\r\n\t\t\t\t<button hover-class=\"none\" @tap=\"getUserProfile\" class=\"bg-green btn1\">登录</button>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<!-- <button hover-class=\"none\" @click=\"isUp = true\" class=\"btn2\">手机号登录</button> -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<block v-if=\"isUp\">\r\n\t\t\t<mobileLogin :isUp=\"isUp\" @close=\"maskClose\" :authKey=\"authKey\" @wechatPhone=\"wechatPhone\"></mobileLogin>\r\n\t\t</block>\r\n\t\t<atModel v-if=\"isPhoneBox\" \r\n\t\t:userPhoneType=\"true\" \r\n\t\t:isPhoneBox=\"isPhoneBox\"  \r\n\t\t:authKey=\"authKey\" \r\n\t\t:content=\"getPhoneContent\"\r\n\t\t @closeModel=\"bindPhoneClose\" \r\n\t\t @confirmModel=\"confirmModel\"></atModel>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tconst app = getApp();\r\n\tlet statusBarHeight = uni.getSystemInfoSync().statusBarHeight + 'px';\r\n\timport mobileLogin from '@/components/login_mobile/index.vue'\r\n\timport atModel from '@/components/accredit/index.vue'\r\n\timport {mapGetters} from \"vuex\";\r\n\timport {getLogo,getUserPhone} from '@/api/public';\r\n\timport {\r\n\t\tLOGO_URL,\r\n\t\tEXPIRES_TIME,\r\n\t\tUSER_INFO,\r\n\t\tSTATE_R_KEY\r\n\t} from '@/config/cache';\r\n\timport {getUserInfo,spread} from '@/api/user.js'\r\n\timport Routine from '@/libs/routine';\r\n\timport wechat from \"@/libs/wechat\";\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tisUp: false,\r\n\t\t\t\tphone: '',\r\n\t\t\t\tstatusBarHeight: statusBarHeight,\r\n\t\t\t\tisHome: false,\r\n\t\t\t\tisPhoneBox: false,\r\n\t\t\t\tlogoUrl: '',\r\n\t\t\t\tcode: '',\r\n\t\t\t\tauthKey: '',\r\n\t\t\t\toptions: '',\r\n\t\t\t\tuserInfo: {},\r\n\t\t\t\tcodeNum: 0,\r\n\t\t\t\ttheme:app.globalData.theme,\r\n\t\t\t\tgetPhoneContent:'申请获取您的手机号用于注册，完成后可使用商城更多功能'\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomponents: {\r\n\t\t\tmobileLogin,\r\n\t\t\tatModel\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\tgetLogo().then(res => {\r\n\t\t\t\tthis.logoUrl = res.data.logoUrl\r\n\t\t\t})\r\n\t\t\tlet that = this\r\n\t\t\t// #ifdef H5\r\n\t\t\tdocument.body.addEventListener(\"focusout\", () => {\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tconst scrollHeight = document.documentElement.scrollTop || document.body.scrollTop ||\r\n\t\t\t\t\t\t0;\r\n\t\t\t\t\twindow.scrollTo(0, Math.max(scrollHeight - 1, 0));\r\n\t\t\t\t}, 100);\r\n\t\t\t});\r\n\t\t\tconst {\r\n\t\t\t\tcode,\r\n\t\t\t\tstate,\r\n\t\t\t\tscope\r\n\t\t\t} = options;\r\n\t\t\tthis.options = options\r\n\t\t\t// 获取确认授权code\r\n\t\t\tthis.code = code || ''\r\n\t\t\t//if(!code) location.replace(decodeURIComponent(decodeURIComponent(option.query.back_url)));\r\n\t\t\tif (code && this.options.scope !== 'snsapi_base') {\r\n\t\t\t\tlet spread = app.globalData.spread ? app.globalData.spread : 0;\r\n\t\t\t\t//公众号授权登录回调 wechatAuth(code, Cache.get(\"spread\"), loginType)\r\n\t\t\t\twechat.auth(code, spread).then(res => {\r\n\t\t\t\t\tif (res.type === 'register') {\r\n\t\t\t\t\t\tthis.authKey = res.key;\r\n\t\t\t\t\t\tthis.isUp = true\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (res.type === 'login') {\r\n\t\t\t\t\t\tthis.$store.commit('LOGIN', {\r\n\t\t\t\t\t\t\ttoken: res.token\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthis.$store.commit(\"SETUID\", res.uid);\r\n\t\t\t\t\t\tthis.getUserInfo();\r\n\t\t\t\t\t\t//this.wechatPhone();\r\n\t\t\t\t\t\t//location.replace(decodeURIComponent(decodeURIComponent(option.query.back_url)));\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch(error => {});\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t\tlet pages = getCurrentPages();\r\n\t\t\t// let prePage = pages[pages.length - 2];\r\n\t\t\t// if (prePage.route == 'pages/order_addcart/order_addcart') {\r\n\t\t\t// \tthis.isHome = true\r\n\t\t\t// } else {\r\n\t\t\t// \tthis.isHome = false\r\n\t\t\t// }\r\n\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tback() {\r\n\t\t\t\tuni.navigateBack();\r\n\t\t\t},\r\n\t\t\thome() {\r\n\t\t\t\tuni.switchTab({\r\n\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tmodelCancel(){\r\n\t\t\t\tthis.isPhoneBox = false;\r\n\t\t\t},\r\n\t\t\t// 弹窗关闭\r\n\t\t\tmaskClose() {\r\n\t\t\t\t// this.isUp = false  //点击模态框会关闭登录弹框，防止用户误触而关闭\r\n\t\t\t},\r\n\t\t\tbindPhoneClose(data) {\r\n\t\t\t\tif (data.isStatus) {\r\n\t\t\t\t\tthis.isPhoneBox = false\r\n\t\t\t\t\tthis.$util.Tips({\r\n\t\t\t\t\t\ttitle: '登录成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\ttab: 3\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.isPhoneBox = false\r\n\t\t\t\t}\r\n\r\n\t\t\t},\r\n\t\t\t// #ifdef MP\r\n\t\t\t/**\r\n\t\t\t * 获取个人用户信息\r\n\t\t\t */\r\n\t\t\tgetUserInfo: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetUserInfo().then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthat.userInfo = res.data\r\n\t\t\t\t\tthat.$store.commit(\"UPDATE_USERINFO\", res.data);\r\n\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\ttitle: '登录成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\ttab: 3\r\n\t\t\t\t\t})\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetUserProfile() {\r\n\t\t\t\tlet self = this;\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '正在登录中'\r\n\t\t\t\t});\r\n\t\t\t\tRoutine.getUserProfile().then(res => {\r\n\t\t\t\t\tRoutine.getCode()\r\n\t\t\t\t\t\t.then(code => {\r\n\t\t\t\t\t\t\tself.getWxUser(code, res);\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(res => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t\t.catch(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tgetWxUser(code, res) {\r\n\t\t\t\tlet self = this\r\n\t\t\t\tlet userInfo = res.userInfo;\r\n\t\t\t\tuserInfo.code = code;\r\n\t\t\t\tuserInfo.spread_spid = app.globalData.spread; //获取推广人ID\r\n\t\t\t\tuserInfo.avatar = userInfo.userInfo.avatarUrl;\r\n\t\t\t\tuserInfo.city = userInfo.userInfo.city;\r\n\t\t\t\tuserInfo.country = userInfo.userInfo.country;\r\n\t\t\t\tuserInfo.nickName = userInfo.userInfo.nickName;\r\n\t\t\t\tuserInfo.province = userInfo.userInfo.province;\r\n\t\t\t\tuserInfo.sex = userInfo.userInfo.gender;\r\n\t\t\t\tuserInfo.type = 'routine'\r\n\t\t\t\tRoutine.authUserInfo(userInfo.code, userInfo)\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tself.authKey = res.data.key;\r\n\t\t\t\t\t\tif (res.data.type === 'register') {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tself.isPhoneBox = true\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (res.data.type === 'login') {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tself.$store.commit('LOGIN', {\r\n\t\t\t\t\t\t\t\ttoken: res.data.token\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tself.$store.commit(\"SETUID\", res.data.uid);\r\n\t\t\t\t\t\t\tself.getUserInfo();\r\n\t\t\t\t\t\t\tif(app.globalData.spread){\r\n\t\t\t\t\t\t\t\tspread(app.globalData.spread).then(res => {}) //登录成功后读取spread绑定分销关系\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tself.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: res,\r\n\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\ttab: 3\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(res => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res,\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef H5\r\n\t\t\t// 获取url后面的参数\r\n\t\t\tgetQueryString(name) {\r\n\t\t\t\tvar reg = new RegExp(\"(^|&)\" + name + \"=([^&]*)(&|$)\", \"i\");\r\n\t\t\t\tvar reg_rewrite = new RegExp(\"(^|/)\" + name + \"/([^/]*)(/|$)\", \"i\");\r\n\t\t\t\tvar r = window.location.search.substr(1).match(reg);\r\n\t\t\t\tvar q = window.location.pathname.substr(1).match(reg_rewrite);\r\n\t\t\t\tif (r != null) {\r\n\t\t\t\t\treturn unescape(r[2]);\r\n\t\t\t\t} else if (q != null) {\r\n\t\t\t\t\treturn unescape(q[2]);\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn null;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 公众号登录\r\n\t\t\twechatLogin() {\r\n\t\t\t\tif (!this.code && this.options.scope !== 'snsapi_base') {\r\n\t\t\t\t\tthis.$wechat.oAuth('snsapi_userinfo', '/pages/users/wechat_login/index');\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// if (this.authKey) {\r\n\t\t\t\t\t// \tthis.isUp = true;\r\n\t\t\t\t\t// }\r\n\t\t\t\t\tthis.isUp = true;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 输入手机号后的回调\r\n\t\t\twechatPhone() {\r\n\t\t\t\tthis.$Cache.clear('snsapiKey');\r\n\t\t\t\tif (this.options.back_url) {\r\n\t\t\t\t\tlet url = uni.getStorageSync('snRouter');\r\n\t\t\t\t\turl = url.indexOf('/pages/index/index') != -1 ? '/' : url;\r\n\t\t\t\t\tif (url.indexOf('/pages/users/wechat_login/index') !== -1) {\r\n\t\t\t\t\t\turl = '/';\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!url) {\r\n\t\t\t\t\t\turl = '/pages/index/index';\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.isUp = false\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '登录成功',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\tsetTimeout(res => {\r\n\t\t\t\t\t\tlocation.href = url\r\n\t\t\t\t\t}, 800)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\tpage {\r\n\t\tbackground: #fff;\r\n\t\theight: 100%;\r\n\t}\r\n    \r\n\t.page {\r\n\t\tbackground: #fff;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.wechat_login {\r\n\t\tpadding: 72rpx 34rpx;\r\n\r\n\t\t.img image {\r\n\t\t\twidth: 100%;\r\n\t\t}\r\n\r\n\t\t.btn-wrapper {\r\n\t\t\tmargin-top: 86rpx;\r\n\t\t\tpadding: 0 66rpx;\r\n\r\n\t\t\tbutton {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 86rpx;\r\n\t\t\t\tline-height: 86rpx;\r\n\t\t\t\tmargin-bottom: 40rpx;\r\n\t\t\t\tborder-radius: 120rpx;\r\n\t\t\t\tfont-size: 30rpx;\r\n\r\n\t\t\t\t&.btn1 {\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&.btn2 {\r\n\t\t\t\t\tcolor: #666666;\r\n\t\t\t\t\tborder: 1px solid #666666;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.title-bar {\r\n\t\tposition: relative;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tfont-size: 36rpx;\r\n\t}\r\n\r\n\t.icon {\r\n\t\tposition: absolute;\r\n\t\tleft: 30rpx;\r\n\t\ttop: 0;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\twidth: 86rpx;\r\n\t\theight: 86rpx;\r\n\r\n\t\timage {\r\n\t\t\twidth: 50rpx;\r\n\t\t\theight: 50rpx;\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294178879\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}