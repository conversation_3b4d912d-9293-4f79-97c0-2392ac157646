{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_spread_money/index.vue?7ed3", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_spread_money/index.vue?0ac2", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_spread_money/index.vue?1422", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_spread_money/index.vue?9ba8", "uni-app:///pages/users/user_spread_money/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_spread_money/index.vue?0cf5", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_spread_money/index.vue?7c96"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "authorize", "emptyPage", "home", "filters", "statusFilter", "data", "name", "type", "page", "limit", "recordList", "recordType", "statuss", "isAuto", "isShowAuth", "extractCount", "theme", "commissionCount", "bgColor", "computed", "onLoad", "that", "uni", "frontColor", "backgroundColor", "onShow", "title", "icon", "duration", "mask", "success", "setTimeout", "delta", "methods", "onLoadFun", "auth<PERSON><PERSON><PERSON>", "getList", "recordListNew", "getRecordList", "onReachBottom"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtDA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACkFnnB;AAIA;AAGA;AAQA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AAAA,eACA;EACAC;IAEAC;IAEAC;IACAC;EACA;EACAC;IACAC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;EACAC;IACA;MACA;MACA;MACA;IACA;MACA;IACA;IACA;IACAC;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;MACAH;QACAI;MACA;MACA;MACA;MACA;IACA;MACAJ;QACAI;MACA;MACA;MACA;MACA;IACA;MACAJ;QACAI;QACAC;QACAC;QACAC;QACAC;UACAC;YAEAT;cACAU;YACA;UAMA;QACA;MACA;IACA;EAEA;EACAC;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;QACA5B;QACAC;MACA;QACA;QACA;QACA4B;QACAhB;QACAA;QACAA;MACA;IACA;IACAiB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA9B;QACAC;MACA;QACA;UACA;UACA;UACA4B;UACAhB;UACAA;UACAA;QACA;MACA;IACA;EACA;EACAkB;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjPA;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/users/user_spread_money/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/users/user_spread_money/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=9de6df8a&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=9de6df8a&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"9de6df8a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/users/user_spread_money/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=9de6df8a&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l1 =\n    _vm.recordType == 4\n      ? _vm.__map(_vm.recordList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var g0 = _vm.recordList.length\n          var l0 =\n            g0 > 0\n              ? _vm.__map(item.list, function (child, indexn) {\n                  var $orig = _vm.__get_orig(child)\n                  var f0 = _vm._f(\"statusFilter\")(child.status)\n                  return {\n                    $orig: $orig,\n                    f0: f0,\n                  }\n                })\n              : null\n          return {\n            $orig: $orig,\n            g0: g0,\n            l0: l0,\n          }\n        })\n      : null\n  var g1 = _vm.recordType == 4 ? _vm.recordList.length : null\n  var l2 = !(_vm.recordType == 4)\n    ? _vm.__map(_vm.recordList, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g2 = _vm.recordList.length\n        return {\n          $orig: $orig,\n          g2: g2,\n        }\n      })\n    : null\n  var g3 = !(_vm.recordType == 4) ? _vm.recordList.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l1: l1,\n        g1: g1,\n        l2: l2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :data-theme=\"theme\">\r\n\t\t<view class='commission-details'>\r\n\t\t\t<view class='promoterHeader'>\r\n\t\t\t\t<view class='headerCon acea-row row-between-wrapper'>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<view class='name'>{{name}}</view>\r\n\t\t\t\t\t\t<view class='money' v-if=\"recordType == 4\">￥<text class='num'>{{extractCount}}</text></view>\r\n\t\t\t\t\t\t<view class='money' v-else>￥<text class='num'>{{commissionCount}}</text></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='iconfont icon-jinbi1'></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class='sign-record' v-if=\"recordType == 4\">\r\n\t\t\t\t<block v-for=\"(item,index) in recordList\" :key=\"index\" v-if=\"recordList.length>0\">\r\n\t\t\t\t\t<view class='list pad30'>\r\n\t\t\t\t\t\t<view class='item'>\r\n\t\t\t\t\t\t\t<view class='data'>{{item.date}}</view>\r\n\t\t\t\t\t\t\t<view class='listn borRadius14'>\r\n\t\t\t\t\t\t\t\t<block v-for=\"(child,indexn) in item.list\" :key=\"indexn\">\r\n\t\t\t\t\t\t\t\t\t<view class='itemn acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class='name line1'>{{child.status | statusFilter}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view>{{child.createTime}}</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class='num font_color' v-if=\"child.status == -1\">+{{child.extractPrice}}\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class='num' v-else>-{{child.extractPrice}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<!-- <view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class='name line1'>{{child.status === -1 ? '提现失败' : '提现成功'}}<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tv-show=\"child.status === -1\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle=\"font-size: 12px;color: red;\">{{'('+child.failMsg+')'}}</span>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view>{{child.createTime}}</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class='num font-color' v-if=\"child.status == -1\">+{{child.extractPrice}}\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class='num' v-else>-{{child.extractPrice}}</view> -->\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<view v-if=\"recordList.length == 0\">\r\n\t\t\t\t\t<emptyPage title='暂无提现记录~'></emptyPage>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class='sign-record' v-else>\r\n\t\t\t\t<block v-for=\"(item,index) in recordList\" :key=\"index\" v-if=\"recordList.length>0\">\r\n\t\t\t\t\t<view class='list pad30'>\r\n\t\t\t\t\t\t<view class='item'>\r\n\t\t\t\t\t\t\t<view class='data'>{{item.date}}</view>\r\n\t\t\t\t\t\t\t<view class='listn borRadius14'>\r\n\t\t\t\t\t\t\t\t<block v-for=\"(child,indexn) in item.list\" :key=\"indexn\">\r\n\t\t\t\t\t\t\t\t\t<view class='itemn acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class='name line1'>{{child.title}}</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view>{{child.updateTime}}</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class='num font_color' v-if=\"child.type == 1\">+{{child.price}}\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class='num' v-else>-{{child.price}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<view v-if=\"recordList.length == 0\">\r\n\t\t\t\t\t<emptyPage title='暂无佣金记录~'></emptyPage>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- #ifdef MP -->\r\n\t\t<!-- <authorize @onLoadFun=\"onLoadFun\" :isAuto=\"isAuto\" :isShowAuth=\"isShowAuth\" @authColse=\"authColse\"></authorize> -->\r\n\t\t<!-- #endif -->\r\n\t\t<!-- <home></home> -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tgetCommissionInfo,\r\n\t\tgetRecordApi,\r\n\t} from '@/api/user.js';\r\n\timport {\r\n\t\ttoLogin\r\n\t} from '@/libs/login.js';\r\n\timport {\r\n\t\tmapGetters\r\n\t} from \"vuex\";\r\n\t// #ifdef MP\r\n\timport authorize from '@/components/Authorize';\r\n\t// #endif\r\n\timport emptyPage from '@/components/emptyPage.vue'\r\n\timport home from '@/components/home';\r\n\timport {setThemeColor} from '@/utils/setTheme.js'\r\n\tconst app = getApp();\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\t// #ifdef MP\r\n\t\t\tauthorize,\r\n\t\t\t// #endif\r\n\t\t\temptyPage,\r\n\t\t\thome\r\n\t\t},\r\n\t\tfilters: {\r\n\t\t\tstatusFilter(status) {\r\n\t\t\t\tconst statusMap = {\r\n\t\t\t\t\t'-1': '未通过',\r\n\t\t\t\t\t'0': '审核中',\r\n\t\t\t\t\t'1': '已提现'\r\n\t\t\t\t}\r\n\t\t\t\treturn statusMap[status]\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tname: '',\r\n\t\t\t\ttype: 0,\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tlimit: 10,\r\n\t\t\t\trecordList: [],\r\n\t\t\t\trecordType: 0,\r\n\t\t\t\tstatuss: false,\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false, //是否隐藏授权\r\n\t\t\t\textractCount: 0,\r\n\t\t\t\ttheme:app.globalData.theme,\r\n\t\t\t\tcommissionCount:0,\r\n\t\t\t\tbgColor:'#e93323'\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: mapGetters(['isLogin']),\r\n\t\tonLoad(options) {\r\n\t\t\tif (this.isLogin) {\r\n\t\t\t\tthis.type = options.type;\r\n\t\t\t\tthis.extractCount = options.extractCount;\r\n\t\t\t\tthis.commissionCount = options.commissionCount;\r\n\t\t\t} else {\r\n\t\t\t\ttoLogin();\r\n\t\t\t}\r\n\t\t\tlet that = this;\r\n\t\t\tthat.bgColor = setThemeColor();\r\n\t\t\tuni.setNavigationBarColor({\r\n\t\t\t\tfrontColor: '#ffffff',\r\n\t\t\t\tbackgroundColor:that.bgColor,\r\n\t\t\t});\r\n\t\t},\r\n\t\tonShow: function() {\r\n\t\t\tlet type = this.type;\r\n\t\t\tif (type == 1) {\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: \"提现记录\"\r\n\t\t\t\t});\r\n\t\t\t\tthis.name = '提现总额';\r\n\t\t\t\tthis.recordType = 4;\r\n\t\t\t\tthis.getList();\r\n\t\t\t} else if (type == 2) {\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: \"佣金记录\"\r\n\t\t\t\t});\r\n\t\t\t\tthis.name = '佣金明细';\r\n\t\t\t\tthis.recordType = 3;\r\n\t\t\t\tthis.getRecordList();\r\n\t\t\t} else {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '参数错误',\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 1000,\r\n\t\t\t\t\tmask: true,\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t// #ifndef H5\r\n\t\t\t\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\t\t\t\tdelta: 1,\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\t\t\thistory.back();\r\n\t\t\t\t\t\t\t// #endif\r\n\r\n\t\t\t\t\t\t}, 1200)\r\n\t\t\t\t\t},\r\n\t\t\t\t});\r\n\t\t\t}\r\n\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tonLoadFun() {\r\n\t\t\t\tthis.getRecordList();\r\n\t\t\t},\r\n\t\t\t// 授权关闭\r\n\t\t\tauthColse: function(e) {\r\n\t\t\t\tthis.isShowAuth = e\r\n\t\t\t},\r\n\t\t\tgetList: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet recordList = that.recordList;\r\n\t\t\t\tlet recordListNew = [];\r\n\t\t\t\tif (that.statuss == true) return;\r\n\t\t\t\tgetRecordApi({\r\n\t\t\t\t\tpage: that.page,\r\n\t\t\t\t\tlimit: that.limit\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tlet len = res.data.list ? res.data.list.length : 0;\r\n\t\t\t\t\tlet recordListData = res.data.list || [];\r\n\t\t\t\t\trecordListNew = recordList.concat(recordListData);\r\n\t\t\t\t\tthat.statuss = that.limit > len;\r\n\t\t\t\t\tthat.page = that.page + 1;\r\n\t\t\t\t\tthat.$set(that, 'recordList', recordListNew);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetRecordList: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet page = that.page;\r\n\t\t\t\tlet limit = that.limit;\r\n\t\t\t\tlet statuss = that.statuss;\r\n\t\t\t\tlet recordType = that.recordType;\r\n\t\t\t\tlet recordList = that.recordList;\r\n\t\t\t\tlet recordListNew = [];\r\n\t\t\t\tif (statuss == true) return;\r\n\t\t\t\tgetCommissionInfo({\r\n\t\t\t\t\tpage: page,\r\n\t\t\t\t\tlimit: limit\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.data.list) {\r\n\t\t\t\t\t\tlet len = res.data.list ? res.data.list.length : 0;\r\n\t\t\t\t\t\tlet recordListData = res.data.list || [];\r\n\t\t\t\t\t\trecordListNew = recordList.concat(recordListData);\r\n\t\t\t\t\t\tthat.statuss = limit > len;\r\n\t\t\t\t\t\tthat.page = page + 1;\r\n\t\t\t\t\t\tthat.$set(that, 'recordList', recordListNew);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\tonReachBottom: function() {\r\n\t\t\tthis.getRecordList();\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.commission-details .promoterHeader .headerCon .money {\r\n\t\tfont-size: 36rpx;\r\n\t}\r\n\t.promoterHeader{\r\n\t\t@include main_bg_color(theme);\r\n\t}\r\n\t.commission-details .promoterHeader .headerCon .money .num {\r\n\t\tfont-family: 'Guildford Pro';\r\n\t}\r\n\t.font_color{\r\n\t\tcolor: #E93323 !important;\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=9de6df8a&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=9de6df8a&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294179982\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}