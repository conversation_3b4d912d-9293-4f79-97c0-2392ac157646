{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_vip/index.vue?5823", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_vip/index.vue?7e9a", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_vip/index.vue?c131", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_vip/index.vue?3482", "uni-app:///pages/users/user_vip/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_vip/index.vue?3fe9", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_vip/index.vue?f15c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "home", "computed", "data", "levelInfo", "levelList", "current", "widthLen", "loading", "loadend", "loadTitle", "page", "limit", "expList", "theme", "onLoad", "methods", "getInfo", "that", "list", "length", "title", "getlevelList", "onReachBottom"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACqInnB;AAGA;AAGA;EAAA;IAAA;EAAA;AAAA;AACA;AAAA,eACA;EACAC;IACAC;EACA;EACAC;EACAC;IACA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACAC;MACA;MACA;QACA;QACA;QACAC;QACAb;UACA;YACAc;UACA;QACA;QACA;QACAD;QACA;QACA,oFACAE;QACA;QACA;QACA;QACA;QAEAF;MAEA;QACA;UACAG;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACAX;QACAC;MACA;QACA;UACAH;QACA;QACAS;QACAA;QACAA;QACAA;QACAA;MACA;QACAA;QACAA;MACA;IACA;EACA;EACAK;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC9NA;AAAA;AAAA;AAAA;AAA0oC,CAAgB,gnCAAG,EAAC,C;;;;;;;;;;;ACA9pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/users/user_vip/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/users/user_vip/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=2af3764c&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/users/user_vip/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=2af3764c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.expList.length\n  var g1 = _vm.expList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template >\r\n\t<view class=\"memberVip\" :data-theme=\"theme\">\r\n\t\t<view class=\"bg\">\r\n\t\t\t<view class=\"header\">\r\n\t\t\t\t<view class=\"picTxt acea-row row-middle\">\r\n\t\t\t\t\t<view class=\"pictrue\">\r\n\t\t\t\t\t\t<image :src=\"userInfo.avatar\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"text acea-row row-middle\">\r\n\t\t\t\t\t\t<view class=\"name line1\">{{userInfo.nickname}}</view>\r\n\t\t\t\t\t\t<view class=\"vip\" v-if='userInfo.vip'>\r\n\t\t\t\t\t\t\t<image :src=\"userInfo.vipIcon\"></image>{{userInfo.vipName}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"experience\">\r\n\t\t\t\t<view class=\"title\">当前经验值</view>\r\n\t\t\t\t<view class=\"num\">{{levelInfo}}</view>\r\n\t\t\t\t<view class=\"axis\">\r\n\t\t\t\t\t<view class=\"bar\">\r\n\t\t\t\t\t\t<view class=\"barCon\">\r\n\t\t\t\t\t\t\t<view class=\"solidBar\" :style=\"'width: ' + widthLen +'%;'\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"acea-row row-around row-middle\">\r\n\t\t\t\t\t\t\t<view class=\"spotw acea-row row-center\" v-for=\"(item,index) in levelList\" :key='index'>\r\n\t\t\t\t\t\t\t\t<view class=\"spot\"\r\n\t\t\t\t\t\t\t\t\t:class=\"current >item.experience?'past':'' + ' ' + current==item.experience?'on':''\">\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"numList acea-row row-around row-middle\">\r\n\t\t\t\t\t\t<view class=\"item\" :class=\"current >=item.experience?'past':''\"\r\n\t\t\t\t\t\t\tv-for=\"(item,index) in levelList\" :key=\"index\">{{item.experience}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"vipList acea-row\">\r\n\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t<view class=\"pictrue\">\r\n\t\t\t\t\t\t\t<image src=\"./../static/vip01.png\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"name\">会员折扣</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t<view class=\"pictrue\">\r\n\t\t\t\t\t\t\t<image src=\"./../static/vip02.png\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"name\">专属徽章</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t<view class=\"pictrue\">\r\n\t\t\t\t\t\t\t<image src=\"./../static/vip03.png\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"name\">会员升级</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t<view class=\"pictrue\">\r\n\t\t\t\t\t\t\t<image src=\"./../static/vip04.png\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"name\">经验积累</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t<view class=\"pictrue\">\r\n\t\t\t\t\t\t\t<image src=\"./../static/vip05.png\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"name\">更多特权</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"module\">\r\n\t\t\t\t<view class=\"public_title acea-row row-middle\">\r\n\t\t\t\t\t<view class=\"icons\"></view>获取经验\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"gainList\">\r\n\t\t\t\t\t<view class=\"item acea-row row-between-wrapper\">\r\n\t\t\t\t\t\t<view class=\"picTxt acea-row row-middle\">\r\n\t\t\t\t\t\t\t<view class=\"pictrue\"><text class=\"iconfont icon-qiandao2\"></text></view>\r\n\t\t\t\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t\t\t\t<view class=\"name line1\">签到</view>\r\n\t\t\t\t\t\t\t\t<view class=\"info line1\">每日签到可获得经验值</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<navigator url='/pages/users/user_sgin/index' class=\"button\" hover-class=\"none\">去获取</navigator>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item acea-row row-between-wrapper\">\r\n\t\t\t\t\t\t<view class=\"picTxt acea-row row-middle\">\r\n\t\t\t\t\t\t\t<view class=\"pictrue on\"><text class=\"iconfont icon-shangpin\"></text></view>\r\n\t\t\t\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t\t\t\t<view class=\"name line1\">购买商品</view>\r\n\t\t\t\t\t\t\t\t<view class=\"info line1\">购买商品可获得对应是经验值</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<navigator url=\"/pages/goods_cate/goods_cate\" class=\"button\" hover-class=\"none\"\r\n\t\t\t\t\t\t\topen-type='switchTab'>去获取</navigator>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- <view class=\"item acea-row row-between-wrapper\">\r\n\t\t\t\t\t\t<view class=\"picTxt acea-row row-middle\">\r\n\t\t\t\t\t\t\t<view class=\"pictrue on2\"><text class=\"iconfont icon-yaoqing\"></text></view>\r\n\t\t\t\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t\t\t\t<view class=\"name line1\">邀请好友</view>\r\n\t\t\t\t\t\t\t\t<view class=\"info line1\">邀请好友注册商城可获得经验值</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<navigator url=\"/pages/users/user_spread_code/index\" class=\"button\" hover-class=\"none\">去获取</navigator>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"detailed\" v-if=\"expList.length\">\r\n\t\t\t<view class=\"public_title acea-row row-middle\">\r\n\t\t\t\t<view class=\"icons\"></view>经验值明细\r\n\t\t\t</view>\r\n\t\t\t<view class=\"list\">\r\n\t\t\t\t<view class=\"item acea-row row-between-wrapper\" v-for=\"(item,index) in expList\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t\t<view class=\"name\">{{item.title}}</view>\r\n\t\t\t\t\t\t<view class=\"data\">{{item.createTime}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"num\" v-if=\"item.type == 1\">+{{item.experience}}</view>\r\n\t\t\t\t\t<view class=\"num on\" v-else>-{{item.experience}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class='loadingicon acea-row row-center-wrapper' v-if=\"expList.length\">\r\n\t\t\t<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>{{loadTitle}}\r\n\t\t</view>\r\n\t\t<!-- <home></home> -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport home from '@/components/home';\r\n\timport {\r\n\t\tmapGetters\r\n\t} from \"vuex\";\r\n\timport {\r\n\t\tgetlevelInfo,\r\n\t\tgetlevelExpList\r\n\t} from '@/api/user.js';\r\n\tlet app = getApp();\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\thome\r\n\t\t},\r\n\t\tcomputed: mapGetters(['userInfo']),\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t//userInfo: '',\r\n\t\t\t\tlevelInfo: '',\r\n\t\t\t\tlevelList: [],\r\n\t\t\t\tcurrent: 0,\r\n\t\t\t\twidthLen: 0,\r\n\t\t\t\tloading: false,\r\n\t\t\t\tloadend: false,\r\n\t\t\t\tloadTitle: '加载更多', //提示语\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tlimit: 20,\r\n\t\t\t\texpList: [],\r\n\t\t\t\ttheme:app.globalData.theme,\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.levelInfo = this.userInfo.experience;\r\n\t\t\tthis.getInfo();\r\n\t\t\tthis.getlevelList();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetInfo: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetlevelInfo().then(res => {\r\n\t\t\t\t\tlet levelList = res.data;\r\n\t\t\t\t\tlet list = []\r\n\t\t\t\t\tthat.levelList = levelList;\r\n\t\t\t\t\tlevelList.map((item, index) => {\r\n\t\t\t\t\t\tif (item.experience <= that.levelInfo) {\r\n\t\t\t\t\t\t\tlist.push(item.experience)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\tlet maxn = Math.max.apply(null, list);\r\n\t\t\t\t\tthat.current = maxn;\r\n\t\t\t\t\t// 解决len取的值没有时；\r\n\t\t\t\t\tlet levelListLen = levelList[list.length] ? levelList[list.length] : levelList[list\r\n\t\t\t\t\t\t.length - 1];\r\n\t\t\t\t\t// 解决除数不能为0\r\n\t\t\t\t\tlet divisor = levelListLen.experience - maxn ? levelListLen.experience - maxn : 1;\r\n\t\t\t\t\t// 每小格所占的百分比\r\n\t\t\t\t\tlet per = (that.levelInfo - maxn) / divisor / levelList.length;\r\n\r\n\t\t\t\t\tthat.widthLen = ((list.length - 0.5) / (levelList.length)) * 100 + per * 100\r\n\r\n\t\t\t\t}).catch(function(res) {\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: res\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetlevelList: function() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tif (this.loadend) return false;\r\n\t\t\t\tif (this.loading) return false;\r\n\t\t\t\tgetlevelExpList({\r\n\t\t\t\t\tpage: that.page,\r\n\t\t\t\t\tlimit: that.limit\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tlet list = res.data.list,\r\n\t\t\t\t\t\tloadend = list.length < that.limit;\r\n\t\t\t\t\tlet expList = that.$util.SplitArray(list, that.expList);\r\n\t\t\t\t\tthat.$set(that, 'expList', expList);\r\n\t\t\t\t\tthat.loadend = loadend;\r\n\t\t\t\t\tthat.loadTitle = loadend ? '我也是有底线的' : '加载更多';\r\n\t\t\t\t\tthat.page = that.page + 1;\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.loadTitle = '加载更多';\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\tonReachBottom: function() {\r\n\t\t\tthis.getlevelList();\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.memberVip {\r\n\t\t.bg {\r\n\t\t\tbackground-color: #fff;\r\n\r\n\t\t\t.header {\r\n\t\t\t\tbackground-image: url('data:image/png;base64,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******************************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**********************************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');\r\n\t\t\t\tbackground-repeat: no-repeat;\r\n\t\t\t\tbackground-size: 100% 100%;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 380rpx;\r\n\t\t\t\tbackground-color: #fff;\r\n\r\n\t\t\t\t.picTxt {\r\n\t\t\t\t\tpadding: 28rpx 39rpx;\r\n\r\n\t\t\t\t\t.pictrue {\r\n\t\t\t\t\t\twidth: 70rpx;\r\n\t\t\t\t\t\theight: 70rpx;\r\n\t\t\t\t\t\tborder-radius: 50%;\r\n\r\n\t\t\t\t\t\timage {\r\n\t\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.text {\r\n\t\t\t\t\t\tcolor: #FAE2C1;\r\n\t\t\t\t\t\tmargin-left: 23rpx;\r\n\r\n\t\t\t\t\t\t.name {\r\n\t\t\t\t\t\t\tmax-width: 385rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.vip {\r\n\t\t\t\t\t\t\tpadding: 6rpx 18rpx;\r\n\t\t\t\t\t\t\tborder: 1px solid rgba(250, 226, 193, 1);\r\n\t\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\t\tfont-size: 18rpx;\r\n\t\t\t\t\t\t\tmargin-left: 15rpx;\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t\t\t\timage {\r\n\t\t\t\t\t\t\t\twidth: 20rpx;\r\n\t\t\t\t\t\t\t\theight: 20rpx;\r\n\t\t\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\t\t\tmargin-right: 8rpx;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.experience {\r\n\t\t\t\tbackground-image: url('data:image/jpg;base64,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');\r\n\t\t\t\tbackground-repeat: no-repeat;\r\n\t\t\t\tbackground-size: 100% 100%;\r\n\t\t\t\twidth: 690rpx;\r\n\t\t\t\theight: 360rpx;\r\n\t\t\t\tmargin: -250rpx auto 0 auto;\r\n\t\t\t\tborder-radius: 23rpx;\r\n\t\t\t\tpadding: 22rpx 27rpx;\r\n\t\t\t\tbox-sizing: border-box;\r\n\r\n\t\t\t\t.title {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: #AE8B4A;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.num {\r\n\t\t\t\t\tfont-size: 60rpx;\r\n\t\t\t\t\tcolor: #775C29;\r\n\t\t\t\t\tmargin-top: 6rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.axis {\r\n\t\t\t\t\tmargin: 10rpx 0 15rpx 0;\r\n\t\t\t\t\toverflow: hidden;\r\n\r\n\t\t\t\t\t.bar {\r\n\t\t\t\t\t\twidth: 630rpx;\r\n\r\n\t\t\t\t\t\t.spotw {\r\n\t\t\t\t\t\t\twidth: 96rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.barCon {\r\n\t\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\t\theight: 3rpx;\r\n\t\t\t\t\t\t\tbackground: #D7BD89;\r\n\t\t\t\t\t\t\tborder-radius: 2rpx;\r\n\r\n\t\t\t\t\t\t\t.solidBar {\r\n\t\t\t\t\t\t\t\twidth: 0;\r\n\t\t\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\t\t\tbackground: #775C29;\r\n\t\t\t\t\t\t\t\tborder-radius: 2rpx;\r\n\t\t\t\t\t\t\t\ttransition: width 0.6s ease;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.spot {\r\n\t\t\t\t\t\t\twidth: 8rpx;\r\n\t\t\t\t\t\t\theight: 8rpx;\r\n\t\t\t\t\t\t\tbackground: #D7BD89;\r\n\t\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\t\tmargin-top: -5rpx;\r\n\r\n\t\t\t\t\t\t\t&.past {\r\n\t\t\t\t\t\t\t\tbackground: #775C29;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t&.on {\r\n\t\t\t\t\t\t\t\tbackground: #775C29;\r\n\t\t\t\t\t\t\t\tbox-shadow: 0rpx 0rpx 8rpx #000;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.numList {\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tcolor: #D7BD89;\r\n\t\t\t\t\t\tmargin-top: 14rpx;\r\n\r\n\t\t\t\t\t\t.item {\r\n\t\t\t\t\t\t\twidth: 96rpx;\r\n\t\t\t\t\t\t\ttext-align: center;\r\n\r\n\t\t\t\t\t\t\t&.past {\r\n\t\t\t\t\t\t\t\tcolor: #775C29;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.vipList {\r\n\t\t\t\t\t.item {\r\n\t\t\t\t\t\twidth: 20%;\r\n\t\t\t\t\t\ttext-align: center;\r\n\r\n\t\t\t\t\t\t.pictrue {\r\n\t\t\t\t\t\t\twidth: 70rpx;\r\n\t\t\t\t\t\t\theight: 70rpx;\r\n\t\t\t\t\t\t\tmargin: 0 auto;\r\n\r\n\t\t\t\t\t\t\timage {\r\n\t\t\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.name {\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\tcolor: #403D4E;\r\n\t\t\t\t\t\t\tmargin-top: 18rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\r\n\r\n\t\t\t.module {\r\n\t\t\t\tpadding: 40rpx 30rpx 0 30rpx;\r\n\r\n\t\t\t\t.gainList {\r\n\t\t\t\t\tmargin-top: 10rpx;\r\n\r\n\t\t\t\t\t.item {\r\n\t\t\t\t\t\theight: 130rpx;\r\n\t\t\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t\t\t.picTxt {\r\n\t\t\t\t\t\t\t.pictrue {\r\n\t\t\t\t\t\t\t\twidth: 70rpx;\r\n\t\t\t\t\t\t\t\theight: 70rpx;\r\n\t\t\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\t\t\tbackground: linear-gradient(-45deg, rgba(249, 119, 107, 1) 0%, rgba(255, 147, 137, 1) 100%);\r\n\t\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t\t\tline-height: 70rpx;\r\n\t\t\t\t\t\t\t\tcolor: #fff;\r\n\r\n\t\t\t\t\t\t\t\t&.on {\r\n\t\t\t\t\t\t\t\t\tbackground: linear-gradient(-45deg, rgba(254, 160, 96, 1) 0%, rgba(255, 204, 170, 1) 100%);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t&.on2 {\r\n\t\t\t\t\t\t\t\t\tbackground: linear-gradient(-45deg, rgba(157, 208, 116, 1) 0%, rgba(161, 214, 124, 1) 100%);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t.text {\r\n\t\t\t\t\t\t\t\tmargin-left: 30rpx;\r\n\t\t\t\t\t\t\t\twidth: 400rpx;\r\n\r\n\t\t\t\t\t\t\t\t.name {\r\n\t\t\t\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t\t\t\t\tcolor: #282828;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t.info {\r\n\t\t\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t\t\t\t\tmargin-top: 6rpx;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.button {\r\n\t\t\t\t\t\t\twidth: 140rpx;\r\n\t\t\t\t\t\t\theight: 50rpx;\r\n\t\t\t\t\t\t\tbackground: linear-gradient(-90deg, rgba(231, 182, 103, 1) 0%, rgba(255, 234, 181, 1) 100%);\r\n\t\t\t\t\t\t\tborder-radius: 25rpx;\r\n\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t\tline-height: 50rpx;\r\n\t\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\t\tcolor: #8D5306;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t&~.item {\r\n\t\t\t\t\t\t\t&::after {\r\n\t\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\t\tcontent: ' ';\r\n\t\t\t\t\t\t\t\twidth: 720rpx;\r\n\t\t\t\t\t\t\t\theight: 1rpx;\r\n\t\t\t\t\t\t\t\tbackground: rgba(245, 245, 245, 1);\r\n\t\t\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\t\t\tleft: 0;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.public_title {\r\n\t\t\tcolor: #282828;\r\n\t\t\tfont-size: 30rpx;\r\n\r\n\t\t\t.icons {\r\n\t\t\t\twidth: 6rpx;\r\n\t\t\t\theight: 28rpx;\r\n\t\t\t\tbackground: rgba(230, 192, 131, 1);\r\n\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.detailed {\r\n\t\t\tpadding: 30rpx 30rpx 0 30rpx;\r\n\t\t\tmargin-top: 15rpx;\r\n\t\t\tbackground-color: #fff;\r\n\r\n\t\t\t.list {\r\n\t\t\t\tmargin-top: 15rpx;\r\n\r\n\t\t\t\t.item {\r\n\t\t\t\t\theight: 122rpx;\r\n\t\t\t\t\tborder-bottom: 1px solid #EEEEEE;\r\n\r\n\t\t\t\t\t.text {\r\n\t\t\t\t\t\t.name {\r\n\t\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\t\tcolor: #282828;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.data {\r\n\t\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.num {\r\n\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\tcolor: $theme-color;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.on {\r\n\t\t\t\t\t\tcolor: #16AC57;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294179968\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}