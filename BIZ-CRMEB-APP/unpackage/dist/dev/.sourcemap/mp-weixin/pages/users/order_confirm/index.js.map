{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/order_confirm/index.vue?98d2", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/order_confirm/index.vue?5b47", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/order_confirm/index.vue?d9ae", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/order_confirm/index.vue?e6cf", "uni-app:///pages/users/order_confirm/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/order_confirm/index.vue?7939", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/order_confirm/index.vue?0db3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "navBar", "couponListWindow", "addressWindow", "orderGoods", "home", "authorize", "onReady", "query", "select", "boundingClientRect", "exec", "data", "marTop", "navTitle", "homeTop", "orderShow", "textareaStatus", "cartArr", "value", "title", "payStatus", "payType", "openType", "active", "coupon", "list", "statusTile", "address", "addressId", "addressInfo", "couponId", "cartId", "userInfo", "mark", "couponTitle", "coupon_price", "useIntegral", "integral_price", "integral", "ChangePrice", "formIds", "status", "is_address", "toPay", "shippingType", "system_store", "storePostage", "contacts", "contactsTel", "mydata", "storeList", "store_self_mention", "cartInfo", "priceGroup", "animated", "totalPrice", "integralRatio", "pagesUrl", "orderKey", "offlinePostage", "isAuto", "isShowAuth", "payChannel", "news", "again", "addAgain", "bargain", "combination", "secKill", "orderInfoVo", "addressList", "orderProNum", "preOrderNo", "theme", "formContent", "send<PERSON><PERSON><PERSON>", "computed", "<PERSON><PERSON><PERSON>", "watch", "is<PERSON>ogin", "handler", "deep", "onLoad", "onShow", "uni", "_this", "methods", "getNavH", "add", "subtract", "alert", "getloadPreOrder", "url", "bindHideKeyAddressinfo1", "onLoadFun", "getList", "latitude", "longitude", "page", "limit", "changeClose", "showStoreList", "computedPrice", "computedPrice1", "freightFee", "addressType", "bindPickerChange", "ChangCouponsClone", "changeTextareaStatus", "Chang<PERSON>ou<PERSON>ns", "ChangeIntegral", "OnDefault<PERSON><PERSON><PERSON>", "OnChangeAddress", "getAddressRecognition", "addressRecognition", "id", "bindHideKeyboard", "getCouponList", "getaddressInfo", "res", "that", "getAddressDefault", "payItem", "setTimeout", "couponTap", "car", "on<PERSON><PERSON><PERSON>", "payment", "getOrderPay", "orderNo", "scene", "tab", "location", "weixinPay", "timeStamp", "nonceStr", "package", "signType", "paySign", "ticket", "success", "icon", "fail", "complete", "SubOrder", "storeId"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACgLnnB;AASA;AACA;AACA;AACA;AAMA;AACA;AAIA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AACA;AACA;AACA;AAAA,eACA;EACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IAEAC;EAEA;EACAC;IACA;MAAA;MAGA;MACA;MACAC,MACAC,gBACAC;QACA;MACA,GACAC;IAEA;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACA;MACAC;QACA;QACA;QACAC;QACAC;QACAC;MACA,GACA;QACA;QACA;QACAF;QACAC;QACAC;MACA,EAUA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;QACAA;QACAC;QACAC;MACA;MAAA;MACAC;QACAA;QACAC;MACA;MAAA;MACAC;MAAA;MACAD;MAAA;MACAE;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;IACA;EACA;EACAC,0CACA;IACAC;MACA;MACA;QACAA;QACA;MACA;IACA;EAAA,EACA;EACAC;IACAC;MACAC;QACA;UACA;UACA;QACA;MACA;;MACAC;IACA;EACA;EACAC;IAKA;;IAKA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACA;MACA;IACA;MACA;IACA;EACA;EACA;AACA;AACA;EACAC;IACA;IACA;IACA;IACA;MACA;IAAA;IAIAC;MACA;QACAC;MACA;MACA;MACAD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;;EACAE;IACAC;MACA;IACA;IACAC,0BAEA;IACAC;MACA;QACAC;QACA;MACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAQA;QACA;QACA;UACA;QACA;MACA;QACAP;UACAQ;QACA;MACA;IACA;IACAC;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;IAAA,CACA;IACA;AACA;AACA;IACAC;MAAA;MACA;MACA;MACA;QACAC;QAAA;QACAC;QAAA;QACAC;QACAC;MACA;MACA;QACA;QACA;QACA;MACA;QACA;UACAhF;QACA;MACA;IACA;IACA;IACAiF;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;QACAjB;UACAQ;QACA;MACA;IACA;IACA;IACAU;MAAA;MACA;MACA;QACA1E;QACAQ;QACAN;QACAc;QACA4B;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;UACArD;QACA;MACA;IACA;IAEA;IACAoF;MAAA;MACA;MACA;MACA;QACA3E;QACAQ;QACAN;QACA0E;QACA5D;QACA4B;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;UACArD;QACA;MACA;IACA;IAEAsF;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;QACApF;QACAS;QACAJ;MACA;QACA;UACAL;UACAA;QACA;MACA;MACA;QACA;QACAA;QACAA;MACA;QACA;QACAA;QACAA;QACAS;QACAJ;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAgF;MACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MAAA;MACA;MAEA;QACA;UACAC;UACAC;QACA;UACA;YACA;YACA;YACA;YACA;YACA;UACA;QAEA;UACA;YACAhG;UACA;QACA;MACA;IACA;IAEAiG;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MAAA;MACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;QACA;UACA;YACAC;YACAC;YACAA;YACAA;UACA;QACA;MACA;QACAC;UACA;YACAF;YACAC;YACAA;YACAA;UACA;QACA;MACA;IACA;IACAE;MACA;MACA;MACAF;MACAA;MACAA;;MAaA;MACAG;QACAH;MACA;IACA;IACAI;MACA;MACA;IACA;IACAC;MACA;MACAL;IACA;IACAM;MACA;MACAN;MACAA;MACAA;IACA;IACAO;MACA;MACA;QACAP;MACA;QACApC;QACA;UACAjE;QACA,mCACA;MACA;IACA;IACA6G;MACA;MACA;MACA;QACAC;QACAnE;QACAzC;QACA6G;MACA;QACA;QACA;UACA;YACAV;YACA;UACA;YACApC;YACA;cACAjE;YACA;cACAgH;cACAvC;YACA;YACA;UACA;YACAR;YACAuC;cACAS;YACA;YACA;UACA;YAmDA;QAAA;MAEA;QACAhD;QACA;UACAjE;QACA;MACA;IACA;IACAkH;MACA;MAEAjD;QACAkD;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;UACAxD;UACA;YACA;cACA,uBACA;gBAAAjE;gBAAA0H;cAAA,GACA;gBAAAV;gBAAAvC;cAAA;YACA;YACA,uBACA;cAAAzE;cAAA0H;YAAA,GACA;cAAAV;cAAAvC;YAAA;UACA;QACA;QACAkD;UACA1D;UACA,uBACA;YAAAjE;UAAA,GACA;YAAAgH;YAAAvC;UAAA;QACA;QACAmD;UACA3D;UACA;UACA,gEACA;YAAAjE;UAAA,GACA;YAAAgH;YAAAvC;UAAA;QACA;MACA;IAuFA;IACAoD;MACA;QACArI;MAEA;QACAQ;MACA;MACA;MACA;QACAA;MACA;MACA;QACA;UACAA;QACA;MACA;MACAR;QACAiB;QACAE;QACAT;QACAe;QACAoC;QACAvC;QACAgH;QACArG;QACAkB;QACAa;MAEA;MACA,+FACA;QACAxD;MACA;MACAiE;QACAjE;MACA;MAEA;QACAqG;MACA;IAKA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7+BA;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/users/order_confirm/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/users/order_confirm/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=064d693c&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=064d693c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"064d693c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/users/order_confirm/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=064d693c&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !(_vm.shippingType == 0) ? _vm.storeList.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :data-theme=\"theme\">\r\n\t\t<!-- #ifndef APP-PLUS -->\r\n\t\t<view class='cart_nav'>\r\n\t\t\t<nav-bar :navTitle='navTitle' @getNavH='getNavH'></nav-bar>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- #endif -->\r\n\t\t<view class='order-submission' :style=\"'margin-top:'+(marTop)+'rpx;'\">\r\n\t\t\t<view class=\"allAddress\" :style=\"store_self_mention ? '':'padding-top:0;'\">\r\n\t\t\t\t<view class=\"nav acea-row\">\r\n\t\t\t\t\t<view class=\"item font_color\" :class=\"shippingType == 0 ? 'on' : 'on2'\" @tap=\"addressType(0)\"\r\n\t\t\t\t\t\tv-if='store_self_mention'></view>\r\n\t\t\t\t\t<view class=\"item font_color\" :class=\"shippingType == 1 ? 'on' : 'on2'\" @tap=\"addressType(1)\"\r\n\t\t\t\t\t\tv-if='store_self_mention'></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='address acea-row row-between-wrapper' @tap='onAddress' v-if='shippingType == 0' :style=\"store_self_mention ? '':'border-top-left-radius: 14rpx;border-top-right-radius: 14rpx;'\">\r\n\t\t\t\t\t<view class='addressCon' v-if=\"addressInfo.detail\">\r\n\t\t\t\t\t\t<view class='name'>{{addressInfo.realName}}\r\n\t\t\t\t\t\t\t<text class='phone'>{{addressInfo.phone}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"acea-row\">\r\n\t\t\t\t\t\t\t<text class='default font_color'\r\n\t\t\t\t\t\t\t\tv-if=\"addressInfo.isDefault\">[默认]</text>\r\n\t\t\t\t\t\t\t<text class=\"line2\">{{addressInfo.province}}{{addressInfo.city}}{{addressInfo.district}}{{addressInfo.detail}}</text>\t\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='addressCon' v-else>\r\n\t\t\t\t\t\t<view class='setaddress'>设置收货地址</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='iconfont icon-jiantou'></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='address acea-row row-between-wrapper' v-else @tap=\"showStoreList\">\r\n\t\t\t\t\t<block v-if=\"storeList.length>0\">\r\n\t\t\t\t\t\t<view class='addressCon'>\r\n\t\t\t\t\t\t\t<view class='name'>{{system_store.name}}\r\n\t\t\t\t\t\t\t\t<text class='phone'>{{system_store.phone}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"line1\"> {{system_store.address}}{{\", \" + system_store.detailedAddress}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='iconfont icon-jiantou'></view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t<view>暂无门店信息</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='line'>\r\n\t\t\t\t\t<image src='/static/images/line.jpg'></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"pad30\">\r\n\t\t\t\t<orderGoods :cartInfo=\"cartInfo\" :orderProNum=\"orderProNum\"></orderGoods>\r\n\t\t\t\r\n\t\t\t\t<view class='wrapper borRadius14'>\r\n\t\t\t\t\t<!-- <view class='item acea-row row-between-wrapper' @tap='couponTap'\r\n\t\t\t\t\t\tv-if=\"!orderInfoVo.bargainId && !orderInfoVo.combinationId && !orderInfoVo.seckillId && productType==='normal'\">\r\n\t\t\t\t\t\t<view>物流快递</view>\r\n\t\t\t\t\t\t<view class='discount'>{{couponTitle}}\r\n\t\t\t\t\t\t\t<text class='iconfont icon-jiantou'></text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t\t<!-- <view class='item acea-row row-between-wrapper' @tap='couponTap'\r\n\t\t\t\t\t\tv-if=\"!orderInfoVo.bargainId && !orderInfoVo.combinationId && !orderInfoVo.seckillId && productType==='normal'\">\r\n\t\t\t\t\t\t<view>优惠券</view>\r\n\t\t\t\t\t\t<view class='discount'>{{couponTitle}}\r\n\t\t\t\t\t\t\t<text class='iconfont icon-jiantou'></text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view> -->\r\n\r\n\t\t\t\t\t<!-- <view class='item acea-row row-between-wrapper'\r\n\t\t\t\t\t\tv-if=\"!orderInfoVo.bargainId && !orderInfoVo.combinationId && !orderInfoVo.seckillId && productType==='normal'\">\r\n\t\t\t\t\t\t<view>积分抵扣</view>\r\n\t\t\t\t\t\t<view class='discount acea-row row-middle'>\r\n\t\t\t\t\t\t\t<view> {{useIntegral ? \"剩余积分\":\"当前积分\"}}\r\n\t\t\t\t\t\t\t\t<text class='num font_color'>{{useIntegral ? orderInfoVo.surplusIntegral : orderInfoVo.userIntegral || 0}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<checkbox-group @change=\"ChangeIntegral\">\r\n\t\t\t\t\t\t\t\t<checkbox :checked='useIntegral ? true : false' :disabled=\"orderInfoVo.userIntegral==0 && !useIntegral\"/>\r\n\t\t\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t\t<!-- <view class='item acea-row row-between-wrapper'\r\n\t\t\t\t\t\tv-if=\"priceGroup.vipPrice > 0 && userInfo.vip && !pinkId && !BargainId && !combinationId && !seckillId\">\r\n\t\t\t\t\t\t<view>会员优惠</view>\r\n\t\t\t\t\t\t<view class='discount'>-￥{{priceGroup.vipPrice}}</view>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t\t<!-- <view class='item acea-row row-between-wrapper' v-if='shippingType==0'>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view>物流快递</view>\r\n\t\t\t\t\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class='item'  v-if='shippingType==0' >\r\n\t\t\t\t\t\t<view class=\"flex justify-between\">\r\n\t\t\t\t\t\t\t<view>地址识别</view>\r\n\t\t\t\t\t\t\t<!-- s -->\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<textarea v-if=\"textareaStatus\" placeholder-class='placeholder' @blur='getAddressRecognition'\r\n\t\t\t\t\t\t\tvalue=\"\" name=\"addressRecognition\" placeholder='粘贴地址智能识别收货地址'></textarea>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item' v-if='shippingType==0'>\r\n\t\t\t\t\t<view>寄件信息</view>\r\n\t\t\t\t\t\t<textarea v-if=\"coupon.coupon===false\" placeholder-class='placeholder' @input='bindHideKeyAddressinfo1'\r\n\t\t\t\t\t\t\tvalue=\"\" name=\"sendAddress\" placeholder='请粘贴寄件信息(300字内)'></textarea>\r\n\t\t\t\t\t</view>\t\t\t\t\t\t\r\n\t\t\t\t\t\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class='item' v-if=\"textareaStatus\">\r\n\t\t\t\t\t\t<view class=\"flex justify-between\">\r\n\t\t\t\t\t\t\t<view>备注信息</view>\r\n\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t<text style=\"color:#666;\">{{markNum ? markNum : 150}}</text>/<text>150</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<textarea v-if=\"coupon.coupon===false\" placeholder-class='placeholder' @input='bindHideKeyboard'\r\n\t\t\t\t\t\t\tvalue=\"\" name=\"mark\" placeholder='请添加备注（150字以内）'></textarea>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='wrapper borRadius14'>\r\n\t\t\t\t\t<view class='item'>\r\n\t\t\t\t\t\t<view>支付方式</view>\r\n\t\t\t\t\t\t<view class='list'>\r\n\t\t\t\t\t\t\t<view class='payItem acea-row row-middle' :class='active==index ?\"on\":\"\"'\r\n\t\t\t\t\t\t\t\t@tap='payItem(index)' v-for=\"(item,index) in cartArr\" :key='index'\r\n\t\t\t\t\t\t\t\tv-if=\"item.payStatus==1\">\r\n\t\t\t\t\t\t\t\t<view class='name acea-row row-center-wrapper'>\r\n\t\t\t\t\t\t\t\t\t<view class='iconfont animated'\r\n\t\t\t\t\t\t\t\t\t\t:class='(item.icon) + \" \" + (animated==true&&active==index ?\"bounceIn\":\"\")'>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t{{item.name}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class='tip'>{{item.title}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='moneyList borRadius14'>\r\n\t\t\t\t\t<view class='item acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t<view>商品总价：</view>\r\n\t\t\t\t\t\t<view class='money'>￥{{orderInfoVo.proTotalFee || 0}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item acea-row row-between-wrapper' v-if=\"orderInfoVo.couponFee > 0\">\r\n\t\t\t\t\t\t<view>优惠券抵扣：</view>\r\n\t\t\t\t\t\t<view class='money'>-￥{{orderInfoVo.couponFee}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item acea-row row-between-wrapper' v-if=\"orderInfoVo.deductionPrice > 0\">\r\n\t\t\t\t\t\t<view>积分抵扣：</view>\r\n\t\t\t\t\t\t<view class='money'>-￥{{orderInfoVo.deductionPrice}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item acea-row row-between-wrapper' v-if=\"orderInfoVo.freightFee > 0\">\r\n\t\t\t\t\t\t<view>运费：</view>\r\n\t\t\t\t\t\t<view class='money'>+￥{{orderInfoVo.freightFee}}</view>\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style='height:120rpx;'></view>\r\n\t\t\t</view>\r\n\t\t\t<view class='footer acea-row row-between-wrapper'>\r\n\t\t\t\t<view>合计:\r\n\t\t\t\t\t<text class='price_color'>￥{{orderInfoVo.payFee || 0}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='settlement' style='z-index:100' @tap=\"SubOrder\">立即结算</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"alipaysubmit\" v-html=\"formContent\"></view>\r\n\t\t<couponListWindow :coupon='coupon' @ChangCouponsClone=\"ChangCouponsClone\" :openType='openType' @ChangCoupons=\"ChangCoupons\" :orderShow=\"orderShow\"></couponListWindow>\r\n\t\t<addressWindow ref=\"addressWindow\" @changeTextareaStatus=\"changeTextareaStatus\" :address='address'\r\n\t\t\t:pagesUrl=\"pagesUrl\" @OnDefaultAddress=\"OnDefaultAddress\"  @OnChangeAddress=\"OnChangeAddress\" @changeClose=\"changeClose\"></addressWindow>\r\n\t\t<!-- #ifdef MP -->\r\n\t\t<!-- <authorize @onLoadFun=\"onLoadFun\" :isAuto=\"isAuto\" :isShowAuth=\"isShowAuth\" @authColse=\"authColse\"></authorize> -->\r\n\t\t<!-- #endif -->\r\n\t\t<!-- <home></home> -->\r\n\t</view>\r\n</template>\r\n<script>\r\n\timport {\r\n\t\t//orderConfirm,\r\n\t\tgetCouponsOrderPrice,\r\n\t\torderCreate,\r\n\t\tpostOrderComputed,\r\n\t\torderPay,\r\n\t\twechatQueryPayResult,\r\n\t\tloadPreOrderApi\r\n\t} from '@/api/order.js';\r\n\timport {getAddressList,getAddressDetail,addressRecognition} from '@/api/user.js';\r\n\timport {openPaySubscribe,openOrderSubscribe} from '@/utils/SubscribeMessage.js';\r\n\timport {storeListApi} from '@/api/store.js';\r\n\timport {CACHE_LONGITUDE,CACHE_LATITUDE} from '@/config/cache.js';\r\n\timport couponListWindow from '@/components/couponListWindow';\r\n\timport addressWindow from '@/components/addressWindow';\r\n\timport orderGoods from '@/components/orderGoods';\r\n\timport home from '@/components/home';\r\n\timport navBar from '@/components/navBar';\r\n\timport {toLogin} from '@/libs/login.js';\r\n\timport {mapGetters} from \"vuex\";\r\n\t// #ifdef MP\r\n\timport authorize from '@/components/Authorize';\r\n\t// #endif\r\n\timport {Debounce} from '@/utils/validate.js'\r\n\tlet app = getApp();\r\n\tlet addressRecognitionId = null;//识别地址ID\r\n\tlet addressRecognitionDetail = null; //识别地址内容\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tnavBar,\r\n\t\t\tcouponListWindow,\r\n\t\t\taddressWindow,\r\n\t\t\torderGoods,\r\n\t\t\thome,\r\n\t\t\t// #ifdef MP\r\n\t\t\tauthorize\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tonReady() {\r\n\t\t\tthis.$nextTick(function() {\r\n\t\t\t\t// #ifdef MP\r\n\t\t\t\t\r\n\t\t\t\tconst menuButton = uni.getMenuButtonBoundingClientRect();\r\n\t\t\t\tconst query = uni.createSelectorQuery().in(this);\r\n\t\t\t\tquery\r\n\t\t\t\t\t.select('#home')\r\n\t\t\t\t\t.boundingClientRect(data => {\r\n\t\t\t\t\t\tthis.homeTop = menuButton.top * 2 + menuButton.height - data.height;\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.exec();\r\n\t\t\t\t// #endif\r\n\t\t\t});\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tmarTop: 0,\r\n\t\t\t\tnavTitle: '提交订单',\r\n\t\t\t\thomeTop: 20,\r\n\t\t\t\torderShow: 'orderShow', //下单页面使用优惠券组件不展示tab切换页\r\n\t\t\t\ttextareaStatus: true,\r\n\t\t\t\t//支付方式\r\n\t\t\t\tcartArr: [{\r\n\t\t\t\t\t\t\"name\": \"微信支付\",\r\n\t\t\t\t\t\t\"icon\": \"icon-weixin2\",\r\n\t\t\t\t\t\tvalue: 'weixin',\r\n\t\t\t\t\t\ttitle: '微信快捷支付',\r\n\t\t\t\t\t\tpayStatus: 1,\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"name\": \"余额支付\",\r\n\t\t\t\t\t\t\"icon\": \"icon-yuezhifu\",\r\n\t\t\t\t\t\tvalue: 'yue',\r\n\t\t\t\t\t\ttitle: '可用余额:',\r\n\t\t\t\t\t\tpayStatus: 1,\r\n\t\t\t\t\t},\r\n\t\t\t\t\t// #ifndef MP\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"name\": \"支付宝支付\",\r\n\t\t\t\t\t\t\"icon\": \"icon-zhifubao\",\r\n\t\t\t\t\t\tvalue: 'alipay',\r\n\t\t\t\t\t\ttitle: '支付宝快捷支付',\r\n\t\t\t\t\t\tpayStatus: 1,\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t],\r\n\t\t\t\tpayType: 'weixin', //支付方式\r\n\t\t\t\topenType: 1, //优惠券打开方式 1=使用\r\n\t\t\t\tactive: 0, //支付方式切换\r\n\t\t\t\tcoupon: {\r\n\t\t\t\t\tcoupon: false,\r\n\t\t\t\t\tlist: [],\r\n\t\t\t\t\tstatusTile: '立即使用'\r\n\t\t\t\t}, //优惠券组件\r\n\t\t\t\taddress: {\r\n\t\t\t\t\taddress: false,\r\n\t\t\t\t\taddressId: 0\r\n\t\t\t\t}, //地址组件\r\n\t\t\t\taddressInfo: {}, //地址信息\r\n\t\t\t\taddressId: 0, //地址id\r\n\t\t\t\tcouponId: 0, //优惠券id\r\n\t\t\t\tcartId: '', //购物车id\r\n\t\t\t\tuserInfo: {}, //用户信息\r\n\t\t\t\tmark: '', //备注信息\r\n\t\t\t\tcouponTitle: '请选择', //优惠券\r\n\t\t\t\tcoupon_price: 0, //优惠券抵扣金额\r\n\t\t\t\tuseIntegral: false, //是否使用积分\r\n\t\t\t\tintegral_price: 0, //积分抵扣金额\r\n\t\t\t\tintegral: 0,\r\n\t\t\t\tChangePrice: 0, //使用积分抵扣变动后的金额\r\n\t\t\t\tformIds: [], //收集formid\r\n\t\t\t\tstatus: 0,\r\n\t\t\t\tis_address: false,\r\n\t\t\t\ttoPay: false, //修复进入支付时页面隐藏从新刷新页面\r\n\t\t\t\tshippingType: 0,\r\n\t\t\t\tsystem_store: {},\r\n\t\t\t\tstorePostage: 0,\r\n\t\t\t\tcontacts: '',\r\n\t\t\t\tcontactsTel: '',\r\n\t\t\t\tmydata: {},\r\n\t\t\t\tstoreList: [],\r\n\t\t\t\tstore_self_mention: 0,\r\n\t\t\t\tcartInfo: [],\r\n\t\t\t\tpriceGroup: {},\r\n\t\t\t\tanimated: false,\r\n\t\t\t\ttotalPrice: 0,\r\n\t\t\t\tintegralRatio: \"0\",\r\n\t\t\t\tpagesUrl: \"\",\r\n\t\t\t\torderKey: \"\",\r\n\t\t\t\t// usableCoupon: {},\r\n\t\t\t\tofflinePostage: \"\",\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false, //是否隐藏授权\r\n\t\t\t\tpayChannel: '',\r\n\t\t\t\tnews: true,\r\n\t\t\t\tagain: false,\r\n\t\t\t\taddAgain: false,\r\n\t\t\t\tbargain: false, //是否是砍价\r\n\t\t\t\tcombination: false, //是否是拼团\r\n\t\t\t\tsecKill: false, //是否是秒杀\r\n\t\t\t\torderInfoVo: {},\r\n\t\t\t\taddressList: [], //地址列表数据\r\n\t\t\t\torderProNum: 0,\r\n\t\t\t\tpreOrderNo: '', //预下单订单号\r\n\t\t\t\ttheme: app.globalData.theme,\r\n\t\t\t\tformContent:'',\r\n\t\t\t\tsendAddress:'',\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed:{\r\n\t\t\t...mapGetters(['isLogin', 'systemPlatform', 'productType']),\r\n\t\t\tmarkNum(){\r\n\t\t\t\tlet markNum = 0;\r\n\t\t\t\tif(this.mark){\r\n\t\t\t\t\tmarkNum = 150 - this.mark.length\r\n\t\t\t\t\treturn markNum\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tisLogin: {\r\n\t\t\t\thandler: function(newV, oldV) {\r\n\t\t\t\t\tif (newV) {\r\n\t\t\t\t\t\tthis.getloadPreOrder();\r\n\t\t\t\t\t\t//this.getaddressInfo();\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tdeep: true\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\t// #ifdef H5\r\n\t\t\tthis.payChannel = this.$wechat.isWeixin() ? 'public' : 'weixinh5';\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef MP\r\n\t\t\tthis.payChannel = 'routine';\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\tthis.payChannel = this.systemPlatform === 'ios' ? 'weixinAppIos' : 'weixinAppAndroid';\r\n\t\t\t// #endif\r\n\t\t\t// if (!options.cartId) return this.$util.Tips({\r\n\t\t\t// \ttitle: '请选择要购买的商品'\r\n\t\t\t// }, {\r\n\t\t\t// \ttab: 3,\r\n\t\t\t// \turl: 1\r\n\t\t\t// });\r\n            this.preOrderNo = options.preOrderNo || 0;\r\n\t\t\tthis.addressId = options.addressId || 0;\r\n\t\t\tthis.is_address = options.is_address ? true : false;\r\n\t\t\tif (this.isLogin) {\r\n\t\t\t\t//this.getaddressInfo();\r\n\t\t\t\tthis.getloadPreOrder();\r\n\t\t\t} else {\r\n\t\t\t\ttoLogin();\r\n\t\t\t}\r\n\t\t},\r\n\t\t/**\r\n\t\t * 生命周期函数--监听页面显示\r\n\t\t */\r\n\t\tonShow: function() {\r\n\t\t\tlet _this = this\r\n\t\t\t// wx.getLaunchOptionsSync \r\n\t\t\tthis.textareaStatus = true;\r\n\t\t\tif (this.isLogin && this.toPay == false) {\r\n\t\t\t\t//this.getaddressInfo();\r\n\t\t\t\t\r\n\t\t\t}\r\n\r\n\t\t\tuni.$on(\"handClick\", res => {\r\n\t\t\t\tif (res) {\r\n\t\t\t\t\t_this.system_store = res.address\r\n\t\t\t\t}\r\n\t\t\t\t// 清除监听\r\n\t\t\t\tuni.$off('handClick');\r\n\t\t\t})\r\n\t\t\t// let pages = getCurrentPages();\r\n\t\t\t// let currPage = pages[pages.length - 1]; //当前页面\r\n\t\t\t// if (currPage.data.storeItem) {\r\n\t\t\t// \tlet json = currPage.data.storeItem;\r\n\t\t\t// \tthis.$set(this, 'system_store', json);\r\n\t\t\t// }\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetNavH(marTop){\r\n\t\t\t\tthis.marTop = marTop;\r\n\t\t\t},\r\n\t\t\t add:function(count){\r\n\t\t\t          \r\n\t\t\t       },\r\n\t\t\t        subtract:function(count){\r\n\t\t\t          if (this.count<=0){\r\n\t\t\t            alert('受不了啦，宝贝不能再减少啦')\r\n\t\t\t            this.count=0;\r\n\t\t\t          }else {\r\n\t\t\t            this.count-=1;\r\n\t\t\t          }\r\n\t\t\t        },\r\n\t\t\t// 订单详情\r\n\t\t\tgetloadPreOrder: function() {\r\n\t\t\t\tloadPreOrderApi(this.preOrderNo).then(res => {\r\n\t\t\t\t\tlet orderInfoVo = res.data.orderInfoVo;\r\n\t\t\t\t\tthis.orderInfoVo = orderInfoVo;\r\n\t\t\t\t\tthis.cartInfo = orderInfoVo.orderDetailList;\r\n\t\t\t\t\tthis.orderProNum = orderInfoVo.orderProNum;\r\n\t\t\t\t\tthis.address.addressId = this.addressId ? this.addressId :orderInfoVo.addressId;\r\n\t\t\t\t\tthis.cartArr[1].title = '可用余额:' + orderInfoVo.userBalance;\r\n\t\t\t\t\tthis.cartArr[1].payStatus = parseInt(res.data.yuePayStatus) === 1 ? 1 : 2;\r\n\t\t\t\t\tthis.cartArr[0].payStatus = parseInt(res.data.payWeixinOpen) === 1 ? 1 : 0;\r\n\t\t\t\t\t// #ifndef MP\r\n\t\t\t\t\tthis.cartArr[2].payStatus = parseInt(res.data.aliPayStatus) === 1 ? 1 : 2;\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\tif(this.$wechat.isWeixin()) this.cartArr.pop();\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.store_self_mention = (res.data.storeSelfMention == '1' || res.data.storeSelfMention == 'true')&& this.productType === 'normal' ? true : false;\r\n\t\t\t\t\t//调用子页面方法授权后执行获取地址列表\r\n\t\t\t\t\tthis.$nextTick(function() {\r\n\t\t\t\t\t\tthis.$refs.addressWindow.getAddressList();\r\n\t\t\t\t\t})\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl:'/pages/users/order_list/index'\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tbindHideKeyAddressinfo1:function(e){\r\n\t\t\t\tthis.sendAddress = e.detail.value;\r\n\t\t\t},\t\t\t\t\r\n\t\t\t/**\r\n\t\t\t * 授权回调事件\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tonLoadFun: function() {\r\n\t\t\t\t//this.getaddressInfo();\r\n\t\t\t\t//调用子页面方法授权后执行获取地址列表\r\n\t\t\t\t// this.$scope.selectComponent('#address-window').getAddressList();\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 获取门店列表数据\r\n\t\t\t */\r\n\t\t\tgetList: function() {\r\n\t\t\t\tlet longitude = uni.getStorageSync(\"user_longitude\"); //经度\r\n\t\t\t\tlet latitude = uni.getStorageSync(\"user_latitude\"); //纬度\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tlatitude: latitude, //纬度\r\n\t\t\t\t\tlongitude: longitude, //经度\r\n\t\t\t\t\tpage: 1,\r\n\t\t\t\t\tlimit: 10\r\n\t\t\t\t}\r\n\t\t\t\tstoreListApi(data).then(res => {\r\n\t\t\t\t\tlet list = res.data.list || [];\r\n\t\t\t\t\tthis.$set(this, 'storeList', list);\r\n\t\t\t\t\tthis.$set(this, 'system_store', list[0]);\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\treturn this.$util.Tips({\r\n\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 关闭地址弹窗；\r\n\t\t\tchangeClose: function() {\r\n\t\t\t\tthis.$set(this.address, 'address', false);\r\n\t\t\t},\r\n\t\t\t/*\r\n\t\t\t * 跳转门店列表\r\n\t\t\t */\r\n\t\t\tshowStoreList: function() {\r\n\t\t\t\tlet _this = this\r\n\t\t\t\tif (this.storeList.length > 0) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/users/goods_details_store/index'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 计算订单价格\r\n\t\t\tcomputedPrice: function() {\r\n\t\t\t\tlet shippingType = this.shippingType;\r\n\t\t\t\tpostOrderComputed({\r\n\t\t\t\t\taddressId: this.address.addressId,\r\n\t\t\t\t\tuseIntegral: this.useIntegral ? true : false,\r\n\t\t\t\t\tcouponId: this.couponId,\r\n\t\t\t\t\tshippingType: parseInt(shippingType) + 1,\r\n\t\t\t\t\tpreOrderNo: this.preOrderNo\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tlet data = res.data;\r\n\t\t\t\t\tthis.orderInfoVo.couponFee = data.couponFee;\r\n\t\t\t\t\t//赋值操作，userIntegral 当前积分，surplusIntegral 剩余积分\r\n\t\t\t\t\tthis.orderInfoVo.userIntegral = data.surplusIntegral;\r\n\t\t\t\t\tthis.orderInfoVo.deductionPrice = data.deductionPrice;\r\n\t\t\t\t\tthis.orderInfoVo.freightFee = data.freightFee;\r\n\t\t\t\t\tthis.orderInfoVo.payFee = data.payFee;\r\n\t\t\t\t\tthis.orderInfoVo.proTotalFee = data.proTotalFee;\r\n\t\t\t\t\tthis.orderInfoVo.useIntegral = data.useIntegral;\r\n\t\t\t\t\tthis.orderInfoVo.usedIntegral = data.usedIntegral;\r\n\t\t\t\t\tthis.orderInfoVo.surplusIntegral = data.surplusIntegral;\r\n\t\t\t\t\t//this.orderInfoVo.userIntegral = data.userIntegral;\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\treturn this.$util.Tips({\r\n\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 计算订单价格\r\n\t\t\tcomputedPrice1: function(e) {\r\n\t\t\t\tthis.orderInfoVo.freightFee = e.detail.value;\r\n\t\t\t\tlet shippingType = this.shippingType;\r\n\t\t\t\tpostOrderComputed({\r\n\t\t\t\t\taddressId: this.address.addressId,\r\n\t\t\t\t\tuseIntegral: this.useIntegral ? true : false,\r\n\t\t\t\t\tcouponId: this.couponId,\r\n\t\t\t\t\tfreightFee: this.orderInfoVo.freightFee,\r\n\t\t\t\t\tshippingType: parseInt(shippingType) + 1,\r\n\t\t\t\t\tpreOrderNo: this.preOrderNo\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tlet data = res.data;\r\n\t\t\t\t\tthis.orderInfoVo.couponFee = data.couponFee;\r\n\t\t\t\t\t//赋值操作，userIntegral 当前积分，surplusIntegral 剩余积分\r\n\t\t\t\t\tthis.orderInfoVo.userIntegral = data.surplusIntegral;\r\n\t\t\t\t\tthis.orderInfoVo.deductionPrice = data.deductionPrice;\r\n\t\t\t\t\tthis.orderInfoVo.freightFee = data.freightFee;\r\n\t\t\t\t\tthis.orderInfoVo.payFee = data.payFee;\r\n\t\t\t\t\tthis.orderInfoVo.proTotalFee = data.proTotalFee;\r\n\t\t\t\t\tthis.orderInfoVo.useIntegral = data.useIntegral;\r\n\t\t\t\t\tthis.orderInfoVo.usedIntegral = data.usedIntegral;\r\n\t\t\t\t\tthis.orderInfoVo.surplusIntegral = data.surplusIntegral;\r\n\t\t\t\t\t//this.orderInfoVo.userIntegral = data.userIntegral;\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\treturn this.$util.Tips({\r\n\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\taddressType: function(e) {\r\n\t\t\t\tlet index = e;\r\n\t\t\t\tthis.shippingType = parseInt(index);\r\n\t\t\t\tthis.computedPrice();\r\n\t\t\t\tif (index == 1) this.getList();\r\n\t\t\t},\r\n\t\t\tbindPickerChange: function(e) {\r\n\t\t\t\tlet value = e.detail.value;\r\n\t\t\t\tthis.shippingType = value;\r\n\t\t\t\tthis.computedPrice();\r\n\t\t\t},\r\n\t\t\tChangCouponsClone: function() {\r\n\t\t\t\tthis.$set(this.coupon, 'coupon', false);\r\n\t\t\t},\r\n\t\t\tchangeTextareaStatus: function() {\r\n\t\t\t\tfor (let i = 0, len = this.coupon.list.length; i < len; i++) {\r\n\t\t\t\t\tthis.coupon.list[i].use_title = '';\r\n\t\t\t\t\tthis.coupon.list[i].is_use = 0;\r\n\t\t\t\t}\r\n\t\t\t\tthis.textareaStatus = true;\r\n\t\t\t\tthis.status = 0;\r\n\t\t\t\tthis.$set(this.coupon, 'list', this.coupon.list);\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 处理点击优惠券后的事件\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tChangCoupons: function(e) {\r\n\t\t\t\t// this.usableCoupon = e\r\n\t\t\t\t// this.coupon.coupon = false\r\n\t\t\t\tlet index = e,\r\n\t\t\t\t\tlist = this.coupon.list,\r\n\t\t\t\t\tcouponTitle = '请选择',\r\n\t\t\t\t\tcouponId = 0;\r\n\t\t\t\tfor (let i = 0, len = list.length; i < len; i++) {\r\n\t\t\t\t\tif (i != index) {\r\n\t\t\t\t\t\tlist[i].use_title = '';\r\n\t\t\t\t\t\tlist[i].isUse = 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (list[index].isUse) {\r\n\t\t\t\t\t//不使用优惠券\r\n\t\t\t\t\tlist[index].use_title = '';\r\n\t\t\t\t\tlist[index].isUse = 0;\r\n\t\t\t\t} else {\r\n\t\t\t\t\t//使用优惠券\r\n\t\t\t\t\tlist[index].use_title = '不使用';\r\n\t\t\t\t\tlist[index].isUse = 1;\r\n\t\t\t\t\tcouponTitle = list[index].name;\r\n\t\t\t\t\tcouponId = list[index].id;\r\n\t\t\t\t}\r\n\t\t\t\tthis.couponTitle = couponTitle;\r\n\t\t\t\tthis.couponId = couponId;\r\n\t\t\t\tthis.$set(this.coupon, 'coupon', false);\r\n\t\t\t\tthis.$set(this.coupon, 'list', list);\r\n\t\t\t\tthis.computedPrice();\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 使用积分抵扣\r\n\t\t\t */\r\n\t\t\tChangeIntegral: function() {\r\n\t\t\t\tthis.useIntegral = !this.useIntegral;\r\n\t\t\t\tthis.computedPrice();\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 首次进页面展示默认地址\r\n\t\t\t */\r\n\t\t\tOnDefaultAddress: function(e) {\r\n\t\t\t\tthis.addressInfo = e;\r\n\t\t\t\tthis.address.addressId = e.id;\r\n\t\t\t\tif(this.addressInfo) this.computedPrice();\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 选择地址后改变事件\r\n\t\t\t * @param object e\r\n\t\t\t */\r\n\t\t\tOnChangeAddress: function(e) {\r\n\t\t\t\tthis.addressInfo = e;\r\n\t\t\t\tthis.address.addressId = e.id;\r\n\t\t\t\tthis.textareaStatus = true;\r\n\t\t\t\t//this.orderInfoVo.addressId = e;\r\n\t\t\t\tthis.address.address = false;\r\n\t\t\t\t//this.getaddressInfo();\r\n\t\t\t\tthis.computedPrice();\r\n\t\t\t},\r\n\t\t\tgetAddressRecognition: function(e){\r\n\t\t\t\tvar temp = e.detail.value;\r\n\t\t\t\t\r\n\t\t\t\tif(this.addressRecognitionDetail != temp.trim && temp.trim){\r\n\t\t\t\t\taddressRecognition({\r\n\t\t\t\t\t\taddressRecognition:e.detail.value,\r\n\t\t\t\t\t\tid:this.addressRecognitionId\r\n\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\tif (res.data) {\r\n\t\t\t\t\t\t\tthis.addressRecognitionId = res.data.id;\r\n\t\t\t\t\t\t\tthis.addressInfo = res.data;\r\n\t\t\t\t\t\t\tthis.address.addressId = res.data.id;\r\n\t\t\t\t\t\t\tthis.addressRecognitionDetail = e.detail.value;//保存旧地址\r\n\t\t\t\t\t\t\tif(this.addressInfo) this.computedPrice();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\treturn this.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\tbindHideKeyboard: function(e) {\r\n\t\t\t\tthis.mark = e.detail.value;\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 获取当前金额可用优惠券\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tgetCouponList: function() {\r\n\t\t\t\tgetCouponsOrderPrice(this.preOrderNo).then(res => {\r\n\t\t\t\t\tthis.$set(this.coupon, 'list', res.data);\r\n\t\t\t\t\tthis.openType = 1;\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/*\r\n\t\t\t * 获取默认收货地址或者获取某条地址信息\r\n\t\t\t */\r\n\t\t\tgetaddressInfo: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (that.addressId) {\r\n\t\t\t\t\tgetAddressDetail(that.addressId).then(res => {\r\n\t\t\t\t\t\tif (res.data) {\r\n\t\t\t\t\t\t\tres.data.isDefault = parseInt(res.data.isDefault);\r\n\t\t\t\t\t\t\tthat.addressInfo = res.data || {};\r\n\t\t\t\t\t\t\tthat.addressId = res.data.id || 0;\r\n\t\t\t\t\t\t\tthat.address.addressId = res.data.id || 0;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tgetAddressDefault().then(res => {\r\n\t\t\t\t\t\tif (res.data) {\r\n\t\t\t\t\t\t\tres.data.isDefault = parseInt(res.data.isDefault);\r\n\t\t\t\t\t\t\tthat.addressInfo = res.data || {};\r\n\t\t\t\t\t\t\tthat.addressId = res.data.id || 0;\r\n\t\t\t\t\t\t\tthat.address.addressId = res.data.id || 0;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tpayItem: function(e) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet active = e;\r\n\t\t\t\tthat.active = active;\r\n\t\t\t\tthat.animated = true;\r\n\t\t\t\tthat.payType = that.cartArr[active].value;\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tif(that.payType == 'alipay' && this.$wechat.isWeixin() == true){\r\n\t\t\t\t\tthat.payChannel = 'public';\r\n\t\t\t\t}else if(that.payType == 'alipay' && this.$wechat.isWeixin() == false){\r\n\t\t\t\t\tthat.payChannel = 'weixinh5';\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tif(that.payType == 'alipay'){\r\n\t\t\t\t\tthat.payChannel = 'appAliPay';\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\t//that.computedPrice();\r\n\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\tthat.car();\r\n\t\t\t\t}, 500);\r\n\t\t\t},\r\n\t\t\tcouponTap: function() {\r\n\t\t\t\tthis.coupon.coupon = true;\r\n\t\t\t\tif(!this.coupon.list.length)this.getCouponList();\r\n\t\t\t},\r\n\t\t\tcar: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthat.animated = false;\r\n\t\t\t},\r\n\t\t\tonAddress: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthat.textareaStatus = false;\r\n\t\t\t\tthat.address.address = true;\r\n\t\t\t\tthat.pagesUrl = '/pages/users/user_address_list/index?preOrderNo='+ this.preOrderNo;\r\n\t\t\t},\r\n\t\t\tpayment: function(data) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\torderCreate(data).then(res => {\r\n\t\t\t\t\tthat.getOrderPay(res.data.orderNo, '支付成功');\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t}, '/pages/users/order_list/index'\r\n\t\t\t\t\t);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetOrderPay: function(orderNo, message) {\r\n\t\t\t\tlet that = this; \r\n\t\t\t\tlet goPages = '/pages/order_pay_status/index?order_id=' + orderNo;\r\n\t\t\t\torderPay({\r\n\t\t\t\t\torderNo: orderNo,\r\n\t\t\t\t\tpayChannel: that.payChannel,\r\n\t\t\t\t\tpayType: that.payType,\r\n\t\t\t\t\tscene: that.productType==='normal'? 0 :1177 //下单时小程序的场景值\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tlet jsConfig = res.data.jsConfig;\r\n\t\t\t\t\tswitch (res.data.payType) {\r\n\t\t\t\t\t\tcase 'weixin':\r\n\t\t\t\t\t\t\tthat.weixinPay(jsConfig,orderNo,goPages);\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'yue':\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: message\r\n\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\t\turl: goPages + '&status=1'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'weixinh5':\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\tlocation.href = jsConfig.mwebUrl + '&redirect_url=' + window.location.protocol + '//' + window.location.host +goPages + '&status=1';\r\n\t\t\t\t\t\t\t}, 100)\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'alipay':\r\n\t\t\t\t\t\t\t//#ifdef H5\r\n\t\t\t\t\t\t\tif (this.$wechat.isWeixin()) {\r\n\t\t\t\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\t\t\t\turl: `/pages/users/alipay_invoke/index?id=${orderNo}&type=order`\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t//h5支付\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tthat.formContent = res.data.alipayRequest;\r\n\t\t\t\t\t\t\t\tuni.setStorage({key: 'orderNo', data:orderNo});\r\n\t\t\t\t\t\t\t\tthat.$nextTick(() => {\r\n\t\t\t\t\t\t\t\t\tdocument.forms['punchout_form'].submit();\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t//#endif\r\n\t\t\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\t\t\tlet alipayRequest = res.data.alipayRequest;\r\n\t\t\t\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\t\t\t\tprovider: 'alipay',\r\n\t\t\t\t\t\t\t\torderInfo: alipayRequest,\r\n\t\t\t\t\t\t\t\tsuccess: (e) => {\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: \"支付成功\"\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\tsetTimeout(res => {\r\n\t\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\t\turl:'/pages/users/alipay_return/alipay_return?out_trade_no=' + orderNo + '&payChannel=' + 'appAlipay'\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t}, 1000) \r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfail: (e) => {\r\n\t\t\t\t\t\t\t\t\tconsole.log(e,'失败');\r\n\t\t\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\t\t\tcontent: \"支付失败\",\r\n\t\t\t\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t//点击确认的操作\r\n\t\t\t\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\turl:'/pages/users/alipay_return/alipay_return?out_trade_no=' + orderNo + '&payChannel=' + 'appAlipay'\r\n\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tcomplete: () => {\r\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tweixinPay(jsConfig,orderNo,goPages){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\t// #ifdef MP\r\n\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\ttimeStamp: jsConfig.timeStamp,\r\n\t\t\t\t\tnonceStr: jsConfig.nonceStr,\r\n\t\t\t\t\tpackage: jsConfig.packages,\r\n\t\t\t\t\tsignType: jsConfig.signType,\r\n\t\t\t\t\tpaySign: jsConfig.paySign,\r\n\t\t\t\t\tticket: that.productType==='normal'? null : jsConfig.ticket,\r\n\t\t\t\t\tsuccess: function(ress) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\topenOrderSubscribe().then(()=>{\r\n\t\t\t\t\t\t\tif (that.orderInfoVo.bargainId || that.orderInfoVo.combinationId || that.pinkId || that.orderInfoVo.seckillId){\r\n\t\t\t\t\t\t\t\treturn that.$util.Tips(\r\n\t\t\t\t\t\t\t\t{title: '支付成功',icon: 'success'}, \r\n\t\t\t\t\t\t\t\t{tab: 4,url: goPages});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\treturn that.$util.Tips(\r\n\t\t\t\t\t\t\t\t{title: '支付成功',icon: 'success'}, \r\n\t\t\t\t\t\t\t\t{tab: 5,url: goPages},);\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function(e) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\treturn that.$util.Tips(\r\n\t\t\t\t\t\t{title: '取消支付'}, \r\n\t\t\t\t\t\t{tab: 5,url: goPages + '&status=2'});\r\n\t\t\t\t\t},\r\n\t\t\t\t\tcomplete: function(e) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t//关闭当前页面跳转至订单状态\r\n\t\t\t\t\t\tif (e.errMsg == 'requestPayment:cancel') return that.$util.Tips(\r\n\t\t\t\t\t\t\t{title: '取消支付'}, \r\n\t\t\t\t\t\t\t{tab: 5,url: goPages + '&status=2'});\r\n\t\t\t\t\t},\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\ttimestamp: jsConfig.timeStamp,\r\n\t\t\t\t\tnonceStr: jsConfig.nonceStr,\r\n\t\t\t\t\tpackage: jsConfig.packages,\r\n\t\t\t\t\tsignType: jsConfig.signType,\r\n\t\t\t\t\tpaySign: jsConfig.paySign\r\n\t\t\t\t};\r\n\t\t\t\tthat.$wechat.pay(data).then(res => {\r\n\t\t\t\t\tif (res.errMsg == 'chooseWXPay:cancel') {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\treturn that.$util.Tips(\r\n\t\t\t\t\t\t{title: '取消支付'}, \r\n\t\t\t\t\t\t{tab: 5,url: goPages + '&status=2'});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\twechatQueryPayResult(orderNo).then(res => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\treturn that.$util.Tips(\r\n\t\t\t\t\t\t\t{title: '支付成功',icon: 'success'},\r\n\t\t\t\t\t\t\t{tab: 5,url: goPages});\r\n\t\t\t\t\t\t}).cache(err => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}).cache(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\treturn that.$util.Tips(\r\n\t\t\t\t\t{title: '取消支付'}, \r\n\t\t\t\t\t{tab: 5,url: goPages + '&status=2'});\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\tprovider: 'wxpay',\r\n\t\t\t\t\torderInfo: {\r\n\t\t\t\t\t\t\"appid\": jsConfig.appId, // 微信开放平台 - 应用 - AppId，注意和微信小程序、公众号 AppId 可能不一致\r\n\t\t\t\t\t\t\"noncestr\": jsConfig.nonceStr, // 随机字符串\r\n\t\t\t\t\t\t\"package\": \"Sign=WXPay\", // 固定值\r\n\t\t\t\t\t\t\"partnerid\": jsConfig.partnerid, // 微信支付商户号\r\n\t\t\t\t\t\t\"prepayid\": jsConfig.packages, // 统一下单订单号 \r\n\t\t\t\t\t\t\"timestamp\": Number(jsConfig.timeStamp), // 时间戳（单位：秒）\r\n\t\t\t\t\t\t\"sign\": this.systemPlatform === 'ios' ? 'MD5' : jsConfig.paySign // 签名，这里用的 MD5 签名\r\n\t\t\t\t\t}, //微信、支付宝订单数据 【注意微信的订单信息，键值应该全部是小写，不能采用驼峰命名】\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\twechatQueryPayResult(orderNo).then(res => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tlet url = '/pages/order_pay_status/index?order_id=' + orderNo +'&msg=支付成功';\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: \"支付成功\"\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tsetTimeout(res => {\r\n\t\t\t\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\t\t\t\turl: url\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}, 2000)\r\n\t\t\t\t\t\t}).cache(err => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tlet url = '/pages/order_pay_status/index?order_id=' + orderNo +'&msg=支付失败';\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\tcontent: \"支付失败\",\r\n\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\t\t\t\t\turl: url\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t},\r\n\t\t\t\t\tcomplete: (err) => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tSubOrder(e) {\r\n\t\t\t\tlet that = this,\r\n\t\t\t\t\tdata = {};\r\n\t\t\t\t\r\n\t\t\t\tif (!that.payType) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '请选择支付方式'\r\n\t\t\t\t});\r\n\t\t\t\t// alert(that.address.addressId);  this.addressInfo.realNam\r\n\t\t\t\tif (!that.address.addressId && !that.shippingType) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '请选择收货地址'\r\n\t\t\t\t});\r\n\t\t\t\tif (that.shippingType == 1) {\r\n\t\t\t\t\tif (that.storeList.length == 0) return that.$util.Tips({\r\n\t\t\t\t\t\ttitle: '暂无门店,请选择其他方式'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\tdata = {\r\n\t\t\t\t\taddressId: that.address.addressId,\r\n\t\t\t\t\tcouponId: that.couponId,\r\n\t\t\t\t\tpayType: that.payType,\r\n\t\t\t\t\tuseIntegral: that.useIntegral,\r\n\t\t\t\t\tpreOrderNo: that.preOrderNo,\r\n\t\t\t\t\tmark: that.mark,\r\n\t\t\t\t\tstoreId: that.system_store.id || 0,\r\n\t\t\t\t\tshippingType: that.$util.$h.Add(that.shippingType, 1),\r\n\t\t\t\t\tpayChannel: that.payChannel,\r\n\t\t\t\t\tsendAddress: that.sendAddress\r\n\r\n\t\t\t\t};\r\n\t\t\t\tif (data.payType == 'yue' && parseFloat(that.userInfo.nowMoney) < parseFloat(that.totalPrice))\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: '余额不足！'\r\n\t\t\t\t\t});\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '订单支付中'\r\n\t\t\t\t});\r\n\t\t\t\t// #ifdef MP\r\n\t\t\t\topenPaySubscribe().then(() => {\r\n\t\t\t\t\tthat.payment(data);\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef MP\r\n\t\t\t\tthat.payment(data);\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.font_color{\r\n\t\t@include main_color(theme);\r\n\t}\r\n\t.price_color{\r\n\t\t@include price_color(theme);\r\n\t}\r\n\t.line2{\r\n\t\twidth: 504rpx;\r\n\t}\r\n\t.textR {\r\n\t\ttext-align: right;\r\n\t}\r\n\r\n\t.order-submission .line {\r\n\t\twidth: 100%;\r\n\t\theight: 3rpx;\r\n\t}\r\n\r\n\t.order-submission .line image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.order-submission .address {\r\n\t\tpadding: 28rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.order-submission .address .addressCon {\r\n\t\twidth: 596rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.order-submission .address .addressCon .name {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #282828;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\r\n\t.order-submission .address .addressCon .name .phone {\r\n\t\tmargin-left: 50rpx;\r\n\t}\r\n\r\n\t.order-submission .address .addressCon .default {\r\n\t\tmargin-right: 12rpx;\r\n\t}\r\n\r\n\t.order-submission .address .addressCon .setaddress {\r\n\t\tcolor: #333;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t.order-submission .address .iconfont {\r\n\t\tfont-size: 35rpx;\r\n\t\tcolor: #707070;\r\n\t}\r\n\r\n\t.order-submission .allAddress {\r\n\t\twidth: 100%;\r\n\t\t@include index-gradient(theme);\r\n\t\tpadding: 83rpx 30rpx 0 30rpx;\r\n\t}\r\n\r\n\t.order-submission .allAddress .nav {\r\n\t\twidth: 690rpx;\r\n\t\tmargin: 0 auto;\r\n\t}\r\n\r\n\t.order-submission .allAddress .nav .item {\r\n\t\twidth: 334rpx;\r\n\t}\r\n\r\n\t.order-submission .allAddress .nav .item.on {\r\n\t\tposition: relative;\r\n\t\twidth: 230rpx;\r\n\t}\r\n\r\n\t.order-submission .allAddress .nav .item.on::before {\r\n\t\tposition: absolute;\r\n\t\tbottom: 0;\r\n\t\tcontent: \"快递配送\";\r\n\t\tfont-size: 28rpx;\r\n\t\tdisplay: block;\r\n\t\theight: 0;\r\n\t\twidth: 336rpx;\r\n\t\tborder-width: 0 20rpx 80rpx 0;\r\n\t\tborder-style: none solid solid;\r\n\t\tborder-color: transparent transparent #fff;\r\n\t\tz-index: 2;\r\n\t\tborder-radius: 14rpx 36rpx 0 0;\r\n\t\ttext-align: center;\r\n\t\tline-height: 80rpx;\r\n\t}\r\n\r\n\t.order-submission .allAddress .nav .item:nth-of-type(2).on::before {\r\n\t\tcontent: \"到店自提\";\r\n\t\tborder-width: 0 0 80rpx 20rpx;\r\n\t\tborder-radius: 36rpx 14rpx 0 0;\r\n\t}\r\n\r\n\t.order-submission .allAddress .nav .item.on2 {\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.order-submission .allAddress .nav .item.on2::before {\r\n\t\tposition: absolute;\r\n\t\tbottom: 0;\r\n\t\tcontent: \"到店自提\";\r\n\t\tfont-size: 28rpx;\r\n\t\tdisplay: block;\r\n\t\theight: 0;\r\n\t\twidth: 401rpx;\r\n\t\tborder-width: 0 0 60rpx 60rpx;\r\n\t\tborder-style: none solid solid;\r\n\t\tborder-color: transparent transparent rgba(255,255,255,0.6);\r\n\t\tborder-radius: 36rpx 14rpx 0 0;\r\n\t\ttext-align: center;\r\n\t\tline-height: 60rpx;\r\n\t}\r\n\r\n\t.order-submission .allAddress .nav .item:nth-of-type(1).on2::before {\r\n\t\tcontent: \"快递配送\";\r\n\t\tborder-width: 0 60rpx 60rpx 0;\r\n\t\tborder-radius: 14rpx 36rpx 0 0;\r\n\t}\r\n\r\n\t.order-submission .allAddress .address {\r\n\t\twidth: 690rpx;\r\n\t\tmax-height: 180rpx;\r\n\t\tmargin: -2rpx auto 0 auto;\r\n\t}\r\n\r\n\t.order-submission .allAddress .line {\r\n\t\twidth: 100%;\r\n\t\tmargin: 0 auto;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item .discount .placeholder {\r\n\t\tcolor: #ccc;\r\n\t}\r\n\r\n\t.order-submission .wrapper {\r\n\t\tbackground-color: #fff;\r\n\t\tmargin-top: 15rpx;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item {\r\n\t\tpadding: 27rpx 24rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #333333;\r\n\t\tborder-bottom: 1px solid #F5F5F5;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item .discount {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\t\r\n\t.order-submission .wrapper .item .discount1 {\r\n\t\tdisplay: flex;\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\t\r\n\t.order-submission .wrapper .item .reduce {\r\n\t\tdisplay: flex;\r\n\t\tfont-size: 30rpx;\r\n\t\tborder-color: #e3e3e3;\r\n\t\tcolor: #dedede;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item .discount .iconfont {\r\n\t\tcolor: #515151;\r\n\t\tfont-size: 30rpx;\r\n\t\tmargin-left: 15rpx;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item .discount .num {\r\n\t\tfont-size: 32rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item .shipping {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #999;\r\n\t\tposition: relative;\r\n\t\tpadding-right: 58rpx;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item .shipping .iconfont {\r\n\t\tfont-size: 35rpx;\r\n\t\tcolor: #707070;\r\n\t\tposition: absolute;\r\n\t\tright: 0;\r\n\t\ttop: 50%;\r\n\t\ttransform: translateY(-50%);\r\n\t\tmargin-left: 30rpx;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item textarea {\r\n\t\tbackground-color: #f9f9f9;\r\n\t\twidth: auto !important;\r\n\t\theight: 140rpx;\r\n\t\tborder-radius: 14rpx;\r\n\t\tmargin-top: 30rpx;\r\n\t\tpadding: 15rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tfont-weight: 400;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item .placeholder {\r\n\t\tcolor: #ccc;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item .list {\r\n\t\tmargin-top: 35rpx;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item .list .payItem {\r\n\t\tborder: 1px solid #eee;\r\n\t\tborder-radius: 14rpx;\r\n\t\theight: 86rpx;\r\n\t\twidth: 100%;\r\n\t\tbox-sizing: border-box;\r\n\t\tmargin-top: 20rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #282828;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item .list .payItem.on {\r\n\t\t// border-color: #fc5445;\r\n\t\t@include coupons_border_color(theme);\r\n\t\tcolor: $theme-color;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item .list .payItem .name {\r\n\t\twidth: 50%;\r\n\t\ttext-align: center;\r\n\t\tborder-right: 1px solid #eee;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item .list .payItem .name .iconfont {\r\n\t\twidth: 44rpx;\r\n\t\theight: 44rpx;\r\n\t\tborder-radius: 50%;\r\n\t\ttext-align: center;\r\n\t\tline-height: 44rpx;\r\n\t\tbackground-color: #fe960f;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 30rpx;\r\n\t\tmargin-right: 15rpx;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item .list .payItem .name .iconfont.icon-weixin2 {\r\n\t\tbackground-color: #41b035;\r\n\t}\r\n\t.order-submission .wrapper .item .list .payItem .name .iconfont.icon-zhifubao {\r\n\t\tbackground-color: #00AAEA;\r\n\t}\r\n\t.order-submission .wrapper .item .list .payItem .tip {\r\n\t\twidth: 49%;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #aaa;\r\n\t}\r\n\r\n\t.order-submission .moneyList {\r\n\t\tmargin-top: 15rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tpadding: 30rpx;\r\n\t\tmargin-bottom: calc(constant(safe-area-inset-bottom)); ///兼容 IOS<11.2/\r\n\t\tmargin-bottom: calc(env(safe-area-inset-bottom)); ///兼容 IOS>11.2/\r\n\t}\r\n\r\n\t.order-submission .moneyList .item {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #282828;\r\n\t}\r\n\r\n\t.order-submission .moneyList .item~.item {\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\r\n\t.order-submission .moneyList .item .money {\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t.order-submission .footer {\r\n\t\twidth: 100%;\r\n\t\theight: 100rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tpadding: 0 30rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333;\r\n\t\tbox-sizing: border-box;\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\t\tleft: 0;\r\n\t\theight: calc(100rpx+ constant(safe-area-inset-bottom)); ///兼容 IOS<11.2/\r\n\t\theight: calc(100rpx + env(safe-area-inset-bottom)); ///兼容 IOS>11.2/\r\n\t}\r\n\r\n\t.order-submission .footer .settlement {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #fff;\r\n\t\twidth: 240rpx;\r\n\t\theight: 70rpx;\r\n\t\t@include main_bg_color(theme);\r\n\t\tborder-radius: 50rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 70rpx;\r\n\t}\r\n\r\n\t.footer .transparent {\r\n\t\topacity: 0\r\n\t}\r\n\t/deep/ checkbox .uni-checkbox-input.uni-checkbox-input-checked {\r\n\t\t@include main_bg_color(theme);\r\n\t\tborder: none !important;\r\n\t\tcolor: #fff!important\r\n\t}\r\n\t\r\n\t/deep/ checkbox .wx-checkbox-input.wx-checkbox-input-checked {\r\n\t\t@include main_bg_color(theme);\r\n\t\tborder: none !important;\r\n\t\tcolor: #fff!important;\r\n\t\tmargin-right: 0 !important;\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=064d693c&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=064d693c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294179691\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}