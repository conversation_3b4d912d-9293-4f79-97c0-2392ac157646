{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_address/index.vue?2c7e", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_address/index.vue?a66c", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_address/index.vue?6cd5", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_address/index.vue?34ca", "uni-app:///pages/users/user_address/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_address/index.vue?370c", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_address/index.vue?849e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "authorize", "home", "data", "cartId", "pinkId", "couponId", "id", "userAddress", "isDefault", "region", "valueRegion", "isAuto", "isShowAuth", "district", "multiArray", "multiIndex", "cityId", "bargain", "combination", "secKill", "theme", "showLoading", "computed", "watch", "is<PERSON>ogin", "handler", "deep", "onLoad", "uni", "title", "methods", "getUserAddress", "that", "initialize", "province", "city", "area", "bindRegionChange", "child", "value", "bindMultiPickerColumnChange", "column", "currentCity", "onLoadFun", "auth<PERSON><PERSON><PERSON>", "toggleTab", "onConfirm", "chooseLocation", "success", "console", "getWxAddress", "scope", "addressP", "address", "realName", "postCode", "phone", "detail", "setTimeout", "url", "res", "delta", "icon", "fail", "Tips", "content", "get<PERSON><PERSON><PERSON>", "then", "catch", "formSubmit", "mask", "preOrderNo", "ChangeIsDefault"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC0DnnB;AAIA;AAGA;AAGA;AAGA;AAOA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;AAAA,eACA;EACAC;IAEAC;IAEAC;EACA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;QACAC;MACA;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;EACAC;IACAC;MACAC;QACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IAAA;IACA;MACA;MACA;MACA;IACA;MACA;MACAC;QACAC;MACA;MACA;QACA;QACA;QACAD;QACA;MACA;IACA;IACA;MACA;MACA;MACAA;QACAC;MACA;MACA;IACA;MACA;IACA;EACA;EACAC;IAOAC;MACA;MACA;MACA;QACA;UACA;UACAC;UACAA;UACAA;QACA;MACA;IACA;IACAC;MACA;QAAAC;QAAAC;QAAAC;MACA;QACA;QACA;QACAJ;UACAE;UACA;YACAF;YACAA;UACA;QACA;QACAA;UACAG;UACA;YACAH;YACAA;UACA;QACA;QACAA;UACAI;UACA;YACAJ;YACAA;UACA;QACA;QACA;MACA;IACA;IACAK;MACA;QACAH;UACAI;QACA;QACAH;UACAG;QACA;QACAF;UACApB;QACA;QACAF;QACAyB;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;QACAC;QACAF;QACAG;UACAJ;QACA;QACAxB;QACAC;MACAA;MACA;QACA;UACA;YACAuB;UACA;UACAxB;YACA;UACA;UACAA;YACA;UACA;UACA;QACA;UACA;UACAA;YACA;UACA;UACA;QACA;UAEA;MAAA;MAGA;MACA;MACA;MAKA;MACA;IACA;;IACA;IACA6B;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IAEAC;MAAA;MACAnB;QACAoB;UACAC;UACA;UACA;UACA;QACA;MACA;IACA;;IACA;IACAC;MACA;MACAtB;QACAuB;QACAH;UACApB;YACAoB;cACA;cACAI;cACAA;cACAA;cACAA;cACA;gBACAC;gBACA7C;gBACA8C;gBACAC;gBACAC;gBACAC;gBACAnD;cACA;gBACAoD;kBACA;oBACA;oBACA;oBACA;oBACA1B;oBACAA;oBACAA;oBACAJ;sBACA+B,kDACAxD,SACA,iBACA6B,eACA1B,KACAsD,SACAtD,MACA,aACAF,SACA,eACAC,WACA,mBACAc,UACA,kBACAa,mBACA,mBACAf;oBACA;kBACA;oBACAW;sBACAiC;oBACA;kBACA;gBACA;gBACA;kBACAhC;kBACAiC;gBACA;cACA;gBACA;kBACAjC;gBACA;cACA;YACA;YACAkC;cACA,4DACAC;gBACAnC;cACA;YACA;UACA;QACA;QACAkC;UACAnC;YACAC;YACAoC;YACAjB;cACA;gBACApB;kBACAoB;gBACA;cACA;gBACA;kBACAnB;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAqC;MAAA;MACA;MACAlC;QACA;QACA;UACA1B;UACAgD;UACAE;UACAH;YACAnB;YACAC;YACAtB;YACAG;UACA;UACAyC;UACAjD;UACA+C;QACA,GACAY;UACAT;YACA;cACA;cACA;cACA;cACA1B;cACAA;cACAA;cACAJ;gBACA+B,kDACAxD,8CACAyD,SACAtD,2CACAD,wCACA,mDACA2B;cACA;YACA;cACAJ;gBACA+B;cACA;cACA;YACA;UACA;UACA;UACA3B;YACAH;YACAiC;UACA;QACA,GACAM;UACA;UACA;YACAvC;UACA;QACA;MACA;QACAoB;MACA;IACA;IACA;AACA;AACA;AACA;IACAoB;MACA;QACA9B;MACA;QACAV;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;MACAU;MACA;MACAA;QACAL;QACAC;QACAtB;QACAG;MACA;MACAuB;MAEAX;QACAC;QACAyC;MACA;MACA;QACA,aACAtC;UACAH;UACAiC;QACA,QAEA9B;UACAH;UACAiC;QACA;QACAJ;UACA;YACA9B;cACA+B,2DACAY,sDACArE;YACA;UACA;YAKA;cACA2D;YACA;UAEA;QACA;MACA;QACA;UACAhC;QACA;MACA;IACA;IACA2C;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjgBA;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/users/user_address/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/users/user_address/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=1c98413a&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=1c98413a&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1c98413a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/users/user_address/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=1c98413a&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :data-theme=\"theme\">\r\n\t\t<form @submit=\"formSubmit\" report-submit='true'>\r\n\t\t\t<view class='addAddress pad30'>\r\n\t\t\t\t<view class='list borRadius14'>\r\n\t\t\t\t\t<view class='item acea-row row-between-wrapper' style=\"border: none;\">\r\n\t\t\t\t\t\t<view class='name'>姓名</view>\r\n\t\t\t\t\t\t<input type='text' placeholder='请输入姓名' placeholder-style=\"color:#ccc;\" name='realName'\r\n\t\t\t\t\t\t\t:value=\"userAddress.realName\" placeholder-class='placeholder' maxlength=\"20\"></input>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t<view class='name'>联系电话</view>\r\n\t\t\t\t\t\t<input type='number' placeholder='请输入联系电话' placeholder-style=\"color:#ccc;\" name=\"phone\"\r\n\t\t\t\t\t\t\t:value='userAddress.phone' placeholder-class='placeholder' maxlength=\"11\"></input>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item acea-row row-between-wrapper relative'>\r\n\t\t\t\t\t\t<view class='name'>所在地区</view>\r\n\t\t\t\t\t\t<view class=\"address\">\r\n\t\t\t\t\t\t\t<picker mode=\"multiSelector\" @change=\"bindRegionChange\"\r\n\t\t\t\t\t\t\t\t@columnchange=\"bindMultiPickerColumnChange\" :value=\"valueRegion\" :range=\"multiArray\">\r\n\t\t\t\t\t\t\t\t<view class='acea-row'>\r\n\t\t\t\t\t\t\t\t\t<view class=\"picker line1\">{{region[0]}}，{{region[1]}}，{{region[2]}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class='iconfont icon-xiangyou abs_right'></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item acea-row row-between-wrapper relative'>\r\n\t\t\t\t\t\t<view class='name'>详细地址</view>\r\n\t\t\t\t\t\t<input type='text' placeholder='请填写具体地址' placeholder-style=\"color:#ccc;\" name='detail'\r\n\t\t\t\t\t\t\tplaceholder-class='placeholder' v-model='userAddress.detail' maxlength=\"14\"></input>\r\n\t\t\t\t\t\t<view class='iconfont icon-dizhi font_color abs_right' @tap=\"chooseLocation\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='default acea-row row-middle borRadius14'>\r\n\t\t\t\t\t<checkbox-group @change='ChangeIsDefault'>\r\n\t\t\t\t\t\t<checkbox :checked=\"userAddress.isDefault\" />设置为默认地址\r\n\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<button class='keepBnt bg_color' form-type=\"submit\">立即保存</button>\r\n\t\t\t\t<!-- #ifdef MP -->\r\n\t\t\t\t<view class=\"wechatAddress\" v-if=\"!id\" @click=\"getWxAddress\">导入微信地址</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t<view class=\"wechatAddress\" v-if=\"this.$wechat.isWeixin() && !id\" @click=\"getAddress\">导入微信地址</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t</view>\r\n\t\t</form>\r\n\t\t<view v-show=\"showLoading\" class=\"bg-fixed\"></view>\r\n\t\t<!-- #ifdef MP -->\r\n\t\t<!-- <authorize @onLoadFun=\"onLoadFun\" :isAuto=\"isAuto\" :isShowAuth=\"isShowAuth\" @authColse=\"authColse\"></authorize> -->\r\n\t\t<!-- #endif -->\r\n\t\t<!-- <home></home> -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\teditAddress,\r\n\t\tgetAddressDetail\r\n\t} from '@/api/user.js';\r\n\timport {\r\n\t\tgetCityList\r\n\t} from \"@/utils\";\r\n\timport {\r\n\t\tgetCity\r\n\t} from '@/api/api.js';\r\n\timport {\r\n\t\ttoLogin\r\n\t} from '@/libs/login.js';\r\n\timport {\r\n\t\tmapGetters\r\n\t} from \"vuex\";\r\n\t// #ifdef MP\r\n\timport authorize from '@/components/Authorize';\r\n\t// #endif\r\n\timport home from '@/components/home';\r\n\timport {\r\n\t\tDebounce\r\n\t} from '@/utils/validate.js'\r\n\tlet app = getApp();\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\t// #ifdef MP\r\n\t\t\tauthorize,\r\n\t\t\t// #endif\r\n\t\t\thome\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcartId: '', //购物车id\r\n\t\t\t\tpinkId: 0, //拼团id\r\n\t\t\t\tcouponId: 0, //优惠券id\r\n\t\t\t\tid: 0, //地址id\r\n\t\t\t\tuserAddress: {\r\n\t\t\t\t\tisDefault: false\r\n\t\t\t\t}, //地址详情\r\n\t\t\t\tregion: ['省', '市', '区'],\r\n\t\t\t\tvalueRegion: [0, 0, 0],\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false, //是否隐藏授权\r\n\t\t\t\tdistrict: [],\r\n\t\t\t\tmultiArray: [],\r\n\t\t\t\tmultiIndex: [0, 0, 0],\r\n\t\t\t\tcityId: 0,\r\n\t\t\t\tbargain: false, //是否是砍价\r\n\t\t\t\tcombination: false, //是否是拼团\r\n\t\t\t\tsecKill: false, //是否是秒杀\r\n\t\t\t\ttheme: app.globalData.theme,\r\n\t\t\t\tshowLoading: false\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: mapGetters(['isLogin']),\r\n\t\twatch: {\r\n\t\t\tisLogin: {\r\n\t\t\t\thandler: function(newV, oldV) {\r\n\t\t\t\t\tif (newV) {\r\n\t\t\t\t\t\tthis.getUserAddress();\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tdeep: true\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\tif (this.$Cache.getItem('cityList')) {\r\n\t\t\t\t//检测城市数据缓存是否过期，有的话从缓存取，没有的话请求接口\r\n\t\t\t\tthis.district = this.$Cache.getItem('cityList');\r\n\t\t\t\tthis.initialize();\r\n\t\t\t} else {\r\n\t\t\t\tthis.showLoading = true;\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '数据加载中...'\r\n\t\t\t\t});\r\n\t\t\t\tgetCityList().then(res=>{\r\n\t\t\t\t\tthis.district = res\r\n\t\t\t\t\tthis.initialize();\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthis.showLoading = false;\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tif (this.isLogin) {\r\n\t\t\t\tthis.preOrderNo = options.preOrderNo || 0;\r\n\t\t\t\tthis.id = options.id || 0;\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: options.id ? '修改地址' : '添加地址'\r\n\t\t\t\t})\r\n\t\t\t\tthis.getUserAddress();\r\n\t\t\t} else {\r\n\t\t\t\ttoLogin();\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\t// 获取选择的地区\r\n\t\t\thandleGetRegion(region) {\r\n\t\t\t\tthis.region = region\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\tgetUserAddress: function() {\r\n\t\t\t\tif (!this.id) return false;\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetAddressDetail(this.id).then(res => {\r\n\t\t\t\t\tif(res.data){\r\n\t\t\t\t\t\tlet region = [res.data.province, res.data.city, res.data.district];\r\n\t\t\t\t\t\tthat.$set(that, 'userAddress', res.data);\r\n\t\t\t\t\t\tthat.$set(that, 'region', region);\r\n\t\t\t\t\t\tthat.city_id = res.data.cityId;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tinitialize: function() {\r\n\t\t\t\tlet that = this,province = [],city = [],area = [];\r\n\t\t\t\tif (that.district.length) {\r\n\t\t\t\t\tlet cityChildren = that.district[0].child || [];\r\n\t\t\t\t\tlet areaChildren = cityChildren.length ? (cityChildren[0].child || []) : [];\r\n\t\t\t\t\tthat.district.forEach(function(item,i) {\r\n\t\t\t\t\t\tprovince.push(item.name);\r\n\t\t\t\t\t\tif (item.name === that.region[0]) {\r\n\t\t\t\t\t\t\tthat.valueRegion[0] = i\r\n\t\t\t\t\t\t\tthat.multiIndex[0] = i\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthat.district[this.valueRegion[0]].child.forEach((item,i)=>{\r\n\t\t\t\t\t\tcity.push(item.name);\r\n\t\t\t\t\t\tif (that.region[1] == item.name) {\r\n\t\t\t\t\t\t\tthat.valueRegion[1] = i\r\n\t\t\t\t\t\t\tthat.multiIndex[1] = i\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthat.district[this.valueRegion[0]].child[this.valueRegion[1]].child.forEach((item,i)=>{\r\n\t\t\t\t\t\tarea.push(item.name);\r\n\t\t\t\t\t\tif (that.region[2] == item.name) {\r\n\t\t\t\t\t\t\tthat.valueRegion[2] = i\r\n\t\t\t\t\t\t\tthat.multiIndex[2] = i\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.multiArray = [province, city, area]\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tbindRegionChange: function(e) {\r\n\t\t\t\tlet multiIndex = this.multiIndex,\r\n\t\t\t\t\tprovince = this.district[multiIndex[0]] || {\r\n\t\t\t\t\t\tchild: []\r\n\t\t\t\t\t},\r\n\t\t\t\t\tcity = province.child[multiIndex[1]] || {\r\n\t\t\t\t\t\tchild: []\r\n\t\t\t\t\t},\r\n\t\t\t\t\tarea = city.child[multiIndex[2]] || {\r\n\t\t\t\t\t\tcityId: 0\r\n\t\t\t\t\t},\r\n\t\t\t\t\tmultiArray = this.multiArray,\r\n\t\t\t\t\tvalue = e.detail.value;\r\n\t\t\t\tthis.region = [multiArray[0][value[0]], multiArray[1][value[1]], multiArray[2][value[2]]]\r\n\t\t\t\tthis.cityId = area.cityId;\r\n\t\t\t\tthis.valueRegion = [0, 0, 0]\r\n\t\t\t\tthis.initialize();\r\n\t\t\t},\r\n\t\t\tbindMultiPickerColumnChange: function(e) {\r\n\t\t\t\tlet that = this,\r\n\t\t\t\t\tcolumn = e.detail.column,\r\n\t\t\t\t\tvalue = e.detail.value,\r\n\t\t\t\t\tcurrentCity = this.district[value] || {\r\n\t\t\t\t\t\tchild: []\r\n\t\t\t\t\t},\r\n\t\t\t\t\tmultiArray = that.multiArray,\r\n\t\t\t\t\tmultiIndex = that.multiIndex;\r\n\t\t\t\tmultiIndex[column] = value;\r\n\t\t\t\tswitch (column) {\r\n\t\t\t\t\tcase 0:\r\n\t\t\t\t\t\tlet areaList = currentCity.child[0] || {\r\n\t\t\t\t\t\t\tchild: []\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\tmultiArray[1] = currentCity.child.map((item) => {\r\n\t\t\t\t\t\t\treturn item.name;\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tmultiArray[2] = areaList.child.map((item) => {\r\n\t\t\t\t\t\t\treturn item.name;\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 1:\r\n\t\t\t\t\t\tlet cityList = that.district[multiIndex[0]].child[multiIndex[1]].child || [];\r\n\t\t\t\t\t\tmultiArray[2] = cityList.map((item) => {\r\n\t\t\t\t\t\t\treturn item.name;\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 2:\r\n\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t\t// #ifdef MP || APP-PLUS\r\n\t\t\t\tthis.$set(this.multiArray, 0, multiArray[0]);\r\n\t\t\t\tthis.$set(this.multiArray, 1, multiArray[1]);\r\n\t\t\t\tthis.$set(this.multiArray, 2, multiArray[2]);\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tthis.multiArray = multiArray;\r\n\t\t\t\t// #endif\r\n\t\t\t\tthis.multiIndex = multiIndex\r\n\t\t\t\t// this.setData({ multiArray: multiArray, multiIndex: multiIndex});\r\n\t\t\t},\r\n\t\t\t// 授权回调\r\n\t\t\tonLoadFun: function() {\r\n\t\t\t\tthis.getUserAddress();\r\n\t\t\t},\r\n\t\t\t// 授权关闭\r\n\t\t\tauthColse: function(e) {\r\n\t\t\t\tthis.isShowAuth = e\r\n\t\t\t},\r\n\t\t\ttoggleTab(str) {\r\n\t\t\t\tthis.$refs[str].show();\r\n\t\t\t},\r\n\t\t\tonConfirm(val) {\r\n\t\t\t\tthis.region = val.checkArr[0] + '-' + val.checkArr[1] + '-' + val.checkArr[2];\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\tchooseLocation: function() {\r\n\t\t\t\tuni.chooseLocation({\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\tthis.$set(this.userAddress, 'detail', res.name);\r\n\t\t\t\t\t\t// this.$set(this.userAddress, 'detail', res.address.replace(/.+?(省|市|自治区|自治州|县|区)/g,\r\n\t\t\t\t\t\t// \t''));\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 导入共享地址（小程序）\r\n\t\t\tgetWxAddress: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tuni.authorize({\r\n\t\t\t\t\tscope: 'scope.address',\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tuni.chooseAddress({\r\n\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\tlet addressP = {};\r\n\t\t\t\t\t\t\t\taddressP.province = res.provinceName;\r\n\t\t\t\t\t\t\t\taddressP.city = res.cityName;\r\n\t\t\t\t\t\t\t\taddressP.district = res.countyName;\r\n\t\t\t\t\t\t\t\taddressP.cityId = 0;\r\n\t\t\t\t\t\t\t\teditAddress({\r\n\t\t\t\t\t\t\t\t\taddress: addressP,\r\n\t\t\t\t\t\t\t\t\tisDefault: 1,\r\n\t\t\t\t\t\t\t\t\trealName: res.userName,\r\n\t\t\t\t\t\t\t\t\tpostCode: res.postalCode,\r\n\t\t\t\t\t\t\t\t\tphone: res.telNumber,\r\n\t\t\t\t\t\t\t\t\tdetail: res.detailInfo,\r\n\t\t\t\t\t\t\t\t\tid: 0\r\n\t\t\t\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t\t\t\tif (that.cartId) {\r\n\t\t\t\t\t\t\t\t\t\t\tlet cartId = that.cartId;\r\n\t\t\t\t\t\t\t\t\t\t\tlet pinkId = that.pinkId;\r\n\t\t\t\t\t\t\t\t\t\t\tlet couponId = that.couponId;\r\n\t\t\t\t\t\t\t\t\t\t\tthat.cartId = '';\r\n\t\t\t\t\t\t\t\t\t\t\tthat.pinkId = '';\r\n\t\t\t\t\t\t\t\t\t\t\tthat.couponId = '';\r\n\t\t\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\t\t\turl: '/pages/users/order_confirm/index?cartId=' +\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcartId +\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t'&addressId=' + (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tthat.id ? that\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t.id :\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tres.data\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t.id) +\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t'&pinkId=' +\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tpinkId +\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t'&couponId=' +\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcouponId +\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t'&secKill=' + that\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t.secKill +\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t'&combination=' +\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tthat.combination +\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t'&bargain=' + that\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t.bargain\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\t\t\t\t\t\t\t\tdelta: 1\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: \"添加成功\",\r\n\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail: function(res) {\r\n\t\t\t\t\t\t\t\tif (res.errMsg == 'chooseAddress:cancel') return that.$util\r\n\t\t\t\t\t\t\t\t\t.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '取消选择'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function(res) {\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\ttitle: '您已拒绝导入微信地址权限',\r\n\t\t\t\t\t\t\tcontent: '是否进入权限管理，调整授权？',\r\n\t\t\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\tuni.openSetting({\r\n\t\t\t\t\t\t\t\t\t\tsuccess: function(res) {}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '已取消！'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t},\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 导入共享地址（微信）；\r\n\t\t\tgetAddress() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthat.$wechat.openAddress().then(userInfo => {\r\n\t\t\t\t\t// open();\r\n\t\t\t\t\teditAddress({\r\n\t\t\t\t\t\t\tid: this.id,\r\n\t\t\t\t\t\t\trealName: userInfo.userName,\r\n\t\t\t\t\t\t\tphone: userInfo.telNumber,\r\n\t\t\t\t\t\t\taddress: {\r\n\t\t\t\t\t\t\t\tprovince: userInfo.provinceName,\r\n\t\t\t\t\t\t\t\tcity: userInfo.cityName,\r\n\t\t\t\t\t\t\t\tdistrict: userInfo.countryName,\r\n\t\t\t\t\t\t\t\tcityId: 0\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tdetail: userInfo.detailInfo,\r\n\t\t\t\t\t\t\tisDefault: 1,\r\n\t\t\t\t\t\t\tpostCode: userInfo.postalCode\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.then(() => {\r\n\t\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t\tif (that.cartId) {\r\n\t\t\t\t\t\t\t\t\tlet cartId = that.cartId;\r\n\t\t\t\t\t\t\t\t\tlet pinkId = that.pinkId;\r\n\t\t\t\t\t\t\t\t\tlet couponId = that.couponId;\r\n\t\t\t\t\t\t\t\t\tthat.cartId = '';\r\n\t\t\t\t\t\t\t\t\tthat.pinkId = '';\r\n\t\t\t\t\t\t\t\t\tthat.couponId = '';\r\n\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\turl: '/pages/users/order_confirm/index?cartId=' +\r\n\t\t\t\t\t\t\t\t\t\t\tcartId + '&addressId=' + (that.id ? that.id :\r\n\t\t\t\t\t\t\t\t\t\t\t\tres.data\r\n\t\t\t\t\t\t\t\t\t\t\t\t.id) + '&pinkId=' + pinkId + '&couponId=' +\r\n\t\t\t\t\t\t\t\t\t\t\tcouponId + '&secKill=' + that.secKill +\r\n\t\t\t\t\t\t\t\t\t\t\t'&combination=' + that.combination + '&bargain=' +\r\n\t\t\t\t\t\t\t\t\t\t\tthat.bargain\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\turl: '/pages/users/user_address_list/index'\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t// history.back();\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t\t\t// close();\r\n\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: \"添加成功\",\r\n\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\t\t// close();\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: err || \"添加失败\"\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tconsole.log(err);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 提交用户添加地址\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tformSubmit: Debounce(function(e) {\r\n\t\t\t\tlet that = this,\r\n\t\t\t\t\tvalue = e.detail.value;\r\n\t\t\t\tif (!value.realName) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '请填写收货人姓名'\r\n\t\t\t\t});\r\n\t\t\t\tif (!value.phone) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '请填写联系电话'\r\n\t\t\t\t});\r\n\t\t\t\tif (!/^1(3|4|5|7|8|9|6)\\d{9}$/i.test(value.phone)) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '请输入正确的手机号码'\r\n\t\t\t\t});\r\n\t\t\t\tif (that.region == '省-市-区') return that.$util.Tips({\r\n\t\t\t\t\ttitle: '请选择所在地区'\r\n\t\t\t\t});\r\n\t\t\t\tif (!value.detail) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '请填写详细地址'\r\n\t\t\t\t});\r\n\t\t\t\tvalue.id = that.id;\r\n\t\t\t\tlet regionArray = that.region;\r\n\t\t\t\tvalue.address = {\r\n\t\t\t\t\tprovince: regionArray[0],\r\n\t\t\t\t\tcity: regionArray[1],\r\n\t\t\t\t\tdistrict: regionArray[2],\r\n\t\t\t\t\tcityId: that.cityId,\r\n\t\t\t\t};\r\n\t\t\t\tvalue.isDefault = that.userAddress.isDefault;\r\n\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '保存中',\r\n\t\t\t\t\tmask: true\r\n\t\t\t\t})\r\n\t\t\t\teditAddress(value).then(res => {\r\n\t\t\t\t\tif (that.id)\r\n\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: '修改成功',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\telse\r\n\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: '添加成功',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\tif (that.preOrderNo > 0) {\r\n\t\t\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\t\t\turl: '/pages/users/order_confirm/index?preOrderNo=' + that\r\n\t\t\t\t\t\t\t\t\t.preOrderNo + '&addressId=' + (that.id ? that.id : res\r\n\t\t\t\t\t\t\t\t\t\t.data.id)\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\t\t\treturn history.back();\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t// #ifndef H5\r\n\t\t\t\t\t\t\treturn uni.navigateBack({\r\n\t\t\t\t\t\t\t\tdelta: 1,\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t}),\r\n\t\t\tChangeIsDefault: function(e) {\r\n\t\t\t\tthis.$set(this.userAddress, 'isDefault', !this.userAddress.isDefault);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.bg-fixed{\r\n\t\twidth: 100%;\r\n\t\theight: 750rpx;\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t}\r\n\t.addAddress {\r\n\t\tpadding-top: 20rpx;\r\n\t}\r\n\r\n\t.bg_color {\r\n\t\t@include main_bg_color(theme);\r\n\t}\r\n\r\n\t.addAddress .list {\r\n\t\tbackground-color: #fff;\r\n\t\tpadding: 0 24rpx;\r\n\t}\r\n\r\n\t.addAddress .list .item {\r\n\t\tborder-top: 1rpx solid #eee;\r\n\t\theight: 90rpx;\r\n\t\tline-height: 90rpx;\r\n\t}\r\n\r\n\t.addAddress .list .item .name {\r\n\t\t// width: 195rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.addAddress .list .item .address {\r\n\t\tflex: 1;\r\n\t\tmargin-left: 50rpx;\r\n\t}\r\n\r\n\t.addAddress .list .item input {\r\n\t\twidth: 475rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: 400;\r\n\t}\r\n\r\n\t.addAddress .list .item .placeholder {\r\n\t\tcolor: #ccc;\r\n\t}\r\n\r\n\t.addAddress .list .item picker .picker {\r\n\t\twidth: 410rpx;\r\n\t\tfont-size: 30rpx;\r\n\t}\r\n\r\n\t.addAddress .default {\r\n\t\tpadding: 0 30rpx;\r\n\t\theight: 90rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tmargin-top: 23rpx;\r\n\t}\r\n\r\n\t.addAddress .default checkbox {\r\n\t\tmargin-right: 15rpx;\r\n\t}\r\n\r\n\t.addAddress .keepBnt {\r\n\t\twidth: 690rpx;\r\n\t\theight: 86rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 86rpx;\r\n\t\tmargin: 80rpx auto 24rpx auto;\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #fff;\r\n\t}\r\n\r\n\t.addAddress .wechatAddress {\r\n\t\twidth: 690rpx;\r\n\t\theight: 86rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 86rpx;\r\n\t\tmargin: 0 auto;\r\n\t\tfont-size: 32rpx;\r\n\t\t// color: #E93323 ;\r\n\t\t@include main_color(theme);\r\n\t\t@include coupons_border_color(theme);\r\n\t}\r\n\r\n\t.font_color {\r\n\t\t@include main_color(theme);\r\n\t}\r\n\r\n\t.relative {\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.icon-dizhi {\r\n\t\tfont-size: 44rpx;\r\n\t\tz-index: 100;\r\n\t}\r\n\r\n\t.abs_right {\r\n\t\tposition: absolute;\r\n\t\tright: 0;\r\n\t}\r\n\r\n\t/deep/ checkbox .uni-checkbox-input.uni-checkbox-input-checked {\r\n\t\t@include main_bg_color(theme);\r\n\t\t@include coupons_border_color(theme);\r\n\t\tcolor: #fff !important\r\n\t}\r\n\r\n\t/deep/ checkbox .wx-checkbox-input.wx-checkbox-input-checked {\r\n\t\t@include main_bg_color(theme);\r\n\t\t@include coupons_border_color(theme);\r\n\t\tcolor: #fff !important;\r\n\t\tmargin-right: 0 !important;\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=1c98413a&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=1c98413a&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294179754\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}