{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_pwd_edit/index.vue?51b1", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_pwd_edit/index.vue?7615", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_pwd_edit/index.vue?5fe7", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_pwd_edit/index.vue?cb3e", "uni-app:///pages/users/user_pwd_edit/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_pwd_edit/index.vue?8c28", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_pwd_edit/index.vue?d35d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "mixins", "data", "userInfo", "phone", "password", "<PERSON><PERSON>a", "qr_password", "isAuto", "isShowAuth", "theme", "bgColor", "computed", "watch", "is<PERSON>ogin", "handler", "deep", "onLoad", "that", "uni", "frontColor", "backgroundColor", "methods", "onLoadFun", "auth<PERSON><PERSON><PERSON>", "getUserInfo", "code", "title", "checkPasd", "checkPassword", "editPwd", "account", "tab", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC0BnnB;AACA;AAIA;AAGA;AAGA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AAAA,eACA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;EACAC;IACAC;MACAC;QACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IACA;MACA;IACA;MACA;IACA;IACA;IACAC;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;QACA;QACA;QACAP;QACAA;MACA;IACA;IACA;AACA;AACA;AACA;IACAQ;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAR;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA;kBACAS;gBACA;cAAA;gBAAA;gBAAA,OACA;kBACAT;oBACAS;kBACA;kBACAT;gBACA;kBACA;oBACAS;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;QAAAvB;MACAa;MACA;MACA;QACAS;MACA;IACA;IACAE;MACA;QAAAtB;MACA;QACAoB;MACA;IACA;IACAG;MACA;QACAzB;QACAE;QACAD;MACA;QACAqB;MACA;MACA;QACAA;MACA;MACA;AACA;AACA;MACA;QACAI;QACAzB;QACAD;MACA;QACA;UACAsB;QACA;UACAK;UACAC;QACA;MACA;QACA;UACAN;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7KA;AAAA;AAAA;AAAA;AAA0oC,CAAgB,gnCAAG,EAAC,C;;;;;;;;;;;ACA9pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/users/user_pwd_edit/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/users/user_pwd_edit/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=751ef713&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/users/user_pwd_edit/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=751ef713&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :data-theme=\"theme\" class=\"upda_pasd\">\r\n\t\t<view class=\"ChangePassword\">\r\n\t\t\t<form @submit=\"editPwd\" report-submit='true'>\r\n\t\t\t\t<view class=\"phone\">当前手机号：{{phone}}</view>\r\n\t\t\t\t<view class=\"list\">\r\n\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t<input type='password' placeholder='6-18位字母数字' placeholder-class='placeholder' name=\"password\" :value=\"password\" maxlength=\"18\" @blur=\"checkPasd\"></input>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t<input type='password' placeholder='确认新密码' placeholder-class='placeholder' name=\"qr_password\" :value=\"qr_password\" maxlength=\"18\" @blur=\"checkPassword\"></input>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- <view class=\"item acea-row row-between-wrapper\">\r\n\t\t\t\t\t\t<input type='number' placeholder='填写验证码' placeholder-class='placeholder' class=\"codeIput\" name=\"captcha\" :value=\"captcha\" maxlength=\"6\"></input>\r\n\t\t\t\t\t\t<button class=\"code\" :class=\"disabled === true ? 'on' : ''\" :disabled='disabled' @click=\"code\">\r\n\t\t\t\t\t\t\t{{ text }}\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t</view>\r\n\t\t\t\t<button form-type=\"submit\" class=\"confirmBnt\">确认修改</button>\r\n\t\t\t</form>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport sendVerifyCode from \"@/mixins/SendVerifyCode\";\r\n\timport {\r\n\t\tphoneRegisterReset,\r\n\t\tregisterVerify\r\n\t} from '@/api/api.js';\r\n\timport {\r\n\t\tgetUserInfo\r\n\t} from '@/api/user.js';\r\n\timport {\r\n\t\ttoLogin\r\n\t} from '@/libs/login.js';\r\n\timport {mapGetters} from \"vuex\";\r\n\timport {setThemeColor} from '@/utils/setTheme.js'\r\n\tconst app = getApp();\r\n\texport default {\r\n\t\tmixins: [sendVerifyCode],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tuserInfo: {},\r\n\t\t\t\tphone: '',\r\n\t\t\t\tpassword: '',\r\n\t\t\t\tcaptcha: '',\r\n\t\t\t\tqr_password: '',\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false, //是否隐藏授权\r\n\t\t\t\ttheme:app.globalData.theme,\r\n\t\t\t\tbgColor:''\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: mapGetters(['isLogin']),\r\n\t\twatch:{\r\n\t\t\tisLogin:{\r\n\t\t\t\thandler:function(newV,oldV){\r\n\t\t\t\t\tif(newV){\r\n\t\t\t\t\t\tthis.getUserInfo();\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tdeep:true\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tif (this.isLogin) {\r\n\t\t\t\tthis.getUserInfo();\r\n\t\t\t} else {\r\n\t\t\t\ttoLogin();\r\n\t\t\t}\r\n\t\t\tlet that = this;\r\n\t\t\tthat.bgColor = setThemeColor();\r\n\t\t\tuni.setNavigationBarColor({\r\n\t\t\t\tfrontColor: '#ffffff',\r\n\t\t\t\tbackgroundColor:that.bgColor,\r\n\t\t\t});\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t/**\r\n\t\t\t * 授权回调\r\n\t\t\t */\r\n\t\t\tonLoadFun: function(e) {\r\n\t\t\t\tthis.getUserInfo();\r\n\t\t\t},\r\n\t\t\t// 授权关闭\r\n\t\t\tauthColse: function(e) {\r\n\t\t\t\tthis.isShowAuth = e\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 获取个人用户信息\r\n\t\t\t */\r\n\t\t\tgetUserInfo: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetUserInfo().then(res => {\r\n\t\t\t\t\tlet tel = res.data.phone;\r\n\t\t\t\t\tlet phone = tel.substr(0, 3) + \"****\" + tel.substr(7);\r\n\t\t\t\t\tthat.$set(that, 'userInfo', res.data);\r\n\t\t\t\t\tthat.phone = phone;\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 发送验证码\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tasync code() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (!that.userInfo.phone) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '手机号码不存在,无法发送验证码！'\r\n\t\t\t\t});\r\n\t\t\t\tawait registerVerify(that.userInfo.phone).then(res => {\r\n\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\ttitle: res.message\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthat.sendCode();\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * H5登录 修改密码\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tcheckPasd(e){\r\n\t\t\t\tlet that = this,password = e.detail.value;\r\n\t\t\t\tthat.password = password;\r\n\t\t\t\t// if (!/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,8}$/i.test(password)) return that.$util.Tips({\r\n\t\t\t\tif (!/^[a-zA-Z]\\w{5,17}$/i.test(password)) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '密码规则为6-18位字母加数字'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tcheckPassword(e){\r\n\t\t\t\tlet that = this,qr_password = e.detail.value;\r\n\t\t\t\tif (qr_password != that.password) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '两次输入的密码不一致！'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\teditPwd: function(e) {\r\n\t\t\t\tlet that = this,\r\n\t\t\t\t\tpassword = e.detail.value.password,\r\n\t\t\t\t\tqr_password = e.detail.value.qr_password,\r\n\t\t\t\t\tcaptcha = e.detail.value.captcha;\r\n\t\t\t\tif (!password) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '请输入新密码'\r\n\t\t\t\t});\r\n\t\t\t\tif (!qr_password) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '请确认新密码'\r\n\t\t\t\t});\r\n\t\t\t\t/* if (!captcha) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '请输入验证码'\r\n\t\t\t\t}); */\r\n\t\t\t\tphoneRegisterReset({\r\n\t\t\t\t\taccount: that.userInfo.phone,\r\n\t\t\t\t\tcaptcha: captcha,\r\n\t\t\t\t\tpassword: password\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: res.message\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\ttab: 3,\r\n\t\t\t\t\t\turl: 1\r\n\t\t\t\t\t});\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\tpage {\r\n\t\tbackground-color: #fff !important;\r\n\t}\r\n\t.upda_pasd{\r\n\t\tbackground: #FFFFFF;\r\n\t\theight: 100vh;\r\n\t}\r\n\t.ChangePassword .phone {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\ttext-align: center;\r\n\t\tpadding-top: 100rpx;\r\n\t}\r\n\r\n\t.ChangePassword .list {\r\n\t\twidth: 580rpx;\r\n\t\tmargin: 53rpx auto 0 auto;\r\n\t}\r\n\r\n\t.ChangePassword .list .item {\r\n\t\twidth: 100%;\r\n\t\theight: 110rpx;\r\n\t\tborder-bottom: 2rpx solid #f0f0f0;\r\n\t}\r\n\r\n\t.ChangePassword .list .item input {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tfont-size: 32rpx;\r\n\t}\r\n\r\n\t.ChangePassword .list .item .placeholder {\r\n\t\tcolor: #b9b9bc;\r\n\t}\r\n\r\n\t.ChangePassword .list .item input.codeIput {\r\n\t\twidth: 340rpx;\r\n\t}\r\n\r\n\t.ChangePassword .list .item .code {\r\n\t\tfont-size: 32rpx;\r\n\t\t// background-color: #fff;\r\n\t\t@include main_color(theme);\r\n\t}\r\n\r\n\t.ChangePassword .list .item .code.on {\r\n\t\tcolor: #b9b9bc !important;\r\n\t}\r\n\r\n\t.ChangePassword .confirmBnt {\r\n\t\tfont-size: 32rpx;\r\n\t\twidth: 580rpx;\r\n\t\theight: 90rpx;\r\n\t\tborder-radius: 45rpx;\r\n\t\tcolor: #fff;\r\n\t\tmargin: 92rpx auto 0 auto;\r\n\t\ttext-align: center;\r\n\t\tline-height: 90rpx;\r\n\t\t@include main_bg_color(theme);\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294179671\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}