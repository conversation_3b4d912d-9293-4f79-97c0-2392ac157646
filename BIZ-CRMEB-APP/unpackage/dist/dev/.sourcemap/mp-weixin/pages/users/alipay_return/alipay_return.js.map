{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/alipay_return/alipay_return.vue?e85c", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/alipay_return/alipay_return.vue?fe1d", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/alipay_return/alipay_return.vue?96c6", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/alipay_return/alipay_return.vue?d4cb", "uni-app:///pages/users/alipay_return/alipay_return.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/alipay_return/alipay_return.vue?24e9", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/alipay_return/alipay_return.vue?080d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "orderId", "payPrice", "order_pay_info", "paid", "_status", "isAuto", "isShowAuth", "status", "msg", "payResult", "payTime", "theme", "onLoad", "methods", "getOrderPayInfo", "uni", "title", "that", "alipayQueryPay", "goOrderDetails", "url", "goIndex"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACc;;;AAG1E;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAumB,CAAgB,ioBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC2C3nB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;QACAC;MACA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC,4BAiCA;EACAC;IACAC;MACA;MACAC;QACAC;MACA;MACA;QACAC;QACA;UACAA;QACA;QACAF;MACA;QACAA;MACA;IACA;IACAG;MAAA;MACA;QACA;QACAH;UACAC;QACA;QACA;QACAD;MACA;QACA;QACA;QACA;QACAA;QACA;UACAC;QACA;MACA;IACA;IACAG;MACAJ;QACAK;MACA;IACA;IACAC;MACAN;QACAK;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9IA;AAAA;AAAA;AAAA;AAAkpC,CAAgB,wnCAAG,EAAC,C;;;;;;;;;;;ACAtqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/users/alipay_return/alipay_return.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/users/alipay_return/alipay_return.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./alipay_return.vue?vue&type=template&id=15da524b&\"\nvar renderjs\nimport script from \"./alipay_return.vue?vue&type=script&lang=js&\"\nexport * from \"./alipay_return.vue?vue&type=script&lang=js&\"\nimport style0 from \"./alipay_return.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/users/alipay_return/alipay_return.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./alipay_return.vue?vue&type=template&id=15da524b&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./alipay_return.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./alipay_return.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :data-theme=\"theme\">\r\n\t\t<view class='payment-status'>\r\n\t\t\t<!--失败时： 用icon-iconfontguanbi fail替换icon-duihao2 bg-color-->\r\n\t\t\t<view class='iconfont icons icon-duihao2 bg_color'\r\n\t\t\t\tv-if=\"order_pay_info.paid === 1\"></view>\r\n\t\t\t<view v-if=\"order_pay_info.paid === 2\" class='iconfont icons icon-iconfontguanbi'></view>\r\n\t\t\t<!-- 失败时：订单支付失败 -->\r\n\t\t\t<view class='status'>{{payResult}}</view>\r\n\t\t\t<view class='wrapper'>\r\n\t\t\t\t<view class='item acea-row row-between-wrapper'>\r\n\t\t\t\t\t<view>订单编号</view>\r\n\t\t\t\t\t<view class='itemCom'>{{orderId}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='item acea-row row-between-wrapper'>\r\n\t\t\t\t\t<view>下单时间</view>\r\n\t\t\t\t\t<view class='itemCom'>{{order_pay_info.createTime?order_pay_info.createTime:'-'}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='item acea-row row-between-wrapper'>\r\n\t\t\t\t\t<view>支付方式</view>\r\n\t\t\t\t\t<view class='itemCom'>支付宝支付</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='item acea-row row-between-wrapper'>\r\n\t\t\t\t\t<view>支付金额</view>\r\n\t\t\t\t\t<view class='itemCom'>{{order_pay_info.payPrice}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!--失败时加上这个  -->\r\n\t\t\t\t<view class='item acea-row row-between-wrapper'\r\n\t\t\t\t\tv-if=\"order_pay_info.paid === 2\">\r\n\t\t\t\t\t<view>失败原因</view>\r\n\t\t\t\t\t<view class='itemCom'>{{msg}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!--失败时： 重新购买 -->\r\n\t\t\t<view @tap=\"goOrderDetails\">\r\n\t\t\t\t<button formType=\"submit\" class='returnBnt bg_color' hover-class='none'>查看订单</button>\r\n\t\t\t</view>\r\n\t\t\t<button @click=\"goIndex\" class='returnBnt cart-color' formType=\"submit\" hover-class='none'>返回首页</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {getOrderDetail,alipayQueryPayResult} from '@/api/order.js';\r\n\tlet app = getApp();\r\n\texport default{\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\torderId: '',\r\n\t\t\t\tpayPrice:'',\r\n\t\t\t\torder_pay_info: {\r\n\t\t\t\t\tpaid: 0,\r\n\t\t\t\t\t_status: {}\r\n\t\t\t\t},\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false, //是否隐藏授权\r\n\t\t\t\tstatus: 0,\r\n\t\t\t\tmsg: '',\r\n\t\t\t\tpayResult: '订单查询中...',\r\n\t\t\t\tpayTime:'',\r\n\t\t\t\ttheme:app.globalData.theme,\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad(e){\r\n\t\t\t// #ifdef H5\r\n\t\t\tvar url = window.location.search;\r\n\t\t\tif(url){\r\n\t\t\t\tvar theRequest = new Object();\r\n\t\t\t\tif (url.indexOf(\"?\") != -1) {\r\n\t\t\t\t    var str = url.substr(1);\r\n\t\t\t\t    var strs = str.split(\"&\");\r\n\t\t\t\t    for (var i = 0; i < strs.length; i++) {\r\n\t\t\t\t\t\ttheRequest[strs[i].split('=')[0]] = decodeURI(strs[i].split('=')[1]);\r\n\t\t\t\t    }\r\n\t\t\t\t}\r\n\t\t\t\tthis.orderId = theRequest.out_trade_no; //返回的订单号\r\n\t\t\t\tthis.getOrderPayInfo();\r\n\t\t\t}else{\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tuni.getStorage({\r\n\t\t\t\t    key: 'orderNo',\r\n\t\t\t\t    success: function (res) {\r\n\t\t\t\t        that.orderId = res.data; //如果是支付宝中途放弃支付跳转到这个页面，就从缓存读取订单号查询订单详情和支付结果\r\n\t\t\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\t\t\tthat.getOrderPayInfo();\r\n\t\t\t\t\t\t},200)\r\n\t\t\t\t    }\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t\t\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\tconsole.log(e);\r\n\t\t\tthis.orderId = e.out_trade_no;\r\n\t\t\tthis.getOrderPayInfo();\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tmethods:{\r\n\t\t\tgetOrderPayInfo: function() { \r\n\t\t\t\tlet that = this;\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '正在加载中'\r\n\t\t\t\t});\r\n\t\t\t\tgetOrderDetail(that.orderId).then(res => {\r\n\t\t\t\t\tthat.$set(that, 'order_pay_info', res.data);\r\n\t\t\t\t\tif(res.data.paid === false){\r\n\t\t\t\t\t\tthat.alipayQueryPay();\r\n\t\t\t\t\t}\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\talipayQueryPay() {\r\n\t\t\t\talipayQueryPayResult(this.orderId).then(res => {\r\n\t\t\t\t\tthis.payResult = '支付成功';\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: '支付成功'\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.order_pay_info.paid = 1;\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tthis.order_pay_info.paid = 2;\r\n\t\t\t\t\tthis.payResult = err;\r\n\t\t\t\t\tthis.msg = err;\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\treturn this.$util.Tips({\r\n\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgoOrderDetails(){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/order_details/index?order_id=' + this.orderId\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgoIndex(){\r\n\t\t\t\tuni.switchTab({\r\n\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style lang=\"scss\">\r\n\t.icon-iconfontguanbi {\r\n\t\tbackground-color: #999 !important;\r\n\t\ttext-shadow: none !important;\r\n\t}\r\n\t.bg_color{\r\n\t\t@include main_bg_color(theme);\r\n\t}\r\n\t.cart_color{\r\n\t\t@include main_color(theme);\r\n\t\t@include coupons_border_color(theme);\r\n\t}\r\n\t.payment-status {\r\n\t\tbackground-color: #fff;\r\n\t\tmargin: 195rpx 30rpx 0 30rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tpadding: 1rpx 0 28rpx 0;\r\n\t}\r\n\r\n\t.payment-status .icons {\r\n\t\tfont-size: 70rpx;\r\n\t\twidth: 140rpx;\r\n\t\theight: 140rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tcolor: #fff;\r\n\t\ttext-align: center;\r\n\t\tline-height: 140rpx;\r\n\t\ttext-shadow: 0px 4px 0px #df1e14;\r\n\t\tborder: 6rpx solid #f5f5f5;\r\n\t\tmargin: -76rpx auto 0 auto;\r\n\t\tbackground-color: #999;\r\n\t}\r\n\r\n\t.payment-status .iconfont {\r\n\t\tfont-size: 70rpx;\r\n\t\twidth: 140rpx;\r\n\t\theight: 140rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tcolor: #fff;\r\n\t\ttext-align: center;\r\n\t\tline-height: 140rpx;\r\n\t\ttext-shadow: 0px 4px 0px #df1e14;\r\n\t\tborder: 6rpx solid #f5f5f5;\r\n\t\tmargin: -76rpx auto 0 auto;\r\n\t\tbackground-color: #999;\r\n\t}\r\n\r\n\t.payment-status .iconfont.fail {\r\n\t\ttext-shadow: 0px 4px 0px #7a7a7a;\r\n\t}\r\n\r\n\t.payment-status .status {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\ttext-align: center;\r\n\t\tmargin: 25rpx 0 37rpx 0;\r\n\t}\r\n\r\n\t.payment-status .wrapper {\r\n\t\tborder: 1rpx solid #eee;\r\n\t\tmargin: 0 30rpx 47rpx 30rpx;\r\n\t\tpadding: 35rpx 0;\r\n\t\tborder-left: 0;\r\n\t\tborder-right: 0;\r\n\t}\r\n\r\n\t.payment-status .wrapper .item {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #282828;\r\n\t}\r\n\r\n\t.payment-status .wrapper .item~.item {\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\r\n\t.payment-status .wrapper .item .itemCom {\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.payment-status .returnBnt {\r\n\t\twidth: 630rpx;\r\n\t\theight: 86rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 30rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 86rpx;\r\n\t\tmargin: 0 auto 20rpx auto;\r\n\t\t\r\n\t}\r\n\t.cart-color {\r\n\t\t@include main_color(theme);\r\n\t\t@include coupons_border_color(theme);\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./alipay_return.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./alipay_return.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294178922\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}