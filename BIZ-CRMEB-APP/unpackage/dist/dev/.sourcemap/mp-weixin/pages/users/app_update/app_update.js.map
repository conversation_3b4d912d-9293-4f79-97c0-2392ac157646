{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/app_update/app_update.vue?346a", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/app_update/app_update.vue?4db6", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/app_update/app_update.vue?0ece", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/app_update/app_update.vue?eb2d", "uni-app:///pages/users/app_update/app_update.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/app_update/app_update.vue?f728", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/app_update/app_update.vue?0bf7"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "appUpdate", "onLoad", "plus", "that", "methods", "appVersionConfig", "uni", "success", "title", "content", "showCancel", "cancelColor", "confirmColor", "icon"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAomB,CAAgB,8nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACexnB;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACA;QACAF;QACAA;QACAA;QACAA;QACAD;UACA;UACA;UACAI;YACAC;cACA;gBACAD;kBACAE;kBACAC;kBACAC;kBACAC;kBACAC;kBACAL;oBACA;sBACA;wBACA;0BACAL;0BACA;wBACA;0BACAA;0BACA;sBAAA;oBAGA;kBACA;gBACA;cACA;gBACAI;kBACAE;kBACAK;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5EA;AAAA;AAAA;AAAA;AAAs3B,CAAgB,u3BAAG,EAAC,C;;;;;;;;;;;ACA14B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/users/app_update/app_update.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/users/app_update/app_update.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./app_update.vue?vue&type=template&id=66300c5f&\"\nvar renderjs\nimport script from \"./app_update.vue?vue&type=script&lang=js&\"\nexport * from \"./app_update.vue?vue&type=script&lang=js&\"\nimport style0 from \"./app_update.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/users/app_update/app_update.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./app_update.vue?vue&type=template&id=66300c5f&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./app_update.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./app_update.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"app_update\">\r\n\t\t<view class=\"logo_box\">\r\n\t\t\t<image src=\"../../../static/images/crmeb_java.png\"></image>\r\n\t\t\t<view class=\"title\">crmeb</view>\r\n\t\t\t<view class=\"version\">Version {{appUpdate.versionCode}}</view> \r\n\t\t</view>\r\n\t\t<view class=\"jiancha\" @click=\"appVersionConfig()\">\r\n\t\t\t<text>检查新版本</text>\r\n\t\t\t<text class=\"iconfont icon-you\"></text>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {getAppVersion} from '@/api/api.js'; \r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tappUpdate:{} \r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tlet that = this;\r\n\t\t\tplus.runtime.getProperty(plus.runtime.appid,function(inf){\r\n\t\t\t\tthat.$set(that.appUpdate,'versionCode',inf.version);\r\n\t\t\t})\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tappVersionConfig(){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\t//app升级\r\n\t\t\t\t// 获取本地应用资源版本号  \r\n\t\t\t\tgetAppVersion().then(res=>{\r\n\t\t\t\t\tthat.$set(that.appUpdate,'androidAddress',res.data.androidAddress);\r\n\t\t\t\t\tthat.$set(that.appUpdate,'appVersion',res.data.appVersion);\r\n\t\t\t\t\tthat.$set(that.appUpdate,'iosAddress',res.data.iosAddress);\r\n\t\t\t\t\tthat.$set(that.appUpdate,'openUpgrade',res.data.openUpgrade);\r\n\t\t\t\t\tplus.runtime.getProperty(plus.runtime.appid,function(inf){\r\n\t\t\t\t\t\tlet nowVersion = (inf.version).split('.').join('');\r\n\t\t\t\t\t\tlet appVersion = (res.data.appVersion).split('.').join('');\r\n\t\t\t\t\t\tuni.getSystemInfo({\r\n\t\t\t\t\t\t\tsuccess:(res) => {\r\n\t\t\t\t\t\t\t\tif(appVersion > nowVersion){\r\n\t\t\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '更新提示',\r\n\t\t\t\t\t\t\t\t\t\tcontent: '发现新版本，是否前去下载?',\r\n\t\t\t\t\t\t\t\t\t\tshowCancel:that.appUpdate.openUpgrade == 'false' ? true : false,\r\n\t\t\t\t\t\t\t\t\t\tcancelColor: '#eeeeee',\r\n\t\t\t\t\t\t\t\t\t\tconfirmColor: '#FF0000',\r\n\t\t\t\t\t\t\t\t\t\tsuccess(response) {\r\n\t\t\t\t\t\t\t\t\t\t\tif (response.confirm) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tswitch (res.platform){\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcase \"android\":\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tplus.runtime.openURL(that.appUpdate.androidAddress); \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcase \"ios\":\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tplus.runtime.openURL(encodeURI(that.appUpdate.iosAddress));\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}else if(appVersion <= nowVersion){ \r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle:'已是最新版本', \r\n\t\t\t\t\t\t\t\t\t\ticon:'none' \r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}  \r\n\t\t\t\t\t\t}) \r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.app_update{\r\n\t\tbackground-color: #fff;\r\n\t\theight: 100vh;\r\n\t}\r\n\t.logo_box{\r\n\t\theight: 500rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t}\r\n\t.logo_box image{\r\n\t\tdisplay: block; \r\n\t\tmargin-top:80rpx; \r\n\t\twidth: 120rpx;\r\n\t\theight: 120rpx;\r\n\t\tborder-radius: 50%;\r\n\t}\r\n\t.title{\r\n\t\tfont-size: 34rpx;\r\n\t\tfont-family: PingFang SC;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333333;\r\n\t\tmargin: 20rpx auto 20rpx;\r\n\t}\r\n\t.version{\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-family: PingFang SC;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #333333;\r\n\t}\r\n\t.jiancha{\r\n\t\twidth: 690rpx;\r\n\t\tmargin: 20rpx auto 0;\r\n\t\tpadding: 0 20rpx 0;\r\n\t\theight: 100rpx;\r\n\t\tline-height: 100rpx;\r\n\t\tcolor: #333333;\r\n\t\tfont-size: 30rpx;\r\n\t\tborder-top:1px solid #f5f5f5;\r\n\t\tborder-bottom:1px solid #f5f5f5;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./app_update.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./app_update.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294173481\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}