{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_payment/index.vue?5cfb", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_payment/index.vue?ef9f", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_payment/index.vue?be3e", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_payment/index.vue?f7c2", "uni-app:///pages/users/user_payment/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_payment/index.vue?5220", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/users/user_payment/index.vue?fa97"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "authorize", "home", "data", "now_money", "navRecharge", "active", "number", "placeholder", "from", "isAuto", "isShowAuth", "picList", "activePic", "money", "numberPic", "rechar_id", "rechargeAttention", "theme", "cartArr", "value", "title", "payStatus", "payType", "openType", "curActive", "animated", "formContent", "computed", "watch", "is<PERSON>ogin", "handler", "deep", "onLoad", "methods", "picCharge", "get<PERSON><PERSON><PERSON>ge", "then", "catch", "mes", "payConfig", "onLoadFun", "auth<PERSON><PERSON><PERSON>", "navRecharges", "payItem", "that", "submitSub", "uni", "content", "success", "price", "amount1", "amount2", "icon", "tab", "url", "type", "timeStamp", "nonceStr", "package", "signType", "paySign", "fail", "complete", "addMoney"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvBA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC8EnnB;AAQA;AACA;AAGA;AAOA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AAAA,eACA;EACAC;IAEAC;IAEAC;EACA;EACAC;IACA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;QACA;QACA;QACAC;QACAC;QACAC;MACA,EAUA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;EACAC;IACAC;MACAC;QACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IAOA;MACA;MACA;IACA;MACA;IACA;EACA;EACAC;IACA;AACA;AACA;IACAC;MACA;MACA;QACA;QACA;MACA;QACA;QACA;QACA;MACA;IACA;IAGA;AACA;AACA;IACAC;MAAA;MACA,4BACAC;QACA;QACA;UACA;UACA;QACA;QACA;MACA,GACAC;QACA;UACAC;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QAIA;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACAC;MACAA;MACAA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;QACA;UACA;YACAzB;UACA;QACA;QACA0B;UACA1B;UACA2B;UACAC;YACA;cACA;gBACAC;cACA;gBACAL;kBACAM;kBACAC;gBACA;gBACA;kBACA/B;kBACAgC;gBACA;kBACAC;kBACAC;gBACA;cACA;gBACA;kBACAlC;gBACA;cACA;YACA;cACA;gBACAA;cACA;YACA;UACA;QACA;MACA;QACA0B;UACA1B;QACA;QACA;QACA;UACA;YACA;cACAA;YACA;UACA;UACA;YACA;cACAA;YACA;UACA;UACA;YACA;cACAA;YACA;UACA;QACA;UACAP;QACA;QACA;UACA;YAsDA;cACAoC;cACAM;cACAxC;YACA;cACA+B;cACA;cACAA;gBACAU;gBACAC;gBACAC;gBACAC;gBACAC;gBACAZ;kBACAJ;oBACAM;oBACAC;kBACA;kBACA;oBACA/B;oBACAgC;kBACA;oBACAC;oBACAC;kBACA;gBACA;gBACAO;kBACA;oBACAzC;kBACA;gBACA;gBACA0C;kBACA;oBACA1C;kBACA;gBACA;cACA;YACA;cACA0B;cACA;gBACA1B;cACA;YACA;YAyDA;UACA;YACA;;YAiEA;QAAA;MAEA;IACA;IACA2C;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACthBA;AAAA;AAAA;AAAA;AAA0oC,CAAgB,gnCAAG,EAAC,C;;;;;;;;;;;ACA9pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/users/user_payment/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/users/user_payment/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=6b0986f5&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/users/user_payment/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=6b0986f5&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = !_vm.active ? parseFloat(_vm.activePic) : null\n  var m1 = !_vm.active ? parseFloat(_vm.picList.length) : null\n  var m2 = !_vm.active ? parseFloat(_vm.activePic) : null\n  var m3 = !_vm.active ? parseFloat(_vm.picList.length) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :data-theme=\"theme\" class=\"user_payment\">\r\n\t\t<form @submit=\"submitSub\" report-submit='true'>\r\n\t\t\t<view class=\"payment-top acea-row row-column row-center-wrapper\">\r\n\t\t\t\t<span class=\"name1\">我的余额</span>\r\n\t\t\t\t<view class=\"pic\">\r\n\t\t\t\t\t￥<span class=\"pic-font\">{{ userInfo.nowMoney || 0 }}</span>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"payment\">\r\n\t\t\t\t<view class=\"nav acea-row row-around row-middle\">\r\n\t\t\t\t\t<view class=\"item\" :class=\"active==index?'on':''\" v-for=\"(item,index) in navRecharge\" :key=\"index\" @click=\"navRecharges(index)\">{{item}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='tip picList' v-if='!active'>\r\n\t\t\t\t\t<view class=\"pic-box pic-box-color acea-row row-center-wrapper row-column\" :class=\"activePic === index ? 'pic-box-color-active' : ''\"\r\n\t\t\t\t\t v-for=\"(item, index) in picList\" :key=\"index\" @click=\"picCharge(index, item)\">\r\n\t\t\t\t\t\t<view class=\"pic-number-pic\">\r\n\t\t\t\t\t\t\t{{ item.price }}<span class=\"pic-number\"> 元</span>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"pic-number\">赠送：{{ item.giveMoney }} 元</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"pic-box pic-box-color acea-row row-center-wrapper\" :class=\"parseFloat(activePic)===parseFloat(picList.length)?'pic-box-color-active':''\" @click=\"picCharge(picList.length)\">\r\n\t\t\t\t\t\t<input type=\"number\" placeholder=\"其他\" v-model=\"money\" maxlength=\"5\" class=\"pic-box-money pic-number-pic uni-input\" :class=\"parseFloat(activePic) === parseFloat(picList.length) ? 'pic-box-color-active' : ''\" @blur=\"addMoney()\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tips-box\">\r\n\t\t\t\t\t\t<view class=\"tips mt-30\">注意事项：</view>\r\n\t\t\t\t\t\t<view class=\"tips-samll\" v-for=\"item in rechargeAttention\" :key=\"item\">\r\n\t\t\t\t\t\t\t{{ item }}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tip\" v-else>\r\n\t\t\t\t\t<view class='input'><text>￥</text>\r\n\t\t\t\t\t<input placeholder=\"0.00\" type='number' placeholder-class='placeholder' :value=\"number\"\r\n\t\t\t\t\t\t name=\"number\"></input></view>\r\n\t\t\t\t\t<view class=\"tips-title\">\r\n\t\t\t\t\t\t<view style=\"font-weight: bold; font-size: 26rpx;\">提示：</view>\r\n\t\t\t\t\t\t<view style=\"margin-top: 10rpx;\">当前佣金为 <text class='font-color'>￥{{userInfo.brokeragePrice || 0}}</text></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tips-box\">\r\n\t\t\t\t\t\t<view class=\"tips mt-30\">注意事项：</view>\r\n\t\t\t\t\t\t<view class=\"tips-samll\" v-for=\"item in rechargeAttention\" :key=\"item\">\r\n\t\t\t\t\t\t\t{{ item }}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #ifndef  MP-->\r\n\t\t\t\t<view class='wrapper borRadius14  px-30'  v-if='!active'>\r\n\t\t\t\t\t<view class='item'>\r\n\t\t\t\t\t\t<view>支付方式</view>\r\n\t\t\t\t\t\t<view class='list'>\r\n\t\t\t\t\t\t\t<view class='payItem acea-row row-middle' :class='curActive==index ?\"on\":\"\"'\r\n\t\t\t\t\t\t\t\t@tap='payItem(index)' v-for=\"(item,index) in cartArr\" :key='index'\r\n\t\t\t\t\t\t\t\tv-if=\"item.payStatus==1\">\r\n\t\t\t\t\t\t\t\t<view class='name acea-row row-center-wrapper'>\r\n\t\t\t\t\t\t\t\t\t<view class='iconfont animated'\r\n\t\t\t\t\t\t\t\t\t\t:class='(item.icon) + \" \" + (animated==true&&active==index ?\"bounceIn\":\"\")'>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t{{item.name}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class='tip'>{{item.title}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<button class='but' formType=\"submit\"> {{active ? '立即转入': '立即充值' }}</button>\r\n\t\t\t\t<view class=\"alipaysubmit\" v-html=\"formContent\"></view>\r\n\t\t\t</view>\r\n\t\t</form>\r\n\t\t<!-- #ifdef MP -->\r\n\t\t<!-- <authorize @onLoadFun=\"onLoadFun\" :isAuto=\"isAuto\" :isShowAuth=\"isShowAuth\" @authColse=\"authColse\"></authorize> -->\r\n\t\t<!-- #endif -->\r\n\t\t<!-- <home></home> -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\trechargeRoutine,\r\n\t\trechargeWechat,\r\n\t\tgetRechargeApi,\r\n\t\ttransferIn,\r\n\t\tappWechat,\r\n\t\talipayFull\r\n\t} from '@/api/user.js';\r\n\timport { wechatQueryPayResult,getPayConfig} from '@/api/order.js';\r\n\timport {\r\n\t\ttoLogin\r\n\t} from '@/libs/login.js';\r\n\timport {\r\n\t\tmapGetters\r\n\t} from \"vuex\";\r\n\t// #ifdef MP\r\n\timport authorize from '@/components/Authorize';\r\n\t// #endif\r\n\timport home from '@/components/home';\r\n\timport {Debounce} from '@/utils/validate.js'\r\n\tlet app = getApp();\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\t// #ifdef MP\r\n\t\t\tauthorize,\r\n\t\t\t// #endif\r\n\t\t\thome\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\tlet that = this;\r\n\t\t\treturn {\r\n\t\t\t\tnow_money: 0,\r\n\t\t\t\tnavRecharge: ['账户充值', '佣金转入'],\r\n\t\t\t\tactive: 0,\r\n\t\t\t\tnumber: '',\r\n\t\t\t\tplaceholder: \"0.00\",\r\n\t\t\t\tfrom: '',\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false, //是否隐藏授权\r\n\t\t\t\tpicList: [],\r\n\t\t\t\tactivePic: 0,\r\n\t\t\t\tmoney: \"\",\r\n\t\t\t\tnumberPic: '',\r\n\t\t\t\trechar_id: 0,\r\n\t\t\t\trechargeAttention: [],\r\n\t\t\t\ttheme:app.globalData.theme,\r\n\t\t\t\t//支付方式\r\n\t\t\t\tcartArr: [{\r\n\t\t\t\t\t\t\"name\": \"微信支付\",\r\n\t\t\t\t\t\t\"icon\": \"icon-weixin2\",\r\n\t\t\t\t\t\tvalue: 'weixin',\r\n\t\t\t\t\t\ttitle: '微信快捷支付',\r\n\t\t\t\t\t\tpayStatus: 1,\r\n\t\t\t\t\t},\r\n\t\t\t\t\t// #ifndef MP\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"name\": \"支付宝支付\",\r\n\t\t\t\t\t\t\"icon\": \"icon-zhifubao\",\r\n\t\t\t\t\t\tvalue: 'alipay',\r\n\t\t\t\t\t\ttitle: '支付宝快捷支付',\r\n\t\t\t\t\t\tpayStatus: 1,\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t],\r\n\t\t\t\tpayType: 'weixin', //支付方式\r\n\t\t\t\topenType: 1, //优惠券打开方式 1=使用\r\n\t\t\t\tcurActive: 0, //支付方式切换\r\n\t\t\t\tanimated: false,\r\n\t\t\t\tformContent:''\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: mapGetters(['isLogin', 'systemPlatform','userInfo']),\r\n\t\twatch:{\r\n\t\t\tisLogin:{\r\n\t\t\t\thandler:function(newV,oldV){\r\n\t\t\t\t\tif(newV){\r\n\t\t\t\t\t\tthis.getRecharge();\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tdeep:true\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\t// #ifdef H5\r\n\t\t\tthis.from = this.$wechat.isWeixin() ? \"public\" : \"weixinh5\";\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\tthis.from = this.systemPlatform === 'ios' ? 'weixinAppIos' : 'weixinAppAndroid';\r\n\t\t\t// #endif\r\n\t\t\tif (this.isLogin) {\r\n\t\t\t\tthis.getRecharge();\r\n\t\t\t\tthis.payConfig();\r\n\t\t\t} else {\r\n\t\t\t\ttoLogin();\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t/**\r\n\t\t\t * 选择金额\r\n\t\t\t */\r\n\t\t\tpicCharge(idx, item) {\r\n\t\t\t\tthis.activePic = idx;\r\n\t\t\t\tif (item === undefined) {\r\n\t\t\t\t\tthis.rechar_id = 0;\r\n\t\t\t\t\tthis.numberPic = \"\";\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.money = \"\";\r\n\t\t\t\t\tthis.rechar_id = item.id;\r\n\t\t\t\t\tthis.numberPic = item.price;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\r\n\t\t\t/**\r\n\t\t\t * 充值额度选择\r\n\t\t\t */\r\n\t\t\tgetRecharge() {\r\n\t\t\t\tgetRechargeApi()\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tthis.picList = res.data.rechargeQuota;\r\n\t\t\t\t\t\tif (this.picList[0]) {\r\n\t\t\t\t\t\t\tthis.rechar_id = this.picList[0].id;\r\n\t\t\t\t\t\t\tthis.numberPic = this.picList[0].price;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.rechargeAttention = res.data.rechargeAttention || [];\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(res => {\r\n\t\t\t\t\t\tthis.$dialog.toast({\r\n\t\t\t\t\t\t\tmes: res\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 支付配置\r\n\t\t\tpayConfig(){\r\n\t\t\t\tgetPayConfig().then(res=>{\r\n\t\t\t\t\tthis.cartArr[0].payStatus = parseInt(res.data.payWeixinOpen) === 1 ? 1 : 0;\r\n\t\t\t\t\t// #ifndef MP\r\n\t\t\t\t\tthis.cartArr[1].payStatus = parseInt(res.data.aliPayStatus) === 1 ? 1 : 2;\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tif(this.$wechat.isWeixin()) this.cartArr.pop();\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tonLoadFun: function() {\r\n\t\t\t\tthis.getRecharge();\r\n\t\t\t},\r\n\t\t\t// 授权关闭\r\n\t\t\tauthColse: function(e) {\r\n\t\t\t\tthis.isShowAuth = e\r\n\t\t\t},\r\n\t\t\tnavRecharges: function(index) {\r\n\t\t\t\tthis.active = index;\r\n\t\t\t},\r\n\t\t\tpayItem: function(e) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet active = e;\r\n\t\t\t\tthat.curActive = active;\r\n\t\t\t\tthat.animated = true;\r\n\t\t\t\tthat.payType = that.cartArr[active].value;\r\n\t\t\t},\r\n\t\t\t/*\r\n\t\t\t * 用户充值\r\n\t\t\t */\r\n\t\t\tsubmitSub: Debounce(function(e) {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tlet value = e.detail.value.number ? e.detail.value.number :that.numberPic;\r\n\t\t\t\t// 转入余额\r\n\t\t\t\tif (that.active) {\r\n\t\t\t\t\tif (parseFloat(value) < 0 || parseFloat(value) == NaN || value == undefined || value == \"\") {\r\n\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: '请输入金额'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '转入余额',\r\n\t\t\t\t\t\tcontent: '转入余额后无法再次转出，确认是否转入余额',\r\n\t\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\ttransferIn({\r\n\t\t\t\t\t\t\t\t\t\t\tprice: parseFloat(value)\r\n\t\t\t\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\t\t\t\tthat.$store.commit(\"changInfo\", {\r\n\t\t\t\t\t\t\t\t\t\tamount1: 'brokeragePrice',\r\n\t\t\t\t\t\t\t\t\t\tamount2: that.$util.$h.Sub(that.userInfo.brokeragePrice, parseFloat(value))\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '转入成功',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\t\t\t\turl: '/pages/users/user_money/index'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}).catch(err=>{\r\n\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\ttitle: '已取消'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\ttitle: '正在支付',\r\n\t\t\t\t\t})\r\n\t\t\t\t\tlet money = parseFloat(this.money);\r\n\t\t\t\t\tif (this.rechar_id == 0) {\r\n\t\t\t\t\t\tif (Number.isNaN(money)) {\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: '充值金额必须为数字'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (money <= 0) {\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: '充值金额不能为0'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (money > 50000) {\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: '充值金额最大值为50000'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tmoney = this.numberPic\r\n\t\t\t\t\t}\r\n\t\t\t\t\tswitch (that.payType){\r\n\t\t\t\t\t\tcase 'weixin':\r\n\t\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\t\tappWechat({\r\n\t\t\t\t\t\t\tfrom: that.from,\r\n\t\t\t\t\t\t\tprice: money,\r\n\t\t\t\t\t\t\ttype: 0,\r\n\t\t\t\t\t\t\trechar_id: this.rechar_id\r\n\t\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tlet jsConfig = res.data.jsConfig;\r\n\t\t\t\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\t\t\t\tprovider: 'wxpay',\r\n\t\t\t\t\t\t\t\torderInfo: {\r\n\t\t\t\t\t\t\t\t\t\"appid\": jsConfig.appId, // 微信开放平台 - 应用 - AppId，注意和微信小程序、公众号 AppId 可能不一致\r\n\t\t\t\t\t\t\t\t\t\"noncestr\": jsConfig.nonceStr, // 随机字符串\r\n\t\t\t\t\t\t\t\t\t\"package\": \"Sign=WXPay\", // 固定值\r\n\t\t\t\t\t\t\t\t\t\"partnerid\": jsConfig.partnerid, // 微信支付商户号\r\n\t\t\t\t\t\t\t\t\t\"prepayid\": jsConfig.packages, // 统一下单订单号 \r\n\t\t\t\t\t\t\t\t\t\"timestamp\": Number(jsConfig.timeStamp), // 时间戳（单位：秒）\r\n\t\t\t\t\t\t\t\t\t\"sign\": this.systemPlatform === 'ios' ? 'MD5' : jsConfig.paySign // 签名，这里用的 MD5 签名\r\n\t\t\t\t\t\t\t\t}, //微信、支付宝订单数据 【注意微信的订单信息，键值应该全部是小写，不能采用驼峰命名】\r\n\t\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\t\tthat.$store.commit(\"changInfo\", {\r\n\t\t\t\t\t\t\t\t\t\tamount1: 'nowMoney',\r\n\t\t\t\t\t\t\t\t\t\tamount2: that.$util.$h.Add(value, that.userInfo.nowMoney)\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\t\t\t\turl: '/pages/users/user_money/index'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '支付失败'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tcomplete: function(res) {\r\n\t\t\t\t\t\t\t\t\tif (res.errMsg == 'requestPayment:cancel') return that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '取消支付'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// #ifdef MP\r\n\t\t\t\t\t\trechargeRoutine({\r\n\t\t\t\t\t\t\tprice: money,\r\n\t\t\t\t\t\t\ttype: 0,\r\n\t\t\t\t\t\t\trechar_id: this.rechar_id\r\n\t\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tlet jsConfig = res.data.data.jsConfig;\r\n\t\t\t\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\t\t\t\ttimeStamp: jsConfig.timeStamp,\r\n\t\t\t\t\t\t\t\tnonceStr: jsConfig.nonceStr,\r\n\t\t\t\t\t\t\t\tpackage: jsConfig.packages,\r\n\t\t\t\t\t\t\t\tsignType: jsConfig.signType,\r\n\t\t\t\t\t\t\t\tpaySign: jsConfig.paySign,\r\n\t\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\t\tthat.$store.commit(\"changInfo\", {\r\n\t\t\t\t\t\t\t\t\t\tamount1: 'nowMoney',\r\n\t\t\t\t\t\t\t\t\t\tamount2: that.$util.$h.Add(value, that.userInfo.nowMoney)\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\t\t\t\turl: '/pages/users/user_money/index'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '支付失败'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tcomplete: function(res) {\r\n\t\t\t\t\t\t\t\t\tif (res.errMsg == 'requestPayment:cancel') return that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '取消支付'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\t\t\trechargeWechat({\r\n\t\t\t\t\t\t\t\tprice: money,\r\n\t\t\t\t\t\t\t\tfrom: that.from,\r\n\t\t\t\t\t\t\t\trechar_id: that.rechar_id,\r\n\t\t\t\t\t\t\t\tpayType: 0\r\n\t\t\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\t\t\tlet jsConfig = res.data.jsConfig;\r\n\t\t\t\t\t\t\t\tlet orderNo = res.data.orderNo;\r\n\t\t\t\t\t\t\t\tlet data = {\r\n\t\t\t\t\t\t\t\t\ttimestamp:jsConfig.timeStamp,\r\n\t\t\t\t\t\t\t\t\tnonceStr:jsConfig.nonceStr,\r\n\t\t\t\t\t\t\t\t\tpackage:jsConfig.packages,\r\n\t\t\t\t\t\t\t\t\tsignType:jsConfig.signType,\r\n\t\t\t\t\t\t\t\t\tpaySign:jsConfig.paySign\r\n\t\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\t\tif (that.from == \"weixinh5\") {\r\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '支付成功'\r\n\t\t\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\t\t\t\turl:'/pages/users/user_money/index'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\t\tlocation.href = jsConfig.mwebUrl;\r\n\t\t\t\t\t\t\t\t\t}, 100)\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tthat.$wechat.pay(data)\r\n\t\t\t\t\t\t\t\t\t\t.finally(() => {\r\n\t\t\t\t\t\t\t\t\t\t\tthat.$store.commit(\"changInfo\", {\r\n\t\t\t\t\t\t\t\t\t\t\t\tamount1: 'nowMoney',\r\n\t\t\t\t\t\t\t\t\t\t\t\tamount2: that.$util.$h.Add(value, that.userInfo.nowMoney)\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\t\t\t\t\t\turl: '/pages/users/user_money/index'\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t.catch(function(err) {\r\n\t\t\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '支付失败'\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}).catch(res=>{\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\ttitle: res\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'alipay':\r\n\t\t\t\t\t\t\t// alipayFull\r\n\t\t\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\t\t\talipayFull({\r\n\t\t\t\t\t\t\t\tfrom: 'appAliPay',\r\n\t\t\t\t\t\t\t\tprice: money,\r\n\t\t\t\t\t\t\t\tpayType: 'alipay',\r\n\t\t\t\t\t\t\t\trechar_id: this.rechar_id \r\n\t\t\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tlet alipayRequest = res.data.alipayRequest;\r\n\t\t\t\t\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\t\t\t\t\tprovider: 'alipay',\r\n\t\t\t\t\t\t\t\t\torderInfo: alipayRequest,\r\n\t\t\t\t\t\t\t\t\tsuccess: (e) => {\r\n\t\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\t\t\t\t\turl: '/pages/users/user_money/index'\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\tfail: (e) => {\r\n\t\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: '支付失败'\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\tcomplete: () => {\r\n\t\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\t\t\tif (this.$wechat.isWeixin()) {\r\n\t\t\t\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\t\t\t\turl: `/pages/users/alipay_invoke/index?price=${money}&rechar_id=${this.rechar_id}&type=users`\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t} else{\r\n\t\t\t\t\t\t\t\talipayFull({\r\n\t\t\t\t\t\t\t\t\tfrom: 'alipay',\r\n\t\t\t\t\t\t\t\t\tprice: money,\r\n\t\t\t\t\t\t\t\t\tpayType: 'alipay',\r\n\t\t\t\t\t\t\t\t\trechar_id: this.rechar_id\r\n\t\t\t\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\t\t\t\t//h5支付  \r\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\tthat.formContent = res.data.alipayRequest;\r\n\t\t\t\t\t\t\t\t\tthat.$nextTick(() => {\r\n\t\t\t\t\t\t\t\t\t\tdocument.forms['punchout_form'].submit();\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}).catch(res=>{\r\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: res\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}),\r\n\t\t\taddMoney(){\r\n\t\t\t\tthis.money = this.money.replace(/[^\\d]/g,'').replace(/^0{1,}/g,'');\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.user_payment{\r\n\t\theight: 100vh;\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\t.payment {\r\n\t\tposition: relative;\r\n\t\ttop: -60rpx;\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 10rpx;\r\n\t\tpadding-top: 25rpx;\r\n\t\tborder-top-right-radius: 14rpx;\r\n\t\tborder-top-left-radius: 14rpx;\r\n\t}\r\n\r\n\t.payment .nav {\r\n\t\theight: 75rpx;\r\n\t\tline-height: 75rpx;\r\n\t\tpadding: 0 100rpx;\r\n\t}\r\n\r\n\t.payment .nav .item {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.payment .nav .item.on {\r\n\t\tfont-weight: bold;\r\n\t\t@include tab_border_bottom(theme);\r\n\t}\r\n\r\n\t.payment .input {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tborder-bottom: 1px dashed #dddddd;\r\n\t\tmargin: 60rpx auto 0 auto;\r\n\t\tpadding-bottom: 20rpx;\r\n\t\tfont-size: 56rpx;\r\n\t\tcolor: #333333;\r\n\t\tflex-wrap: nowrap;\r\n\r\n\t}\r\n\r\n\t.payment .input text {\r\n\t\tpadding-left: 106rpx;\r\n\t}\r\n\r\n\t.payment .input input {\r\n\t\tpadding-right: 106rpx;\r\n\t\twidth: 300rpx;\r\n\t\theight: 94rpx;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 70rpx;\r\n\t}\r\n\r\n\t.payment .placeholder {\r\n\t\tcolor: #d0d0d0;\r\n\t\theight: 100%;\r\n\t\tline-height: 94rpx;\r\n\t}\r\n\r\n\t.payment .tip {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #888888;\r\n\t\tpadding: 0 30rpx;\r\n\t\t// margin-top: 25rpx;\r\n\t}\r\n\r\n\t.payment .but {\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 30rpx;\r\n\t\twidth: 700rpx;\r\n\t\theight: 86rpx;\r\n\t\tborder-radius: 43rpx;\r\n\t\tmargin: 50rpx auto 0 auto;\r\n\t\t@include linear-gradient(theme);\r\n\t\tline-height: 86rpx;\r\n\t}\r\n\r\n\t.payment-top {\r\n\t\twidth: 100%;\r\n\t\theight: 350rpx;\r\n\t\t@include main_bg_color(theme);\r\n\r\n\t\t.name1 {\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t\t\tmargin-top: -38rpx;\r\n\t\t\tmargin-bottom: 30rpx;\r\n\t\t}\r\n\r\n\t\t.pic {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tcolor: #fff;\r\n\t\t}\r\n\r\n\t\t.pic-font {\r\n\t\t\tfont-size: 78rpx;\r\n\t\t\tcolor: #fff;\r\n\t\t}\r\n\t}\r\n\r\n\t.picList {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tmargin: 30rpx 0;\r\n\r\n\t\t.pic-box {\r\n\t\t\twidth: 32%;\r\n\t\t\theight: auto;\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t\tmargin-top: 21rpx;\r\n\t\t\tpadding: 20rpx 0;\r\n\t\t\tmargin-right: 12rpx;\r\n\r\n\t\t\t&:nth-child(3n) {\r\n\t\t\t\tmargin-right: 0;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.pic-box-color {\r\n\t\t\tbackground-color: #f4f4f4;\r\n\t\t\tcolor: #656565;\r\n\t\t}\r\n\r\n\t\t.pic-number {\r\n\t\t\tfont-size: 22rpx;\r\n\t\t}\r\n\r\n\t\t.pic-number-pic {\r\n\t\t\tfont-size: 38rpx;\r\n\t\t\tmargin-right: 10rpx;\r\n\t\t\ttext-align: center;\r\n\t\t}\r\n\r\n\t}\r\n    .pic-box-color-active {\r\n\t\t\t@include linear-gradient(theme);\r\n\t\t\tcolor: #fff !important;\r\n\t}\r\n\t.tips-box {\r\n\t\t.tips {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #333333;\r\n\t\t\tfont-weight: 800;\r\n\t\t\tmargin-bottom: 14rpx;\r\n\t\t\tmargin-top: 20rpx;\r\n\t\t}\r\n\r\n\t\t.tips-samll {\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: #333333;\r\n\t\t\tmargin-bottom: 14rpx;\r\n\t\t}\r\n\r\n\t\t.tip-box {\r\n\t\t\tmargin-top: 30rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.tips-title {\r\n\t\tmargin-top: 20rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\t.wrapper .item textarea {\r\n\t\tbackground-color: #f9f9f9;\r\n\t\twidth: auto !important;\r\n\t\theight: 140rpx;\r\n\t\tborder-radius: 14rpx;\r\n\t\tmargin-top: 30rpx;\r\n\t\tpadding: 15rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tfont-weight: 400;\r\n\t}\r\n\t.px-30{\r\n\t\tpadding-left: 30rpx;\r\n\t\tpadding-rigt: 30rpx;\r\n\t}\r\n\t .wrapper .item .placeholder {\r\n\t\tcolor: #ccc;\r\n\t}\r\n\t\r\n\t.wrapper .item .list {\r\n\t\tmargin-top: 35rpx;\r\n\t}\r\n\t\r\n\t.wrapper .item .list .payItem {\r\n\t\tborder: 1px solid #eee;\r\n\t\tborder-radius: 14rpx;\r\n\t\theight: 86rpx;\r\n\t\twidth: 95%;\r\n\t\tbox-sizing: border-box;\r\n\t\tmargin-top: 20rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #282828;\r\n\t}\r\n\t\r\n\t.wrapper .item .list .payItem.on {\r\n\t\t// border-color: #fc5445;\r\n\t\t@include coupons_border_color(theme);\r\n\t\tcolor: $theme-color;\r\n\t}\r\n\t\r\n\t.name {\r\n\t\twidth: 50%;\r\n\t\ttext-align: center;\r\n\t\tborder-right: 1px solid #eee;\r\n\t}\r\n\t.name .iconfont {\r\n\t\twidth: 44rpx;\r\n\t\theight: 44rpx;\r\n\t\tborder-radius: 50%;\r\n\t\ttext-align: center;\r\n\t\tline-height: 44rpx;\r\n\t\tbackground-color: #fe960f;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 30rpx;\r\n\t\tmargin-right: 15rpx;\r\n\t}\r\n\t.name .iconfont.icon-weixin2 {\r\n\t\tbackground-color: #41b035;\r\n\t}\r\n\t.name .iconfont.icon-zhifubao {\r\n\t\tbackground-color: #00AAEA;\r\n\t}\r\n\t.payItem .tip {\r\n\t\twidth: 49%;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #aaa;\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294179704\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}