{"version": 3, "sources": ["webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/addressWindow/index.vue?600b", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/addressWindow/index.vue?7d46", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/addressWindow/index.vue?b0d9", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/addressWindow/index.vue?825c", "uni-app:///components/addressWindow/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/addressWindow/index.vue?eec8", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/addressWindow/index.vue?5162"], "names": ["props", "pagesUrl", "type", "default", "address", "addressId", "isLog", "data", "active", "is_loading", "addressList", "methods", "<PERSON><PERSON><PERSON><PERSON>", "a", "close", "goAddressPages", "uni", "url", "getAddressList", "page", "limit", "that", "defaultAddress"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC0BnnB;;;;;;;;;;;;;;;;;;;;;;;;;;gBAGA;EACAA;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;QACA;UACAC;UACAC;QACA;MACA;IACA;IACAC;MACAJ;MACAC;IACA;EACA;EACAI;IACA;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACAC;MACA;MACA;MACA;QACA;UACAC;QACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACAC;QACAC;MACA;IACA;IACAC;MAAA;MACA;MACA;QACAC;QACAC;MACA;QACA;QACAC;QACAA;QACA;QACA;QACA;QACA;UACA;YACAA;YACAC;UACA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACrGA;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/addressWindow/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=9b690cc8&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=9b690cc8&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"9b690cc8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/addressWindow/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=9b690cc8&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.is_loading && !_vm.addressList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class=\"address-window\" :class=\"address.address==true?'on':''\">\r\n\t\t\t<view class='title'>选择地址<text class='iconfont icon-guanbi' @tap='close'></text></view>\r\n\t\t\t<view class='list'>\r\n\t\t\t\t<view class='item acea-row row-between-wrapper' :class='active==index?\"font_color\":\"\"' v-for=\"(item,index) in addressList\"\r\n\t\t\t\t @tap='tapAddress(index,item.id)' :key='index'>\r\n\t\t\t\t\t<text class='iconfont icon-ditu' :class='active==index?\"font_color\":\"\"'></text>\r\n\t\t\t\t\t<view class='address'>\r\n\t\t\t\t\t\t<view class='name' :class='active==index?\"font_color\":\"\"'>{{item.realName}}<text class='phone'>{{item.phone}}</text></view>\r\n\t\t\t\t\t\t<view class='line1'>{{item.province}}{{item.city}}{{item.district}}{{item.detail}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class='iconfont icon-complete' :class='active==index?\"font-color\":\"\"'></text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- 无地址 -->\r\n\t\t\t<view class='pictrue' v-if=\"!is_loading && !addressList.length\">\r\n\t\t\t\t<image src='../../static/images/noAddress.png'></image>\r\n\t\t\t</view>\r\n\t\t\t<view class='addressBnt bg_color' @tap='goAddressPages'>选择其他地址</view>\r\n\t\t</view>\r\n\t\t<view class='mask' catchtouchmove=\"true\" :hidden='address.address==false' @tap='close'></view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tgetAddressList\r\n\t} from '@/api/user.js';\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\tpagesUrl: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '',\r\n\t\t\t},\r\n\t\t\taddress: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: function() {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\taddress: true,\r\n\t\t\t\t\t\taddressId: 0,\r\n\t\t\t\t\t};\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tisLog: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false,\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tactive: 0,\r\n\t\t\t\tis_loading: true,\r\n\t\t\t\taddressList: []\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\ttapAddress: function(e, addressid) {\r\n\t\t\t\tthis.active = e;\r\n\t\t\t\tlet a = {};\r\n\t\t\t\tfor (let i = 0, leng = this.addressList.length; i < leng; i++) {\r\n\t\t\t\t\tif (this.addressList[i].id == addressid) {\r\n\t\t\t\t\t\ta = this.addressList[i];\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthis.$emit('OnChangeAddress', a);\r\n\t\t\t},\r\n\t\t\tclose: function() {\r\n\t\t\t\tthis.$emit('changeClose');\r\n\t\t\t\tthis.$emit('changeTextareaStatus');\r\n\t\t\t},\r\n\t\t\tgoAddressPages: function() {\r\n\t\t\t\tthis.$emit('changeClose');\r\n\t\t\t\tthis.$emit('changeTextareaStatus');\r\n\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\turl: this.pagesUrl\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetAddressList: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetAddressList({\r\n\t\t\t\t\tpage: 1,\r\n\t\t\t\t\tlimit: 5\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tlet addressList = res.data.list;\r\n\t\t\t\t\tthat.$set(that, 'addressList', addressList);\r\n\t\t\t\t\tthat.is_loading = false;\r\n\t\t\t\t\tlet defaultAddress = {};\r\n\t\t\t\t\t//处理默认选中项\r\n\t\t\t\t\tif(!that.address.addressId) return;\r\n\t\t\t\t\tfor (let i = 0, leng = addressList.length; i < leng; i++) {\r\n\t\t\t\t\t\tif (addressList[i].id == that.address.addressId) {\r\n\t\t\t\t\t\t\tthat.active = i;\r\n\t\t\t\t\t\t\tdefaultAddress = this.addressList[i];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.$emit('OnDefaultAddress', defaultAddress);\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.address-window {\r\n\t\tbackground-color: #fff;\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\tz-index: 101;\r\n\t\ttransform: translate3d(0, 100%, 0);\r\n\t\ttransition: all .3s cubic-bezier(.25, .5, .5, .9);\r\n\t}\r\n\t.font_color{\r\n\t\t@include main_color(theme);\r\n\t}\r\n\t.bg_color{\r\n\t\t@include main_bg_color(theme);\r\n\t}\r\n\t.address-window.on {\r\n\t\ttransform: translate3d(0, 0, 0);\r\n\t}\r\n\r\n\t.address-window .title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\ttext-align: center;\r\n\t\theight: 123rpx;\r\n\t\tline-height: 123rpx;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.address-window .title .iconfont {\r\n\t\tposition: absolute;\r\n\t\tright: 30rpx;\r\n\t\tcolor: #8a8a8a;\r\n\t\tfont-size: 35rpx;\r\n\t}\r\n\r\n\t.address-window .list .item {\r\n\t\tmargin-left: 30rpx;\r\n\t\tpadding-right: 30rpx;\r\n\t\tborder-bottom: 1px solid #eee;\r\n\t\theight: 129rpx;\r\n\t\tfont-size: 25rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.address-window .list .item .iconfont {\r\n\t\tfont-size: 37rpx;\r\n\t\tcolor: #2c2c2c;\r\n\t}\r\n\r\n\t.address-window .list .item .iconfont.icon-complete {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #fff;\r\n\t}\r\n\r\n\t.address-window .list .item .address {\r\n\t\twidth: 560rpx;\r\n\t}\r\n\r\n\t.address-window .list .item .address .name {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #282828;\r\n\t\tmargin-bottom: 4rpx;\r\n\t}\r\n\r\n\t.address-window .list .item .address .name .phone {\r\n\t\tmargin-left: 18rpx;\r\n\t}\r\n\r\n\t.address-window .addressBnt {\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #fff;\r\n\t\twidth: 690rpx;\r\n\t\theight: 86rpx;\r\n\t\tborder-radius: 43rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 86rpx;\r\n\t\tmargin: 85rpx auto;\r\n\t}\r\n\r\n\t.address-window .pictrue {\r\n\t\twidth: 414rpx;\r\n\t\theight: 336rpx;\r\n\t\tmargin: 0 auto;\r\n\t}\r\n\r\n\t.address-window .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=9b690cc8&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=9b690cc8&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294180406\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}