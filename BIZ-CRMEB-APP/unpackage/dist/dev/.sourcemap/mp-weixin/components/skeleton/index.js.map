{"version": 3, "sources": ["webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/skeleton/index.vue?84c4", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/skeleton/index.vue?10bc", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/skeleton/index.vue?43b5", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/skeleton/index.vue?9358", "uni-app:///components/skeleton/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/skeleton/index.vue?e01a", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/skeleton/index.vue?b024"], "names": ["name", "props", "bgcolor", "type", "value", "selector", "loading", "show", "isNodes", "data", "loadingAni", "systemInfo", "skeletonRectLists", "skeletonCircleLists", "watch", "mounted", "methods", "attachedAction", "width", "height", "readyAction", "uni", "that", "rectHandle", "radiusHandle"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCkBnnB;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;EACA;EACAK;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAN;MACA;IACA;EACA;EACAO;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;QACAC;QACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACAC;QACA,uBACAC;MACA;;MAEA;MACA;;MAEA;MACA;IACA;IACAC;MACA;;MAEA;MACAF;QACAC;MACA;IAEA;IACAE;MACA;MAEAH;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnGA;AAAA;AAAA;AAAA;AAAi3B,CAAgB,k3BAAG,EAAC,C;;;;;;;;;;;ACAr4B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/skeleton/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=914242ea&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/skeleton/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=914242ea&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view v-if=\"show\"\r\n\t\t:style=\"{width: systemInfo.width + 'px', height: systemInfo.height + 'px', backgroundColor: bgcolor, position: 'absolute', left: 0, top: 0, zIndex: 9998}\">\r\n\t\t<view v-for=\"(item,rect_idx) in skeletonRectLists\" :key=\"rect_idx + 'rect'\"\r\n\t\t\t:class=\"[loading == 'chiaroscuro' ? 'chiaroscuro' : '']\"\r\n\t\t\t:style=\"{width: item.width + 'px', height: item.height + 'px', backgroundColor: 'rgb(194, 207, 214,.3)', position: 'absolute', left: item.left + 'px', top: item.top + 'px'}\">\r\n\t\t</view>\r\n\t\t<view v-for=\"(item,circle_idx) in skeletonCircleLists\" :key=\"circle_idx + 'circle'\"\r\n\t\t\t:class=\"loading == 'chiaroscuro' ? 'chiaroscuro' : ''\"\r\n\t\t\t:style=\"{width: item.width + 'px', height: item.height + 'px', backgroundColor: 'rgb(194, 207, 214,.3)', borderRadius: item.width + 'px', position: 'absolute', left: item.left + 'px', top: item.top + 'px'}\">\r\n\t\t</view>\r\n\t\t<view class=\"spinbox\" v-if=\"loading == 'spin'\">\r\n\t\t\t<view class=\"spin\"></view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tname: \"skeleton\",\r\n\t\tprops: {\r\n\t\t\tbgcolor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tvalue: '#FFF'\r\n\t\t\t},\r\n\t\t\tselector: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tvalue: 'skeleton'\r\n\t\t\t},\r\n\t\t\tloading: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tvalue: 'spin'\r\n\t\t\t},\r\n\t\t\tshow: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tvalue: false\r\n\t\t\t},\r\n\t\t\tisNodes: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tvalue: false\r\n\t\t\t} //控制什么时候开始抓取元素节点,只要数值改变就重新抓取\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tloadingAni: ['spin', 'chiaroscuro'],\r\n\t\t\t\tsystemInfo: {},\r\n\t\t\t\tskeletonRectLists: [],\r\n\t\t\t\tskeletonCircleLists: []\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tisNodes(val) {\r\n\t\t\t\tthis.readyAction();\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.attachedAction();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tattachedAction: function() {\r\n\t\t\t\t//默认的首屏宽高，防止内容闪现\r\n\t\t\t\tconst systemInfo = uni.getSystemInfoSync();\r\n\t\t\t\tthis.systemInfo = {\r\n\t\t\t\t\twidth: systemInfo.windowWidth,\r\n\t\t\t\t\theight: systemInfo.windowHeight\r\n\t\t\t\t};\r\n\t\t\t\tthis.loading = this.loadingAni.includes(this.loading) ? this.loading : 'spin';\r\n\t\t\t},\r\n\t\t\treadyAction: function() {\r\n\t\t\t\tconst that = this;\r\n\t\t\t\t//绘制背景\r\n\t\t\t\tuni.createSelectorQuery().selectAll(`.${this.selector}`).boundingClientRect().exec(function(res) {\r\n\t\t\t\t\tif(res[0].length>0)\r\n\t\t\t\t\tthat.systemInfo.height = res[0][0].height + res[0][0].top;\r\n\t\t\t\t});\r\n\r\n\t\t\t\t//绘制矩形\r\n\t\t\t\tthis.rectHandle();\r\n\r\n\t\t\t\t//绘制圆形\r\n\t\t\t\tthis.radiusHandle();\r\n\t\t\t},\r\n\t\t\trectHandle: function() {\r\n\t\t\t\tconst that = this;\r\n\r\n\t\t\t\t//绘制不带样式的节点\r\n\t\t\t\tuni.createSelectorQuery().selectAll(`.${this.selector}-rect`).boundingClientRect().exec(function(res) {\r\n\t\t\t\t\tthat.skeletonRectLists = res[0];\r\n\t\t\t\t});\r\n\r\n\t\t\t},\r\n\t\t\tradiusHandle() {\r\n\t\t\t\tconst that = this;\r\n\r\n\t\t\t\tuni.createSelectorQuery().selectAll(`.${this.selector}-radius`).boundingClientRect().exec(function(res) {\r\n\t\t\t\t\tthat.skeletonCircleLists = res[0];\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.spinbox {\r\n\t\tposition: fixed;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\theight: 100%;\r\n\t\twidth: 100%;\r\n\t\tz-index: 9999\r\n\t}\r\n\r\n\t.spin {\r\n\t\tdisplay: inline-block;\r\n\t\twidth: 64rpx;\r\n\t\theight: 64rpx;\r\n\t}\r\n\r\n\t.spin:after {\r\n\t\tcontent: \" \";\r\n\t\tdisplay: block;\r\n\t\twidth: 46rpx;\r\n\t\theight: 46rpx;\r\n\t\tmargin: 1rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tborder: 5rpx solid #409eff;\r\n\t\tborder-color: #409eff transparent #409eff transparent;\r\n\t\tanimation: spin 1.2s linear infinite;\r\n\t}\r\n\r\n\t@keyframes spin {\r\n\t\t0% {\r\n\t\t\ttransform: rotate(0deg);\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\ttransform: rotate(360deg);\r\n\t\t}\r\n\t}\r\n\r\n\t.chiaroscuro {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground: rgb(194, 207, 214);\r\n\t\tanimation-duration: 2s;\r\n\t\tanimation-name: blink;\r\n\t\tanimation-iteration-count: infinite;\r\n\t}\r\n\r\n\t@keyframes blink {\r\n\t\t0% {\r\n\t\t\topacity: .4;\r\n\t\t}\r\n\r\n\t\t50% {\r\n\t\t\topacity: 1;\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\topacity: .4;\r\n\t\t}\r\n\t}\r\n\r\n\t@keyframes flush {\r\n\t\t0% {\r\n\t\t\tleft: -100%;\r\n\t\t}\r\n\r\n\t\t50% {\r\n\t\t\tleft: 0;\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\tleft: 100%;\r\n\t\t}\r\n\t}\r\n\r\n\t.shine {\r\n\t\tanimation: flush 2s linear infinite;\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tbottom: 0;\r\n\t\twidth: 100%;\r\n\t\tbackground: linear-gradient(to left,\r\n\t\t\t\trgba(255, 255, 255, 0) 0%,\r\n\t\t\t\trgba(255, 255, 255, .85) 50%,\r\n\t\t\t\trgba(255, 255, 255, 0) 100%)\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294173475\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}