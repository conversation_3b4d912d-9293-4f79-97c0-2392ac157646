{"version": 3, "sources": ["webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/countDown/index.vue?37c1", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/countDown/index.vue?adcc", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/countDown/index.vue?0505", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/countDown/index.vue?b28a", "uni-app:///components/countDown/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/countDown/index.vue?6bda", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/countDown/index.vue?6182"], "names": ["name", "props", "justifyLeft", "type", "default", "tipText", "dayText", "hourText", "minuteText", "secondText", "datatime", "isDay", "isCol", "bgColor", "data", "day", "hour", "minute", "second", "created", "mounted", "methods", "show_time", "Math", "that", "runTime", "setInterval"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACqC;;;AAGzF;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCennB;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACAQ;MACAT;MACAC;IACA;IACAS;MACAV;MACAC;IACA;EACA;EACAU;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;EACAC;IACAC;MACA;MAEA;QACA;QACA;QACA;UACAN;UACAC;UACAC;QACA;UACA;UACA;YACAH;UACA;YACAA;UACA;UACAC;UACAC;UACAC,SACAK,sBACAR,qBACAC,iBACAC;UACA;UACA;UACA;UACAO;UACAA;UACAA;UACAA;QACA;UACAA;UACAA;UACAA;UACAA;QACA;MACA;MACAC;MACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACnHA;AAAA;AAAA;AAAA;AAAy4B,CAAgB,04BAAG,EAAC,C;;;;;;;;;;;ACA75B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/countDown/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=e5c41dae&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=e5c41dae&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"e5c41dae\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/countDown/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=e5c41dae&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"time\" :style=\"justifyLeft\">\r\n\t\t<text class=\"\" v-if=\"tipText\">{{ tipText }}</text>\r\n\t\t<text class=\"styleAll p6\" v-if=\"isDay === true\" :style=\"{background:bgColor.bgColor,color:bgColor.Color}\">{{ day }}{{bgColor.isDay?'天':''}}</text>\r\n\t\t<text class=\"timeTxt\" v-if=\"dayText\" :style=\"{width:bgColor.timeTxtwidth,color:bgColor.bgColor}\">{{ dayText }}</text>\r\n\t\t<text class=\"styleAll\" :class='isCol?\"timeCol\":\"\"' :style=\"{background:bgColor.bgColor,color:bgColor.Color,width:bgColor.width}\">{{ hour }}</text>\r\n\t\t<text class=\"timeTxt\" v-if=\"hourText\" :class='isCol?\"whit\":\"\"' :style=\"{width:bgColor.timeTxtwidth,color:bgColor.bgColor}\">{{ hourText }}</text>\r\n\t\t<text class=\"styleAll\" :class='isCol?\"timeCol\":\"\"' :style=\"{background:bgColor.bgColor,color:bgColor.Color,width:bgColor.width}\">{{ minute }}</text>\r\n\t\t<text class=\"timeTxt\" v-if=\"minuteText\" :class='isCol?\"whit\":\"\"' :style=\"{width:bgColor.timeTxtwidth,color:bgColor.bgColor}\">{{ minuteText }}</text>\r\n\t\t<text class=\"styleAll\" :class='isCol?\"timeCol\":\"\"' :style=\"{background:bgColor.bgColor,color:bgColor.Color,width:bgColor.width}\">{{ second }}</text>\r\n\t\t<text class=\"timeTxt\" v-if=\"secondText\">{{ secondText }}</text>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tname: \"countDown\",\r\n\t\tprops: {\r\n\t\t\tjustifyLeft: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"\"\r\n\t\t\t},\r\n\t\t\t//距离开始提示文字\r\n\t\t\ttipText: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"倒计时\"\r\n\t\t\t},\r\n\t\t\tdayText: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"天\"\r\n\t\t\t},\r\n\t\t\thourText: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"时\"\r\n\t\t\t},\r\n\t\t\tminuteText: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"分\"\r\n\t\t\t},\r\n\t\t\tsecondText: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"秒\"\r\n\t\t\t},\r\n\t\t\tdatatime: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\tisDay: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tisCol: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tbgColor: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: null\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata: function() {\r\n\t\t\treturn {\r\n\t\t\t\tday: \"00\",\r\n\t\t\t\thour: \"00\",\r\n\t\t\t\tminute: \"00\",\r\n\t\t\t\tsecond: \"00\"\r\n\t\t\t};\r\n\t\t},\r\n\t\tcreated: function() {\r\n\t\t\tthis.show_time();\r\n\t\t},\r\n\t\tmounted: function() {},\r\n\t\tmethods: {\r\n\t\t\tshow_time: function() {\r\n\t\t\t\tlet that = this;\r\n\r\n\t\t\t\tfunction runTime() {\r\n\t\t\t\t\t//时间函数\r\n\t\t\t\t\tlet intDiff = that.datatime - Date.parse(new Date()) / 1000; //获取数据中的时间戳的时间差；\r\n\t\t\t\t\tlet day = 0,\r\n\t\t\t\t\t\thour = 0,\r\n\t\t\t\t\t\tminute = 0,\r\n\t\t\t\t\t\tsecond = 0;\r\n\t\t\t\t\tif (intDiff > 0) {\r\n\t\t\t\t\t\t//转换时间\r\n\t\t\t\t\t\tif (that.isDay === true) {\r\n\t\t\t\t\t\t\tday = Math.floor(intDiff / (60 * 60 * 24));\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tday = 0;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\thour = Math.floor(intDiff / (60 * 60)) - day * 24;\r\n\t\t\t\t\t\tminute = Math.floor(intDiff / 60) - day * 24 * 60 - hour * 60;\r\n\t\t\t\t\t\tsecond =\r\n\t\t\t\t\t\t\tMath.floor(intDiff) -\r\n\t\t\t\t\t\t\tday * 24 * 60 * 60 -\r\n\t\t\t\t\t\t\thour * 60 * 60 -\r\n\t\t\t\t\t\t\tminute * 60;\r\n\t\t\t\t\t\tif (hour <= 9) hour = \"0\" + hour;\r\n\t\t\t\t\t\tif (minute <= 9) minute = \"0\" + minute;\r\n\t\t\t\t\t\tif (second <= 9) second = \"0\" + second;\r\n\t\t\t\t\t\tthat.day = day;\r\n\t\t\t\t\t\tthat.hour = hour;\r\n\t\t\t\t\t\tthat.minute = minute;\r\n\t\t\t\t\t\tthat.second = second;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthat.day = \"00\";\r\n\t\t\t\t\t\tthat.hour = \"00\";\r\n\t\t\t\t\t\tthat.minute = \"00\";\r\n\t\t\t\t\t\tthat.second = \"00\";\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\trunTime();\r\n\t\t\t\tsetInterval(runTime, 1000);\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style scoped>\r\n\t.p6{\r\n\t\tpadding: 0 8rpx;\r\n\t}\r\n\t.styleAll{\r\n\t\t/* color: #fff; */\r\n\t\tfont-size: 24rpx;\r\n\t\theight: 36rpx;\r\n\t\tline-height: 36rpx;\r\n\t\tborder-radius: 6rpx;\r\n\t\ttext-align: center;\r\n\t\t/* padding: 0 6rpx; */\r\n\t}\r\n\t.timeTxt{\r\n\t\t    text-align: center;\r\n\t\t    /* width: 16rpx; */\r\n\t\t    height: 36rpx;\r\n\t\t    line-height: 36rpx;\r\n\t\t    display: inline-block;\r\n\t}\r\n\t.whit{\r\n\t\tcolor: #fff !important;\r\n\t}\r\n\t.time {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.red {\r\n\t\tcolor: #fc4141;\r\n\t\tmargin: 0 4rpx;\r\n\t}\r\n\r\n\t.timeCol {\r\n\t\t/* width: 40rpx;\r\n\t\theight: 40rpx;\r\n\t\tline-height: 40rpx;\r\n\t\ttext-align:center;\r\n\t\tborder-radius: 6px;\r\n\t\tbackground: #fff;\r\n\t\tfont-size: 24rpx; */\r\n\t\tcolor: #E93323;\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=e5c41dae&scoped=true&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=e5c41dae&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294178127\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}