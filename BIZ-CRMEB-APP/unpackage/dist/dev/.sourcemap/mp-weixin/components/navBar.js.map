{"version": 3, "sources": ["webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/navBar.vue?0a62", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/navBar.vue?3d2d", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/navBar.vue?8ec2", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/navBar.vue?e2cb", "uni-app:///components/navBar.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/navBar.vue?d8ee", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/navBar.vue?5e2c"], "names": ["data", "homeTop", "navH", "currentPage", "selectNavList", "name", "icon", "url", "props", "navTitle", "type", "default", "created", "uni", "success", "app", "onReady", "query", "select", "boundingClientRect", "exec", "methods", "returns", "showNav", "linkPage", "animationType", "animationDuration", "touchStart"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAgmB,CAAgB,0nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACoCpnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AAAA,eACA;EACAA;IACA;MACAC;MACAC;MACAC;MACAC,gBACA;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA;IAEA;EACA;EACAC;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IAEA;IACAC;MACAC;QACAC;MACA;IACA;IACA;IAKA;EACA;EACAC;IACA;MAAA;MAEA;MACA;MACAC,MACAC,gBACAC;QACA;MACA,GACAC;IAEA;EACA;EACAC;IACAC;MACAT;QACAN;MACA;IACA;IACAgB;MACA;IACA;IACA;IACAC;MACA;QACAX;UACAN;QACA;MACA;QACAM;UACAY;UACAC;UACAnB;QACA;MACA;MACA;IACA;IACAoB;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnHA;AAAA;AAAA;AAAA;AAAmqC,CAAgB,yoCAAG,EAAC,C;;;;;;;;;;;ACAvrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/navBar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./navBar.vue?vue&type=template&id=f6fba62a&scoped=true&\"\nvar renderjs\nimport script from \"./navBar.vue?vue&type=script&lang=js&\"\nexport * from \"./navBar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./navBar.vue?vue&type=style&index=0&id=f6fba62a&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"f6fba62a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/navBar.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./navBar.vue?vue&type=template&id=f6fba62a&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./navBar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./navBar.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class=\"cart_nav\" :style='\"height:\"+navH+\"rpx;\"'>\r\n\t\t\t<view class='navbarCon acea-row'>\r\n\t\t\t\t<!-- #ifdef MP -->\r\n\t\t\t\t<view class=\"select_nav flex justify-center align-center\" id=\"home\" :style=\"{ top: homeTop + 'rpx' }\">\r\n\t\t\t\t\t<text class=\"iconfont icon-fanhui2 px-20\" @tap=\"returns\"></text>\r\n\t\t\t\t\t<text class=\"iconfont icon-gengduo5 px-20\" @tap.stop=\"showNav\"></text>\r\n\t\t\t\t\t<text class=\"nav_line\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t<view id=\"home\" class=\"home acea-row row-center-wrapper iconfont icon-shouye4 h5_back\"\r\n\t\t\t\t\t:style=\"{ top: homeTop + 'rpx' }\" @tap=\"returns\">\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<!-- #ifndef APP-PLUS -->\r\n\t\t\t\t<view class=\"nav_title\" :style=\"{ top: homeTop + 'rpx' }\">{{navTitle}}</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t<view class=\"right_select\" :style=\"{ top: homeTop + 'rpx' }\" @tap=\"showNav\">\r\n\t\t\t\t\t<text class=\"iconfont icon-gengduo2\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"dialog_nav\" :style='\"top:\"+navH+\"rpx;\"' v-show=\"currentPage\">\r\n\t\t\t<view class=\"dialog_nav_item\" v-for=\"(item,index) in selectNavList\" :key=\"index\" @click=\"linkPage(item.url)\">\r\n\t\t\t\t<text class=\"iconfont\" :class=\"item.icon\"></text>\r\n\t\t\t\t<text class=\"pl-20\">{{item.name}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport animationType from '@/utils/animationType.js'\r\n\tlet app = getApp();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\thomeTop: 20,\r\n\t\t\t\tnavH:\"\",\r\n\t\t\t\tcurrentPage:false,\r\n\t\t\t\tselectNavList:[\r\n\t\t\t\t\t{name:'首页',icon:'icon-shouye8',url:'/pages/index/index'},\r\n\t\t\t\t\t{name:'搜索',icon:'icon-sousuo6',url:'/pages/goods_search/index'},\r\n\t\t\t\t\t{name:'我的收藏',icon:'icon-shoucang3',url:'/pages/users/user_goods_collection/index'},\r\n\t\t\t\t\t{name:'个人中心',icon:'icon-gerenzhongxin1',url:'/pages/user/index'},\r\n\t\t\t\t]\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops:{\r\n\t\t\tnavTitle:{\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault:''\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated(){\r\n\t\t       // #ifdef MP\r\n\t\t\t   // 获取导航高度；\r\n\t\t\t   uni.getSystemInfo({\r\n\t\t\t   \tsuccess: function(res) {\r\n\t\t\t   \t\tapp.globalData.navHeight = res.statusBarHeight * (750 / res.windowWidth) + 91;\r\n\t\t\t   \t}\r\n\t\t\t   });\r\n\t\t       this.navH = app.globalData.navHeight;\r\n\t\t       // #endif\r\n\t\t       // #ifndef MP\r\n\t\t       this.navH = 96;\r\n\t\t       // #endif\r\n\t\t\t   this.$emit('getNavH', this.navH)\r\n\t\t},\r\n\t\tonReady() {\r\n\t\t\tthis.$nextTick(function() {\r\n\t\t\t\t// #ifdef MP\r\n\t\t\t\tconst menuButton = uni.getMenuButtonBoundingClientRect();\r\n\t\t\t\tconst query = uni.createSelectorQuery().in(this);\r\n\t\t\t\tquery\r\n\t\t\t\t\t.select('#home')\r\n\t\t\t\t\t.boundingClientRect(data => {\r\n\t\t\t\t\t\tthis.homeTop = menuButton.top * 2 + menuButton.height - data.height;\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.exec();\r\n\t\t\t\t// #endif\r\n\t\t\t});\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\treturns: function() {\r\n\t\t\t\tuni.switchTab({\r\n\t\t\t\t\turl:'/pages/index/index'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tshowNav(){\r\n\t\t\t\tthis.currentPage = !this.currentPage;\r\n\t\t\t},\r\n\t\t\t//下拉导航页面跳转\r\n\t\t\tlinkPage(url){\r\n\t\t\t\tif(url == '/pages/index/index' || url == '/pages/user/index'){\r\n\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\turl\r\n\t\t\t\t\t})\r\n\t\t\t\t}else{\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\tanimationType: animationType.type,\r\n\t\t\t\t\t\tanimationDuration: animationType.duration,\r\n\t\t\t\t\t\turl\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tthis.currentPage = false\r\n\t\t\t},\r\n\t\t\ttouchStart(){\r\n\t\t\t\tthis.currentPage = false;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.pl-20{\r\n\t\tpadding-left: 20rpx;\r\n\t}\r\n\t.cart_nav{\r\n\t\tposition: fixed;\r\n\t\t@include main_bg_color(theme);\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tz-index: 99;\r\n\t\twidth: 100%;\r\n\t}\r\n\t.navbarCon {\r\n\t\tposition: absolute;\r\n\t\tbottom: 0;\r\n\t\theight: 100rpx;\r\n\t\twidth: 100%;\r\n\t}\r\n\t.h5_back {\r\n\t\tcolor: #fff;\r\n\t\tposition: fixed;\r\n\t\tleft:20rpx;\r\n\t\tfont-size: 32rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 58rpx;\r\n\t}\r\n\t.select_nav{\r\n\t\twidth: 170rpx !important;\r\n\t\theight: 60rpx !important;\r\n\t\tborder-radius: 33rpx;\r\n\t\tbackground: rgba(255, 255, 255, 0.6);\r\n\t\tcolor: #000;\r\n\t\tposition: fixed;\r\n\t\tfont-size: 18px;\r\n\t\tline-height: 58rpx;\r\n\t\tz-index: 1000;\r\n\t\tleft: 14rpx;\r\n\t}\r\n\t.px-20{\r\n\t\tpadding: 0 20rpx 0;\r\n\t}\r\n\t.nav_line{\r\n\t\tcontent: '';\r\n\t\tdisplay: inline-block;\r\n\t\twidth: 1px;\r\n\t\theight: 34rpx;\r\n\t\tbackground: #fff;\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tmargin: auto;\r\n\t}\r\n\t.container_detail{\r\n\t\t/* #ifdef MP */\r\n\t\tmargin-top:32rpx;\r\n\t\t/* #endif */\r\n\t}\r\n\t.tab_nav{\r\n\t\twidth: 100%;\r\n\t\theight: 48px;\r\n\t\tpadding:0 30rpx 0;\r\n\t}\r\n\t.nav_title{\r\n\t\twidth: 200rpx;\r\n\t\theight: 58rpx;\r\n\t\tline-height: 58rpx;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 36rpx;\r\n\t\tposition: fixed;\r\n\t\ttext-align: center;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tmargin: auto;\r\n\t}\r\n\t.right_select{\r\n\t\tposition: fixed;\r\n\t\tright: 20rpx;\r\n\t\tcolor: #fff;\r\n\t\ttext-align: center;\r\n\t\tline-height: 58rpx;\r\n\t}\r\n\t.select_nav{\r\n\t\twidth: 170rpx !important;\r\n\t\theight: 60rpx !important;\r\n\t\tborder-radius: 33rpx;\r\n\t\tbackground: rgba(255, 255, 255, 0.6);\r\n\t\tcolor: #000;\r\n\t\tposition: fixed;\r\n\t\tfont-size: 18px;\r\n\t\tline-height: 58rpx;\r\n\t\tz-index: 1000;\r\n\t\tleft: 14rpx;\r\n\t}\r\n\t.px-20{\r\n\t\tpadding: 0 20rpx 0;\r\n\t}\r\n\t.justify-center{\r\n\t\tjustify-content: center;\r\n\t}\r\n\t.align-center {\r\n\t\talign-items: center;\r\n\t}\r\n\t.dialog_nav{\r\n\t\tposition: fixed;\r\n\t\t/* #ifdef MP */\r\n\t\tleft: 14rpx;\r\n\t\t/* #endif */\r\n\t\t/* #ifdef H5 || APP-PLUS*/\r\n\t\tright: 14rpx;\r\n\t\t/* #endif */\r\n\t\twidth: 240rpx;\r\n\t\tbackground: #FFFFFF;\r\n\t\tbox-shadow: 0px 0px 16rpx rgba(0, 0, 0, 0.08);\r\n\t\tz-index: 999;\r\n\t\tborder-radius: 14rpx;\r\n\t\t&::before{\r\n\t\t\tcontent: '';\r\n\t\t\twidth: 0;\r\n\t\t\theight: 0;\r\n\t\t\tposition: absolute;\r\n\t\t\t/* #ifdef MP */\r\n\t\t\tleft: 0;\r\n\t\t\tright: 0;\r\n\t\t\tmargin:auto;\r\n\t\t\t/* #endif */\r\n\t\t\t/* #ifdef H5 || APP-PLUS */\r\n\t\t\tright: 8px;\r\n\t\t\t/* #endif */\r\n\t\t\ttop:-9px;\r\n\t\t\tborder-bottom: 10px solid #fff;\r\n\t\t\tborder-left: 10px solid transparent;    /*transparent 表示透明*/\r\n\t\t\tborder-right: 10px solid transparent;\r\n\t\t}\r\n\t}\r\n\t.dialog_nav_item{\r\n\t\twidth: 100%;\r\n\t\theight: 84rpx;\r\n\t\tline-height: 84rpx;\r\n\t\tpadding: 0 20rpx 0;\r\n\t\tbox-sizing: border-box;\r\n\t\tborder-bottom: #eee;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333;\r\n\t\tposition: relative;\r\n\t\t.iconfont{\r\n\t\t\tfont-size: 32rpx;\r\n\t\t}\r\n\t\t&::after{\r\n\t\t\tcontent: '';\r\n\t\t\tposition: absolute;\r\n\t\t\twidth:86px;\r\n\t\t\theight: 1px;\r\n\t\t\tbackground-color: #EEEEEE;\r\n\t\t\tbottom: 0;\r\n\t\t\tright: 0;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./navBar.vue?vue&type=style&index=0&id=f6fba62a&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./navBar.vue?vue&type=style&index=0&id=f6fba62a&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294180109\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}