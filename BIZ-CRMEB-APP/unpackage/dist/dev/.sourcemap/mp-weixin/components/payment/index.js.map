{"version": 3, "sources": ["webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/payment/index.vue?efdd", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/payment/index.vue?ecbf", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/payment/index.vue?bff6", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/payment/index.vue?0b4e", "uni-app:///components/payment/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/payment/index.vue?2b87", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/payment/index.vue?1711"], "names": ["props", "payMode", "type", "default", "pay_close", "order_id", "totalPrice", "data", "formContent", "payChannel", "computed", "created", "methods", "close", "action", "payConfig", "goPay", "title", "uni", "orderNo", "payType", "that", "icon", "location", "weixinPay", "timeStamp", "nonceStr", "package", "signType", "paySign", "success", "fail", "complete", "Tips"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC2BnnB;AAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAGA;EACAA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;EACA;EACAI;IACA;MACAC;MACAC;IACA;EACA;EACAC;EACAC;IACA;EACA;EACAC;IACAC;MACA;QACAC;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;MAIA;IACA;IACAC;MACA;MACA;MACA;QACAC;MACA;MACA;QACAA;MACA;MACAC;QACAD;MACA;MAiBA;QACAE;QAEAV;QAKAW;MACA;QACA;QACAC;QACA;UACA;YACAA;YACA;UACA;YACAH;YACA;cACAD;cACAK;YACA;cACAD;gBACAP;cACA;YACA;YACA;UACA;YACAI;YACAK,kFACA;YACA;cACAN;cACAK;YACA;cACAD;gBACAP;cACA;YACA;YACA;UACA;YAkDA;QAAA;MAEA;QACAI;QACA;UACAD;QACA;UACAI;YACAP;UACA;QACA;MACA;IACA;IACAU;MACA;MAEAN;QACAO;QACAC;QACAC;QACAC;QACAC;QACAC;UACAZ;UACA;YACAA;YACA;cACAD;cACAK;YACA;cACAD;gBACAP;cACA;YACA;UACA;YACAI;YACA;cACAD;YACA;UACA;QACA;QACAc;UACAb;UACA;YACAD;UACA;YACAI;cACAP;YACA;UACA;QACA;QACAkB;UACAd;UACA,2DACAe;YACAhB;UACA;YACAI;cACAP;YACA;UACA;QACA;MACA;IAwFA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC/VA;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/payment/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=15ef15de&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=15ef15de&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"15ef15de\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/payment/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=15ef15de&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class=\"payment\" :class=\"pay_close ? 'on' : ''\">\r\n\t\t\t<view class=\"title acea-row row-center-wrapper\">\r\n\t\t\t\t选择付款方式<text class=\"iconfont icon-guanbi\" @click='close'></text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item acea-row row-between-wrapper\" @click='goPay(item.number || 0 , item.value)'\r\n\t\t\t\tv-for=\"(item,index) in payMode\" :key=\"index\" v-if=\"item.payStatus==1\">\r\n\t\t\t\t<view class=\"left acea-row row-between-wrapper\">\r\n\t\t\t\t\t<view class=\"iconfont\" :class=\"item.icon\"></view>\r\n\t\t\t\t\t<view class=\"text\">\r\n\t\t\t\t\t\t<view class=\"name\">{{item.name}}</view>\r\n\t\t\t\t\t\t<view class=\"info\" v-if=\"item.number\">\r\n\t\t\t\t\t\t\t{{item.title}} <span class=\"money\">￥{{ item.number }}</span>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"info\" v-else>{{item.title}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"iconfont icon-xiangyou\"></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"mask\" @click='close' v-if=\"pay_close\"></view>\r\n\t\t<view class=\"alipaysubmit\" v-html=\"formContent\"></view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\torderPay,\r\n\t\twechatQueryPayResult,\r\n\t\tgetPayConfig\r\n\t} from '@/api/order.js';\r\n\timport {\r\n\t\tmapGetters\r\n\t} from \"vuex\";\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\tpayMode: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault: function() {\r\n\t\t\t\t\treturn [];\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tpay_close: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false,\r\n\t\t\t},\r\n\t\t\torder_id: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\ttotalPrice: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '0'\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tformContent:'',\r\n\t\t\t\tpayChannel:''\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: mapGetters(['systemPlatform']),\r\n\t\tcreated(){\r\n\t\t\tthis.payConfig();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tclose: function() {\r\n\t\t\t\tthis.$emit('onChangeFun', {\r\n\t\t\t\t\taction: 'payClose'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tpayConfig(){\r\n\t\t\t\tgetPayConfig().then(res=>{\r\n\t\t\t\t\tthis.payMode[1].payStatus = parseInt(res.data.yuePayStatus) === 1 ? 1 : 2;\r\n\t\t\t\t\tthis.payMode[0].payStatus = parseInt(res.data.payWeixinOpen) === 1 ? 1 : 0;\r\n\t\t\t\t\t// #ifndef MP\r\n\t\t\t\t\tthis.payMode[2].payStatus = parseInt(res.data.aliPayStatus) === 1 ? 1 : 2;\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgoPay: function(number, paytype) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet goPages = '/pages/order_pay_status/index?order_id=' + that.order_id;\r\n\t\t\t\tif (!that.order_id) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '请选择要支付的订单'\r\n\t\t\t\t});\r\n\t\t\t\tif (paytype == 'yue' && parseFloat(number) < parseFloat(that.totalPrice)) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '余额不足！'\r\n\t\t\t\t});\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '支付中'\r\n\t\t\t\t});\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tif(paytype == 'alipay'){\r\n\t\t\t\t\tthat.payChannel = 'alipay';\r\n\t\t\t\t}else if(paytype == 'weixin' && this.$wechat.isWeixin()){\r\n\t\t\t\t\tthat.payChannel = 'public';\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthat.payChannel = 'weixinh5';\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tif(paytype == 'alipay'){\r\n\t\t\t\t\tthat.payChannel = 'appAliPay';\r\n\t\t\t\t}else if(paytype == 'weixin'){\r\n\t\t\t\t\tthat.payChannel = that.systemPlatform === 'ios' ? 'weixinAppIos' : 'weixinAppAndroid';\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\torderPay({\r\n\t\t\t\t\torderNo: that.order_id,\r\n\t\t\t\t\t// #ifdef MP\r\n\t\t\t\t\tpayChannel: 'routine',\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifndef MP\r\n\t\t\t\t\tpayChannel:that.payChannel,\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tpayType: paytype\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tlet jsConfig = res.data.jsConfig;\r\n\t\t\t\t\tthat.order_id = res.data.orderNo;\r\n\t\t\t\t\tswitch (res.data.payType) {\r\n\t\t\t\t\t\tcase 'weixin':\r\n\t\t\t\t\t\tthat.weixinPay(jsConfig);\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'yue':\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: '余额支付成功',\r\n\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t}, () => {\r\n\t\t\t\t\t\t\t\tthat.$emit('onChangeFun', {\r\n\t\t\t\t\t\t\t\t\taction: 'pay_complete'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'weixinh5':\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tlocation.replace(jsConfig.mwebUrl + '&redirect_url=' + window.location.protocol +\r\n\t\t\t\t\t\t\t\t'//' + window.location.host + goPages + '&status=1');\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: \"支付中\",\r\n\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t}, () => {\r\n\t\t\t\t\t\t\t\tthat.$emit('onChangeFun', {\r\n\t\t\t\t\t\t\t\t\taction: 'pay_complete'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'alipay':\r\n\t\t\t\t\t\t\t//#ifdef H5\r\n\t\t\t\t\t\t\tif (this.$wechat.isWeixin()) {\r\n\t\t\t\t\t\t\t\t//微信公众号内支付\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t//h5支付\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tthat.formContent = res.data.alipayRequest;\r\n\t\t\t\t\t\t\t\tuni.setStorage({key: 'orderNo', data:that.order_id});\r\n\t\t\t\t\t\t\t\tthat.$nextTick(() => {\r\n\t\t\t\t\t\t\t\t\tdocument.forms['punchout_form'].submit();\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t//#endif\r\n\t\t\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\t\t\tlet alipayRequest = res.data.alipayRequest;\r\n\t\t\t\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\t\t\t\tprovider: 'alipay',\r\n\t\t\t\t\t\t\t\torderInfo: alipayRequest,\r\n\t\t\t\t\t\t\t\tsuccess: (e) => {\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: \"支付成功\"\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\tsetTimeout(res => {\r\n\t\t\t\t\t\t\t\t\t\tthat.$emit('onChangeFun', {\r\n\t\t\t\t\t\t\t\t\t\t\taction: 'pay_complete'\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t}, 2000)\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfail: (e) => {\r\n\t\t\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\t\t\tcontent: \"支付失败\",\r\n\t\t\t\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t//点击确认的操作\r\n\t\t\t\t\t\t\t\t\t\t\t\tthat.$emit('onChangeFun', {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\taction: 'pay_fail'\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tcomplete: () => {\r\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t}, () => {\r\n\t\t\t\t\t\tthat.$emit('onChangeFun', {\r\n\t\t\t\t\t\t\taction: 'pay_fail'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tweixinPay(jsConfig){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\t// #ifdef MP\r\n\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\ttimeStamp: jsConfig.timeStamp,\r\n\t\t\t\t\tnonceStr: jsConfig.nonceStr,\r\n\t\t\t\t\tpackage: jsConfig.packages,\r\n\t\t\t\t\tsignType: jsConfig.signType,\r\n\t\t\t\t\tpaySign: jsConfig.paySign,\r\n\t\t\t\t\tsuccess: function(ress) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\twechatQueryPayResult(that.order_id).then(res => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: \"支付成功\",\r\n\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t}, () => {\r\n\t\t\t\t\t\t\t\tthat.$emit('onChangeFun', {\r\n\t\t\t\t\t\t\t\t\taction: 'pay_complete'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}).cache(err => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function(e) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: '取消支付'\r\n\t\t\t\t\t\t}, () => {\r\n\t\t\t\t\t\t\tthat.$emit('onChangeFun', {\r\n\t\t\t\t\t\t\t\taction: 'pay_fail'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t\tcomplete: function(e) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tif (e.errMsg == 'requestPayment:cancel') return that.$util\r\n\t\t\t\t\t\t\t.Tips({\r\n\t\t\t\t\t\t\t\ttitle: '取消支付'\r\n\t\t\t\t\t\t\t}, () => {\r\n\t\t\t\t\t\t\t\tthat.$emit('onChangeFun', {\r\n\t\t\t\t\t\t\t\t\taction: 'pay_fail'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tlet datas = {\r\n\t\t\t\t\ttimestamp: jsConfig.timeStamp,\r\n\t\t\t\t\tnonceStr: jsConfig.nonceStr,\r\n\t\t\t\t\tpackage: jsConfig.packages,\r\n\t\t\t\t\tsignType: jsConfig.signType,\r\n\t\t\t\t\tpaySign: jsConfig.paySign\r\n\t\t\t\t};\r\n\t\t\t\tthat.$wechat.pay(datas).then(res => {\r\n\t\t\t\t\tif (res.errMsg == 'chooseWXPay:cancel') {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: '支付失败'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\twechatQueryPayResult(that.order_id).then(res => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: \"支付成功\",\r\n\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t}, () => {\r\n\t\t\t\t\t\t\t\tthat.$emit('onChangeFun', {\r\n\t\t\t\t\t\t\t\t\taction: 'pay_complete'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}).cache(err => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t}).cache(errW => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: errW\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\tprovider: 'wxpay',\r\n\t\t\t\t\torderInfo: {\r\n\t\t\t\t\t\t\"appid\": jsConfig.appId, // 微信开放平台 - 应用 - AppId，注意和微信小程序、公众号 AppId 可能不一致\r\n\t\t\t\t\t\t\"noncestr\": jsConfig.nonceStr, // 随机字符串\r\n\t\t\t\t\t\t\"package\": \"Sign=WXPay\", // 固定值\r\n\t\t\t\t\t\t\"partnerid\": jsConfig.partnerid, // 微信支付商户号\r\n\t\t\t\t\t\t\"prepayid\": jsConfig.packages, // 统一下单订单号 \r\n\t\t\t\t\t\t\"timestamp\": Number(jsConfig.timeStamp), // 时间戳（单位：秒）\r\n\t\t\t\t\t\t\"sign\": this.systemPlatform === 'ios' ? 'MD5' : jsConfig\r\n\t\t\t\t\t\t\t.paySign // 签名，这里用的 MD5 签名\r\n\t\t\t\t\t}, //微信、支付宝订单数据 【注意微信的订单信息，键值应该全部是小写，不能采用驼峰命名】\r\n\t\t\t\t\tsuccess: (e) => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tlet url = '/pages/order_pay_status/index?order_id=' + that.order_id;\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: \"支付成功\"\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tsetTimeout(res => {\r\n\t\t\t\t\t\t\tthat.$emit('onChangeFun', {\r\n\t\t\t\t\t\t\t\taction: 'pay_complete'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}, 2000)\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (e) => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\tcontent: \"支付失败\",\r\n\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\tthat.$emit('onChangeFun', {\r\n\t\t\t\t\t\t\t\t\t\taction: 'pay_fail'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t},\r\n\t\t\t\t\tcomplete: () => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t},\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.payment {\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\tborder-radius: 16rpx 16rpx 0 0;\r\n\t\tbackground-color: #fff;\r\n\t\tpadding-bottom: 60rpx;\r\n\t\tz-index: 99;\r\n\t\ttransition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);\r\n\t\ttransform: translate3d(0, 100%, 0);\r\n\t}\r\n\r\n\t.payment.on {\r\n\t\ttransform: translate3d(0, 0, 0);\r\n\t}\r\n\r\n\t.payment .title {\r\n\t\ttext-align: center;\r\n\t\theight: 123rpx;\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #282828;\r\n\t\tfont-weight: bold;\r\n\t\tpadding-right: 30rpx;\r\n\t\tmargin-left: 30rpx;\r\n\t\tposition: relative;\r\n\t\tborder-bottom: 1rpx solid #eee;\r\n\t}\r\n\r\n\t.payment .title .iconfont {\r\n\t\tposition: absolute;\r\n\t\tright: 30rpx;\r\n\t\ttop: 50%;\r\n\t\ttransform: translateY(-50%);\r\n\t\tfont-size: 43rpx;\r\n\t\tcolor: #8a8a8a;\r\n\t\tfont-weight: normal;\r\n\t}\r\n\r\n\t.payment .item {\r\n\t\tborder-bottom: 1rpx solid #eee;\r\n\t\theight: 130rpx;\r\n\t\tmargin-left: 30rpx;\r\n\t\tpadding-right: 30rpx;\r\n\t}\r\n\r\n\t.payment .item .left {\r\n\t\twidth: 610rpx;\r\n\t}\r\n\r\n\t.payment .item .left .text {\r\n\t\twidth: 540rpx;\r\n\t}\r\n\r\n\t.payment .item .left .text .name {\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #282828;\r\n\t}\r\n\r\n\t.payment .item .left .text .info {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.payment .item .left .text .info .money {\r\n\t\tcolor: #ff9900;\r\n\t}\r\n\r\n\t.payment .item .left .iconfont {\r\n\t\tfont-size: 45rpx;\r\n\t\tcolor: #09bb07;\r\n\t}\r\n\r\n\t.payment .item .left .iconfont.icon-zhifubao {\r\n\t\tcolor: #00aaea;\r\n\t}\r\n\r\n\t.payment .item .left .iconfont.icon-yuezhifu {\r\n\t\tcolor: #ff9900;\r\n\t}\r\n\r\n\t.payment .item .left .iconfont.icon-yuezhifu1 {\r\n\t\tcolor: #eb6623;\r\n\t}\r\n\r\n\t.payment .item .iconfont {\r\n\t\tfont-size: 0.3rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=15ef15de&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=15ef15de&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294180153\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}