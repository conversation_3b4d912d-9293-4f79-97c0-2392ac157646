{"version": 3, "sources": ["webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/orderGoods/index.vue?9a43", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/orderGoods/index.vue?55a0", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/orderGoods/index.vue?e850", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/orderGoods/index.vue?0d46", "uni-app:///components/orderGoods/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/orderGoods/index.vue?1643", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/orderGoods/index.vue?05f2"], "names": ["props", "evaluate", "type", "default", "cartInfo", "orderId", "ids", "jump", "orderProNum", "productType", "data", "totalNmu", "watch", "nVal", "num", "methods", "evaluateTap", "uni", "url", "jumpCon"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC4BnnB;EACAA;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;QACA;MACA;IACA;IACAM;MACAP;MACAC;QACA;MACA;IACA;EACA;EACAO;IACA;MACAC;IACA;EACA;EACAC;IACAR;MACA;MACAS;QACAC;MACA;MACA;IACA;EACA;EACAC;IACAC;MACAC;QACAC;MACA;IACA;IACAC;MACA;MACA;QACAF;UACAC;QACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC9FA;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/orderGoods/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=0d23a466&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=0d23a466&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0d23a466\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/orderGoods/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=0d23a466&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"orderGoods borRadius14\">\r\n\t\t<view class='total'>共{{ orderProNum?orderProNum:totalNmu}}件商品</view>\r\n\t\t<view class='goodWrapper pad30'>\r\n\t\t\t<view class='item acea-row row-between-wrapper' v-for=\"(item,index) in cartInfo\" :key=\"index\"\r\n\t\t\t\t@click=\"jumpCon(item.productId)\">\r\n\t\t\t\t<view class='pictrue'>\r\n\t\t\t\t\t<image :src='item.image'></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='text'>\r\n\t\t\t\t\t<view class='acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t<view class='name line1'>{{item.productName ? item.productName : item.storeName}}</view>\r\n\t\t\t\t\t\t<view class='num'>x {{item.payNum ? item.payNum : item.cartNum}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='attr line1' v-if=\"item.sku\">{{item.sku}}</view>\r\n\t\t\t\t\t<!-- <view class='money'>￥{{item.vipPrice !=null && item.vipPrice>=0 ? item.vipPrice : item.price}}</view> -->\r\n\t\t\t\t\t<view class='money'>·</view>\r\n\t\t\t\t\t<view class='evaluate' v-if='item.isReply==0 && evaluate==2' @click.stop=\"evaluateTap(item)\">评价\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='evaluate' v-else-if=\"item.isReply==1\">已评价</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\tevaluate: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0,\r\n\t\t\t},\r\n\t\t\tcartInfo: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault: function() {\r\n\t\t\t\t\treturn [];\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\torderId: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '',\r\n\t\t\t},\r\n\t\t\tids: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0,\r\n\t\t\t},\r\n\t\t\tjump: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false,\r\n\t\t\t},\r\n\t\t\torderProNum: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: function() {\r\n\t\t\t\t\treturn 0;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tproductType: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: function() {\r\n\t\t\t\t\treturn 0;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttotalNmu: ''\r\n\t\t\t};\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tcartInfo: function(nVal, oVal) {\r\n\t\t\t\tlet num = 0\r\n\t\t\t\tnVal.forEach((item, index) => {\r\n\t\t\t\t\tnum += item.cartNum\r\n\t\t\t\t})\r\n\t\t\t\tthis.totalNmu = num\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tevaluateTap(item) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: \"/pages/users/goods_comment_con/index?unique=\" + item.attrId + \"&orderId=\" + this.orderId + '&id=' + this.ids\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tjumpCon: function(id) {\r\n\t\t\t\tlet type = this.productType==0?'normal':'video'\r\n\t\t\t\tif (this.jump) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/pages/goods_details/index?id=${id}&type=${type}`\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.orderGoods {\r\n\t\tbackground-color: #fff;\r\n\t\tmargin-top: 15rpx;\r\n\t}\r\n\t.money{\r\n\t\t@include price_color(theme);\r\n\t}\r\n\t.orderGoods .total {\r\n\t\twidth: 100%;\r\n\t\theight: 86rpx;\r\n\t\tpadding: 0 24rpx;\r\n\t\tborder-bottom: 2rpx solid #f0f0f0;\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #282828;\r\n\t\tline-height: 86rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.pictrue image {\r\n\t\tbackground: #f4f4f4;\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=0d23a466&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=0d23a466&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294180413\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}