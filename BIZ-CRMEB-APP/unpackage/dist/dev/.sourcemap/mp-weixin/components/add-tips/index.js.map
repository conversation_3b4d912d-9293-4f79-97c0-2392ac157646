{"version": 3, "sources": ["webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/add-tips/index.vue?abc2", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/add-tips/index.vue?c614", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/add-tips/index.vue?a7a4", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/add-tips/index.vue?5d13", "uni-app:///components/add-tips/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/add-tips/index.vue?b245", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/add-tips/index.vue?0fc0"], "names": ["data", "showTip", "boxTop", "<PERSON><PERSON><PERSON><PERSON>", "co<PERSON><PERSON><PERSON><PERSON><PERSON>", "screenWidth", "props", "isCustom", "type", "default", "bgColor", "text", "fontObj", "color", "fontSize", "fontWeight", "borderR", "delay", "closeColor", "isAm", "methods", "tipHidden", "uni", "timeOut", "init", "onReady"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCoBnnB;EACAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;QACA;UACAI;UACAC;UACAC;QACA;MACA;IACA;IACA;IACAC;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;EACA;EACAW;IACAC;MACAC;MACA;IACA;IACAC;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;EACAC;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC1GA;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/add-tips/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=c324e33c&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=c324e33c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"c324e33c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/add-tips/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=c324e33c&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<!-- 小程序顶部提示 -->\r\n\t<view>\r\n\t\t<view class=\"tip_box\" :class=\"{ anScale: isAm }\" v-if=\"showTip\"\r\n\t\t\t:style=\"{ top: isCustom ? boxTop + 'px' : '0px' }\">\r\n\t\t\t<view class=\"arrow\" :style=\"{ 'margin-right': arrowMargin + 'px', borderBottomColor: bgColor }\"></view>\r\n\t\t\t<view class=\"container\" :style=\"{'margin-right': cotainerMargin + 'px',backgroundColor: bgColor,borderRadius: borderR + 'px',}\">\r\n\t\t\t\t<!-- 提示文字 -->\r\n\t\t\t\t<view class=\"tips\" :style=\"{ color: fontObj.color, fontSize: fontObj.fontSize, fontWeight: fontObj.fontWeight }\">\r\n\t\t\t\t\t{{ text }}</view>\r\n\t\t\t\t<view class=\"close\" @tap=\"tipHidden\">\r\n\t\t\t\t\t<text class=\"iconfont icon-cha3\" v-if=\"closeColor\"></text>\r\n\t\t\t\t\t<text class=\"iconfont icon-cha3\" style=\"color:#fff;\" v-else></text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tshowTip: false,\r\n\t\t\t\tboxTop: 0,\r\n\t\t\t\tarrowMargin: 0,\r\n\t\t\t\tcotainerMargin: 0,\r\n\t\t\t\tscreenWidth: 0,\r\n\t\t\t};\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\t/* 是否是自定义头部 */\r\n\t\t\tisCustom: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false,\r\n\t\t\t},\r\n\t\t\t/* 背景颜色 */\r\n\t\t\tbgColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"#ffffff\",\r\n\t\t\t},\r\n\t\t\t/* 提示文字 */\r\n\t\t\ttext: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"添加到我的小程序\",\r\n\t\t\t},\r\n\t\t\t/* 提示文字样式 */\r\n\t\t\tfontObj: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: function() {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tcolor: \"#202020\",\r\n\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\tfontWeight: \"0\",\r\n\t\t\t\t\t};\r\n\t\t\t\t},\r\n\t\t\t},\r\n\t\t\t/* 圆角大小  px*/\r\n\t\t\tborderR: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 5,\r\n\t\t\t},\r\n\t\t\t/* 延时出现 */\r\n\t\t\tdelay: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 2000,\r\n\t\t\t},\r\n\t\t\t/* 关闭btn黑白两色 或者自行添加 */\r\n\t\t\tcloseColor: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true,\r\n\t\t\t},\r\n\t\t\t/* 动画效果 */\r\n\t\t\tisAm: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true,\r\n\t\t\t},\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\ttipHidden: function() {\r\n\t\t\t\tuni.setStorageSync(\"my_tips_2020\", \"true\");\r\n\t\t\t\tthis.showTip = false;\r\n\t\t\t},\r\n\t\t\ttimeOut() {\r\n\t\t\t\tthis.tipHidden();\r\n\t\t\t\tthis.showTip = true;\r\n\t\t\t\t// setTimeout(() => {\r\n\t\t\t\t\t\r\n\t\t\t\t// \tsetTimeout(() => {\r\n\t\t\t\t// \t\tthis.tipHidden();\r\n\t\t\t\t// \t}, this.delay + 2000);\r\n\t\t\t\t// }, this.delay);\r\n\t\t\t},\r\n\t\t\tinit() {\r\n\t\t\t\tif (uni.getStorageSync(\"my_tips_2020\")) return;\r\n\t\t\t\tlet rect = uni.getMenuButtonBoundingClientRect();\r\n\t\t\t\tlet screenWidth = uni.getSystemInfoSync().screenWidth;\r\n\t\t\t\tthis.boxTop = rect.bottom;\r\n\t\t\t\tthis.arrowMargin = rect.width * 0.75 + 4;\r\n\t\t\t\tthis.cotainerMargin = screenWidth - rect.right;\r\n\t\t\t\tthis.timeOut();\r\n\t\t\t},\r\n\t\t},\r\n\t\tonReady() {\r\n\t\t\tthis.init();\r\n\t\t},\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@keyframes anScale {\r\n\t\tfrom {\r\n\t\t\t-webkit-transform: scale3d(0.96, 0.96, 0.96);\r\n\t\t\ttransform: scale3d(0.96, 0.96, 0.96);\r\n\t\t}\r\n\r\n\t\t50% {\r\n\t\t\t-webkit-transform: scale3d(1, 1, 1);\r\n\t\t\ttransform: scale3d(1, 1, 1);\r\n\t\t}\r\n\r\n\t\tto {\r\n\t\t\t-webkit-transform: scale3d(0.96, 0.96, 0.96);\r\n\t\t\ttransform: scale3d(0.96, 0.96, 0.96);\r\n\t\t}\r\n\t}\r\n\r\n\t.anScale {\r\n\t\tanimation: anScale 1s linear infinite;\r\n\t}\r\n\r\n\t.tip_box {\r\n\t\twidth: 70%;\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tright: 0;\r\n\t\tz-index: 100;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: flex-end;\r\n\t\talign-items: flex-end;\r\n\t\tflex-direction: column;\r\n\r\n\t\t.arrow {\r\n\t\t\twidth: 0;\r\n\t\t\theight: 0;\r\n\t\t\tborder: 10rpx solid;\r\n\t\t\tborder-color: transparent;\r\n\t\t}\r\n\r\n\t\t.container {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tpadding: 16rpx 24rpx;\r\n\r\n\t\t\t.tips {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tpadding-right: 12rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.close {\r\n\t\t\t\theight: 30rpx;\r\n\t\t\t\twidth: 30rpx;\r\n\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\tline-height: 30rpx;\r\n\t\t\t\tcolor: #999;\r\n\t\t\t\t.closeImg {\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=c324e33c&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=c324e33c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294179400\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}