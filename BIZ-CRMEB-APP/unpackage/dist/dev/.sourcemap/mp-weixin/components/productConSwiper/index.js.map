{"version": 3, "sources": ["webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/productConSwiper/index.vue?59e4", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/productConSwiper/index.vue?f9f8", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/productConSwiper/index.vue?d628", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/productConSwiper/index.vue?d1f8", "uni-app:///components/productConSwiper/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/productConSwiper/index.vue?9b6b", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/productConSwiper/index.vue?f8e8"], "names": ["props", "imgUrls", "type", "default", "videoline", "value", "data", "indicatorDots", "circular", "autoplay", "interval", "duration", "currents", "controls", "isPlay", "videoContext", "indicatorBg", "created", "that", "mounted", "methods", "videoPause", "bindPause", "change"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACgDnnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBACA;EACAA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACAC;MACAF;MACAG;IACA;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;EACA;EACAC;IACA;MACA;IACA;IAEA;EAEA;EACAC;IACAC,oCAKA;IACAC;MAGA;MACA;MACA;IAOA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AChHA;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/productConSwiper/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=fba0bbae&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=fba0bbae&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"fba0bbae\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/productConSwiper/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=fba0bbae&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class='product-bg'>\r\n\t\t<swiper :indicator-dots=\"indicatorDots\" :indicator-active-color=\"indicatorBg\" :autoplay=\"autoplay\" :circular=\"circular\"\r\n\t\t :interval=\"interval\" :duration=\"duration\" @change=\"change\" v-if=\"isPlay\"> \r\n\t\t <!-- #ifndef APP-PLUS -->\r\n\t\t\t<swiper-item v-if=\"videoline\">\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<view v-show=\"!controls\" style=\"width:100%;height:100% \">\r\n\t\t\t\t\t\t<video id=\"myVideo\" :src='videoline' objectFit=\"cover\" controls style=\"width:100%;height:100% \"\r\n\t\t\t\t\t\t show-center-play-btn show-mute-btn=\"true\" auto-pause-if-navigate :custom-cache=\"false\" :enable-progress-gesture=\"false\" :poster=\"imgUrls[0]\" @pause=\"videoPause\"></video>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"poster\" v-show=\"controls\">\r\n\t\t\t\t\t\t<image class=\"image\" :src=\"imgUrls[0]\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"stop\" v-show=\"controls\" @tap=\"bindPause\">\r\n\t\t\t\t\t\t<image class=\"image\" src=\"../../static/images/stop.png\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</swiper-item>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<!-- #ifdef APP-PLUS -->\r\n\t\t\t<swiper-item v-if=\"videoline\">\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<view class=\"poster\" v-show=\"controls\">\r\n\t\t\t\t\t\t<image class=\"image\" :src=\"imgUrls[0]\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"stop\" v-show=\"controls\" @tap=\"bindPause\">\r\n\t\t\t\t\t\t<image class=\"image\" src=\"../../static/images/stop.png\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</swiper-item>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<block v-for=\"(item,index) in imgUrls\" :key='index'>\r\n\t\t\t\t<swiper-item v-if=\"videoline?index>=1:index>=0\">\r\n\t\t\t\t\t<image :src=\"item\" class=\"slide-image\" />\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</block>\r\n\t\t</swiper>\r\n\t\t<!-- #ifdef APP-PLUS -->\r\n\t\t<view v-if=\"!isPlay\" style=\"width: 100%; height: 750rpx;\">\r\n\t\t\t<video id=\"myVideo\" :src='videoline' objectFit=\"cover\" controls style=\"width:100%;height:100% \"\r\n\t\t\t show-center-play-btn show-mute-btn=\"true\" autoplay=\"true\" auto-pause-if-navigate :custom-cache=\"false\" :enable-progress-gesture=\"false\" :poster=\"imgUrls[0]\" @pause=\"videoPause\"></video>\r\n\t\t</view>\r\n\t\t<!-- #endif -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {setThemeColor} from '@/utils/setTheme.js'\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\timgUrls: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault: function() {\r\n\t\t\t\t\treturn [];\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tvideoline: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tvalue: \"\"\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tindicatorDots: true,\r\n\t\t\t\tcircular: true,\r\n\t\t\t\tautoplay: true,\r\n\t\t\t\tinterval: 3000,\r\n\t\t\t\tduration: 500,\r\n\t\t\t\tcurrents: \"1\",\r\n\t\t\t\tcontrols: true,\r\n\t\t\t\tisPlay:true,\r\n\t\t\t\tvideoContext:'',\r\n\t\t\t\tindicatorBg:'#e93323',\r\n\t\t\t};\r\n\t\t},\r\n\t\tcreated(){\r\n\t\t\tlet that = this;\r\n\t\t\tthat.indicatorBg = setThemeColor();\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tif(this.videoline){\r\n\t\t\t\tthis.imgUrls.shift()\r\n\t\t\t}\r\n\t\t\t// #ifndef APP-PLUS\r\n\t\t\tthis.videoContext = uni.createVideoContext('myVideo', this);\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tvideoPause(e){\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tthis.isPlay= true\r\n\t\t\t\tthis.autoplay = true\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tbindPause: function() {\r\n\t\t\t\t\r\n\t\t\t\t// #ifndef APP-PLUS\r\n\t\t\t\tthis.videoContext.play();\r\n\t\t\t\tthis.$set(this, 'controls', false)\r\n\t\t\t\tthis.autoplay = false\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tthis.isPlay= false\r\n\t\t\t\tthis.videoContext = uni.createVideoContext('myVideo', this);\r\n\t\t\t\tthis.videoContext.play();\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tchange: function(e) {\r\n\t\t\t\tthis.$set(this, 'currents', e.detail.current + 1);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.product-bg {\r\n\t\twidth: 100%;\r\n\t\theight: 750rpx;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.product-bg swiper {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.product-bg .slide-image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.product-bg .pages {\r\n\t\tposition: absolute;\r\n\t\tbackground-color: #fff;\r\n\t\theight: 34rpx;\r\n\t\tpadding: 0 10rpx;\r\n\t\tborder-radius: 3rpx;\r\n\t\tright: 30rpx;\r\n\t\tbottom: 30rpx;\r\n\t\tline-height: 34rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #050505;\r\n\t}\r\n\r\n\t#myVideo {\r\n\t\twidth: 100%;\r\n\t\theight: 100%\r\n\t}\r\n\r\n\t.product-bg .item {\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.product-bg .item .poster {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\theight: 750rpx;\r\n\t\twidth: 100%;\r\n\t\tz-index: 9;\r\n\t}\r\n\r\n\t.product-bg .item .poster .image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.product-bg .item .stop {\r\n\t\tposition: absolute;\r\n\t\ttop: 50%;\r\n\t\tleft: 50%;\r\n\t\twidth: 136rpx;\r\n\t\theight: 136rpx;\r\n\t\tmargin-top: -68rpx;\r\n\t\tmargin-left: -68rpx;\r\n\t\tz-index: 9;\r\n\t}\r\n\r\n\t.product-bg .item .stop .image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=fba0bbae&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=fba0bbae&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294180101\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}