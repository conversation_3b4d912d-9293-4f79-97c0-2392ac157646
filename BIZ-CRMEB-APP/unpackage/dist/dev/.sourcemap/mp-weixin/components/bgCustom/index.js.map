{"version": 3, "sources": ["webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/bgCustom/index.vue?34f3", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/bgCustom/index.vue?355f", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/bgCustom/index.vue?196b", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/bgCustom/index.vue?e894", "uni-app:///components/bgCustom/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/bgCustom/index.vue?c173", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/bgCustom/index.vue?e55f"], "names": ["name", "props", "data", "top", "computed", "methods", "setTouchMove", "that", "open", "bgTheme", "created"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACuBnnB;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAA;EACAC;EACAC;IACA;MACAC;IACA;EACA;EACAC;EACAC;IACAC;MACA;MACA;QACAC;QACA;QACA;QACA;MACA;IACA;;IACAC;MACA,kBACA,mCACA;IACA;IACAC;MACA;IACA;EACA;EACAC,6BACA;AACA;AAAA,2B;;;;;;;;;;;;ACxDA;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/bgCustom/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=5f9afb14&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=5f9afb14&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5f9afb14\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/bgCustom/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=5f9afb14&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view style=\"touch-action: none;\">\r\n\t\t<view class=\"home\" style=\"position:fixed;\" :style=\"{ top: top + 'px'}\" id=\"right-nav\" @touchmove.stop.prevent=\"setTouchMove\">\r\n\t\t\t<view class=\"homeCon\" :class=\"homeActive === true ? 'on' : ''\" v-if=\"homeActive\">\r\n\t\t\t\t<view class=\"bg_item1\" @click=\"bgTheme('#D8EFD2')\"></view>\r\n\t\t\t\t<view class=\"bg_item2\" @click=\"bgTheme('#F9F2E2')\"></view>\r\n\t\t\t\t<view class=\"bg_item3\" @click=\"bgTheme('#D9EBED')\"></view>\r\n\t\t\t</view>\r\n\t\t\t<view @click=\"open\" class=\"pictrueBox\">\r\n\t\t\t\t<view class=\"pictrue\">\r\n\t\t\t\t\t<text class=\"iconfont icon-ziyuan-xianxing\"></text>\r\n\t\t\t\t\t<!-- <image :src=\"\r\n              homeActive === true\r\n                ? '/static/images/close.gif'\r\n                : '/static/images/open.gif'\r\n            \"\r\n\t\t\t\t\t class=\"image\" /> -->\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n<script>\r\n\timport {\r\n\t\tmapGetters\r\n\t} from \"vuex\";\r\n\texport default {\r\n\t\tname: \"Home\",\r\n\t\tprops: {},\r\n\t\tdata: function() {\r\n\t\t\treturn {\r\n\t\t\t\ttop: \"500\"\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: mapGetters([\"homeActive\"]),\r\n\t\tmethods: {\r\n\t\t\tsetTouchMove(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tif (e.touches[0].clientY < 545 && e.touches[0].clientY > 66) {\r\n\t\t\t\t\tthat.top = e.touches[0].clientY\r\n\t\t\t\t\t// that.setData({\r\n\t\t\t\t\t// \ttop: e.touches[0].clientY\r\n\t\t\t\t\t// })\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\topen: function() {\r\n\t\t\t\tthis.homeActive ?\r\n\t\t\t\t\tthis.$store.commit(\"CLOSE_HOME\") :\r\n\t\t\t\t\tthis.$store.commit(\"OPEN_HOME\");\r\n\t\t\t},\r\n\t\t\tbgTheme(value){\r\n\t\t\t\tthis.$emit('getTheme',value)\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.pictrueBox {\r\n\t\twidth: 130rpx;\r\n\t\theight: 120rpx;\r\n\t}\r\n\r\n\t/*返回主页按钮*/\r\n\t.home {\r\n\t\tposition: fixed;\r\n\t\tcolor: white;\r\n\t\ttext-align: center;\r\n\t\tz-index: 9999;\r\n\t\tright: 15rpx;\r\n\t\tdisplay: flex;\r\n\t}\r\n\r\n\t.home .homeCon {\r\n\t\tborder-radius: 50rpx;\r\n\t\topacity: 0;\r\n\t\theight: 0;\r\n\t\tcolor: blue;\r\n\t\twidth: 0;\r\n\t}\r\n\r\n\t.home .homeCon.on {\r\n\t\topacity: 1;\r\n\t\tanimation: bounceInRight 0.5s cubic-bezier(0.215, 0.610, 0.355, 1.000);\r\n\t\twidth: 300rpx;\r\n\t\theight: 80rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-around;\r\n\t\talign-items: center;\r\n\t\tborder: 2rpx solid #666;\r\n\t\tview{\r\n\t\t\twidth: 50rpx;\r\n\t\t\theight: 50rpx;\r\n\t\t\tborder-radius: 50%;\r\n\t\t}\r\n\t\t.bg_item1{\r\n\t\t\tbackground-color: #D8EFD2;\r\n\t\t}\r\n\t\t.bg_item2{\r\n\t\t\tbackground-color: #F9F2E2;\r\n\t\t}\r\n\t\t.bg_item3{\r\n\t\t\tbackground-color: #D9EBED;\r\n\t\t}\r\n\t}\r\n\r\n\t.home .homeCon .iconfont {\r\n\t\tfont-size: 40rpx;\r\n\t\tcolor: #fff;\r\n\t\tdisplay: inline-block;\r\n\t\tmargin: 0 auto;\r\n\t}\r\n\r\n\t.home .pictrue {\r\n\t\twidth: 80rpx;\r\n\t\theight: 80rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 80rpx;\r\n\t\t// border: 2rpx solid #fff;\r\n\t\t@include main_bg_color(theme)\r\n\t\tcolor:#fff;\r\n\t\tborder-radius: 50%;\r\n\t\tmargin: 0 auto;\r\n\t\t.icon-ziyuan-xianxing{\r\n\t\t\tfont-size: 40rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.home .pictrue .image {\r\n\t\t// @include main_bg_color(theme);\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 50%;\r\n\t\ttransform: rotate(90deg);\r\n\t\tms-transform: rotate(90deg);\r\n\t\tmoz-transform: rotate(90deg);\r\n\t\twebkit-transform: rotate(90deg);\r\n\t\to-transform: rotate(90deg);\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=5f9afb14&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=5f9afb14&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294180432\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}