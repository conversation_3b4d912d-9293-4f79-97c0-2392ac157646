{"version": 3, "sources": ["webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/cus-previewImg/cus-previewImg.vue?eac2", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/cus-previewImg/cus-previewImg.vue?6367", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/cus-previewImg/cus-previewImg.vue?77cd", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/cus-previewImg/cus-previewImg.vue?4d3a", "uni-app:///components/cus-previewImg/cus-previewImg.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/cus-previewImg/cus-previewImg.vue?a5cf", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/cus-previewImg/cus-previewImg.vue?001b"], "names": ["name", "props", "list", "type", "required", "default", "circular", "duration", "data", "currentIndex", "showBox", "watch", "methods", "changeSwiper", "open", "close", "shareFriend"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuI;AACvI;AACkE;AACL;AACsC;;;AAGnG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,qGAAM;AACR,EAAE,8GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrBA;AAAA;AAAA;AAAA;AAAwmB,CAAgB,koBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCqB5nB;EACAA;EACAC;IACAC;MACAC;MACAC;MACAC;QACA;MACA;IACA;IACAC;MACAH;MACAE;IACA;IACAE;MACAJ;MACAE;IACA;EACA;EACAG;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACAT;MACA;IAAA;EAEA;EACAU;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;QAAA;MAAA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACrEA;AAAA;AAAA;AAAA;AAA2qC,CAAgB,ipCAAG,EAAC,C;;;;;;;;;;;ACA/rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/cus-previewImg/cus-previewImg.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./cus-previewImg.vue?vue&type=template&id=4a9cdc64&scoped=true&\"\nvar renderjs\nimport script from \"./cus-previewImg.vue?vue&type=script&lang=js&\"\nexport * from \"./cus-previewImg.vue?vue&type=script&lang=js&\"\nimport style0 from \"./cus-previewImg.vue?vue&type=style&index=0&id=4a9cdc64&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4a9cdc64\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/cus-previewImg/cus-previewImg.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cus-previewImg.vue?vue&type=template&id=4a9cdc64&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.showBox ? _vm.list.length : null\n  var m0 = _vm.showBox && g0 > 0 ? Number(_vm.currentIndex) : null\n  var g1 = _vm.showBox && g0 > 0 ? _vm.list.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cus-previewImg.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cus-previewImg.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"previewImg\" v-if=\"showBox\" @touchmove.stop.prevent>\r\n\t\t<view class=\"mask\" @click=\"close\">\r\n\t\t\t<swiper @change=\"changeSwiper\" class=\"mask-swiper\" :current=\"currentIndex\" :circular=\"circular\" :duration=\"duration\">\r\n\t\t\t\t<swiper-item v-for=\"(src, i) in list\" :key=\"i\" class=\"flex flex-column justify-center align-center\">\r\n\t\t\t\t\t<image class=\"mask-swiper-img\" :src=\"src.image\" mode=\"widthFix\" />\r\n\t\t\t\t\t<view class=\"mask_sku\">\r\n\t\t\t\t\t\t<text class=\"sku_name\">{{src.suk}}</text>\r\n\t\t\t\t\t\t<text class=\"sku_price\">￥{{src.price}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t</view>\r\n\t\t<view class=\"pagebox\" v-if=\"list.length>0\">{{ Number(currentIndex) + 1 }} / {{ list.length }}</view>\r\n\t\t<!-- #ifndef MP -->\r\n\t\t<text class=\"iconfont icon-fenxiang share_btn\" @click=\"shareFriend()\"></text>\r\n\t\t<!-- #endif -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tname: 'cus-previewImg',\r\n\t\tprops: {\r\n\t\t\tlist: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\trequired: true,\r\n\t\t\t\tdefault: () => {\r\n\t\t\t\t\treturn [];\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tcircular: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tduration: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 500\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcurrentIndex: 0,\r\n\t\t\t\tshowBox: false\r\n\t\t\t};\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tlist(val) {\r\n\t\t\t\t// console.log('图片预览', val)\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 左右切换\r\n\t\t\tchangeSwiper(e) {\r\n\t\t\t\tthis.currentIndex = e.target.current;\r\n\t\t\t\tthis.$emit('changeSwitch',e.target.current)\r\n\t\t\t},\r\n\t\t\topen(current) {\r\n\t\t\t\tif (!current || !this.list.length) return;\r\n\t\t\t\tthis.currentIndex = this.list.map((item)=>item.suk).indexOf(current);\r\n\t\t\t\tthis.showBox = true;\r\n\t\t\t},\r\n\t\t\tclose() {\r\n\t\t\t\tthis.showBox = false;\r\n\t\t\t},\r\n\t\t\tshareFriend(){\r\n\t\t\t\tthis.$emit('shareFriend')\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@mixin full {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n}\r\n\r\n.previewImg {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tz-index: 300;\r\n\t@include full;\r\n\t.mask {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tbackground-color: #000;\r\n\t\topacity: 1;\r\n\t\tz-index: 8;\r\n\t\t@include full;\r\n\t\t&-swiper {\r\n\t\t\t@include full;\r\n\t\t\t&-img {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.pagebox{\r\n\t\tposition: absolute;\r\n\t\twidth: 100%;\r\n\t\tbottom: 20rpx;\r\n\t\tz-index: 300;\r\n\t\tcolor: #fff;\r\n\t\ttext-align: center;\r\n\t}\r\n}\r\n.mask_sku{\r\n\tcolor: #fff;\r\n\tmax-width: 80%;\r\n\tz-index: 300;\r\n\ttext-align: center;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tmargin-top: 30rpx;\r\n\t.sku_name{\r\n\t\tfont-size: 12px;\r\n\t\tborder: 1px solid #fff;\r\n\t\tpadding: 10rpx 30rpx 10rpx;\r\n\t\tborder-radius: 40px;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\t.sku_price{\r\n\t\tpadding-top: 10px;\r\n\t}\r\n}\r\n.font12{\r\n\tfont-size: 24rpx;\r\n}\r\n.share_btn{\r\n\tposition: absolute;\r\n\ttop:70rpx;\r\n\tright:50rpx;\r\n\tfont-size: 40rpx;\r\n\tcolor:#fff;\r\n\tz-index: 300;\r\n}\r\n.flex-column{flex-direction: column;}\r\n.justify-center {justify-content: center;}\r\n.align-center {align-items: center;}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cus-previewImg.vue?vue&type=style&index=0&id=4a9cdc64&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cus-previewImg.vue?vue&type=style&index=0&id=4a9cdc64&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294179328\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}