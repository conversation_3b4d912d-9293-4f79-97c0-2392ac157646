{"version": 3, "sources": ["webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/couponListWindow/index.vue?7824", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/couponListWindow/index.vue?5c98", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/couponListWindow/index.vue?99c5", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/couponListWindow/index.vue?34e1", "uni-app:///components/couponListWindow/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/couponListWindow/index.vue?b7d9", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/couponListWindow/index.vue?f29e"], "names": ["props", "openType", "type", "default", "coupon", "orderShow", "typeNum", "data", "watch", "methods", "close", "getCouponUser", "ids", "that", "title", "setType"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5BA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACkDnnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAGA;EACAA;IACA;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;IACA;IACAE;MACAH;MACAC;QACA;MACA;IACA;IACAG;MACAJ;MACAC;IACA;EACA;EACAI;IACA;MACAL;IACA;EACA;EACAM;IACA;MAAA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;UACA;UACA;UACAC;UACA;YACAC;YACAA;cACAC;YACA;cACA;gBACAA;cACA;YACA;YACAD;UACA;UACA;QACA;UACAA;UACA;MAAA;IAEA;IACAE;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AC5HA;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/couponListWindow/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=06f3951c&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=06f3951c&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"06f3951c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/couponListWindow/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=06f3951c&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.coupon.list.length\n  var l0 = g0\n    ? _vm.__map(_vm.coupon.list, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = item.money ? Number(item.money) : null\n        return {\n          $orig: $orig,\n          m0: m0,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class='coupon-list-window' :class='coupon.coupon==true?\"on\":\"\"'>\r\n\t\t\t<view v-if=\"!orderShow\"  class=\"nav acea-row row-around\">\r\n\t\t\t\t<view :class=\"['acea-row', 'row-middle', type === 1 ? 'on' : '']\" @click=\"setType(1)\">通用券</view>\r\n\t\t\t\t<view :class=\"['acea-row', 'row-middle', type === 2 ? 'on' : '']\" @click=\"setType(2)\">商品券</view>\r\n\t\t\t\t<view :class=\"['acea-row', 'row-middle', type === 3 ? 'on' : '']\" @click=\"setType(3)\">品类券</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- <view class=\"occupy\" v-if=\"!orderShow\"></view> -->\r\n\t\t\t<!-- <view class='title'>优惠券<text class='iconfont icon-guanbi' @click='close'></text></view> -->\r\n\t\t\t<view class='coupon-list' :style=\"{'margin-top':!orderShow?'0':'50rpx'}\">\r\n\t\t\t\t<block v-if=\"coupon.list.length\">\r\n\t\t\t\t\t<!-- <view class='item acea-row row-center-wrapper' v-for=\"(item,index) in coupon.list\" :key='index'> -->\r\n\t\t\t\t\t<view class='item acea-row row-center-wrapper' v-for=\"(item,index) in coupon.list\"\r\n\t\t\t\t\t\t@click=\"getCouponUser(index,item.id)\" :key='index'>\r\n\t\t\t\t\t\t<view class='money acea-row row-column row-center-wrapper' :class='item.isUse?\"moneyGray\":\"main_bg\"'>\r\n\t\t\t\t\t\t\t<view>￥<text class='num'>{{item.money?Number(item.money):''}}</text></view>\r\n\t\t\t\t\t\t\t<view class=\"pic-num\">满{{item.minPrice}}元可用</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='text'>\r\n\t\t\t\t\t\t\t<view class='condition line2'>\r\n\t\t\t\t\t\t\t\t<span class='line-title' :class='item.isUse?\"gray\":\"select\"' v-if='item.useType===1'>通用</span>\r\n\t\t\t\t\t\t\t\t<span class='line-title' :class='item.isUse?\"gray\":\"select\"'\r\n\t\t\t\t\t\t\t\t\tv-else-if='item.useType===3'>品类</span>\r\n\t\t\t\t\t\t\t\t<span class='line-title' :class='item.isUse?\"gray\":\"select\"' v-else>商品</span>\r\n\t\t\t\t\t\t\t\t<span>{{item.name}}</span>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class='data acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t\t<view v-if=\"item.day>0\">领取后{{item.day}}天内可用</view>\r\n\t\t\t\t\t\t\t\t<view v-else>\r\n\t\t\t\t\t\t\t\t\t{{ item.useStartTimeStr&& item.useEndTimeStr ? item.useStartTimeStr + \" - \" + item.useEndTimeStr : \"\"}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class='bnt gray' v-if=\"item.isUse\">{{item.use_title || '已领取'}}</view>\r\n\t\t\t\t\t\t\t\t<view class='bnt main_bg' v-else>{{coupon.statusTile || '立即领取'}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<!-- 无优惠券 -->\r\n\t\t\t\t<view class='pictrue' v-else>\r\n\t\t\t\t\t<image src='../../static/images/noCoupon.png'></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t</view>\r\n\t\t<view class='mask' catchtouchmove=\"true\" :hidden='coupon.coupon==false' @click='close'></view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tsetCouponReceive\r\n\t} from '@/api/api.js';\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\t//打开状态 0=领取优惠券,1=使用优惠券\r\n\t\t\topenType: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0,\r\n\t\t\t},\r\n\t\t\tcoupon: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: function() {\r\n\t\t\t\t\treturn {};\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//下单页面使用优惠券组件不展示tab切换页\r\n\t\t\torderShow: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: function() {\r\n\t\t\t\t\treturn '';\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\ttypeNum:{\r\n\t\t\t\ttype:Number,\r\n\t\t\t\tdefault:0\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n               type: 1,\r\n\t\t\t};\r\n\t\t},\r\n\t\twatch: {\r\n\t\t    'coupon.type': function (val) {//监听props中的属性\r\n\t\t        this.type = val;\r\n\t\t    }\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tclose: function() {\r\n\t\t\t\tthis.type = this.typeNum;\r\n\t\t\t\tthis.$emit('ChangCouponsClone');\r\n\t\t\t},\r\n\t\t\tgetCouponUser: function(index, id) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet list = that.coupon.list;\r\n\t\t\t\tif (list[index].isUse == true && this.openType == 0) return true;\r\n\t\t\t\tswitch (this.openType) {\r\n\t\t\t\t\tcase 0:\r\n\t\t\t\t\t\t//领取优惠券\r\n\t\t\t\t\t\tlet ids = [];\r\n\t\t\t\t\t\tids.push(id);\r\n\t\t\t\t\t\tsetCouponReceive(id).then(res => {\r\n\t\t\t\t\t\t\tthat.$emit('ChangCouponsUseState', index);\r\n\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: \"领取成功\"\r\n\t\t\t\t\t\t\t}, function(res) {\r\n\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\ttitle: res\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tthat.$emit('ChangCoupons', list[index]);\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 1:\r\n\t\t\t\t\t\tthat.$emit('ChangCoupons', index);\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsetType: function(type) {\r\n\t\t\t\tthis.$emit('tabCouponType', type);\r\n\t\t\t\tthis.type = type;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.coupon-list-window {\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tborder-radius: 16rpx 16rpx 0 0;\r\n\t\tz-index: 555;\r\n\t\ttransform: translate3d(0, 100%, 0);\r\n\t\ttransition: all .3s cubic-bezier(.25, .5, .5, .9);\r\n\t}\r\n\r\n\t.coupon-list-window.on {\r\n\t\ttransform: translate3d(0, 0, 0);\r\n\t}\r\n\r\n\t.coupon-list-window .title {\r\n\t\theight: 124rpx;\r\n\t\twidth: 100%;\r\n\t\ttext-align: center;\r\n\t\tline-height: 124rpx;\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.coupon-list-window .title .iconfont {\r\n\t\tposition: absolute;\r\n\t\tright: 30rpx;\r\n\t\ttop: 50%;\r\n\t\ttransform: translateY(-50%);\r\n\t\tfont-size: 35rpx;\r\n\t\tcolor: #8a8a8a;\r\n\t\tfont-weight: normal;\r\n\t}\r\n\r\n\t.coupon-list-window .coupon-list {\r\n\t\tmargin: 0 0 30rpx 0;\r\n\t\theight: 823rpx;\r\n\t\toverflow: auto;\r\n\t\tpadding-top: 30rpx;\r\n\t}\r\n\r\n\t.coupon-list-window .pictrue {\r\n\t\twidth: 414rpx;\r\n\t\theight: 336rpx;\r\n\t\tmargin: 208rpx auto;\r\n\t}\r\n\r\n\t.coupon-list-window .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.pic-num {\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.line-title {\r\n\t\twidth: 90rpx;\r\n\t\tpadding: 0 10rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tbackground: #fff;\r\n\t\tborder: 1px solid rgba(232, 51, 35, 1);\r\n\t\topacity: 1;\r\n\t\tborder-radius: 20rpx;\r\n\t\tfont-size: 20rpx;\r\n\t\tcolor: #E83323;\r\n\t\tmargin-right: 12rpx;\r\n\t}\r\n\r\n\t.line-title.gray {\r\n\t\tborder-color: #BBB;\r\n\t\tcolor: #bbb;\r\n\t\tbackground-color: #F5F5F5;\r\n\t}\r\n\r\n\t.nav {\r\n\t\t// position: absolute;\r\n\t\t// top: 0;\r\n\t\t// left: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 96rpx;\r\n\t\tborder-bottom: 2rpx solid #F5F5F5;\r\n\t\tborder-top-left-radius: 16rpx;\r\n\t\tborder-top-right-radius: 16rpx;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #999999;\r\n\t}\r\n\r\n\t.nav .acea-row {\r\n\t\tborder-top: 5rpx solid transparent;\r\n\t\tborder-bottom: 5rpx solid transparent;\r\n\t}\r\n\r\n\t.nav .acea-row.on {\r\n\t\t@include tab_border_bottom(theme);\r\n\t\tcolor: #282828;\r\n\t}\r\n\r\n\t.nav .acea-row:only-child {\r\n\t\tborder-bottom-color: transparent;\r\n\t}\r\n\r\n\t.occupy {\r\n\t\theight: 106rpx;\r\n\t}\r\n\r\n\t.coupon-list .item {\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.06);\r\n\t}\r\n\r\n\t.coupon-list .item .money {\r\n\t\tfont-weight: normal;\r\n\t}\r\n\t\r\n\t.main_bg{\r\n\t\t@include main_bg_color(theme);\r\n\t}\r\n\t.select{\r\n\t\t@include main_color(theme);\r\n\t\t@include coupons_border_color(theme);\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=06f3951c&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=06f3951c&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294180164\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}