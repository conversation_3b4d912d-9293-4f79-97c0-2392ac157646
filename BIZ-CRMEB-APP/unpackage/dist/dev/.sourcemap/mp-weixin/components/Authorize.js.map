{"version": 3, "sources": ["webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/Authorize.vue?3e1c", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/Authorize.vue?ec94", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/Authorize.vue?0f26", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/Authorize.vue?afaf", "uni-app:///components/Authorize.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/Authorize.vue?aae7", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/Authorize.vue?965f"], "names": ["name", "props", "isAuto", "type", "default", "isGoIndex", "isShowAuth", "data", "logoUrl", "computed", "watch", "is<PERSON>ogin", "n", "created", "methods", "setAuthStatus", "Routine", "getUserInfo", "userInfo", "uni", "title", "icon", "duration", "setUserInfo", "getLogoUrl", "that", "<PERSON><PERSON>", "close", "currPage", "url"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmmB,CAAgB,6nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACsBvnB;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;AALA;AAAA,eAOA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;EACA;EACAG;IACA;MACAC;IACA;EACA;EACAC;EACAC;IACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;MAAA;MACAC;QACA,2BACA,yBAEA;MACA;QACA,kBACA;MACA;IACA;IACAC;MAAA;MACAD;QACA;QACAE;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAF;UACAG;UACA;UACA;QACA;UACAA;UACAA;YACAC;YACAC;YACAC;UACA;QACA;MACA;QACAH;MACA;IACA;IACAI;MAAA;MACAJ;QAAAC;MAAA;MACAJ;QACA;MACA;QACAG;MACA;IACA;IACAK;MACA;MACA;QACA;QACA;MACA;MACA;QACAC;QACAC;MACA;IACA;IACAC;MACA;QAAAC;MACA;QACAT;UAAAU;QAAA;MACA;QACA;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClIA;AAAA;AAAA;AAAA;AAAsqC,CAAgB,4oCAAG,EAAC,C;;;;;;;;;;;ACA1rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/Authorize.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./Authorize.vue?vue&type=template&id=4147c09e&scoped=true&\"\nvar renderjs\nimport script from \"./Authorize.vue?vue&type=script&lang=js&\"\nexport * from \"./Authorize.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Authorize.vue?vue&type=style&index=0&id=4147c09e&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4147c09e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/Authorize.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Authorize.vue?vue&type=template&id=4147c09e&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Authorize.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Authorize.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class='Popup' v-if='isShowAuth'>\r\n\t\t   <image :src='logoUrl'></image>\r\n\t\t   <view class='title'>授权提醒</view>\r\n\t\t   <view class='tip'>请授权头像等信息，以便为您提供更好的服务</view>\r\n\t\t   <view class='bottom flex'>\r\n\t\t      <view class='item' @click='close'>随便逛逛</view>\r\n\t\t\t  <!-- #ifdef APP-PLUS -->\r\n\t\t\t  <button class='item grant' @click=\"setUserInfo\">去授权</button>\r\n\t\t\t  <!-- #endif -->\r\n\t\t\t  <!-- #ifdef MP -->\r\n\t\t\t  <button class='item grant'  type=\"primary\" open-type=\"getUserInfo\" lang=\"zh_CN\" @getuserinfo=\"setUserInfo\">去授权</button>\r\n\t\t\t  <!-- #endif -->\r\n\t\t   </view>\r\n\t\t</view>\r\n\t\t<view class='mask' v-if='isShowAuth' @click='close'></view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tconst app = getApp();\r\n\timport Cache from '../utils/cache';\r\n\timport { getLogo } from '../api/public';\r\n\timport { LOGO_URL } from '../config/cache';\r\n\timport { mapGetters } from 'vuex';\r\n\timport Routine from '../libs/routine';\r\n\t\r\n\texport default {\r\n\t\tname:'Authorize',\r\n\t\tprops:{\r\n\t\t\tisAuto:{\r\n\t\t\t\ttype:Boolean,\r\n\t\t\t\tdefault:true\r\n\t\t\t},\r\n\t\t\tisGoIndex:{\r\n\t\t\t\ttype:Boolean,\r\n\t\t\t\tdefault:true\r\n\t\t\t},\r\n\t\t\tisShowAuth:{\r\n\t\t\t\ttype:Boolean,\r\n\t\t\t\tdefault:false\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata(){\r\n\t\t\treturn {\r\n\t\t\t\tlogoUrl:''\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed:mapGetters(['isLogin','userInfo']),\r\n\t\twatch:{\r\n\t\t\tisLogin(n){\r\n\t\t\t\tn === true && this.$emit('onLoadFun',this.userInfo);\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.getLogoUrl();\r\n\t\t\tthis.setAuthStatus();\r\n\t\t},\r\n\t\tmethods:{\r\n\t\t\tsetAuthStatus(){\r\n\t\t\t\tRoutine.authorize().then(res=>{\r\n\t\t\t\t\tif(res.islogin === false)\r\n\t\t\t\t\t\tthis.setUserInfo();\r\n\t\t\t\t\telse\r\n\t\t\t\t\t\tthis.$emit('onLoadFun',this.userInfo);\r\n\t\t\t\t}).catch(res=>{\r\n\t\t\t\t\tif (this.isAuto) \r\n\t\t\t\t\t\tthis.$emit('authColse',true);\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetUserInfo(code){\r\n\t\t\t\tRoutine.getUserInfo().then(res=>{\r\n\t\t\t\t\tlet userInfo = res.userInfo\r\n\t\t\t\t\tuserInfo.code = code;\r\n\t\t\t\t\tuserInfo.spread_spid = app.globalData.spread;//获取推广人ID\r\n\t\t\t\t\tuserInfo.spread_code = app.globalData.code;//获取推广人分享二维码ID\r\n\t\t\t\t\tuserInfo.avatar  = userInfo.userInfo.avatarUrl;\r\n\t\t\t\t\tuserInfo.city  = userInfo.userInfo.city;\r\n\t\t\t\t\tuserInfo.country  = userInfo.userInfo.country;\r\n\t\t\t\t\tuserInfo.nickName  = userInfo.userInfo.nickName;\r\n\t\t\t\t\tuserInfo.province  = userInfo.userInfo.province;\r\n\t\t\t\t\tuserInfo.sex  = userInfo.userInfo.gender;\r\n\t\t\t\t\tRoutine.authUserInfo(code,userInfo).then(res=>{\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tthis.$emit('authColse',false);\r\n\t\t\t\t\t\tthis.$emit('onLoadFun',this.userInfo);\r\n\t\t\t\t\t}).catch(res=>{\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle:res.message,\r\n\t\t\t\t\t\t\ticon:'none',\r\n\t\t\t\t\t\t\tduration:2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t})\r\n\t\t\t\t}).catch(res =>{\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tsetUserInfo(){\r\n\t\t\t\tuni.showLoading({title:'正在登录中'});\r\n\t\t\t\tRoutine.getCode().then(code=>{\r\n\t\t\t\t\tthis.getUserInfo(code);\r\n\t\t\t\t}).catch(res=>{\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetLogoUrl(){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (Cache.has(LOGO_URL)) {\r\n\t\t\t\t\tthis.logoUrl = Cache.get(LOGO_URL);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tgetLogo().then(res=>{\r\n\t\t\t\t\tthat.logoUrl = res.data.logoUrl\r\n\t\t\t\t\tCache.set(LOGO_URL,that.logoUrl);\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tclose(){\r\n\t\t\t\tlet pages = getCurrentPages(), currPage  = pages[pages.length - 1];\r\n\t\t\t\tif(this.isGoIndex){\r\n\t\t\t\t\tuni.switchTab({url:'/pages/index/index'});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$emit('authColse',false);\r\n\t\t\t\t}\r\n\t\t\t\t// if (currPage && currPage.isShowAuth != undefined){\r\n\t\t\t\t// \tcurrPage.isShowAuth = true;\r\n\t\t\t\t// }\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang='scss'>\r\n\t.Popup{width:500rpx;background-color:#fff;position:fixed;top:50%;left:50%;margin-left:-250rpx;transform:translateY(-50%);z-index:320;}\r\n\t.Popup image{width:150rpx;height:150rpx;margin:-67rpx auto 0 auto;display:block;border: 8rpx solid #fff;border-radius: 50%}\r\n\t.Popup .title{font-size:28rpx;color:#000;text-align:center;margin-top: 30rpx}\r\n\t.Popup .tip{font-size:22rpx;color:#555;padding:0 24rpx;margin-top:25rpx;}\r\n\t.Popup .bottom .item{width:50%;height:80rpx;background-color:#eeeeee;text-align:center;line-height:80rpx;font-size:24rpx;color:#666;margin-top:54rpx;}\r\n\t.Popup .bottom .item.on{width: 100%}\r\n\t.flex{display:flex;}\r\n\t.Popup .bottom .item.grant{font-size:28rpx;color:#fff;font-weight:bold;background-color:$theme-color;border-radius:0;padding:0;}\r\n\t.mask{position:fixed;top:0;right:0;left:0;bottom:0;background-color:rgba(0,0,0,0.65);z-index:310;}\r\n\t\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Authorize.vue?vue&type=style&index=0&id=4147c09e&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Authorize.vue?vue&type=style&index=0&id=4147c09e&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294179373\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}