{"version": 3, "sources": ["webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/shareRedPackets/index.vue?833c", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/shareRedPackets/index.vue?4925", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/shareRedPackets/index.vue?a605", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/shareRedPackets/index.vue?2958", "uni-app:///components/shareRedPackets/index.vue", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/shareRedPackets/index.vue?5ea7", "webpack:///E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/components/shareRedPackets/index.vue?75a5"], "names": ["props", "sharePacket", "type", "default", "isState", "priceName", "touchstart", "data", "imgHost", "picBg", "top", "created", "uni", "key", "success", "that", "methods", "goShare", "setTouchMove", "handleleterClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACgBnnB;;;;;;;;;;;;;;;;gBACA;EACAA;IACAC;MACAC;MACAC;QACA;UAAAC;UAAAC;UAAAC;QAAA;MACA;IACA;EACA;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACAC;MACAC;QACA;UACA;YACAC;YACA;UACA;YACAA;YACA;UACA;YACAA;YACA;UACA;YACAA;YACA;UACA;YACAA;YACA;QAAA;MAEA;IACA;IACA;MACAA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;MACA;QACAH;MACA;IACA;IACAI;MACA;QACA;MACA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AChFA;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/shareRedPackets/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=1cb57b71&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=1cb57b71&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1cb57b71\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/shareRedPackets/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=1cb57b71&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class='sharing-packets' :style=\"{ top: top + 'px'}\"\r\n\t:class=\"sharePacket.touchstart?'hide_left':'' \"\r\n\t @touchmove.stop.prevent=\"setTouchMove\" @click=\"handleleterClick()\" v-if=\"!sharePacket.isState\">\r\n\t    <view class='sharing-con' :style=\"{backgroundImage:'url('+imgHost+ '/' + picBg+')'}\">\r\n\t        <view class='text' >\r\n\t\t\t\t<view class=\"main_color\">会员分享返</view>\r\n\t\t\t\t<view class='money price'><text class='label'>￥</text>{{sharePacket.priceName}}</view>\r\n\t\t\t\t<view class='tip'>下单即返佣金</view>\r\n\t\t\t\t<view class='shareBut'>立即分享</view>\r\n\t      </view>\r\n\t    </view>  \r\n\t</view>\r\n</template>\r\n<!--  -->\r\n<script>\r\n\timport { getImageDomain } from '@/api/api.js'\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\tsharePacket: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: function(){\r\n\t\t\t\t\treturn {isState: true,priceName:'',touchstart:false}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\timgHost:'',\r\n\t\t\t\tpicBg:'crmebimage/change/share_tip/share_tip1.png',\r\n\t\t\t\ttop: \"260\",\r\n\t\t\t};\r\n\t\t},\r\n\t\tcreated(){\r\n\t\t\tlet that = this;\r\n\t\t\tuni.getStorage({\r\n\t\t\t    key: 'theme',\r\n\t\t\t    success: function (res) {\r\n\t\t\t\t\tswitch (res.data) {\r\n\t\t\t\t\t\tcase 'theme1':\r\n\t\t\t\t\t\t\tthat.picBg = 'crmebimage/change/share_tip/share_tip1.png';\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'theme2':\r\n\t\t\t\t\t\t\tthat.picBg = 'crmebimage/change/share_tip/share_tip2.png';\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'theme3':\r\n\t\t\t\t\t\t\tthat.picBg = 'crmebimage/change/share_tip/share_tip3.png';\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'theme4':\r\n\t\t\t\t\t\t\tthat.picBg = 'crmebimage/change/share_tip/share_tip4.png';\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'theme5':\r\n\t\t\t\t\t\t\tthat.picBg = 'crmebimage/change/share_tip/share_tip5.png';\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t    }\r\n\t\t\t});\r\n\t\t\tgetImageDomain().then(res=>{\r\n\t\t\t\tthat.$set(that,'imgHost',res.data);\r\n\t\t\t})\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgoShare:function(){\r\n\t\t\t\tthis.$emit('listenerActionSheet');\r\n\t\t\t},\r\n\t\t\tsetTouchMove(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tif (e.touches[0].clientY < 545 && e.touches[0].clientY > 66) {\r\n\t\t\t\t\tthat.top = e.touches[0].clientY\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\thandleleterClick(){\r\n\t\t\t\tif(this.sharePacket.touchstart){\r\n\t\t\t\t\tthis.$emit('showShare',false)\r\n\t\t\t\t}else{\r\n\t\t\t\t\t// this.$emit('showShare',true)\r\n\t\t\t\t\tthis.goShare()\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.sharing-packets{\r\n\t\tposition:fixed;left:30rpx;z-index:99;transition: all .2s linear;\r\n\t\t&.hide_left{\r\n\t\t\ttransition: all .2s linear;left: -110rpx;\r\n\t\t\ttransform: scale(.6);\r\n\t\t}\r\n\t}\r\n\t.sharing-packets .iconfont{width:44rpx;height:44rpx;border-radius:50%;text-align:center;line-height:44rpx;background-color:#999;font-size:20rpx;color:#fff;margin:0 auto;box-sizing:border-box;padding-left:1px;}\r\n\t.sharing-packets .line{width:2rpx;height:40rpx;background-color:#999;margin:0 auto;}\r\n\t.sharing-packets .sharing-con{width:187rpx;height:210rpx;position:relative;background-size: cover;}\r\n\t.sharing-packets .sharing-con .text{position:absolute;top:30rpx;font-size:20rpx;width:100%;text-align:center;}\r\n\t.sharing-packets .sharing-con .text .money{font-size:32rpx;font-weight:bold;margin-top:5rpx;}\r\n\t.sharing-packets .sharing-con .text .money .label{font-size:20rpx;}\r\n\t.sharing-packets .sharing-con .text .tip{font-size:18rpx;color:#999;margin-top:5rpx;}\r\n\t.sharing-packets .sharing-con .text .shareBut{font-size:22rpx;color:#fff;margin-top:28rpx;height:50rpx;line-height:50rpx;}\r\n\t.main_color{@include main_color(theme);}\r\n\t.price{@include price_color(theme);}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=1cb57b71&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=1cb57b71&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754294180146\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}