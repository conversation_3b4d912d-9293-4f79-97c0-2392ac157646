@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.money.data-v-59b202a4 {
  font-size: 26rpx;
  color: theme;
}
[data-theme="theme1"] .money.data-v-59b202a4 {
  color: #F93323 !important;
}
[data-theme="theme2"] .money.data-v-59b202a4 {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .money.data-v-59b202a4 {
  color: #FF7600 !important;
}
[data-theme="theme4"] .money.data-v-59b202a4 {
  color: #FD502F !important;
}
[data-theme="theme5"] .money.data-v-59b202a4 {
  color: #FF448F !important;
}
.order-item.data-v-59b202a4 {
  width: 100%;
  display: flex;
  position: relative;
  align-items: right;
  flex-direction: row;
}
.remove.data-v-59b202a4 {
  width: 120rpx;
  height: 40rpx;
  background-color: theme;
  color: #fff;
  position: absolute;
  bottom: 30rpx;
  right: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
}
[data-theme="theme1"] .remove.data-v-59b202a4 {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .remove.data-v-59b202a4 {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .remove.data-v-59b202a4 {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .remove.data-v-59b202a4 {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .remove.data-v-59b202a4 {
  background-color: #FF448F !important;
}
.collectionGoods .nav.data-v-59b202a4 {
  width: 92%;
  height: 90rpx;
  background-color: #fff;
  padding: 0 24rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #282828;
  position: fixed;
  left: 30rpx;
  z-index: 5;
  top: 30rpx;
  border-bottom: 1px solid #EEEEEE;
  border-top-left-radius: 14rpx;
  border-top-right-radius: 14rpx;
}
.collectionGoods .list.data-v-59b202a4 {
  padding: 30rpx;
  margin-top: 90rpx;
}
.collectionGoods .list .name.data-v-59b202a4 {
  width: 434rpx;
  margin-bottom: 56rpx;
}
.collectionGoods .centent.data-v-59b202a4 {
  background-color: #fff;
  border-bottom-left-radius: 14rpx;
  border-bottom-right-radius: 14rpx;
}
.collectionGoods .item.data-v-59b202a4 {
  background-color: #fff;
  padding-left: 24rpx;
  height: 180rpx;
  margin-bottom: 15rpx;
  border-radius: 14rpx;
}
.collectionGoods .item .pictrue.data-v-59b202a4 {
  width: 130rpx;
  height: 130rpx;
  margin-right: 20rpx;
}
.collectionGoods .item .pictrue image.data-v-59b202a4 {
  width: 100%;
  height: 100%;
  border-radius: 14rpx;
}
.collectionGoods .item .text.data-v-59b202a4 {
  width: 535rpx;
  height: 130rpx;
  font-size: 28rpx;
  color: #282828;
}
.collectionGoods .item .text .name.data-v-59b202a4 {
  width: 100%;
}
.collectionGoods .item .text .delete.data-v-59b202a4 {
  font-size: 26rpx;
  color: #282828;
  width: 144rpx;
  height: 46rpx;
  border: 1px solid #bbb;
  border-radius: 4rpx;
  text-align: center;
  line-height: 46rpx;
}
.noCommodity.data-v-59b202a4 {
  background-color: #fff;
  padding-top: 1rpx;
  border-top: 0;
}
.footer.data-v-59b202a4 {
  z-index: 9;
  width: 100%;
  height: 96rpx;
  background-color: #fff;
  position: fixed;
  padding: 0 30rpx;
  box-sizing: border-box;
  border-top: 1rpx solid #eee;
  border-bottom: 1px solid #EEEEEE;
  bottom: 0rpx;
}
.footer .checkAll.data-v-59b202a4 {
  font-size: 28rpx;
  color: #282828;
  margin-left: 16rpx;
}
.footer .button .bnt.data-v-59b202a4 {
  font-size: 28rpx;
  color: #999;
  border-radius: 30rpx;
  border: 1px solid #999;
  width: 160rpx;
  height: 60rpx;
  text-align: center;
  line-height: 60rpx;
}
.font_color.data-v-59b202a4 {
  color: theme;
}
[data-theme="theme1"] .font_color.data-v-59b202a4 {
  color: #e93323 !important;
}
[data-theme="theme2"] .font_color.data-v-59b202a4 {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .font_color.data-v-59b202a4 {
  color: #42CA4D !important;
}
[data-theme="theme4"] .font_color.data-v-59b202a4 {
  color: #1DB0FC !important;
}
[data-theme="theme5"] .font_color.data-v-59b202a4 {
  color: #FF448F !important;
}
.data-v-59b202a4 checkbox .uni-checkbox-input.uni-checkbox-input-checked {
  background-color: theme;
  border: theme;
  color: #fff !important;
}
[data-theme="theme1"].data-v-59b202a4 checkbox .uni-checkbox-input.uni-checkbox-input-checked {
  background-color: #e93323 !important;
}
[data-theme="theme2"].data-v-59b202a4 checkbox .uni-checkbox-input.uni-checkbox-input-checked {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"].data-v-59b202a4 checkbox .uni-checkbox-input.uni-checkbox-input-checked {
  background-color: #42CA4D !important;
}
[data-theme="theme4"].data-v-59b202a4 checkbox .uni-checkbox-input.uni-checkbox-input-checked {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"].data-v-59b202a4 checkbox .uni-checkbox-input.uni-checkbox-input-checked {
  background-color: #FF448F !important;
}
[data-theme="theme1"].data-v-59b202a4 checkbox .uni-checkbox-input.uni-checkbox-input-checked {
  border: 1px solid #e93323;
}
[data-theme="theme2"].data-v-59b202a4 checkbox .uni-checkbox-input.uni-checkbox-input-checked {
  border: 1px solid #FE5C2D;
}
[data-theme="theme3"].data-v-59b202a4 checkbox .uni-checkbox-input.uni-checkbox-input-checked {
  border: 1px solid #42CA4D;
}
[data-theme="theme4"].data-v-59b202a4 checkbox .uni-checkbox-input.uni-checkbox-input-checked {
  border: 1px solid #1DB0FC;
}
[data-theme="theme5"].data-v-59b202a4 checkbox .uni-checkbox-input.uni-checkbox-input-checked {
  border: 1px solid #FF448F;
}
.data-v-59b202a4 checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  background-color: theme;
  border: theme;
  color: #fff !important;
  margin-right: 0 !important;
}
[data-theme="theme1"].data-v-59b202a4 checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  background-color: #e93323 !important;
}
[data-theme="theme2"].data-v-59b202a4 checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"].data-v-59b202a4 checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  background-color: #42CA4D !important;
}
[data-theme="theme4"].data-v-59b202a4 checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"].data-v-59b202a4 checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  background-color: #FF448F !important;
}
[data-theme="theme1"].data-v-59b202a4 checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  border: 1px solid #e93323;
}
[data-theme="theme2"].data-v-59b202a4 checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  border: 1px solid #FE5C2D;
}
[data-theme="theme3"].data-v-59b202a4 checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  border: 1px solid #42CA4D;
}
[data-theme="theme4"].data-v-59b202a4 checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  border: 1px solid #1DB0FC;
}
[data-theme="theme5"].data-v-59b202a4 checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  border: 1px solid #FF448F;
}

