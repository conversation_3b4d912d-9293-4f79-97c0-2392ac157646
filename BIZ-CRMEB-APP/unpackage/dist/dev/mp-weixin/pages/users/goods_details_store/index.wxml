<view class="_div"><view data-ref="container" class="storeBox _div vue-ref"><block wx:for="{{storeList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['checked',['$0'],[[['storeList','',index]]]]]]]}}" class="storeBox-box _div" catchtap="__e"><view class="store-img _div"><image src="{{item.image}}" lazy-load="true" class="_img"></image></view><view class="store-cent-left _div"><view class="store-name _div">{{item.name}}</view><view class="store-address line1 _div">{{''+item.address+(", "+item.detailedAddress)+''}}</view></view><view class="row-right _div"><view class="_div"><view data-event-opts="{{[['tap',[['call',['$0'],[[['storeList','',index,'phone']]]]]]]}}" class="store-phone" bindtap="__e"><text class="iconfont icon-dadianhua01"></text></view></view><view data-event-opts="{{[['tap',[['showMaoLocation',['$0'],[[['storeList','',index]]]]]]]}}" class="store-distance _div" catchtap="__e"><block wx:if="{{item.distance}}"><label class="addressTxt _span">{{"距离"+item.distance/1000+"千米"}}</label></block><block wx:else><label class="addressTxt _span">查看地图</label></block><label class="iconfont icon-youjian _span"></label></view></view></view></block><loading vue-id="60a77fb8-1" loaded="{{loaded}}" loading="{{loading}}" bind:__l="__l"></loading></view><view class="_div"></view></view>