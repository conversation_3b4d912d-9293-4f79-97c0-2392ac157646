@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page.data-v-3ae7937c {
  background: #fff;
}
.appLogin.data-v-3ae7937c {
  margin-top: 60rpx;
}
.appLogin .hds.data-v-3ae7937c {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
  color: #B4B4B4;
}
.appLogin .hds .line.data-v-3ae7937c {
  width: 68rpx;
  height: 1rpx;
  background: #CCCCCC;
}
.appLogin .hds ._p.data-v-3ae7937c {
  margin: 0 20rpx;
}
.appLogin .btn-wrapper.data-v-3ae7937c {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30rpx;
}
.appLogin .btn-wrapper .btn.data-v-3ae7937c {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 68rpx;
  height: 68rpx;
  border-radius: 50%;
}
.appLogin .btn-wrapper .apple-btn.data-v-3ae7937c {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 30rpx;
  background: #000;
  border-radius: 34rpx;
  font-size: 40rpx;
}
.appLogin .btn-wrapper .apple-btn .icon-s-pingguo.data-v-3ae7937c {
  color: #fff;
  font-size: 40rpx;
}
.appLogin .btn-wrapper .iconfont.data-v-3ae7937c {
  font-size: 40rpx;
  color: #fff;
}
.appLogin .btn-wrapper .wx.data-v-3ae7937c {
  margin-right: 30rpx;
  background-color: #61C64F;
}
.appLogin .btn-wrapper .mima.data-v-3ae7937c {
  background-color: #28B3E9;
}
.appLogin .btn-wrapper .yanzheng.data-v-3ae7937c {
  background-color: #F89C23;
}
.main_color.data-v-3ae7937c {
  color: theme;
}
[data-theme="theme1"] .main_color.data-v-3ae7937c {
  color: #e93323 !important;
}
[data-theme="theme2"] .main_color.data-v-3ae7937c {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .main_color.data-v-3ae7937c {
  color: #42CA4D !important;
}
[data-theme="theme4"] .main_color.data-v-3ae7937c {
  color: #1DB0FC !important;
}
[data-theme="theme5"] .main_color.data-v-3ae7937c {
  color: #FF448F !important;
}
.bg_color.data-v-3ae7937c {
  background-color: theme;
}
[data-theme="theme1"] .bg_color.data-v-3ae7937c {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .bg_color.data-v-3ae7937c {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .bg_color.data-v-3ae7937c {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .bg_color.data-v-3ae7937c {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .bg_color.data-v-3ae7937c {
  background-color: #FF448F !important;
}
.code ._img.data-v-3ae7937c {
  width: 100%;
  height: 100%;
}
.acea-row.row-middle input.data-v-3ae7937c {
  margin-left: 20rpx;
  display: block;
}
.login-wrapper.data-v-3ae7937c {
  padding: 30rpx;
}
.login-wrapper .shading.data-v-3ae7937c {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-top: 200rpx;
}
.login-wrapper .shading image.data-v-3ae7937c {
  width: 180rpx;
  height: 180rpx;
}
.login-wrapper .whiteBg.data-v-3ae7937c {
  margin-top: 100rpx;
}
.login-wrapper .whiteBg .list.data-v-3ae7937c {
  border-radius: 16rpx;
  overflow: hidden;
}
.login-wrapper .whiteBg .list .item.data-v-3ae7937c {
  border-bottom: 1px solid #F0F0F0;
  background: #fff;
}
.login-wrapper .whiteBg .list .item .row-middle.data-v-3ae7937c {
  position: relative;
  padding: 16rpx 45rpx;
}
.login-wrapper .whiteBg .list .item .row-middle .texts.data-v-3ae7937c {
  flex: 1;
  font-size: 28rpx;
  height: 80rpx;
  line-height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.login-wrapper .whiteBg .list .item .row-middle input.data-v-3ae7937c {
  flex: 1;
  font-size: 28rpx;
  height: 80rpx;
  line-height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.login-wrapper .whiteBg .list .item .row-middle .code.data-v-3ae7937c {
  position: absolute;
  right: 30rpx;
  top: 50%;
  color: #E93323;
  font-size: 26rpx;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.login-wrapper .whiteBg .logon.data-v-3ae7937c {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 86rpx;
  margin-top: 80rpx;
  background-color: #E93323;
  border-radius: 120rpx;
  color: #FFFFFF;
  font-size: 30rpx;
}
.login-wrapper .whiteBg .tips.data-v-3ae7937c {
  margin: 30rpx;
  text-align: center;
  color: #999;
}

