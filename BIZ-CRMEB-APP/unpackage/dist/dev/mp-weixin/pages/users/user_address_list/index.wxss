@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.address-management.data-v-21ff4d10 {
  padding: 20rpx 30rpx;
}
.address-management.fff.data-v-21ff4d10 {
  background-color: #fff;
  height: 1300rpx;
}
.bg_color.data-v-21ff4d10 {
  background-color: theme;
}
[data-theme="theme1"] .bg_color.data-v-21ff4d10 {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .bg_color.data-v-21ff4d10 {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .bg_color.data-v-21ff4d10 {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .bg_color.data-v-21ff4d10 {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .bg_color.data-v-21ff4d10 {
  background-color: #FF448F !important;
}
.line.data-v-21ff4d10 {
  width: 100%;
  height: 3rpx;
}
.line image.data-v-21ff4d10 {
  width: 100%;
  height: 100%;
  display: block;
}
.address-management .item.data-v-21ff4d10 {
  background-color: #fff;
  padding: 0 20rpx;
  margin-bottom: 20rpx;
}
.address-management .item .address.data-v-21ff4d10 {
  padding: 35rpx 0;
  border-bottom: 1rpx solid #eee;
  font-size: 28rpx;
  color: #282828;
}
.address-management .item .address .consignee.data-v-21ff4d10 {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}
.address-management .item .address .consignee .phone.data-v-21ff4d10 {
  margin-left: 25rpx;
}
.address-management .item .operation.data-v-21ff4d10 {
  height: 83rpx;
  font-size: 28rpx;
  color: #282828;
}
.address-management .item .operation .radio text.data-v-21ff4d10 {
  margin-left: 13rpx;
}
.address-management .item .operation .iconfont.data-v-21ff4d10 {
  color: #2c2c2c;
  font-size: 35rpx;
  vertical-align: -2rpx;
  margin-right: 10rpx;
}
.address-management .item .operation .iconfont.icon-shanchu.data-v-21ff4d10 {
  margin-left: 35rpx;
  font-size: 38rpx;
}
.footer.data-v-21ff4d10 {
  position: fixed;
  width: 100%;
  background-color: #fff;
  bottom: 0;
  height: 106rpx;
  padding: 0 30rpx;
  box-sizing: border-box;
}
.footer .addressBnt.data-v-21ff4d10 {
  width: 330rpx;
  height: 76rpx;
  border-radius: 50rpx;
  text-align: center;
  line-height: 76rpx;
  font-size: 30rpx;
  color: #fff;
}
.footer .addressBnt.on.data-v-21ff4d10 {
  width: 690rpx;
  margin: 0 auto;
}
.footer .addressBnt .iconfont.data-v-21ff4d10 {
  font-size: 35rpx;
  margin-right: 8rpx;
  vertical-align: -1rpx;
}
.footer .addressBnt.wxbnt.data-v-21ff4d10 {
  background-color: theme;
}
[data-theme="theme1"] .footer .addressBnt.wxbnt.data-v-21ff4d10 {
  background-color: #FE960F !important;
}
[data-theme="theme2"] .footer .addressBnt.wxbnt.data-v-21ff4d10 {
  background-color: #FDB000 !important;
}
[data-theme="theme3"] .footer .addressBnt.wxbnt.data-v-21ff4d10 {
  background-color: #FE960F !important;
}
[data-theme="theme4"] .footer .addressBnt.wxbnt.data-v-21ff4d10 {
  background-color: #22CAFD !important;
}
[data-theme="theme5"] .footer .addressBnt.wxbnt.data-v-21ff4d10 {
  background-color: #282828 !important;
}
.data-v-21ff4d10 radio .wx-radio-input.wx-radio-input-checked {
  background-color: theme;
  border: theme;
}
[data-theme="theme1"].data-v-21ff4d10 radio .wx-radio-input.wx-radio-input-checked {
  background-color: #e93323 !important;
}
[data-theme="theme2"].data-v-21ff4d10 radio .wx-radio-input.wx-radio-input-checked {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"].data-v-21ff4d10 radio .wx-radio-input.wx-radio-input-checked {
  background-color: #42CA4D !important;
}
[data-theme="theme4"].data-v-21ff4d10 radio .wx-radio-input.wx-radio-input-checked {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"].data-v-21ff4d10 radio .wx-radio-input.wx-radio-input-checked {
  background-color: #FF448F !important;
}
[data-theme="theme1"].data-v-21ff4d10 radio .wx-radio-input.wx-radio-input-checked {
  border: 1px solid #e93323;
}
[data-theme="theme2"].data-v-21ff4d10 radio .wx-radio-input.wx-radio-input-checked {
  border: 1px solid #FE5C2D;
}
[data-theme="theme3"].data-v-21ff4d10 radio .wx-radio-input.wx-radio-input-checked {
  border: 1px solid #42CA4D;
}
[data-theme="theme4"].data-v-21ff4d10 radio .wx-radio-input.wx-radio-input-checked {
  border: 1px solid #1DB0FC;
}
[data-theme="theme5"].data-v-21ff4d10 radio .wx-radio-input.wx-radio-input-checked {
  border: 1px solid #FF448F;
}
.data-v-21ff4d10 radio .uni-radio-input.uni-radio-input-checked {
  background-color: theme;
  border: none !important;
}
[data-theme="theme1"].data-v-21ff4d10 radio .uni-radio-input.uni-radio-input-checked {
  background-color: #e93323 !important;
}
[data-theme="theme2"].data-v-21ff4d10 radio .uni-radio-input.uni-radio-input-checked {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"].data-v-21ff4d10 radio .uni-radio-input.uni-radio-input-checked {
  background-color: #42CA4D !important;
}
[data-theme="theme4"].data-v-21ff4d10 radio .uni-radio-input.uni-radio-input-checked {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"].data-v-21ff4d10 radio .uni-radio-input.uni-radio-input-checked {
  background-color: #FF448F !important;
}

