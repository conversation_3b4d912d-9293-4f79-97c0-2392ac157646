@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.my-order .header.data-v-09744212 {
  height: 250rpx;
  padding: 0 30rpx;
}
.bg_color.data-v-09744212 {
  background-color: theme;
}
[data-theme="theme1"] .bg_color.data-v-09744212 {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .bg_color.data-v-09744212 {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .bg_color.data-v-09744212 {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .bg_color.data-v-09744212 {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .bg_color.data-v-09744212 {
  background-color: #FF448F !important;
}
.my-order .header .picTxt.data-v-09744212 {
  height: 190rpx;
}
.my-order .header .picTxt .text.data-v-09744212 {
  color: rgba(255, 255, 255, 0.8);
  font-size: 26rpx;
  font-family: 'Guildford Pro';
}
.my-order .header .picTxt .text .name.data-v-09744212 {
  font-size: 34rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20rpx;
}
.my-order .header .picTxt .pictrue.data-v-09744212 {
  width: 122rpx;
  height: 109rpx;
}
.my-order .header .picTxt .pictrue image.data-v-09744212 {
  width: 100%;
  height: 100%;
}
.my-order .nav.data-v-09744212 {
  background-color: #fff;
  width: 690rpx;
  height: 140rpx;
  border-radius: 14rpx;
  margin: -60rpx auto 0 auto;
}
.my-order .nav .item.data-v-09744212 {
  text-align: center;
  font-size: 26rpx;
  color: #282828;
  padding: 26rpx 0;
}
.my-order .nav .item.on.data-v-09744212 {
  font-weight: bold;
  position: relative;
}
.my-order .nav .item.on.data-v-09744212 ::after {
  content: '';
  width: 78rpx;
  height: 4rpx;
  background-color: theme;
  position: absolute;
  bottom: 2rpx;
  left: 0;
}
[data-theme="theme1"] .my-order .nav .item.on.data-v-09744212 ::after {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .my-order .nav .item.on.data-v-09744212 ::after {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .my-order .nav .item.on.data-v-09744212 ::after {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .my-order .nav .item.on.data-v-09744212 ::after {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .my-order .nav .item.on.data-v-09744212 ::after {
  background-color: #FF448F !important;
}
.my-order .nav .item .num.data-v-09744212 {
  margin-top: 18rpx;
}
.my-order .list.data-v-09744212 {
  width: 690rpx;
  margin: 14rpx auto 0 auto;
}
.my-order .list .item.data-v-09744212 {
  background-color: #fff;
  border-radius: 14rpx;
  margin-bottom: 14rpx;
}
.my-order .list .item .title.data-v-09744212 {
  height: 84rpx;
  padding: 0 24rpx;
  border-bottom: 1rpx solid #eee;
  font-size: 28rpx;
  color: #282828;
}
.my-order .list .item .title .sign.data-v-09744212 {
  font-size: 24rpx;
  padding: 0 13rpx;
  height: 36rpx;
  margin-right: 15rpx;
  border-radius: 18rpx;
  border: theme;
  color: theme;
}
[data-theme="theme1"] .my-order .list .item .title .sign.data-v-09744212 {
  border: 1px solid #e93323;
}
[data-theme="theme2"] .my-order .list .item .title .sign.data-v-09744212 {
  border: 1px solid #FE5C2D;
}
[data-theme="theme3"] .my-order .list .item .title .sign.data-v-09744212 {
  border: 1px solid #42CA4D;
}
[data-theme="theme4"] .my-order .list .item .title .sign.data-v-09744212 {
  border: 1px solid #1DB0FC;
}
[data-theme="theme5"] .my-order .list .item .title .sign.data-v-09744212 {
  border: 1px solid #FF448F;
}
[data-theme="theme1"] .my-order .list .item .title .sign.data-v-09744212 {
  color: #e93323 !important;
}
[data-theme="theme2"] .my-order .list .item .title .sign.data-v-09744212 {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .my-order .list .item .title .sign.data-v-09744212 {
  color: #42CA4D !important;
}
[data-theme="theme4"] .my-order .list .item .title .sign.data-v-09744212 {
  color: #1DB0FC !important;
}
[data-theme="theme5"] .my-order .list .item .title .sign.data-v-09744212 {
  color: #FF448F !important;
}
.my-order .list .item .item-info.data-v-09744212 {
  padding: 0 24rpx;
  margin-top: 22rpx;
}
.my-order .list .item .item-info .pictrue.data-v-09744212 {
  width: 120rpx;
  height: 120rpx;
}
.my-order .list .item .item-info .pictrue image.data-v-09744212 {
  width: 100%;
  height: 100%;
  border-radius: 14rpx;
}
.my-order .list .item .item-info .text.data-v-09744212 {
  width: 500rpx;
  font-size: 28rpx;
  color: #999;
}
.my-order .list .item .item-info .text .name.data-v-09744212 {
  width: 350rpx;
  color: #282828;
}
.my-order .list .item .item-info .text .money.data-v-09744212 {
  text-align: right;
}
.font_color.data-v-09744212 {
  color: theme;
}
[data-theme="theme1"] .font_color.data-v-09744212 {
  color: #e93323 !important;
}
[data-theme="theme2"] .font_color.data-v-09744212 {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .font_color.data-v-09744212 {
  color: #42CA4D !important;
}
[data-theme="theme4"] .font_color.data-v-09744212 {
  color: #1DB0FC !important;
}
[data-theme="theme5"] .font_color.data-v-09744212 {
  color: #FF448F !important;
}
.my-order .list .item .totalPrice.data-v-09744212 {
  font-size: 26rpx;
  color: #282828;
  text-align: right;
  margin: 27rpx 0 0 30rpx;
  padding: 0 30rpx 30rpx 0;
  border-bottom: 1rpx solid #eee;
}
.my-order .list .item .totalPrice .money.data-v-09744212 {
  font-size: 28rpx;
  font-weight: bold;
  color: theme;
}
[data-theme="theme1"] .my-order .list .item .totalPrice .money.data-v-09744212 {
  color: #F93323 !important;
}
[data-theme="theme2"] .my-order .list .item .totalPrice .money.data-v-09744212 {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .my-order .list .item .totalPrice .money.data-v-09744212 {
  color: #FF7600 !important;
}
[data-theme="theme4"] .my-order .list .item .totalPrice .money.data-v-09744212 {
  color: #FD502F !important;
}
[data-theme="theme5"] .my-order .list .item .totalPrice .money.data-v-09744212 {
  color: #FF448F !important;
}
.my-order .list .item .bottom.data-v-09744212 {
  height: 107rpx;
  padding: 0 30rpx;
}
.my-order .list .item .bottom .bnt.data-v-09744212 {
  width: 176rpx;
  height: 60rpx;
  text-align: center;
  line-height: 60rpx;
  color: #fff;
  border-radius: 50rpx;
  font-size: 27rpx;
}
.my-order .list .item .bottom .bnt.cancelBnt.data-v-09744212 {
  border: 1rpx solid #ddd;
  color: #aaa;
}
.my-order .list .item .bottom .bnt ~ .bnt.data-v-09744212 {
  margin-left: 17rpx;
}
.noCart.data-v-09744212 {
  margin-top: 171rpx;
  padding-top: 0.1rpx;
}
.noCart .pictrue.data-v-09744212 {
  width: 414rpx;
  height: 336rpx;
  margin: 78rpx auto 56rpx auto;
}
.noCart .pictrue image.data-v-09744212 {
  width: 100%;
  height: 100%;
}

