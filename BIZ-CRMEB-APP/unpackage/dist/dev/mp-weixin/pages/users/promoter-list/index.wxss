@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.promoter-list .nav.data-v-bfc5ae28 {
  background-color: #fff;
  height: 86rpx;
  line-height: 86rpx;
  font-size: 28rpx;
  color: #282828;
  border-bottom: 1rpx solid #eee;
  border-top-left-radius: 14rpx;
  border-top-right-radius: 14rpx;
  margin-top: -30rpx;
}
.promoterHeader.data-v-bfc5ae28 {
  background-color: theme;
}
[data-theme="theme1"] .promoterHeader.data-v-bfc5ae28 {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .promoterHeader.data-v-bfc5ae28 {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .promoterHeader.data-v-bfc5ae28 {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .promoterHeader.data-v-bfc5ae28 {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .promoterHeader.data-v-bfc5ae28 {
  background-color: #FF448F !important;
}
.promoter-list .nav .item.on.data-v-bfc5ae28 {
  color: theme;
  border-bottom: theme;
}
[data-theme="theme1"] .promoter-list .nav .item.on.data-v-bfc5ae28 {
  color: #e93323 !important;
}
[data-theme="theme2"] .promoter-list .nav .item.on.data-v-bfc5ae28 {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .promoter-list .nav .item.on.data-v-bfc5ae28 {
  color: #42CA4D !important;
}
[data-theme="theme4"] .promoter-list .nav .item.on.data-v-bfc5ae28 {
  color: #1DB0FC !important;
}
[data-theme="theme5"] .promoter-list .nav .item.on.data-v-bfc5ae28 {
  color: #FF448F !important;
}
[data-theme="theme1"] .promoter-list .nav .item.on.data-v-bfc5ae28 {
  border-bottom: 4rpx solid #e93323 !important;
}
[data-theme="theme2"] .promoter-list .nav .item.on.data-v-bfc5ae28 {
  border-bottom: 4rpx solid #FE5C2D !important;
}
[data-theme="theme3"] .promoter-list .nav .item.on.data-v-bfc5ae28 {
  border-bottom: 4rpx solid #42CA4D !important;
}
[data-theme="theme4"] .promoter-list .nav .item.on.data-v-bfc5ae28 {
  border-bottom: 4rpx solid #1DB0FC !important;
}
[data-theme="theme5"] .promoter-list .nav .item.on.data-v-bfc5ae28 {
  border-bottom: 4rpx solid #FF448F !important;
}
.promoter-list .search.data-v-bfc5ae28 {
  width: 100%;
  background-color: #fff;
  height: 100rpx;
  padding: 0 24rpx;
  box-sizing: border-box;
  border-bottom-left-radius: 14rpx;
  border-bottom-right-radius: 14rpx;
}
.promoter-list .search .input.data-v-bfc5ae28 {
  width: 592rpx;
  height: 60rpx;
  border-radius: 50rpx;
  background-color: #f5f5f5;
  text-align: center;
  position: relative;
}
.promoter-list .search .input input.data-v-bfc5ae28 {
  height: 100%;
  font-size: 26rpx;
  width: 610rpx;
  text-align: center;
}
.promoter-list .search .input .placeholder.data-v-bfc5ae28 {
  color: #bbb;
}
.promoter-list .search .input .iconfont.data-v-bfc5ae28 {
  position: absolute;
  right: 28rpx;
  color: #999;
  font-size: 28rpx;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.promoter-list .search .iconfont.data-v-bfc5ae28 {
  font-size: 32rpx;
  color: #515151;
  height: 60rpx;
  line-height: 60rpx;
}
.promoter-list .list.data-v-bfc5ae28 {
  margin-top: 20rpx;
}
.promoter-list .list .sortNav.data-v-bfc5ae28 {
  background-color: #fff;
  height: 76rpx;
  border-bottom: 1rpx solid #eee;
  color: #333;
  font-size: 28rpx;
  border-top-left-radius: 14rpx;
  border-top-right-radius: 14rpx;
}
.promoter-list .list .sortNav .sortItem.data-v-bfc5ae28 {
  text-align: center;
  flex: 1;
}
.promoter-list .list .sortNav .sortItem image.data-v-bfc5ae28 {
  width: 24rpx;
  height: 24rpx;
  margin-left: 6rpx;
  vertical-align: -3rpx;
}
.promoter-list .list .item.data-v-bfc5ae28 {
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  height: 152rpx;
  padding: 0 24rpx;
  font-size: 24rpx;
  color: #666;
}
.promoter-list .list .item .picTxt .pictrue.data-v-bfc5ae28 {
  width: 106rpx;
  height: 106rpx;
  border-radius: 50%;
}
.promoter-list .list .item .picTxt .pictrue image.data-v-bfc5ae28 {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 3rpx solid #fff;
  box-shadow: 0 0 10rpx #aaa;
  box-sizing: border-box;
}
.promoter-list .list .item .picTxt .text.data-v-bfc5ae28 {
  font-size: 24rpx;
  color: #666;
  margin-left: 14rpx;
}
.promoter-list .list .item .picTxt .text .name.data-v-bfc5ae28 {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 13rpx;
}
.promoter-list .list .item .right.data-v-bfc5ae28 {
  text-align: right;
  font-size: 22rpx;
  color: #333;
}
.promoter-list .list .item .right .num.data-v-bfc5ae28 {
  margin-right: 7rpx;
}

