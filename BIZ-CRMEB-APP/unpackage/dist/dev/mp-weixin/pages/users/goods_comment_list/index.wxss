@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  background-color: #fff;
  height: 100%;
}
.font_color {
  color: theme;
}
[data-theme="theme1"] .font_color {
  color: #e93323 !important;
}
[data-theme="theme2"] .font_color {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .font_color {
  color: #42CA4D !important;
}
[data-theme="theme4"] .font_color {
  color: #1DB0FC !important;
}
[data-theme="theme5"] .font_color {
  color: #FF448F !important;
}
.evaluate-list {
  padding: 30rpx 0 0 0;
  background-color: #fff;
}
.evaluate-list .generalComment {
  padding: 0 30rpx;
  margin-top: 1rpx;
  background-color: #fff;
  font-size: 28rpx;
  color: #808080;
}
.evaluate-list .generalComment .evaluate {
  margin-right: 7rpx;
  color: #333333;
  font-size: 28rpx;
}
.evaluate-list .nav {
  font-size: 24rpx;
  color: #282828;
  padding: 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f5f5f5;
}
.evaluate-list .nav .item {
  font-size: 24rpx;
  color: #282828;
  border-radius: 27rpx;
  height: 54rpx;
  padding: 0 20rpx;
  background-color: #f4f4f4;
  line-height: 54rpx;
  margin-right: 17rpx;
}
.evaluate-list .nav .item.bg-color {
  color: #fff;
  background-color: theme;
}
[data-theme="theme1"] .evaluate-list .nav .item.bg-color {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .evaluate-list .nav .item.bg-color {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .evaluate-list .nav .item.bg-color {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .evaluate-list .nav .item.bg-color {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .evaluate-list .nav .item.bg-color {
  background-color: #FF448F !important;
}

