@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-5d8acc68 {
  height: 100vh;
  overflow: auto;
  background-color: #A2A2A2 !important;
}
.canvas.data-v-5d8acc68 {
  position: relative;
}
.distribution-posters.data-v-5d8acc68 {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.distribution-posters swiper.data-v-5d8acc68 {
  width: 100%;
  height: 1000rpx;
  position: relative;
  margin-top: 40rpx;
}
.distribution-posters .slide-image.data-v-5d8acc68 {
  width: 100%;
  height: 100%;
  margin: 0 auto;
  border-radius: 15rpx;
}
.distribution-posters .slide-image.active.data-v-5d8acc68 {
  -webkit-transform: none;
          transform: none;
  transition: all 0.2s ease-in 0s;
}
.distribution-posters .slide-image.quiet.data-v-5d8acc68 {
  -webkit-transform: scale(0.83333);
          transform: scale(0.83333);
  transition: all 0.2s ease-in 0s;
}
.distribution-posters .keep.data-v-5d8acc68 {
  font-size: 30rpx;
  color: #fff;
  width: 600rpx;
  height: 80rpx;
  border-radius: 50rpx;
  text-align: center;
  line-height: 80rpx;
  margin: 38rpx auto;
}
.distribution-posters .preserve.data-v-5d8acc68 {
  color: #fff;
  text-align: center;
  margin-top: 38rpx;
}
.distribution-posters .preserve .line.data-v-5d8acc68 {
  width: 100rpx;
  height: 1px;
  background-color: #fff;
}
.distribution-posters .preserve .tip.data-v-5d8acc68 {
  margin: 0 30rpx;
}

