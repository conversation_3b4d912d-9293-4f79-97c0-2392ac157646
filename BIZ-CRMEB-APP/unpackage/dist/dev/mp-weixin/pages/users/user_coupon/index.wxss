@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.navbar.data-v-fe8ae026 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 106rpx;
  background-color: #FFFFFF;
  z-index: 9;
}
.navbar .item.data-v-fe8ae026 {
  border-top: 5rpx solid transparent;
  border-bottom: 5rpx solid transparent;
  font-size: 30rpx;
  color: #999999;
}
.navbar .item.on.data-v-fe8ae026 {
  border-bottom: theme;
  color: theme;
}
[data-theme="theme1"] .navbar .item.on.data-v-fe8ae026 {
  border-bottom: 4rpx solid #e93323 !important;
}
[data-theme="theme2"] .navbar .item.on.data-v-fe8ae026 {
  border-bottom: 4rpx solid #FE5C2D !important;
}
[data-theme="theme3"] .navbar .item.on.data-v-fe8ae026 {
  border-bottom: 4rpx solid #42CA4D !important;
}
[data-theme="theme4"] .navbar .item.on.data-v-fe8ae026 {
  border-bottom: 4rpx solid #1DB0FC !important;
}
[data-theme="theme5"] .navbar .item.on.data-v-fe8ae026 {
  border-bottom: 4rpx solid #FF448F !important;
}
[data-theme="theme1"] .navbar .item.on.data-v-fe8ae026 {
  color: #e93323 !important;
}
[data-theme="theme2"] .navbar .item.on.data-v-fe8ae026 {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .navbar .item.on.data-v-fe8ae026 {
  color: #42CA4D !important;
}
[data-theme="theme4"] .navbar .item.on.data-v-fe8ae026 {
  color: #1DB0FC !important;
}
[data-theme="theme5"] .navbar .item.on.data-v-fe8ae026 {
  color: #FF448F !important;
}
.money.data-v-fe8ae026 {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.bg_color.data-v-fe8ae026 {
  background-color: theme;
}
[data-theme="theme1"] .bg_color.data-v-fe8ae026 {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .bg_color.data-v-fe8ae026 {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .bg_color.data-v-fe8ae026 {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .bg_color.data-v-fe8ae026 {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .bg_color.data-v-fe8ae026 {
  background-color: #FF448F !important;
}
.pic-num.data-v-fe8ae026 {
  color: #ffffff;
  font-size: 24rpx;
}
.coupon-list.data-v-fe8ae026 {
  margin-top: 122rpx;
}
.coupon-list .item .text.data-v-fe8ae026 {
  height: 100%;
}
.coupon-list .item .text .condition.data-v-fe8ae026 {
  /* display: flex;
	align-items: center; */
}
.condition .line-title.data-v-fe8ae026 {
  width: 90rpx;
  height: 40rpx !important;
  line-height: 40rpx !important;
  padding: 2rpx 10rpx;
  box-sizing: border-box;
  border: theme;
  opacity: 1;
  border-radius: 20rpx;
  font-size: 18rpx !important;
  color: theme;
  margin-right: 12rpx;
}
[data-theme="theme1"] .condition .line-title.data-v-fe8ae026 {
  border: 1px solid #e93323;
}
[data-theme="theme2"] .condition .line-title.data-v-fe8ae026 {
  border: 1px solid #FE5C2D;
}
[data-theme="theme3"] .condition .line-title.data-v-fe8ae026 {
  border: 1px solid #42CA4D;
}
[data-theme="theme4"] .condition .line-title.data-v-fe8ae026 {
  border: 1px solid #1DB0FC;
}
[data-theme="theme5"] .condition .line-title.data-v-fe8ae026 {
  border: 1px solid #FF448F;
}
[data-theme="theme1"] .condition .line-title.data-v-fe8ae026 {
  color: #e93323 !important;
}
[data-theme="theme2"] .condition .line-title.data-v-fe8ae026 {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .condition .line-title.data-v-fe8ae026 {
  color: #42CA4D !important;
}
[data-theme="theme4"] .condition .line-title.data-v-fe8ae026 {
  color: #1DB0FC !important;
}
[data-theme="theme5"] .condition .line-title.data-v-fe8ae026 {
  color: #FF448F !important;
}
.noCommodity.data-v-fe8ae026 {
  margin-top: 300rpx;
}
.main_bg.data-v-fe8ae026 {
  background-color: theme;
}
[data-theme="theme1"] .main_bg.data-v-fe8ae026 {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .main_bg.data-v-fe8ae026 {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .main_bg.data-v-fe8ae026 {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .main_bg.data-v-fe8ae026 {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .main_bg.data-v-fe8ae026 {
  background-color: #FF448F !important;
}

