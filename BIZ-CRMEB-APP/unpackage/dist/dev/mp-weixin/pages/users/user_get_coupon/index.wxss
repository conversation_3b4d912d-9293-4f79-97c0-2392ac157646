@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.nav.data-v-67877894 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 106rpx;
  background-color: #FFFFFF;
  font-size: 30rpx;
  color: #999999;
  z-index: 9;
}
.nav .acea-row.data-v-67877894 {
  border-top: 5rpx solid transparent;
  border-bottom: 5rpx solid transparent;
  cursor: pointer;
}
.nav .acea-row.on.data-v-67877894 {
  border-bottom: theme;
  color: theme;
}
[data-theme="theme1"] .nav .acea-row.on.data-v-67877894 {
  border-bottom: 4rpx solid #e93323 !important;
}
[data-theme="theme2"] .nav .acea-row.on.data-v-67877894 {
  border-bottom: 4rpx solid #FE5C2D !important;
}
[data-theme="theme3"] .nav .acea-row.on.data-v-67877894 {
  border-bottom: 4rpx solid #42CA4D !important;
}
[data-theme="theme4"] .nav .acea-row.on.data-v-67877894 {
  border-bottom: 4rpx solid #1DB0FC !important;
}
[data-theme="theme5"] .nav .acea-row.on.data-v-67877894 {
  border-bottom: 4rpx solid #FF448F !important;
}
[data-theme="theme1"] .nav .acea-row.on.data-v-67877894 {
  color: #e93323 !important;
}
[data-theme="theme2"] .nav .acea-row.on.data-v-67877894 {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .nav .acea-row.on.data-v-67877894 {
  color: #42CA4D !important;
}
[data-theme="theme4"] .nav .acea-row.on.data-v-67877894 {
  color: #1DB0FC !important;
}
[data-theme="theme5"] .nav .acea-row.on.data-v-67877894 {
  color: #FF448F !important;
}
.condition .line-title.data-v-67877894 {
  width: 90rpx;
  padding: 0 10rpx;
  box-sizing: border-box;
  background: #fff;
  opacity: 1;
  border-radius: 20rpx;
  font-size: 20rpx;
  margin-right: 12rpx;
}
.condition .line-title.gray.data-v-67877894 {
  border: 1px solid #BBB;
  color: #bbb;
  background-color: #F5F5F5;
}
.coupon-list .pic-num.data-v-67877894 {
  color: #FFFFFF;
  font-size: 24rpx;
}
.main_bg.data-v-67877894 {
  background-color: theme;
}
[data-theme="theme1"] .main_bg.data-v-67877894 {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .main_bg.data-v-67877894 {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .main_bg.data-v-67877894 {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .main_bg.data-v-67877894 {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .main_bg.data-v-67877894 {
  background-color: #FF448F !important;
}
.select.data-v-67877894 {
  color: theme;
  border: theme;
}
[data-theme="theme1"] .select.data-v-67877894 {
  color: #e93323 !important;
}
[data-theme="theme2"] .select.data-v-67877894 {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .select.data-v-67877894 {
  color: #42CA4D !important;
}
[data-theme="theme4"] .select.data-v-67877894 {
  color: #1DB0FC !important;
}
[data-theme="theme5"] .select.data-v-67877894 {
  color: #FF448F !important;
}
[data-theme="theme1"] .select.data-v-67877894 {
  border: 1px solid #e93323;
}
[data-theme="theme2"] .select.data-v-67877894 {
  border: 1px solid #FE5C2D;
}
[data-theme="theme3"] .select.data-v-67877894 {
  border: 1px solid #42CA4D;
}
[data-theme="theme4"] .select.data-v-67877894 {
  border: 1px solid #1DB0FC;
}
[data-theme="theme5"] .select.data-v-67877894 {
  border: 1px solid #FF448F;
}

