@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  background: #fff;
  height: 100%;
}
.page {
  background: #fff;
  height: 100%;
}
.wechat_login {
  padding: 72rpx 34rpx;
}
.wechat_login .img image {
  width: 100%;
}
.wechat_login .btn-wrapper {
  margin-top: 86rpx;
  padding: 0 66rpx;
}
.wechat_login .btn-wrapper button {
  width: 100%;
  height: 86rpx;
  line-height: 86rpx;
  margin-bottom: 40rpx;
  border-radius: 120rpx;
  font-size: 30rpx;
}
.wechat_login .btn-wrapper button.btn1 {
  color: #fff;
}
.wechat_login .btn-wrapper button.btn2 {
  color: #666666;
  border: 1px solid #666666;
}
.title-bar {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
}
.icon {
  position: absolute;
  left: 30rpx;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 86rpx;
  height: 86rpx;
}
.icon image {
  width: 50rpx;
  height: 50rpx;
}

