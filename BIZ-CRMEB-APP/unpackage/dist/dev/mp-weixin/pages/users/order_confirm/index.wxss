@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.font_color.data-v-064d693c {
  color: theme;
}
[data-theme="theme1"] .font_color.data-v-064d693c {
  color: #e93323 !important;
}
[data-theme="theme2"] .font_color.data-v-064d693c {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .font_color.data-v-064d693c {
  color: #42CA4D !important;
}
[data-theme="theme4"] .font_color.data-v-064d693c {
  color: #1DB0FC !important;
}
[data-theme="theme5"] .font_color.data-v-064d693c {
  color: #FF448F !important;
}
.price_color.data-v-064d693c {
  color: theme;
}
[data-theme="theme1"] .price_color.data-v-064d693c {
  color: #F93323 !important;
}
[data-theme="theme2"] .price_color.data-v-064d693c {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .price_color.data-v-064d693c {
  color: #FF7600 !important;
}
[data-theme="theme4"] .price_color.data-v-064d693c {
  color: #FD502F !important;
}
[data-theme="theme5"] .price_color.data-v-064d693c {
  color: #FF448F !important;
}
.line2.data-v-064d693c {
  width: 504rpx;
}
.textR.data-v-064d693c {
  text-align: right;
}
.order-submission .line.data-v-064d693c {
  width: 100%;
  height: 3rpx;
}
.order-submission .line image.data-v-064d693c {
  width: 100%;
  height: 100%;
  display: block;
}
.order-submission .address.data-v-064d693c {
  padding: 28rpx;
  background-color: #fff;
  box-sizing: border-box;
}
.order-submission .address .addressCon.data-v-064d693c {
  width: 596rpx;
  font-size: 26rpx;
  color: #666;
}
.order-submission .address .addressCon .name.data-v-064d693c {
  font-size: 30rpx;
  color: #282828;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.order-submission .address .addressCon .name .phone.data-v-064d693c {
  margin-left: 50rpx;
}
.order-submission .address .addressCon .default.data-v-064d693c {
  margin-right: 12rpx;
}
.order-submission .address .addressCon .setaddress.data-v-064d693c {
  color: #333;
  font-size: 28rpx;
}
.order-submission .address .iconfont.data-v-064d693c {
  font-size: 35rpx;
  color: #707070;
}
.order-submission .allAddress.data-v-064d693c {
  width: 100%;
  background: theme;
  padding: 83rpx 30rpx 0 30rpx;
}
[data-theme="theme1"] .order-submission .allAddress.data-v-064d693c {
  background: -webkit-linear-gradient(270deg, #E93323 0%, #F5F5F5 100%);
}
[data-theme="theme2"] .order-submission .allAddress.data-v-064d693c {
  background: -webkit-linear-gradient(270deg, #FE5C2D 0%, #F5F5F5 100%);
}
[data-theme="theme3"] .order-submission .allAddress.data-v-064d693c {
  background: -webkit-linear-gradient(270deg, #42CA4D 0%, #F5F5F5 100%);
}
[data-theme="theme4"] .order-submission .allAddress.data-v-064d693c {
  background: -webkit-linear-gradient(270deg, #1DB0FC 0%, #F5F5F5 100%);
}
[data-theme="theme5"] .order-submission .allAddress.data-v-064d693c {
  background: -webkit-linear-gradient(270deg, #FF448F 0%, #F5F5F5 100%);
}
.order-submission .allAddress .nav.data-v-064d693c {
  width: 690rpx;
  margin: 0 auto;
}
.order-submission .allAddress .nav .item.data-v-064d693c {
  width: 334rpx;
}
.order-submission .allAddress .nav .item.on.data-v-064d693c {
  position: relative;
  width: 230rpx;
}
.order-submission .allAddress .nav .item.on.data-v-064d693c::before {
  position: absolute;
  bottom: 0;
  content: "快递配送";
  font-size: 28rpx;
  display: block;
  height: 0;
  width: 336rpx;
  border-width: 0 20rpx 80rpx 0;
  border-style: none solid solid;
  border-color: transparent transparent #fff;
  z-index: 2;
  border-radius: 14rpx 36rpx 0 0;
  text-align: center;
  line-height: 80rpx;
}
.order-submission .allAddress .nav .item:nth-of-type(2).on.data-v-064d693c::before {
  content: "到店自提";
  border-width: 0 0 80rpx 20rpx;
  border-radius: 36rpx 14rpx 0 0;
}
.order-submission .allAddress .nav .item.on2.data-v-064d693c {
  position: relative;
}
.order-submission .allAddress .nav .item.on2.data-v-064d693c::before {
  position: absolute;
  bottom: 0;
  content: "到店自提";
  font-size: 28rpx;
  display: block;
  height: 0;
  width: 401rpx;
  border-width: 0 0 60rpx 60rpx;
  border-style: none solid solid;
  border-color: transparent transparent rgba(255, 255, 255, 0.6);
  border-radius: 36rpx 14rpx 0 0;
  text-align: center;
  line-height: 60rpx;
}
.order-submission .allAddress .nav .item:nth-of-type(1).on2.data-v-064d693c::before {
  content: "快递配送";
  border-width: 0 60rpx 60rpx 0;
  border-radius: 14rpx 36rpx 0 0;
}
.order-submission .allAddress .address.data-v-064d693c {
  width: 690rpx;
  max-height: 180rpx;
  margin: -2rpx auto 0 auto;
}
.order-submission .allAddress .line.data-v-064d693c {
  width: 100%;
  margin: 0 auto;
}
.order-submission .wrapper .item .discount .placeholder.data-v-064d693c {
  color: #ccc;
}
.order-submission .wrapper.data-v-064d693c {
  background-color: #fff;
  margin-top: 15rpx;
}
.order-submission .wrapper .item.data-v-064d693c {
  padding: 27rpx 24rpx;
  font-size: 30rpx;
  color: #333333;
  border-bottom: 1px solid #F5F5F5;
}
.order-submission .wrapper .item .discount.data-v-064d693c {
  font-size: 30rpx;
  color: #333;
}
.order-submission .wrapper .item .discount1.data-v-064d693c {
  display: flex;
  font-size: 30rpx;
  color: #333;
}
.order-submission .wrapper .item .reduce.data-v-064d693c {
  display: flex;
  font-size: 30rpx;
  border-color: #e3e3e3;
  color: #dedede;
}
.order-submission .wrapper .item .discount .iconfont.data-v-064d693c {
  color: #515151;
  font-size: 30rpx;
  margin-left: 15rpx;
}
.order-submission .wrapper .item .discount .num.data-v-064d693c {
  font-size: 32rpx;
  margin-right: 20rpx;
}
.order-submission .wrapper .item .shipping.data-v-064d693c {
  font-size: 30rpx;
  color: #999;
  position: relative;
  padding-right: 58rpx;
}
.order-submission .wrapper .item .shipping .iconfont.data-v-064d693c {
  font-size: 35rpx;
  color: #707070;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  margin-left: 30rpx;
}
.order-submission .wrapper .item textarea.data-v-064d693c {
  background-color: #f9f9f9;
  width: auto !important;
  height: 140rpx;
  border-radius: 14rpx;
  margin-top: 30rpx;
  padding: 15rpx;
  box-sizing: border-box;
  font-weight: 400;
}
.order-submission .wrapper .item .placeholder.data-v-064d693c {
  color: #ccc;
}
.order-submission .wrapper .item .list.data-v-064d693c {
  margin-top: 35rpx;
}
.order-submission .wrapper .item .list .payItem.data-v-064d693c {
  border: 1px solid #eee;
  border-radius: 14rpx;
  height: 86rpx;
  width: 100%;
  box-sizing: border-box;
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #282828;
}
.order-submission .wrapper .item .list .payItem.on.data-v-064d693c {
  border: theme;
  color: #E93323;
}
[data-theme="theme1"] .order-submission .wrapper .item .list .payItem.on.data-v-064d693c {
  border: 1px solid #e93323;
}
[data-theme="theme2"] .order-submission .wrapper .item .list .payItem.on.data-v-064d693c {
  border: 1px solid #FE5C2D;
}
[data-theme="theme3"] .order-submission .wrapper .item .list .payItem.on.data-v-064d693c {
  border: 1px solid #42CA4D;
}
[data-theme="theme4"] .order-submission .wrapper .item .list .payItem.on.data-v-064d693c {
  border: 1px solid #1DB0FC;
}
[data-theme="theme5"] .order-submission .wrapper .item .list .payItem.on.data-v-064d693c {
  border: 1px solid #FF448F;
}
.order-submission .wrapper .item .list .payItem .name.data-v-064d693c {
  width: 50%;
  text-align: center;
  border-right: 1px solid #eee;
}
.order-submission .wrapper .item .list .payItem .name .iconfont.data-v-064d693c {
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  text-align: center;
  line-height: 44rpx;
  background-color: #fe960f;
  color: #fff;
  font-size: 30rpx;
  margin-right: 15rpx;
}
.order-submission .wrapper .item .list .payItem .name .iconfont.icon-weixin2.data-v-064d693c {
  background-color: #41b035;
}
.order-submission .wrapper .item .list .payItem .name .iconfont.icon-zhifubao.data-v-064d693c {
  background-color: #00AAEA;
}
.order-submission .wrapper .item .list .payItem .tip.data-v-064d693c {
  width: 49%;
  text-align: center;
  font-size: 26rpx;
  color: #aaa;
}
.order-submission .moneyList.data-v-064d693c {
  margin-top: 15rpx;
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: calc(constant(safe-area-inset-bottom));
  margin-bottom: calc(env(safe-area-inset-bottom));
}
.order-submission .moneyList .item.data-v-064d693c {
  font-size: 28rpx;
  color: #282828;
}
.order-submission .moneyList .item ~ .item.data-v-064d693c {
  margin-top: 20rpx;
}
.order-submission .moneyList .item .money.data-v-064d693c {
  color: #666666;
}
.order-submission .footer.data-v-064d693c {
  width: 100%;
  height: 100rpx;
  background-color: #fff;
  padding: 0 30rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  position: fixed;
  bottom: 0;
  left: 0;
  height: calc(100rpx+ constant(safe-area-inset-bottom));
  height: calc(100rpx + env(safe-area-inset-bottom));
}
.order-submission .footer .settlement.data-v-064d693c {
  font-size: 30rpx;
  color: #fff;
  width: 240rpx;
  height: 70rpx;
  background-color: theme;
  border-radius: 50rpx;
  text-align: center;
  line-height: 70rpx;
}
[data-theme="theme1"] .order-submission .footer .settlement.data-v-064d693c {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .order-submission .footer .settlement.data-v-064d693c {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .order-submission .footer .settlement.data-v-064d693c {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .order-submission .footer .settlement.data-v-064d693c {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .order-submission .footer .settlement.data-v-064d693c {
  background-color: #FF448F !important;
}
.footer .transparent.data-v-064d693c {
  opacity: 0;
}
.data-v-064d693c checkbox .uni-checkbox-input.uni-checkbox-input-checked {
  background-color: theme;
  border: none !important;
  color: #fff !important;
}
[data-theme="theme1"].data-v-064d693c checkbox .uni-checkbox-input.uni-checkbox-input-checked {
  background-color: #e93323 !important;
}
[data-theme="theme2"].data-v-064d693c checkbox .uni-checkbox-input.uni-checkbox-input-checked {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"].data-v-064d693c checkbox .uni-checkbox-input.uni-checkbox-input-checked {
  background-color: #42CA4D !important;
}
[data-theme="theme4"].data-v-064d693c checkbox .uni-checkbox-input.uni-checkbox-input-checked {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"].data-v-064d693c checkbox .uni-checkbox-input.uni-checkbox-input-checked {
  background-color: #FF448F !important;
}
.data-v-064d693c checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  background-color: theme;
  border: none !important;
  color: #fff !important;
  margin-right: 0 !important;
}
[data-theme="theme1"].data-v-064d693c checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  background-color: #e93323 !important;
}
[data-theme="theme2"].data-v-064d693c checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"].data-v-064d693c checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  background-color: #42CA4D !important;
}
[data-theme="theme4"].data-v-064d693c checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"].data-v-064d693c checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  background-color: #FF448F !important;
}

