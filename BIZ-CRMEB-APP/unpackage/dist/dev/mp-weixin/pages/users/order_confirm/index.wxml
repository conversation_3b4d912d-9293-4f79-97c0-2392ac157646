<view data-theme="{{theme}}" class="data-v-064d693c"><view class="cart_nav data-v-064d693c"><nav-bar vue-id="c35ebc38-1" navTitle="{{navTitle}}" data-event-opts="{{[['^getNavH',[['getNavH']]]]}}" bind:getNavH="__e" class="data-v-064d693c" bind:__l="__l"></nav-bar></view><view class="order-submission data-v-064d693c" style="{{('margin-top:'+marTop+'rpx;')}}"><view class="allAddress data-v-064d693c" style="{{(store_self_mention?'':'padding-top:0;')}}"><view class="nav acea-row data-v-064d693c"><block wx:if="{{store_self_mention}}"><view data-event-opts="{{[['tap',[['addressType',[0]]]]]}}" class="{{['item','font_color','data-v-064d693c',shippingType==0?'on':'on2']}}" bindtap="__e"></view></block><block wx:if="{{store_self_mention}}"><view data-event-opts="{{[['tap',[['addressType',[1]]]]]}}" class="{{['item','font_color','data-v-064d693c',shippingType==1?'on':'on2']}}" bindtap="__e"></view></block></view><block wx:if="{{shippingType==0}}"><view data-event-opts="{{[['tap',[['onAddress',['$event']]]]]}}" class="address acea-row row-between-wrapper data-v-064d693c" style="{{(store_self_mention?'':'border-top-left-radius: 14rpx;border-top-right-radius: 14rpx;')}}" bindtap="__e"><block wx:if="{{addressInfo.detail}}"><view class="addressCon data-v-064d693c"><view class="name data-v-064d693c">{{addressInfo.realName+''}}<text class="phone data-v-064d693c">{{addressInfo.phone}}</text></view><view class="acea-row data-v-064d693c"><block wx:if="{{addressInfo.isDefault}}"><text class="default font_color data-v-064d693c">[默认]</text></block><text class="line2 data-v-064d693c">{{addressInfo.province+addressInfo.city+addressInfo.district+addressInfo.detail}}</text></view></view></block><block wx:else><view class="addressCon data-v-064d693c"><view class="setaddress data-v-064d693c">设置收货地址</view></view></block><view class="iconfont icon-jiantou data-v-064d693c"></view></view></block><block wx:else><view data-event-opts="{{[['tap',[['showStoreList',['$event']]]]]}}" class="address acea-row row-between-wrapper data-v-064d693c" bindtap="__e"><block wx:if="{{$root.g0>0}}"><block class="data-v-064d693c"><view class="addressCon data-v-064d693c"><view class="name data-v-064d693c">{{system_store.name+''}}<text class="phone data-v-064d693c">{{system_store.phone}}</text></view><view class="line1 data-v-064d693c">{{''+system_store.address+(", "+system_store.detailedAddress)+''}}</view></view><view class="iconfont icon-jiantou data-v-064d693c"></view></block></block><block wx:else><block class="data-v-064d693c"><view class="data-v-064d693c">暂无门店信息</view></block></block></view></block><view class="line data-v-064d693c"><image src="/static/images/line.jpg" class="data-v-064d693c"></image></view></view><view class="pad30 data-v-064d693c"><order-goods vue-id="c35ebc38-2" cartInfo="{{cartInfo}}" orderProNum="{{orderProNum}}" class="data-v-064d693c" bind:__l="__l"></order-goods><view class="wrapper borRadius14 data-v-064d693c"><block wx:if="{{shippingType==0}}"><view class="item data-v-064d693c"><view class="flex justify-between data-v-064d693c"><view class="data-v-064d693c">地址识别</view></view><block wx:if="{{textareaStatus}}"><textarea placeholder-class="placeholder" value="" name="addressRecognition" placeholder="粘贴地址智能识别收货地址" data-event-opts="{{[['blur',[['getAddressRecognition',['$event']]]]]}}" bindblur="__e" class="data-v-064d693c"></textarea></block></view></block><block wx:if="{{shippingType==0}}"><view class="item data-v-064d693c"><view class="data-v-064d693c">寄件信息</view><block wx:if="{{coupon.coupon===false}}"><textarea placeholder-class="placeholder" value="" name="sendAddress" placeholder="请粘贴寄件信息(300字内)" data-event-opts="{{[['input',[['bindHideKeyAddressinfo1',['$event']]]]]}}" bindinput="__e" class="data-v-064d693c"></textarea></block></view></block><block wx:if="{{textareaStatus}}"><view class="item data-v-064d693c"><view class="flex justify-between data-v-064d693c"><view class="data-v-064d693c">备注信息</view><view class="data-v-064d693c"><text style="color:#666;" class="data-v-064d693c">{{markNum?markNum:150}}</text>/<text class="data-v-064d693c">150</text></view></view><block wx:if="{{coupon.coupon===false}}"><textarea placeholder-class="placeholder" value="" name="mark" placeholder="请添加备注（150字以内）" data-event-opts="{{[['input',[['bindHideKeyboard',['$event']]]]]}}" bindinput="__e" class="data-v-064d693c"></textarea></block></view></block></view><view class="wrapper borRadius14 data-v-064d693c"><view class="item data-v-064d693c"><view class="data-v-064d693c">支付方式</view><view class="list data-v-064d693c"><block wx:for="{{cartArr}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{item.payStatus==1}}"><view data-event-opts="{{[['tap',[['payItem',[index]]]]]}}" class="{{['payItem','acea-row','row-middle','data-v-064d693c',active==index?'on':'']}}" bindtap="__e"><view class="name acea-row row-center-wrapper data-v-064d693c"><view class="{{['iconfont','animated','data-v-064d693c',item.icon+' '+(animated==true&&active==index?'bounceIn':'')]}}"></view>{{''+item.name+''}}</view><view class="tip data-v-064d693c">{{item.title}}</view></view></block></block></view></view></view><view class="moneyList borRadius14 data-v-064d693c"><view class="item acea-row row-between-wrapper data-v-064d693c"><view class="data-v-064d693c">商品总价：</view><view class="money data-v-064d693c">{{"￥"+(orderInfoVo.proTotalFee||0)}}</view></view><block wx:if="{{orderInfoVo.couponFee>0}}"><view class="item acea-row row-between-wrapper data-v-064d693c"><view class="data-v-064d693c">优惠券抵扣：</view><view class="money data-v-064d693c">{{"-￥"+orderInfoVo.couponFee}}</view></view></block><block wx:if="{{orderInfoVo.deductionPrice>0}}"><view class="item acea-row row-between-wrapper data-v-064d693c"><view class="data-v-064d693c">积分抵扣：</view><view class="money data-v-064d693c">{{"-￥"+orderInfoVo.deductionPrice}}</view></view></block><block wx:if="{{orderInfoVo.freightFee>0}}"><view class="item acea-row row-between-wrapper data-v-064d693c"><view class="data-v-064d693c">运费：</view><view class="money data-v-064d693c">{{"+￥"+orderInfoVo.freightFee}}</view></view></block></view><view style="height:120rpx;" class="data-v-064d693c"></view></view><view class="footer acea-row row-between-wrapper data-v-064d693c"><view class="data-v-064d693c">合计:<text class="price_color data-v-064d693c">{{"￥"+(orderInfoVo.payFee||0)}}</text></view><view data-event-opts="{{[['tap',[['SubOrder',['$event']]]]]}}" class="settlement data-v-064d693c" style="z-index:100;" bindtap="__e">立即结算</view></view></view><view class="alipaysubmit data-v-064d693c"><rich-text nodes="{{formContent}}"></rich-text></view><coupon-list-window vue-id="c35ebc38-3" coupon="{{coupon}}" openType="{{openType}}" orderShow="{{orderShow}}" data-event-opts="{{[['^ChangCouponsClone',[['ChangCouponsClone']]],['^ChangCoupons',[['ChangCoupons']]]]}}" bind:ChangCouponsClone="__e" bind:ChangCoupons="__e" class="data-v-064d693c" bind:__l="__l"></coupon-list-window><address-window vue-id="c35ebc38-4" address="{{address}}" pagesUrl="{{pagesUrl}}" data-ref="addressWindow" data-event-opts="{{[['^changeTextareaStatus',[['changeTextareaStatus']]],['^OnDefaultAddress',[['OnDefaultAddress']]],['^OnChangeAddress',[['OnChangeAddress']]],['^changeClose',[['changeClose']]]]}}" bind:changeTextareaStatus="__e" bind:OnDefaultAddress="__e" bind:OnChangeAddress="__e" bind:changeClose="__e" class="data-v-064d693c vue-ref" bind:__l="__l"></address-window></view>