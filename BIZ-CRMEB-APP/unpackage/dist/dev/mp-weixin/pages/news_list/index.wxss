@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  background-color: #fff !important;
}
.newsList .swiper {
  width: 100%;
  position: relative;
  box-sizing: border-box;
  padding: 0 30rpx;
}
.newsList .swiper swiper {
  width: 100%;
  height: 365rpx;
  position: relative;
}
.newsList .swiper .slide-image {
  width: 100%;
  height: 335rpx;
  border-radius: 14rpx;
}
.newsList .swiper .wx-swiper-dot {
  width: 12rpx !important;
  height: 12rpx !important;
  border-radius: 0;
  -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
  -webkit-transform-origin: 0 100%;
          transform-origin: 0 100%;
}
.newsList .swiper .wx-swiper-dot ~ .wx-swiper-dot {
  margin-left: 5rpx;
}
.newsList .swiper .wx-swiper-dots.wx-swiper-dots-horizontal {
  margin-bottom: -15rpx;
}
.newsList .nav {
  padding: 0 24rpx;
  width: 100%;
  white-space: nowrap;
  box-sizing: border-box;
  margin-top: 43rpx;
}
.newsList .nav .item {
  display: inline-block;
  font-size: 32rpx;
  color: #999;
}
.newsList .nav .item.on {
  color: #282828;
}
.newsList .nav .item ~ .item {
  margin-left: 46rpx;
}
.newsList .nav .item .line {
  width: 24rpx;
  height: 4rpx;
  border-radius: 2rpx;
  margin: 10rpx auto 0 auto;
  background-color: theme;
}
[data-theme="theme1"] .newsList .nav .item .line {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .newsList .nav .item .line {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .newsList .nav .item .line {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .newsList .nav .item .line {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .newsList .nav .item .line {
  background-color: #FF448F !important;
}
.newsList .list .item {
  margin: 0 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  padding: 35rpx 0;
}
.newsList .list .item .pictrue {
  width: 250rpx;
  height: 156rpx;
}
.newsList .list .item .pictrue image {
  width: 100%;
  height: 100%;
  border-radius: 14rpx;
}
.newsList .list .item .text {
  width: 420rpx;
  height: 156rpx;
  font-size: 24rpx;
  color: #999;
}
.newsList .list .item .text .name {
  font-size: 30rpx;
  color: #282828;
}
.newsList .list .item .picList .pictrue {
  width: 335rpx;
  height: 210rpx;
  margin-top: 30rpx;
}
.newsList .list .item .picList.on .pictrue {
  width: 217rpx;
  height: 136rpx;
}
.newsList .list .item .picList .pictrue image {
  width: 100%;
  height: 100%;
  border-radius: 6rpx;
}
.newsList .list .item .time {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 22rpx;
}

