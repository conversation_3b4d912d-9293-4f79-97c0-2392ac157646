<view data-theme="{{theme}}"><skeleton class="vue-ref" vue-id="2ec9fb3c-1" show="{{showSkeleton}}" isNodes="{{isNodes}}" loading="chiaroscuro" selector="skeleton" bgcolor="#FFF" data-ref="skeleton" bind:__l="__l"></skeleton><view class="newsList skeleton" style="{{'visibility:'+(showSkeleton?'hidden':'visible')+';'}}"><block wx:if="{{$root.g0>0}}"><view class="swiper skeleton-rect"><swiper indicator-dots="true" autoplay="{{autoplay}}" circular="{{circular}}" interval="{{interval}}" duration="{{duration}}" indicator-color="rgba(102,102,102,0.3)" indicator-active-color="#666"><block wx:for="{{imgUrls}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><swiper-item><navigator url="{{'/pages/news_details/index?id='+item.id}}"><image class="slide-image" src="{{item.imageInput}}" mode="aspectFill"></image></navigator></swiper-item></block></block></swiper></view></block><view class="nav"><scroll-view class="scroll-view_x" style="width:auto;overflow:hidden;" scroll-x="{{true}}" scroll-with-animation="{{true}}" scroll-left="{{scrollLeft}}"><block wx:for="{{navList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view data-event-opts="{{[['tap',[['tabSelect',['$0',index],[[['navList','',index,'id']]]]]]]}}" class="{{['item','borRadius14','skeleton-rect',active==item.id?'on':'']}}" bindtap="__e"><view>{{item.name}}</view><block wx:if="{{active==item.id}}"><view class="line bg_color"></view></block></view></block></block></scroll-view></view><view class="list"><block wx:for="{{articleList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view data-event-opts="{{[['tap',[['toNewDetail',['$0'],[[['articleList','',index,'id']]]]]]]}}" class="item acea-row row-between-wrapper" bindtap="__e"><view class="text acea-row row-column-between"><view class="name line2 skeleton-rect">{{item.title}}</view><view class="skeleton-rect">{{item.createTime}}</view></view><view class="pictrue skeleton-rect"><image src="{{item.imageInput}}"></image></view></view></block></block></view></view><block wx:if="{{$root.g1}}"><view class="noCommodity"><view class="pictrue"><image src="../../static/images/noNews.png"></image></view></view></block></view>