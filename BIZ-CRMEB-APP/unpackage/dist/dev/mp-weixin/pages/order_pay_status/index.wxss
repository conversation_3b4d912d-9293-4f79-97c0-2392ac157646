@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.icon-iconfontguanbi {
  background-color: #999 !important;
  text-shadow: none !important;
}
.bg_color {
  background-color: theme;
}
[data-theme="theme1"] .bg_color {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .bg_color {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .bg_color {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .bg_color {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .bg_color {
  background-color: #FF448F !important;
}
.cart_color {
  color: theme;
  border: theme;
}
[data-theme="theme1"] .cart_color {
  color: #e93323 !important;
}
[data-theme="theme2"] .cart_color {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .cart_color {
  color: #42CA4D !important;
}
[data-theme="theme4"] .cart_color {
  color: #1DB0FC !important;
}
[data-theme="theme5"] .cart_color {
  color: #FF448F !important;
}
[data-theme="theme1"] .cart_color {
  border: 1px solid #e93323;
}
[data-theme="theme2"] .cart_color {
  border: 1px solid #FE5C2D;
}
[data-theme="theme3"] .cart_color {
  border: 1px solid #42CA4D;
}
[data-theme="theme4"] .cart_color {
  border: 1px solid #1DB0FC;
}
[data-theme="theme5"] .cart_color {
  border: 1px solid #FF448F;
}
.payment-status {
  background-color: #fff;
  margin: 195rpx 30rpx 0 30rpx;
  border-radius: 10rpx;
  padding: 1rpx 0 28rpx 0;
}
.payment-status .icons {
  font-size: 70rpx;
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  color: #fff;
  text-align: center;
  line-height: 140rpx;
  text-shadow: 0px 4px 0px rgba(0, 0, 0, 0.1);
  border: 6rpx solid #f5f5f5;
  margin: -76rpx auto 0 auto;
  background-color: #999;
}
.payment-status .iconfont {
  font-size: 70rpx;
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  color: #fff;
  text-align: center;
  line-height: 140rpx;
  text-shadow: 0px 4px 0px rgba(0, 0, 0, 0.1);
  border: 6rpx solid #f5f5f5;
  margin: -76rpx auto 0 auto;
  background-color: #999;
}
.payment-status .iconfont.fail {
  text-shadow: 0px 4px 0px #7a7a7a;
}
.payment-status .status {
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  margin: 25rpx 0 37rpx 0;
}
.payment-status .wrapper {
  border: 1rpx solid #eee;
  margin: 0 30rpx 47rpx 30rpx;
  padding: 35rpx 0;
  border-left: 0;
  border-right: 0;
}
.payment-status .wrapper .item {
  font-size: 28rpx;
  color: #282828;
}
.payment-status .wrapper .item ~ .item {
  margin-top: 20rpx;
}
.payment-status .wrapper .item .itemCom {
  color: #666;
}
.payment-status .returnBnt {
  width: 630rpx;
  height: 86rpx;
  border-radius: 50rpx;
  color: #fff;
  font-size: 30rpx;
  text-align: center;
  line-height: 86rpx;
  margin: 0 auto 20rpx auto;
}
.cart-color {
  color: theme;
  border: theme;
}
[data-theme="theme1"] .cart-color {
  color: #e93323 !important;
}
[data-theme="theme2"] .cart-color {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .cart-color {
  color: #42CA4D !important;
}
[data-theme="theme4"] .cart-color {
  color: #1DB0FC !important;
}
[data-theme="theme5"] .cart-color {
  color: #FF448F !important;
}
[data-theme="theme1"] .cart-color {
  border: 1px solid #e93323;
}
[data-theme="theme2"] .cart-color {
  border: 1px solid #FE5C2D;
}
[data-theme="theme3"] .cart-color {
  border: 1px solid #42CA4D;
}
[data-theme="theme4"] .cart-color {
  border: 1px solid #1DB0FC;
}
[data-theme="theme5"] .cart-color {
  border: 1px solid #FF448F;
}

