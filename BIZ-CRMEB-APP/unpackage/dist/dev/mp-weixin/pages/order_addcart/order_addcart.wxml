<view data-theme="{{theme}}" class="data-v-a5aa7f30"><view class="cart_nav data-v-a5aa7f30" style="{{('height:'+navH+'rpx;')}}"><view class="navbarCon acea-row data-v-a5aa7f30"><view class="select_nav flex justify-center align-center data-v-a5aa7f30" style="{{'top:'+(homeTop+'rpx')+';'}}" id="home"><text data-event-opts="{{[['tap',[['returns',['$event']]]]]}}" class="iconfont icon-fanhui2 px-20 data-v-a5aa7f30" bindtap="__e"></text><text data-event-opts="{{[['tap',[['showNav',['$event']]]]]}}" class="iconfont icon-gengduo5 px-20 data-v-a5aa7f30" catchtap="__e"></text><text class="nav_line data-v-a5aa7f30"></text></view><view class="nav_title data-v-a5aa7f30" style="{{'top:'+(homeTop+'rpx')+';'}}">购物车</view></view></view><view hidden="{{!(currentPage)}}" class="dialog_nav data-v-a5aa7f30" style="{{('top:'+navH+'rpx;')}}"><block wx:for="{{selectNavList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['linkPage',['$0'],[[['selectNavList','',index,'url']]]]]]]}}" class="dialog_nav_item data-v-a5aa7f30" bindtap="__e"><text class="{{['iconfont','data-v-a5aa7f30',item.icon]}}"></text><text class="pl-20 data-v-a5aa7f30">{{item.name}}</text></view></block></view><view data-event-opts="{{[['touchstart',[['touchStart',['$event']]]]]}}" class="shoppingCart copy-data data-v-a5aa7f30" style="{{('top:'+navH+'rpx;')}}" bindtouchstart="__e"><view class="labelNav acea-row row-around data-v-a5aa7f30"><view class="item data-v-a5aa7f30"><text class="iconfont icon-xuanzhong data-v-a5aa7f30"></text>100%正品保证</view><view class="item data-v-a5aa7f30"><text class="iconfont icon-xuanzhong data-v-a5aa7f30"></text>所有商品精挑细选</view><view class="item data-v-a5aa7f30"><text class="iconfont icon-xuanzhong data-v-a5aa7f30"></text>售后无忧</view></view><view class="borRadius14 cartBox data-v-a5aa7f30"><block wx:if="{{$root.g0}}"><view class="nav acea-row row-between-wrapper data-v-a5aa7f30"><view class="data-v-a5aa7f30">购物数量<text class="num font_color data-v-a5aa7f30">{{cartCount}}</text></view><block wx:if="{{$root.g1}}"><view data-event-opts="{{[['tap',[['manage',['$event']]]]]}}" class="administrate acea-row row-center-wrapper data-v-a5aa7f30" bindtap="__e">{{(footerswitch?'管理':'取消')+''}}</view></block></view></block><block wx:if="{{$root.g2}}"><view class="pad30 data-v-a5aa7f30"><view class="list data-v-a5aa7f30"><checkbox-group data-event-opts="{{[['change',[['checkboxChange',['$event']]]]]}}" bindchange="__e" class="data-v-a5aa7f30"><block wx:for="{{cartList.valid}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block class="data-v-a5aa7f30"><view class="item acea-row row-between-wrapper data-v-a5aa7f30"><checkbox value="{{item.id}}" checked="{{item.checked}}" disabled="{{!item.attrStatus&&footerswitch}}" class="data-v-a5aa7f30"></checkbox><navigator class="picTxt acea-row row-between-wrapper data-v-a5aa7f30" url="{{'/pages/goods_details/index?id='+item.productId}}" hover-class="none"><view class="pictrue data-v-a5aa7f30"><image src="{{item.image}}" class="data-v-a5aa7f30"></image></view><view class="text data-v-a5aa7f30"><view class="{{['line1','data-v-a5aa7f30',item.attrStatus?'':'reColor']}}">{{item.storeName+''}}</view><block wx:if="{{item.suk}}"><view class="infor line1 data-v-a5aa7f30">{{"属性："+item.suk}}</view></block><block wx:if="{{item.attrStatus}}"><view class="money mt-28 data-v-a5aa7f30">{{"￥"+(item.vipPrice?item.vipPrice:item.price)}}</view></block><block wx:else><view class="reElection acea-row row-between-wrapper data-v-a5aa7f30"><view class="title data-v-a5aa7f30">请重新选择商品规格</view><view data-event-opts="{{[['tap',[['reElection',['$0'],[[['cartList.valid','',index]]]]]]]}}" class="reBnt cart-color acea-row row-center-wrapper data-v-a5aa7f30" catchtap="__e">重选</view></view></block></view><block wx:if="{{item.attrStatus}}"><view class="carnum acea-row row-center-wrapper data-v-a5aa7f30"><view data-event-opts="{{[['tap',[['subCart',[index]]]]]}}" class="{{['reduce','data-v-a5aa7f30',item.numSub?'on':'']}}" catchtap="__e">-</view><view class="num data-v-a5aa7f30">{{item.cartNum}}</view><view data-event-opts="{{[['tap',[['addCart',[index]]]]]}}" class="{{['plus','data-v-a5aa7f30',item.numAdd?'on':'']}}" catchtap="__e">+</view></view></block></navigator></view></block></block></checkbox-group></view><block wx:if="{{$root.g3>0}}"><view class="invalidGoods borRadius14 data-v-a5aa7f30" style="{{($root.g4?'position: relative;z-index: 111;top: -120rpx;':'position: static;')}}"><view class="goodsNav acea-row row-between-wrapper data-v-a5aa7f30"><block wx:if="{{$root.g5}}"><view data-event-opts="{{[['tap',[['goodsOpen',['$event']]]]]}}" bindtap="__e" class="data-v-a5aa7f30"><text class="{{['iconfont','data-v-a5aa7f30',goodsHidden==true?'icon-xiangxia':'icon-xiangshang']}}"></text>失效商品</view></block><block wx:else><view class="data-v-a5aa7f30">失效商品</view></block><view data-event-opts="{{[['tap',[['unsetCart',['$event']]]]]}}" class="del data-v-a5aa7f30" bindtap="__e"><text class="iconfont icon-shanchu1 data-v-a5aa7f30"></text>清空</view></view><view class="goodsList data-v-a5aa7f30" hidden="{{goodsHidden}}"><block wx:for="{{cartList.invalid}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block class="data-v-a5aa7f30"><view class="item acea-row row-between-wrapper data-v-a5aa7f30"><view class="invalid data-v-a5aa7f30">失效</view><view class="picTxt acea-row row-between-wrapper data-v-a5aa7f30"><view class="pictrue data-v-a5aa7f30"><image src="{{item.image}}" class="data-v-a5aa7f30"></image></view><view class="text acea-row row-column-between data-v-a5aa7f30"><view class="line1 name data-v-a5aa7f30">{{item.storeName}}</view><block wx:if="{{item.suk}}"><view class="infor line1 data-v-a5aa7f30">{{"属性："+item.suk}}</view></block><view class="acea-row row-between-wrapper data-v-a5aa7f30"><view class="end data-v-a5aa7f30">该商品已失效</view></view></view></view></view></block></block></view></view></block><view style="height:120rpx;" class="data-v-a5aa7f30"></view><block wx:if="{{$root.g6}}"><view class="loadingicon acea-row row-center-wrapper data-v-a5aa7f30"><text class="loading iconfont icon-jiazai data-v-a5aa7f30" hidden="{{loadingInvalid==false}}"></text>{{loadTitleInvalid+''}}</view></block></view></block><block wx:if="{{$root.g7}}"><view class="noCart data-v-a5aa7f30"><view class="pictrue data-v-a5aa7f30"><image src="../../static/images/noCart.png" class="data-v-a5aa7f30"></image></view><recommend vue-id="0fa464c4-1" hostProduct="{{hostProduct}}" class="data-v-a5aa7f30" bind:__l="__l"></recommend></view></block></view></view><block wx:if="{{$root.g8>0}}"><view class="footer acea-row row-between-wrapper data-v-a5aa7f30"><view class="data-v-a5aa7f30"><checkbox-group data-event-opts="{{[['change',[['checkboxAllChange',['$event']]]]]}}" bindchange="__e" class="data-v-a5aa7f30"><checkbox value="all" checked="{{!!isAllSelect}}" class="data-v-a5aa7f30"></checkbox><text class="checkAll data-v-a5aa7f30">{{"全选("+$root.g9+")"}}</text></checkbox-group></view><block wx:if="{{footerswitch==true}}"><view class="money acea-row row-middle data-v-a5aa7f30"><text class="price-color data-v-a5aa7f30">{{"￥"+selectCountPrice}}</text><form report-submit="true" data-event-opts="{{[['submit',[['subOrder',['$event']]]]]}}" bindsubmit="__e" class="data-v-a5aa7f30"><button class="placeOrder bg_color data-v-a5aa7f30" formType="submit">立即下单</button></form></view></block><block wx:else><view class="button acea-row row-middle data-v-a5aa7f30"><form report-submit="true" data-event-opts="{{[['submit',[['subCollect',['$event']]]]]}}" bindsubmit="__e" class="data-v-a5aa7f30"><button class="btn_cart_color data-v-a5aa7f30" formType="submit">收藏</button></form><form report-submit="true" data-event-opts="{{[['submit',[['subDel',['$event']]]]]}}" bindsubmit="__e" class="data-v-a5aa7f30"><button class="bnt data-v-a5aa7f30" formType="submit">删除</button></form></view></block></view></block><product-window vue-id="0fa464c4-2" attr="{{attr}}" isShow="{{1}}" iSplus="{{1}}" iScart="{{1}}" id="product-window" data-event-opts="{{[['^myevent',[['onMyEvent']]],['^ChangeAttr',[['ChangeAttr']]],['^ChangeCartNum',[['ChangeCartNum']]],['^attrVal',[['attrVal']]],['^iptCartNum',[['iptCartNum']]],['^goCat',[['reGoCat']]]]}}" bind:myevent="__e" bind:ChangeAttr="__e" bind:ChangeCartNum="__e" bind:attrVal="__e" bind:iptCartNum="__e" bind:goCat="__e" class="data-v-a5aa7f30" bind:__l="__l"></product-window><view class="uni-p-b-96 data-v-a5aa7f30"></view><view class="uni-p-b-98 data-v-a5aa7f30"></view></view>