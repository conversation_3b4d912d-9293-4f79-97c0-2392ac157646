(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/order_addcart/order_addcart"],{

/***/ 154:
/*!********************************************************************************************************!*\
  !*** E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/main.js?{"page":"pages%2Forder_addcart%2Forder_addcart"} ***!
  \********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _order_addcart = _interopRequireDefault(__webpack_require__(/*! ./pages/order_addcart/order_addcart.vue */ 155));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_order_addcart.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 155:
/*!*************************************************************************************!*\
  !*** E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/order_addcart/order_addcart.vue ***!
  \*************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _order_addcart_vue_vue_type_template_id_a5aa7f30_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./order_addcart.vue?vue&type=template&id=a5aa7f30&scoped=true& */ 156);
/* harmony import */ var _order_addcart_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./order_addcart.vue?vue&type=script&lang=js& */ 158);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _order_addcart_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _order_addcart_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _order_addcart_vue_vue_type_style_index_0_id_a5aa7f30_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./order_addcart.vue?vue&type=style&index=0&id=a5aa7f30&scoped=true&lang=scss& */ 160);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 52);

var renderjs





/* normalize component */

var component = Object(_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _order_addcart_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _order_addcart_vue_vue_type_template_id_a5aa7f30_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _order_addcart_vue_vue_type_template_id_a5aa7f30_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "a5aa7f30",
  null,
  false,
  _order_addcart_vue_vue_type_template_id_a5aa7f30_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/order_addcart/order_addcart.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 156:
/*!********************************************************************************************************************************!*\
  !*** E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/order_addcart/order_addcart.vue?vue&type=template&id=a5aa7f30&scoped=true& ***!
  \********************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_order_addcart_vue_vue_type_template_id_a5aa7f30_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order_addcart.vue?vue&type=template&id=a5aa7f30&scoped=true& */ 157);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_order_addcart_vue_vue_type_template_id_a5aa7f30_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_order_addcart_vue_vue_type_template_id_a5aa7f30_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_order_addcart_vue_vue_type_template_id_a5aa7f30_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_order_addcart_vue_vue_type_template_id_a5aa7f30_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 157:
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/order_addcart/order_addcart.vue?vue&type=template&id=a5aa7f30&scoped=true& ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 =
    (_vm.cartList.valid.length === 0 && _vm.cartList.invalid.length === 0) ||
    _vm.cartList.valid.length > 0
  var g1 = g0
    ? _vm.cartList.valid.length > 0 || _vm.cartList.invalid.length > 0
    : null
  var g2 = _vm.cartList.valid.length > 0 || _vm.cartList.invalid.length > 0
  var g3 = g2 ? _vm.cartList.invalid.length : null
  var g4 =
    g2 && g3 > 0
      ? _vm.cartList.valid.length === 0 && _vm.cartList.invalid.length > 0
      : null
  var g5 =
    g2 && g3 > 0
      ? _vm.cartList.invalid.length > 1 || _vm.cartList.valid.length > 0
      : null
  var g6 = g2 ? _vm.cartList.invalid.length && _vm.loadend : null
  var g7 =
    (_vm.cartList.valid.length == 0 &&
      _vm.cartList.invalid.length == 0 &&
      _vm.canShow) ||
    !_vm.isLogin
  var g8 = _vm.cartList.valid.length
  var g9 = g8 > 0 ? _vm.selectValue.length : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
        g1: g1,
        g2: g2,
        g3: g3,
        g4: g4,
        g5: g5,
        g6: g6,
        g7: g7,
        g8: g8,
        g9: g9,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 158:
/*!**************************************************************************************************************!*\
  !*** E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/order_addcart/order_addcart.vue?vue&type=script&lang=js& ***!
  \**************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_order_addcart_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order_addcart.vue?vue&type=script&lang=js& */ 159);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_order_addcart_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_order_addcart_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_order_addcart_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_order_addcart_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_order_addcart_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 159:
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/order_addcart/order_addcart.vue?vue&type=script&lang=js& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 30));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 32));
var _order = __webpack_require__(/*! @/api/order.js */ 56);
var _store = __webpack_require__(/*! @/api/store.js */ 68);
var _public = __webpack_require__(/*! @/api/public.js */ 47);
var _vuex = __webpack_require__(/*! vuex */ 35);
var _animationType = _interopRequireDefault(__webpack_require__(/*! @/utils/animationType.js */ 71));
var _methods;
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

var sysHeight = 0;
var recommend = function recommend() {
  Promise.all(/*! require.ensure | components/recommend/index */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/recommend/index")]).then((function () {
    return resolve(__webpack_require__(/*! @/components/recommend */ 667));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var productWindow = function productWindow() {
  __webpack_require__.e(/*! require.ensure | components/productWindow/index */ "components/productWindow/index").then((function () {
    return resolve(__webpack_require__(/*! @/components/productWindow */ 653));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var authorize = function authorize() {
  Promise.all(/*! require.ensure | components/Authorize */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/Authorize")]).then((function () {
    return resolve(__webpack_require__(/*! @/components/Authorize */ 723));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var app = getApp();
var _default = {
  components: {
    recommend: recommend,
    productWindow: productWindow,
    authorize: authorize
  },
  data: function data() {
    return {
      cartCount: 0,
      goodsHidden: false,
      footerswitch: true,
      hostProduct: [],
      cartList: {
        valid: [],
        invalid: []
      },
      isAllSelect: false,
      //全选
      selectValue: [],
      //选中的数据
      selectCountPrice: 0.00,
      isAuto: false,
      //没有授权的不会自动授权
      isShowAuth: false,
      //是否隐藏授权
      hotScroll: false,
      hotPage: 1,
      hotLimit: 10,
      loading: false,
      loadend: false,
      loadTitle: '加载更多',
      //提示语
      page: 1,
      limit: 20,
      loadingInvalid: false,
      loadendInvalid: false,
      loadTitleInvalid: '加载更多',
      //提示语
      pageInvalid: 1,
      limitInvalid: 20,
      attr: {
        cartAttr: false,
        productAttr: [],
        productSelect: {}
      },
      productValue: [],
      //系统属性
      productInfo: {},
      attrValue: '',
      //已选属性
      attrTxt: '请选择',
      //属性页面提示
      cartId: 0,
      product_id: 0,
      sysHeight: sysHeight,
      canShow: false,
      configApi: {},
      //分享类容配置
      theme: app.globalData.theme,
      navH: "",
      homeTop: 20,
      currentPage: false,
      selectNavList: [{
        name: '首页',
        icon: 'icon-shouye8',
        url: '/pages/index/index'
      }, {
        name: '搜索',
        icon: 'icon-sousuo6',
        url: '/pages/goods_search/index'
      }, {
        name: '我的收藏',
        icon: 'icon-shoucang3',
        url: '/pages/users/user_goods_collection/index'
      }, {
        name: '个人中心',
        icon: 'icon-gerenzhongxin1',
        url: '/pages/user/index'
      }]
    };
  },
  computed: (0, _vuex.mapGetters)(['isLogin']),
  onLoad: function onLoad(options) {
    console.log(app.globalData);
    var that = this;
    that.navH = app.globalData.navHeight;
  },
  onReady: function onReady() {
    this.$nextTick(function () {
      var _this = this;
      var menuButton = uni.getMenuButtonBoundingClientRect();
      var query = uni.createSelectorQuery().in(this);
      query.select('#home').boundingClientRect(function (data) {
        _this.homeTop = menuButton.top * 2 + menuButton.height - data.height;
      }).exec();
    });
  },
  onShow: function onShow() {
    this.canShow = false;
    if (this.isLogin == true) {
      this.hotPage = 1;
      this.hostProduct = [], this.hotScroll = false, this.loadend = false;
      this.page = 1;
      this.cartList.valid = [];
      this.getCartList();
      this.loadendInvalid = false;
      this.pageInvalid = 1;
      this.cartList.invalid = [];
      this.getInvalidList();
      //this.getCartNum();
      this.footerswitch = true;
      this.hotScroll = false;
      this.hotPage = 1;
      this.hotLimit = 10;
      this.cartList = {
        valid: [],
        invalid: []
      }, this.isAllSelect = false; //全选
      this.selectValue = []; //选中的数据
      this.selectCountPrice = 0.00;
      this.cartCount = 0;
      this.isShowAuth = false;
    }
    ;
    uni.showTabBar();
  },
  methods: (_methods = {
    // 授权关闭
    authColse: function authColse(e) {
      this.isShowAuth = e;
    },
    // 修改购物车
    reGoCat: function reGoCat() {
      var that = this,
        productSelect = that.productValue[this.attrValue];
      //如果有属性,没有选择,提示用户选择
      if (that.attr.productAttr.length && productSelect === undefined) return that.$util.Tips({
        title: "产品库存不足，请选择其它"
      });
      var q = {
        id: that.cartId,
        productId: that.product_id,
        num: that.attr.productSelect.cart_num,
        unique: that.attr.productSelect !== undefined ? that.attr.productSelect.unique : that.productInfo.id
      };
      (0, _order.getResetCart)(q).then(function (res) {
        that.attr.cartAttr = false;
        that.$util.Tips({
          title: "添加购物车成功",
          success: function success() {
            that.loadend = false;
            that.page = 1;
            that.cartList.valid = [];
            that.getCartList();
            that.getCartNum();
          }
        });
      }).catch(function (res) {
        return that.$util.Tips({
          title: res
        });
      });
    },
    onMyEvent: function onMyEvent() {
      this.$set(this.attr, 'cartAttr', false);
    },
    reElection: function reElection(item) {
      this.getGoodsDetails(item);
    },
    /**
     * 获取产品详情
     * 
     */
    getGoodsDetails: function getGoodsDetails(item) {
      var _this2 = this;
      uni.showLoading({
        title: '加载中',
        mask: true
      });
      var that = this;
      that.cartId = item.id;
      that.product_id = item.productId;
      (0, _store.getProductDetail)(item.productId).then(function (res) {
        uni.hideLoading();
        that.attr.cartAttr = true;
        var productInfo = res.data.productInfo;
        that.$set(that, 'productInfo', productInfo);
        // that.$set(that.attr, 'productAttr', res.data.productAttr);
        that.$set(that, 'productValue', res.data.productValue);
        var productAttr = res.data.productAttr.map(function (item) {
          return {
            attrName: item.attrName,
            attrValues: item.attrValues.split(','),
            id: item.id,
            isDel: item.isDel,
            productId: item.productId,
            type: item.type
          };
        });
        _this2.$set(that.attr, 'productAttr', productAttr);
        that.DefaultSelect();
      }).catch(function (err) {
        uni.hideLoading();
      });
    },
    /**
     * 属性变动赋值
     * 
     */
    ChangeAttr: function ChangeAttr(res) {
      var productSelect = this.productValue[res];
      if (productSelect && productSelect.stock > 0) {
        this.$set(this.attr.productSelect, "image", productSelect.image);
        this.$set(this.attr.productSelect, "price", productSelect.price);
        this.$set(this.attr.productSelect, "stock", productSelect.stock);
        this.$set(this.attr.productSelect, "unique", productSelect.id);
        this.$set(this.attr.productSelect, "cart_num", 1);
        this.$set(this, "attrValue", res);
        this.$set(this, "attrTxt", "已选择");
      } else {
        this.$set(this.attr.productSelect, "image", this.productInfo.image);
        this.$set(this.attr.productSelect, "price", this.productInfo.price);
        this.$set(this.attr.productSelect, "stock", 0);
        this.$set(this.attr.productSelect, "unique", this.productInfo.id);
        this.$set(this.attr.productSelect, "cart_num", 0);
        this.$set(this, "attrValue", "");
        this.$set(this, "attrTxt", "请选择");
      }
    },
    /**
     * 默认选中属性
     * 
     */
    DefaultSelect: function DefaultSelect() {
      var productAttr = this.attr.productAttr;
      var value = [];
      for (var key in this.productValue) {
        if (this.productValue[key].stock > 0) {
          value = this.attr.productAttr.length ? key.split(",") : [];
          break;
        }
      }
      for (var i = 0; i < productAttr.length; i++) {
        this.$set(productAttr[i], "index", value[i]);
      }
      //sort();排序函数:数字-英文-汉字；
      var productSelect = this.productValue[value.sort().join(",")];
      if (productSelect && productAttr.length) {
        this.$set(this.attr.productSelect, "storeName", this.productInfo.storeName);
        this.$set(this.attr.productSelect, "image", productSelect.image);
        this.$set(this.attr.productSelect, "price", productSelect.price);
        this.$set(this.attr.productSelect, "stock", productSelect.stock);
        this.$set(this.attr.productSelect, "unique", productSelect.id);
        this.$set(this.attr.productSelect, "cart_num", 1);
        this.$set(this, "attrValue", value.sort().join(","));
        this.$set(this, "attrTxt", "已选择");
      } else if (!productSelect && productAttr.length) {
        this.$set(this.attr.productSelect, "storeName", this.productInfo.storeName);
        this.$set(this.attr.productSelect, "image", this.productInfo.image);
        this.$set(this.attr.productSelect, "price", this.productInfo.price);
        this.$set(this.attr.productSelect, "stock", 0);
        this.$set(this.attr.productSelect, "unique", this.productInfo.id);
        this.$set(this.attr.productSelect, "cart_num", 0);
        this.$set(this, "attrValue", "");
        this.$set(this, "attrTxt", "请选择");
      } else if (!productSelect && !productAttr.length) {
        this.$set(this.attr.productSelect, "storeName", this.productInfo.storeName);
        this.$set(this.attr.productSelect, "image", this.productInfo.image);
        this.$set(this.attr.productSelect, "price", this.productInfo.price);
        this.$set(this.attr.productSelect, "stock", this.productInfo.stock);
        this.$set(this.attr.productSelect, "unique", this.productInfo.id || "");
        this.$set(this.attr.productSelect, "cart_num", 1);
        this.$set(this, "attrValue", "");
        this.$set(this, "attrTxt", "请选择");
      }
    },
    attrVal: function attrVal(val) {
      this.$set(this.attr.productAttr[val.indexw], 'index', this.attr.productAttr[val.indexw].attrValues[val.indexn]);
    },
    /**
     * 购物车数量加和数量减
     * 
     */
    ChangeCartNum: function ChangeCartNum(changeValue) {
      //changeValue:是否 加|减
      //获取当前变动属性
      var productSelect = this.productValue[this.attrValue];
      //如果没有属性,赋值给商品默认库存
      if (productSelect === undefined && !this.attr.productAttr.length) productSelect = this.attr.productSelect;
      //无属性值即库存为0；不存在加减；
      if (productSelect === undefined) return;
      var stock = productSelect.stock || 0;
      var num = this.attr.productSelect;
      if (changeValue) {
        num.cart_num++;
        if (num.cart_num > stock) {
          this.$set(this.attr.productSelect, "cart_num", stock ? stock : 1);
          this.$set(this, "cart_num", stock ? stock : 1);
        }
      } else {
        num.cart_num--;
        if (num.cart_num < 1) {
          this.$set(this.attr.productSelect, "cart_num", 1);
          this.$set(this, "cart_num", 1);
        }
      }
    },
    /**
     * 购物车手动填写
     * 
     */
    iptCartNum: function iptCartNum(e) {
      this.$set(this.attr.productSelect, 'cart_num', e);
    },
    subDel: function subDel(event) {
      var that = this,
        selectValue = that.selectValue;
      if (selectValue.length > 0) (0, _order.cartDel)(selectValue).then(function (res) {
        that.loadend = false;
        that.page = 1;
        that.cartList.valid = [];
        that.getCartList();
        that.getCartNum();
      });else return that.$util.Tips({
        title: '请选择产品'
      });
    },
    getSelectValueProductId: function getSelectValueProductId() {
      var that = this;
      var validList = that.cartList.valid;
      var selectValue = that.selectValue;
      var productId = [];
      if (selectValue.length > 0) {
        for (var index in validList) {
          if (that.inArray(validList[index].id, selectValue)) {
            productId.push(validList[index].productId);
          }
        }
      }
      ;
      return productId;
    },
    subCollect: function subCollect(event) {
      var that = this,
        selectValue = that.selectValue;
      if (selectValue.length > 0) {
        var selectValueProductId = that.getSelectValueProductId();
        (0, _store.collectAll)(that.getSelectValueProductId()).then(function (res) {
          return that.$util.Tips({
            title: '收藏成功',
            icon: 'success'
          });
        }).catch(function (err) {
          return that.$util.Tips({
            title: err
          });
        });
      } else {
        return that.$util.Tips({
          title: '请选择产品'
        });
      }
    },
    // 立即下单
    subOrder: function subOrder(event) {
      var that = this,
        selectValue = that.selectValue;
      if (selectValue.length > 0) {
        that.getPreOrder();
      } else {
        return that.$util.Tips({
          title: '请选择产品'
        });
      }
    },
    /**
     * 预下单
     */
    getPreOrder: function getPreOrder() {
      var shoppingCartId = this.selectValue.map(function (item) {
        return {
          "shoppingCartId": Number(item)
        };
      });
      this.$Order.getPreOrder("shoppingCart", shoppingCartId);
    },
    checkboxAllChange: function checkboxAllChange(event) {
      var value = event.detail.value;
      if (value.length > 0) {
        this.setAllSelectValue(1);
      } else {
        this.setAllSelectValue(0);
      }
    },
    setAllSelectValue: function setAllSelectValue(status) {
      var that = this;
      var selectValue = [];
      var valid = that.cartList.valid;
      if (valid.length > 0) {
        var newValid = valid.map(function (item) {
          if (status) {
            if (that.footerswitch) {
              if (item.attrStatus) {
                item.checked = true;
                selectValue.push(item.id);
              } else {
                item.checked = false;
              }
            } else {
              item.checked = true;
              selectValue.push(item.id);
            }
            that.isAllSelect = true;
          } else {
            item.checked = false;
            that.isAllSelect = false;
          }
          return item;
        });
        that.$set(that.cartList, 'valid', newValid);
        that.selectValue = selectValue;
        that.switchSelect();
      }
    },
    checkboxChange: function checkboxChange(event) {
      var that = this;
      var value = event.detail.value;
      var valid = that.cartList.valid;
      var arr1 = [];
      var arr2 = [];
      var arr3 = [];
      var newValid = valid.map(function (item) {
        if (that.inArray(item.id, value)) {
          if (that.footerswitch) {
            if (item.attrStatus) {
              item.checked = true;
              arr1.push(item);
            } else {
              item.checked = false;
            }
          } else {
            item.checked = true;
            arr1.push(item);
          }
        } else {
          item.checked = false;
          arr2.push(item);
        }
        return item;
      });
      if (that.footerswitch) {
        arr3 = arr2.filter(function (item) {
          return !item.attrStatus;
        });
      }
      that.$set(that.cartList, 'valid', newValid);
      // let newArr = that.cartList.valid.filter(item => item.attrStatus);
      that.isAllSelect = newValid.length === arr1.length + arr3.length;
      that.selectValue = value;
      that.switchSelect();
    },
    inArray: function inArray(search, array) {
      for (var i in array) {
        if (array[i] == search) {
          return true;
        }
      }
      return false;
    },
    switchSelect: function switchSelect() {
      var that = this;
      var validList = that.cartList.valid;
      var selectValue = that.selectValue;
      var selectCountPrice = 0.00;
      if (selectValue.length < 1) {
        that.selectCountPrice = selectCountPrice;
      } else {
        for (var index in validList) {
          if (that.inArray(validList[index].id, selectValue)) {
            selectCountPrice = that.$util.$h.Add(selectCountPrice, that.$util.$h.Mul(validList[index].cartNum, validList[index].vipPrice ? validList[index].vipPrice : validList[index].price));
          }
        }
        that.selectCountPrice = selectCountPrice;
      }
    }
  }, (0, _defineProperty2.default)(_methods, "iptCartNum", function iptCartNum(index) {
    var item = this.cartList.valid[index];
    if (item.cartNum) {
      this.setCartNum(item.id, item.cartNum);
    }
    this.switchSelect();
  }), (0, _defineProperty2.default)(_methods, "blurInput", function blurInput(index) {
    var item = this.cartList.valid[index];
    if (!item.cartNum) {
      item.cartNum = 1;
      this.$set(this.cartList, 'valid', this.cartList.valid);
    }
  }), (0, _defineProperty2.default)(_methods, "subCart", function subCart(index) {
    var that = this;
    var status = false;
    var item = that.cartList.valid[index];
    item.cartNum = Number(item.cartNum) - 1;
    if (item.cartNum < 1) status = true;
    if (item.cartNum <= 1) {
      item.cartNum = 1;
      item.numSub = true;
    } else {
      item.numSub = false;
      item.numAdd = false;
    }
    if (false == status) {
      that.setCartNum(item.id, item.cartNum, function (data) {
        that.cartList.valid[index] = item;
        that.switchSelect();
        that.getCartNum();
      });
    }
  }), (0, _defineProperty2.default)(_methods, "addCart", function addCart(index) {
    var that = this;
    var item = that.cartList.valid[index];
    item.cartNum = Number(item.cartNum) + 1;
    var productInfo = item;
    if (item.cartNum >= item.stock) {
      item.cartNum = item.stock;
      item.numAdd = true;
      item.numSub = false;
    } else {
      item.numAdd = false;
      item.numSub = false;
    }
    that.setCartNum(item.id, item.cartNum, function (data) {
      that.cartList.valid[index] = item;
      that.switchSelect();
      that.getCartNum();
    });
  }), (0, _defineProperty2.default)(_methods, "setCartNum", function setCartNum(cartId, cartNum, successCallback) {
    var that = this;
    (0, _order.changeCartNum)(cartId, cartNum).then(function (res) {
      successCallback && successCallback(res.data);
    });
  }), (0, _defineProperty2.default)(_methods, "getCartNum", function getCartNum() {
    var that = this;
    (0, _order.getCartCounts)(true, 'sum').then(function (res) {
      that.cartCount = res.data.count;
    });
  }), (0, _defineProperty2.default)(_methods, "getCartData", function getCartData(data) {
    return new Promise(function (resolve, reject) {
      (0, _order.getCartList)(data).then(function (res) {
        resolve(res.data);
      }).catch(function (err) {
        this.loading = false;
        this.canShow = true;
        this.$util.Tips({
          title: err
        });
      });
    });
  }), (0, _defineProperty2.default)(_methods, "getCartList", function getCartList() {
    var _this3 = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
      var that, data;
      return _regenerator.default.wrap(function _callee2$(_context2) {
        while (1) {
          switch (_context2.prev = _context2.next) {
            case 0:
              uni.showLoading({
                title: '加载中',
                mask: true
              });
              that = _this3;
              data = {
                page: that.page,
                limit: that.limit,
                isValid: true
              };
              (0, _order.getCartCounts)(true, 'sum').then( /*#__PURE__*/function () {
                var _ref = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee(c) {
                  var i, cartList, valid, validList, numSub, numAdd, selectValue, index, productInfo, stock, newArr;
                  return _regenerator.default.wrap(function _callee$(_context) {
                    while (1) {
                      switch (_context.prev = _context.next) {
                        case 0:
                          that.cartCount = c.data.count;
                          if (c.data.count === 0) that.getHostProduct();
                          i = 0;
                        case 3:
                          if (!(i < Math.ceil(that.cartCount / that.limit))) {
                            _context.next = 21;
                            break;
                          }
                          _context.next = 6;
                          return _this3.getCartData(data);
                        case 6:
                          cartList = _context.sent;
                          valid = cartList.list;
                          validList = that.$util.SplitArray(valid, that.cartList.valid);
                          numSub = [{
                            numSub: true
                          }, {
                            numSub: false
                          }];
                          numAdd = [{
                            numAdd: true
                          }, {
                            numAdd: false
                          }], selectValue = [];
                          if (validList.length > 0) {
                            for (index in validList) {
                              if (validList[index].cartNum == 1) {
                                validList[index].numSub = true;
                              } else {
                                validList[index].numSub = false;
                              }
                              productInfo = validList[index];
                              stock = validList[index].stock ? validList[index].stock : 0;
                              if (validList[index].cartNum == stock) {
                                validList[index].numAdd = true;
                              } else if (validList[index].cartNum == validList[index].stock) {
                                validList[index].numAdd = true;
                              } else {
                                validList[index].numAdd = false;
                              }
                              if (validList[index].attrStatus) {
                                validList[index].checked = true;
                                selectValue.push(validList[index].id);
                              } else {
                                validList[index].checked = false;
                              }
                            }
                          }
                          that.$set(that.cartList, 'valid', validList);
                          data.page += 1;
                          that.selectValue = selectValue;
                          newArr = validList.filter(function (item) {
                            return item.attrStatus;
                          });
                          that.isAllSelect = newArr.length == selectValue.length && newArr.length;
                          that.switchSelect();
                        case 18:
                          i++;
                          _context.next = 3;
                          break;
                        case 21:
                          that.loading = false;
                          that.canShow = true;
                          uni.hideLoading();
                        case 24:
                        case "end":
                          return _context.stop();
                      }
                    }
                  }, _callee);
                }));
                return function (_x) {
                  return _ref.apply(this, arguments);
                };
              }());
            case 4:
            case "end":
              return _context2.stop();
          }
        }
      }, _callee2);
    }))();
  }), (0, _defineProperty2.default)(_methods, "getInvalidList", function getInvalidList() {
    var that = this;
    if (this.loadendInvalid) return false;
    if (this.loadingInvalid) return false;
    var data = {
      page: that.pageInvalid,
      limit: that.limitInvalid,
      isValid: false
    };
    (0, _order.getCartList)(data).then(function (res) {
      var invalid = res.data.list,
        loadendInvalid = invalid.length < that.limitInvalid;
      var invalidList = that.$util.SplitArray(invalid, that.cartList.invalid);
      that.$set(that.cartList, 'invalid', invalidList);
      that.loadendInvalid = loadendInvalid;
      that.loadTitleInvalid = loadendInvalid ? '我也是有底线的' : '加载更多';
      that.pageInvalid = that.pageInvalid + 1;
      that.loadingInvalid = false;
      //if(invalid.length===0) that.getHostProduct();
    }).catch(function (res) {
      that.loadingInvalid = false;
      that.loadTitleInvalid = '加载更多';
    });
  }), (0, _defineProperty2.default)(_methods, "getHostProduct", function getHostProduct() {
    var that = this;
    if (that.hotScroll) return;
    (0, _store.getProductHot)(that.hotPage, that.hotLimit).then(function (res) {
      that.hotPage++;
      that.hotScroll = res.data.list.length < that.hotLimit;
      that.hostProduct = that.hostProduct.concat(res.data.list);
    });
  }), (0, _defineProperty2.default)(_methods, "goodsOpen", function goodsOpen() {
    var that = this;
    that.goodsHidden = !that.goodsHidden;
  }), (0, _defineProperty2.default)(_methods, "manage", function manage() {
    var that = this;
    that.footerswitch = !that.footerswitch;
    var arr1 = [];
    var arr2 = [];
    var newValid = that.cartList.valid.map(function (item) {
      if (that.footerswitch) {
        if (item.attrStatus) {
          if (item.checked) {
            arr1.push(item.id);
          }
        } else {
          item.checked = false;
          arr2.push(item);
        }
      } else {
        if (item.checked) {
          arr1.push(item.id);
        }
      }
      return item;
    });
    that.cartList.valid = newValid;
    if (that.footerswitch) {
      that.isAllSelect = newValid.length === arr1.length + arr2.length;
    } else {
      that.isAllSelect = newValid.length === arr1.length;
    }
    that.selectValue = arr1;
    that.switchSelect();
  }), (0, _defineProperty2.default)(_methods, "unsetCart", function unsetCart() {
    var that = this,
      ids = [];
    for (var i = 0, len = that.cartList.invalid.length; i < len; i++) {
      ids.push(that.cartList.invalid[i].id);
    }
    (0, _order.cartDel)(ids).then(function (res) {
      that.$util.Tips({
        title: '清除成功'
      });
      that.$set(that.cartList, 'invalid', []);
      that.getHostProduct();
    }).catch(function (res) {});
  }), (0, _defineProperty2.default)(_methods, "shareApi", function shareApi() {
    var _this4 = this;
    (0, _public.getShare)().then(function (res) {
      _this4.$set(_this4, 'configApi', res.data);
    });
  }), (0, _defineProperty2.default)(_methods, "setOpenShare", function setOpenShare(data) {
    var that = this;
    if (that.$wechat.isWeixin()) {
      var configAppMessage = {
        desc: data.synopsis,
        title: data.title,
        link: location.href,
        imgUrl: data.img
      };
      that.$wechat.wechatEvevt(["updateAppMessageShareData", "updateTimelineShareData"], configAppMessage);
    }
  }), (0, _defineProperty2.default)(_methods, "returns", function returns() {
    uni.switchTab({
      url: '/pages/index/index'
    });
  }), (0, _defineProperty2.default)(_methods, "showNav", function showNav() {
    this.currentPage = !this.currentPage;
  }), (0, _defineProperty2.default)(_methods, "linkPage", function linkPage(url) {
    if (url == '/pages/index/index' || url == '/pages/user/index') {
      uni.switchTab({
        url: url
      });
    } else {
      uni.navigateTo({
        animationType: _animationType.default.type,
        animationDuration: _animationType.default.duration,
        url: url
      });
    }
    this.currentPage = false;
  }), (0, _defineProperty2.default)(_methods, "touchStart", function touchStart() {
    this.currentPage = false;
  }), _methods),
  onReachBottom: function onReachBottom() {
    var that = this;
    if (that.loadend) {
      that.getInvalidList();
    }
    if (that.cartList.valid.length == 0 && that.cartList.invalid.length == 0 && this.hotPage != 1) {
      that.getHostProduct();
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 160:
/*!***********************************************************************************************************************************************!*\
  !*** E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/order_addcart/order_addcart.vue?vue&type=style&index=0&id=a5aa7f30&scoped=true&lang=scss& ***!
  \***********************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_order_addcart_vue_vue_type_style_index_0_id_a5aa7f30_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order_addcart.vue?vue&type=style&index=0&id=a5aa7f30&scoped=true&lang=scss& */ 161);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_order_addcart_vue_vue_type_style_index_0_id_a5aa7f30_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_order_addcart_vue_vue_type_style_index_0_id_a5aa7f30_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_order_addcart_vue_vue_type_style_index_0_id_a5aa7f30_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_order_addcart_vue_vue_type_style_index_0_id_a5aa7f30_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_order_addcart_vue_vue_type_style_index_0_id_a5aa7f30_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 161:
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/order_addcart/order_addcart.vue?vue&type=style&index=0&id=a5aa7f30&scoped=true&lang=scss& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[154,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/order_addcart/order_addcart.js.map