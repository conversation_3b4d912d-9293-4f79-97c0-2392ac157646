@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
 .quality-recommend {
  background-color: #f5f5f5;
}
.saleBox {
  width: 100%;
  height: 298rpx;
  height: 300rpx;
  background-color: theme;
  border-radius: 0 0 50rpx 50rpx;
}
[data-theme="theme1"] .saleBox {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .saleBox {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .saleBox {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .saleBox {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .saleBox {
  background-color: #FF448F !important;
}
.quality-recommend .header {
  width: 710rpx;
  height: 330rpx;
  margin: -276rpx auto 0 auto;
  border-radius: 14rpx;
  overflow: hidden;
}
.quality-recommend .header swiper {
  height: 330rpx !important;
  border-radius: 14rpx;
  overflow: hidden;
}
.quality-recommend .header image {
  width: 100%;
  height: 100%;
  border-radius: 14rpx;
  overflow: hidden;
}
.quality-recommend .header image ._img {
  border-radius: 14rpx;
}
.quality-recommend .wrapper .list {
  width: 690rpx;
  border-radius: 20rpx;
  background-color: #fff;
  margin: 0rpx auto 0 auto;
  padding: 20rpx 20rpx 30rpx;
  box-sizing: border-box;
}
.quality-recommend .wrapper .list .item {
  background: #fff;
  margin-top: 26rpx;
}
.quality-recommend .wrapper .list .item .img_box {
  width: 180rpx;
  height: 180rpx;
  background: #F3F3F3;
  position: relative;
}
.quality-recommend .wrapper .list .item .img_box .pictrue {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}
.quality-recommend .wrapper .list .item .img_box .rank_bdg {
  width: 100%;
  height: 46rpx;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  text-align: center;
  color: #fff;
  font-size: 24rpx;
  line-height: 46rpx;
}
.quality-recommend .wrapper .list .item .ml_11 {
  margin-left: 22rpx;
  border-bottom: 1px solid #eee;
  padding-bottom: 20rpx;
}
.quality-recommend .wrapper .list .item .goods_name {
  width: 420rpx;
  height: 80rpx;
  font-size: 30rpx;
  font-weight: 400;
  color: #333333;
  line-height: 40rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: wrap;
}
.quality-recommend .title {
  height: 120rpx;
  font-size: 32rpx;
  color: #282828;
  background-color: #f5f5f5;
}
.quality-recommend .title .name {
  margin: 0 20rpx;
}
.quality-recommend .title .name .iconfont {
  margin-right: 10rpx;
}
.quality-recommend .title .line {
  width: 230rpx;
  height: 2rpx;
  background-color: #e9e9e9;
}
.price {
  margin-top: 60rpx;
  font-size: 34rpx;
  font-weight: 600;
  color: theme;
}
[data-theme="theme1"] .price {
  color: #F93323 !important;
}
[data-theme="theme2"] .price {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .price {
  color: #FF7600 !important;
}
[data-theme="theme4"] .price {
  color: #FD502F !important;
}
[data-theme="theme5"] .price {
  color: #FF448F !important;
}
.price .price_bdg {
  font-size: 26rpx;
}
.price .otPrice {
  font-size: 24rpx;
  color: #999999;
  font-weight: 400;
  padding-left: 12rpx;
  text-decoration: line-through;
}
.price .cart_icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: theme;
  text-align: center;
  line-height: 40rpx;
}
[data-theme="theme1"] .price .cart_icon {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .price .cart_icon {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .price .cart_icon {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .price .cart_icon {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .price .cart_icon {
  background-color: #FF448F !important;
}
.price .cart_icon .iconfont {
  font-size: 28rpx;
  font-weight: 400;
  color: #fff;
}
.txt-bar {
  padding: 20rpx 0;
  text-align: center;
  font-size: 26rpx;
  color: #666;
  background-color: #f5f5f5;
}

