<view data-theme="{{theme}}" class="data-v-1620faf6"><skeleton vue-id="adfd61be-1" show="{{showSkeleton}}" isNodes="{{isNodes}}" loading="chiaroscuro" selector="skeleton" bgcolor="#FFF" data-ref="skeleton" class="data-v-1620faf6 vue-ref" bind:__l="__l"></skeleton><view class="skeleton data-v-1620faf6" style="{{'visibility:'+(showSkeleton?'hidden':'visible')+';'}}"><view class="{{['navbar','data-v-1620faf6',opacity>0.6?'bgwhite':'']}}"><view class="navbarH data-v-1620faf6" style="{{('height:'+navH+'rpx;')}}"><view class="navbarCon acea-row data-v-1620faf6" style="{{'padding-right:'+(navbarRight+'px')+';'}}"><view class="select_nav flex justify-center align-center data-v-1620faf6" style="{{'top:'+(homeTop+'rpx')+';'}}" id="home"><text data-event-opts="{{[['tap',[['returns',['$event']]]]]}}" class="iconfont icon-fanhui2 px-20 data-v-1620faf6" bindtap="__e"></text><text data-event-opts="{{[['tap',[['showNav',['$event']]]]]}}" class="iconfont icon-gengduo5 px-20 data-v-1620faf6" bindtap="__e"></text><text class="nav_line data-v-1620faf6"></text></view><view class="header flex justify-between align-center data-v-1620faf6"><block wx:for="{{navList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['tap',['$0',index],[[['navList','',index]]]]]]]}}" class="{{['item','data-v-1620faf6',navActive===index?'on':'']}}" bindtap="__e">{{''+item+''}}</view></block></view></view></view></view><view hidden="{{!(currentPage)}}" class="dialog_nav data-v-1620faf6" style="{{('top:'+navH+'rpx;')}}"><block wx:for="{{selectNavList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['linkPage',['$0'],[[['selectNavList','',index,'url']]]]]]]}}" class="{{['dialog_nav_item','data-v-1620faf6',item.after]}}" bindtap="__e"><text class="{{['iconfont','data-v-1620faf6',item.icon]}}"></text><text class="pl-20 data-v-1620faf6">{{item.name}}</text></view></block></view><view class="product-con data-v-1620faf6"><scroll-view style="{{('height:'+height+'px;')}}" scroll-top="{{scrollTop}}" scroll-y="true" scroll-with-animation="true" data-event-opts="{{[['scroll',[['scroll',['$event']]]]]}}" bindscroll="__e" class="data-v-1620faf6"><view id="past0" class="data-v-1620faf6"><product-con-swiper class="skeleton-rect data-v-1620faf6" vue-id="adfd61be-2" imgUrls="{{imgUrls}}" bind:__l="__l"></product-con-swiper><view class="nav acea-row row-between-wrapper mb30 skeleton-rect data-v-1620faf6"><view class="money data-v-1620faf6">￥<text class="num data-v-1620faf6">{{storeInfo.price}}</text><text class="y-money data-v-1620faf6">{{"￥"+storeInfo.otPrice}}</text></view><view class="acea-row row-middle data-v-1620faf6"><block wx:if="{{status==2}}"><view class="time flex data-v-1620faf6"><view class="data-v-1620faf6"><text class="iconfont icon-miaosha1 data-v-1620faf6"></text><text class="pl-06 data-v-1620faf6" style="font-size:26rpx;">距结束:</text></view><count-down vue-id="adfd61be-3" bgColor="{{bgColor}}" is-day="{{false}}" tip-text=" " day-text=" " hour-text=" : " minute-text=" : " second-text=" " datatime="{{datatime}}" class="data-v-1620faf6" bind:__l="__l"></count-down></view></block></view></view><view class="pad30 mb30 data-v-1620faf6"><view class="wrapper borRadius14 mb30 data-v-1620faf6"><view class="introduce acea-row row-between data-v-1620faf6"><view class="infor skeleton-rect data-v-1620faf6">{{''+storeInfo.storeName}}</view><view data-event-opts="{{[['tap',[['listenerActionSheet',['$event']]]]]}}" class="iconfont icon-fenxiang data-v-1620faf6" bindtap="__e"></view></view><view class="label acea-row row-middle data-v-1620faf6"><view class="stock skeleton-rect data-v-1620faf6">{{"累计销售："+$root.m0+storeInfo.unitName}}</view><view class="skeleton-rect data-v-1620faf6">{{"限量: "+(attribute.productSelect.quota?attribute.productSelect.quota:0)+" "+storeInfo.unitName}}</view></view></view><view data-event-opts="{{[['tap',[['selecAttr',['$event']]]]]}}" class="attribute mb30 borRadius14 data-v-1620faf6" bindtap="__e"><view class="acea-row row-between-wrapper data-v-1620faf6"><view class="line1 skeleton-rect data-v-1620faf6">{{attrTxt+'：'}}<text class="atterTxt data-v-1620faf6">{{attrValue}}</text></view><view class="iconfont icon-jiantou data-v-1620faf6"></view></view><block wx:if="{{$root.g0>1}}"><view class="acea-row row-between-wrapper data-v-1620faf6" style="margin-top:7px;padding-left:55px;"><view class="flex data-v-1620faf6"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><image class="attrImg data-v-1620faf6" src="{{item.image}}"></image></block></view><view class="switchTxt data-v-1620faf6">{{"共"+$root.g1+"种规格可选"}}</view></view></block></view><view class="userEvaluation data-v-1620faf6" id="past1"><view class="title acea-row row-between-wrapper data-v-1620faf6" style="{{(replyCount==0?'border-bottom-left-radius:14rpx;border-bottom-right-radius:14rpx;':'')}}"><view class="data-v-1620faf6">{{"用户评价("+replyCount+")"}}</view><navigator class="praise data-v-1620faf6" hover-class="none" url="{{'/pages/users/goods_comment_list/index?productId='+storeInfo.productId}}"><text class="font_color data-v-1620faf6">{{replyChance+"%"}}</text>好评率<text class="iconfont icon-jiantou data-v-1620faf6"></text></navigator></view><block wx:if="{{replyCount}}"><user-evaluation vue-id="adfd61be-4" reply="{{reply}}" class="data-v-1620faf6" bind:__l="__l"></user-evaluation></block></view></view></view><view class="product-intro data-v-1620faf6" id="past2"><view class="title data-v-1620faf6"><image src="../../../static/images/xzuo.png" class="data-v-1620faf6"></image><label class="sp _span data-v-1620faf6">产品详情</label><image src="../../../static/images/xyou.png" class="data-v-1620faf6"></image></view><view class="conter data-v-1620faf6"><jyf-parser vue-id="adfd61be-5" html="{{storeInfo.content}}" tag-style="{{tagStyle}}" data-ref="article" class="data-v-1620faf6 vue-ref" bind:__l="__l"></jyf-parser></view></view><view style="height:120rpx;" class="data-v-1620faf6"></view></scroll-view><view class="footer acea-row row-between-wrapper data-v-1620faf6"><block wx:if="{{chatConfig.telephone_service_switch==='true'}}"><button class="item skeleton-rect data-v-1620faf6" hover-class="none" data-event-opts="{{[['tap',[['kefuClick',['$event']]]]]}}" bindtap="__e"><view class="iconfont icon-kefu data-v-1620faf6"></view><view class="data-v-1620faf6">客服</view></button></block><block wx:else><button class="item skeleton-rect data-v-1620faf6" open-type="contact" hover-class="none"><view class="iconfont icon-kefu data-v-1620faf6"></view><view class="data-v-1620faf6">客服</view></button></block><view data-event-opts="{{[['tap',[['setCollect',['$event']]]]]}}" class="item skeleton-rect data-v-1620faf6" bindtap="__e"><block wx:if="{{userCollect}}"><view class="iconfont icon-shoucang1 data-v-1620faf6"></view></block><block wx:else><view class="iconfont icon-shoucang data-v-1620faf6"></view></block><view class="data-v-1620faf6">收藏</view></view><block wx:if="{{dataShow==0}}"><view class="bnt acea-row skeleton-rect data-v-1620faf6"><block wx:if="{{masterStatus!=='soldOut'}}"><view data-event-opts="{{[['tap',[['openAlone',['$event']]]]]}}" class="joinCart bnts data-v-1620faf6" bindtap="__e">单独购买</view></block><block wx:if="{{masterStatus=='soldOut'}}"><view class="bg-color-hui bnts radius data-v-1620faf6">单独购买</view></block><view class="buy bnts data-v-1620faf6">立即购买</view></view></block><block wx:if="{{$root.g2}}"><view class="bnt acea-row skeleton-rect data-v-1620faf6"><block wx:if="{{masterStatus!=='soldOut'}}"><view data-event-opts="{{[['tap',[['openAlone',['$event']]]]]}}" class="joinCart bnts data-v-1620faf6" bindtap="__e">单独购买</view></block><block wx:if="{{masterStatus=='soldOut'}}"><view class="bg-color-hui bnts radius data-v-1620faf6">单独购买</view></block><view data-event-opts="{{[['tap',[['goCat',['$event']]]]]}}" class="buy bnts data-v-1620faf6" bindtap="__e">立即购买</view></view></block><block wx:if="{{$root.g3}}"><view class="bnt acea-row skeleton-rect data-v-1620faf6"><block wx:if="{{masterStatus!=='soldOut'}}"><view data-event-opts="{{[['tap',[['openAlone',['$event']]]]]}}" class="joinCart bnts data-v-1620faf6" bindtap="__e">单独购买</view></block><block wx:if="{{masterStatus=='soldOut'}}"><view class="bg-color-hui bnts radius data-v-1620faf6">单独购买</view></block><view class="bnts bg-color-hui data-v-1620faf6">已售罄</view></view></block><block wx:if="{{status==0}}"><view class="bnt acea-row skeleton-rect data-v-1620faf6"><block wx:if="{{masterStatus!=='soldOut'}}"><view data-event-opts="{{[['tap',[['openAlone',['$event']]]]]}}" class="joinCart bnts data-v-1620faf6" bindtap="__e">单独购买</view></block><block wx:if="{{masterStatus=='soldOut'}}"><view class="bg-color-hui bnts radius data-v-1620faf6">单独购买</view></block><view class="buy bnts bg-color-hui data-v-1620faf6">已关闭</view></view></block><block wx:if="{{status==1}}"><view class="bnt acea-row skeleton-rect data-v-1620faf6"><block wx:if="{{masterStatus!=='soldOut'}}"><view data-event-opts="{{[['tap',[['openAlone',['$event']]]]]}}" class="joinCart bnts data-v-1620faf6" bindtap="__e">单独购买</view></block><block wx:if="{{masterStatus=='soldOut'}}"><view class="bg-color-hui bnts radius data-v-1620faf6">单独购买</view></block><view class="buy bnts bg-color-hui data-v-1620faf6">未开始</view></view></block><block wx:if="{{$root.g4}}"><view class="bnt acea-row skeleton-rect data-v-1620faf6"><block wx:if="{{masterStatus!=='soldOut'}}"><view data-event-opts="{{[['tap',[['openAlone',['$event']]]]]}}" class="joinCart bnts data-v-1620faf6" bindtap="__e">单独购买</view></block><block wx:if="{{masterStatus=='soldOut'}}"><view class="bg-color-hui bnts radius data-v-1620faf6">单独购买</view></block><view class="buy bnts bg-color-hui data-v-1620faf6">已结束</view></view></block></view></view><product-window vue-id="adfd61be-6" attr="{{attribute}}" limitNum="{{1}}" data-event-opts="{{[['^myevent',[['onMyEvent']]],['^ChangeAttr',[['ChangeAttr']]],['^ChangeCartNum',[['ChangeCartNum']]],['^attrVal',[['attrVal']]],['^iptCartNum',[['iptCartNum']]],['^getImg',[['showImg']]]]}}" bind:myevent="__e" bind:ChangeAttr="__e" bind:ChangeCartNum="__e" bind:attrVal="__e" bind:iptCartNum="__e" bind:getImg="__e" class="data-v-1620faf6" bind:__l="__l"></product-window><view class="{{['generate-posters','data-v-1620faf6',posters?'on':'']}}"><view class="generateCon acea-row row-middle data-v-1620faf6"><button class="item data-v-1620faf6" open-type="share" hover-class="none"><view class="pictrue data-v-1620faf6"><image src="/static/images/weixin.png" class="data-v-1620faf6"></image></view><view class="data-v-1620faf6">分享给好友</view></button><view data-event-opts="{{[['tap',[['getpreviewImage',['$event']]]]]}}" class="item data-v-1620faf6" bindtap="__e"><view class="pictrue data-v-1620faf6"><image src="/static/images/changan.png" class="data-v-1620faf6"></image></view><view class="data-v-1620faf6">预览发图</view></view><button class="item data-v-1620faf6" hover-class="none" data-event-opts="{{[['tap',[['savePosterPath',['$event']]]]]}}" bindtap="__e"><view class="pictrue data-v-1620faf6"><image src="/static/images/haibao.png" class="data-v-1620faf6"></image></view><view class="data-v-1620faf6">保存海报</view></button></view><view data-event-opts="{{[['tap',[['posterImageClose',['$event']]]]]}}" class="generateClose acea-row row-center-wrapper data-v-1620faf6" bindtap="__e">取消</view></view><cus-preview-img vue-id="adfd61be-7" list="{{skuArr}}" data-ref="cusPreviewImg" data-event-opts="{{[['^changeSwitch',[['changeSwitch']]]]}}" bind:changeSwitch="__e" class="data-v-1620faf6 vue-ref" bind:__l="__l"></cus-preview-img><block wx:if="{{posters}}"><view data-event-opts="{{[['tap',[['closePosters',['$event']]]]]}}" class="mask data-v-1620faf6" bindtap="__e"></view></block><block wx:if="{{canvasStatus}}"><view data-event-opts="{{[['tap',[['listenerActionClose',['$event']]]]]}}" class="mask data-v-1620faf6" bindtap="__e"></view></block><block wx:if="{{currentPage}}"><view data-event-opts="{{[['touchmove',[['hideNav',['$event']]]],['tap',[['hideNav']]]]}}" class="mask_transparent data-v-1620faf6" bindtouchmove="__e" bindtap="__e"></view></block><block wx:if="{{canvasStatus}}"><view class="poster-pop data-v-1620faf6"><image src="{{posterImage}}" class="data-v-1620faf6"></image></view></block><block wx:else><view class="canvas data-v-1620faf6"><canvas style="width:750px;height:1190px;" canvas-id="firstCanvas" class="data-v-1620faf6"></canvas><canvas style="{{'width:'+(qrcodeSize+'px')+';'+('height:'+(qrcodeSize+'px')+';')}}" canvas-id="qrcode" class="data-v-1620faf6"></canvas></view></block><block wx:if="{{H5ShareBox}}"><view class="share-box data-v-1620faf6"><image src="/static/images/share-info.png" data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" bindtap="__e" class="data-v-1620faf6"></image></view></block></view></view>