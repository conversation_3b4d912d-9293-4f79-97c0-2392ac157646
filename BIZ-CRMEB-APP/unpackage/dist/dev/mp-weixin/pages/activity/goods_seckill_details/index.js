(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/activity/goods_seckill_details/index"],{

/***/ 605:
/*!*******************************************************************************************************************!*\
  !*** E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/main.js?{"page":"pages%2Factivity%2Fgoods_seckill_details%2Findex"} ***!
  \*******************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _index = _interopRequireDefault(__webpack_require__(/*! ./pages/activity/goods_seckill_details/index.vue */ 606));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_index.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 606:
/*!**********************************************************************************************!*\
  !*** E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_seckill_details/index.vue ***!
  \**********************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _index_vue_vue_type_template_id_1620faf6_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.vue?vue&type=template&id=1620faf6&scoped=true& */ 607);
/* harmony import */ var _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.vue?vue&type=script&lang=js& */ 609);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _index_vue_vue_type_style_index_0_id_1620faf6_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.vue?vue&type=style&index=0&id=1620faf6&scoped=true&lang=scss& */ 611);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 52);

var renderjs





/* normalize component */

var component = Object(_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _index_vue_vue_type_template_id_1620faf6_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _index_vue_vue_type_template_id_1620faf6_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "1620faf6",
  null,
  false,
  _index_vue_vue_type_template_id_1620faf6_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/activity/goods_seckill_details/index.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 607:
/*!*****************************************************************************************************************************************!*\
  !*** E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_seckill_details/index.vue?vue&type=template&id=1620faf6&scoped=true& ***!
  \*****************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_1620faf6_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=1620faf6&scoped=true& */ 608);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_1620faf6_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_1620faf6_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_1620faf6_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_1620faf6_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 608:
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_seckill_details/index.vue?vue&type=template&id=1620faf6&scoped=true& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    jyfParser: function () {
      return Promise.all(/*! import() | components/jyf-parser/jyf-parser */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/jyf-parser/jyf-parser")]).then(__webpack_require__.bind(null, /*! @/components/jyf-parser/jyf-parser.vue */ 730))
    },
    cusPreviewImg: function () {
      return __webpack_require__.e(/*! import() | components/cus-previewImg/cus-previewImg */ "components/cus-previewImg/cus-previewImg").then(__webpack_require__.bind(null, /*! @/components/cus-previewImg/cus-previewImg.vue */ 740))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var m0 =
    parseFloat(_vm.storeInfo.sales) + parseFloat(_vm.storeInfo.ficti) || 0
  var g0 = _vm.skuArr.length
  var l0 = g0 > 1 ? _vm.skuArr.slice(0, 4) : null
  var g1 = g0 > 1 ? _vm.skuArr.length : null
  var g2 =
    _vm.status == 2 &&
    _vm.attribute.productSelect.quota > 0 &&
    _vm.datatime > new Date().getTime() / 1000
  var g3 =
    _vm.status == 2 &&
    _vm.attribute.productSelect.quota <= 0 &&
    _vm.datatime > new Date().getTime() / 1000
  var g4 = _vm.status == 2 && new Date().getTime() / 1000 - _vm.datatime >= 0
  if (!_vm._isMounted) {
    _vm.e0 = function ($event) {
      _vm.H5ShareBox = false
    }
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        m0: m0,
        g0: g0,
        l0: l0,
        g1: g1,
        g2: g2,
        g3: g3,
        g4: g4,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 609:
/*!***********************************************************************************************************************!*\
  !*** E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_seckill_details/index.vue?vue&type=script&lang=js& ***!
  \***********************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js& */ 610);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_D_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 610:
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_seckill_details/index.vue?vue&type=script&lang=js& ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni, wx) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _uqrcode = _interopRequireDefault(__webpack_require__(/*! @/js_sdk/Sansnn-uQRCode/uqrcode.js */ 176));
var _app = __webpack_require__(/*! @/config/app.js */ 40);
var _vuex = __webpack_require__(/*! vuex */ 35);
var _activity = __webpack_require__(/*! @/api/activity.js */ 122);
var _store = __webpack_require__(/*! @/api/store.js */ 68);
var _base64src = __webpack_require__(/*! @/utils/base64src.js */ 177);
var _api = __webpack_require__(/*! @/api/api.js */ 49);
var _public = __webpack_require__(/*! @/api/public */ 47);
var _login = __webpack_require__(/*! @/libs/login.js */ 33);
var _utils = __webpack_require__(/*! @/utils */ 148);
var _user = __webpack_require__(/*! @/api/user */ 38);
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

var app = getApp();
var productConSwiper = function productConSwiper() {
  Promise.all(/*! require.ensure | components/productConSwiper/index */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/productConSwiper/index")]).then((function () {
    return resolve(__webpack_require__(/*! @/components/productConSwiper/index.vue */ 747));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var productWindow = function productWindow() {
  __webpack_require__.e(/*! require.ensure | components/productWindow/index */ "components/productWindow/index").then((function () {
    return resolve(__webpack_require__(/*! @/components/productWindow/index.vue */ 653));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var userEvaluation = function userEvaluation() {
  __webpack_require__.e(/*! require.ensure | components/userEvaluation/index */ "components/userEvaluation/index").then((function () {
    return resolve(__webpack_require__(/*! @/components/userEvaluation/index.vue */ 761));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var cusPreviewImg = function cusPreviewImg() {
  __webpack_require__.e(/*! require.ensure | components/cus-previewImg/cus-previewImg */ "components/cus-previewImg/cus-previewImg").then((function () {
    return resolve(__webpack_require__(/*! @/components/cus-previewImg/cus-previewImg.vue */ 740));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var authorize = function authorize() {
  Promise.all(/*! require.ensure | components/Authorize */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/Authorize")]).then((function () {
    return resolve(__webpack_require__(/*! @/components/Authorize */ 723));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var parser = function parser() {
  Promise.all(/*! require.ensure | components/jyf-parser/jyf-parser */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/jyf-parser/jyf-parser")]).then((function () {
    return resolve(__webpack_require__(/*! @/components/jyf-parser/jyf-parser */ 730));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var home = function home() {
  __webpack_require__.e(/*! require.ensure | components/home/<USER>/ "components/home/<USER>").then((function () {
    return resolve(__webpack_require__(/*! @/components/home/<USER>/ 646));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var countDown = function countDown() {
  __webpack_require__.e(/*! require.ensure | components/countDown/index */ "components/countDown/index").then((function () {
    return resolve(__webpack_require__(/*! @/components/countDown */ 709));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var shareRedPackets = function shareRedPackets() {
  __webpack_require__.e(/*! require.ensure | components/shareRedPackets/index */ "components/shareRedPackets/index").then((function () {
    return resolve(__webpack_require__(/*! @/components/shareRedPackets */ 768));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var _default = {
  data: function data() {
    return {
      showSkeleton: true,
      //骨架屏显示隐藏
      isNodes: 0,
      //控制什么时候开始抓取元素节点,只要数值改变就重新抓取
      bgColor: {
        'bgColor': '#333333',
        'Color': '#fff',
        'isDay': true,
        'width': '44rpx',
        'timeTxtwidth': '16rpx'
      },
      dataShow: 0,
      id: 0,
      time: 0,
      countDownHour: "00",
      countDownMinute: "00",
      countDownSecond: "00",
      storeInfo: {},
      imgUrls: [],
      parameter: {
        'navbar': '1',
        'return': '1',
        'title': '抢购详情页',
        'color': false
      },
      attribute: {
        cartAttr: false,
        productAttr: [],
        productSelect: {}
      },
      productValue: [],
      isOpen: false,
      attr: '请选择',
      attrValue: '',
      status: 1,
      isAuto: false,
      isShowAuth: false,
      iShidden: false,
      limitNum: 1,
      //限制本属性产品的个数；
      personNum: 0,
      //限制用户购买的个数；
      iSplus: false,
      replyCount: 0,
      //总评论数量
      reply: [],
      //评论列表
      replyChance: 0,
      navH: "",
      navList: ['商品', '评价', '详情'],
      opacity: 0,
      scrollY: 0,
      topArr: [],
      toView: '',
      height: 0,
      heightArr: [],
      lock: false,
      scrollTop: 0,
      tagStyle: {
        img: 'width:100%;display:block;',
        table: 'width:100%',
        video: 'width:100%'
      },
      datatime: 0,
      navActive: 0,
      meunHeight: 0,
      backH: '',
      posters: false,
      weixinStatus: false,
      posterImageStatus: false,
      canvasStatus: false,
      //海报绘图标签
      storeImage: '',
      //海报产品图
      PromotionCode: '',
      //二维码图片
      posterImage: '',
      //海报路径
      posterbackgd: '/static/images/posterbackgd.png',
      actionSheetHidden: false,
      cart_num: '',
      attrTxt: '',
      qrcodeSize: 600,
      imagePath: '',
      //海报路径
      imgTop: '',
      H5ShareBox: false,
      //公众号分享图片
      sharePacket: {
        isState: true //默认不显示
      },

      buyNum: 1,
      errT: '',
      returnShow: true,
      homeTop: 20,
      navbarRight: 0,
      userCollect: false,
      theme: app.globalData.theme,
      skuArr: [],
      currentPage: false,
      selectSku: {},
      selectNavList: [{
        name: '首页',
        icon: 'icon-shouye8',
        url: '/pages/index/index',
        after: 'dialog_after'
      }, {
        name: '搜索',
        icon: 'icon-sousuo6',
        url: '/pages/goods_search/index',
        after: 'dialog_after'
      }, {
        name: '购物车',
        icon: 'icon-gouwuche7',
        url: '/pages/order_addcart/order_addcart',
        after: 'dialog_after'
      }, {
        name: '我的收藏',
        icon: 'icon-shoucang3',
        url: '/pages/users/user_goods_collection/index',
        after: 'dialog_after'
      }, {
        name: '个人中心',
        icon: 'icon-gerenzhongxin1',
        url: '/pages/user/index'
      }],
      chatConfig: {
        consumer_hotline: '',
        telephone_service_switch: 'false'
      },
      //客服配置
      masterStatus: ''
    };
  },
  components: {
    shareRedPackets: shareRedPackets,
    productConSwiper: productConSwiper,
    'productWindow': productWindow,
    userEvaluation: userEvaluation,
    cusPreviewImg: cusPreviewImg,
    "jyf-parser": parser,
    home: home,
    countDown: countDown,
    authorize: authorize
  },
  computed: (0, _vuex.mapGetters)(['isLogin', 'uid', 'chatUrl']),
  watch: {
    isLogin: {
      handler: function handler(newV, oldV) {
        if (newV) {
          this.getSeckillDetail();
        }
      },
      deep: true
    }
  },
  onLoad: function onLoad(options) {
    var _this = this;
    setTimeout(function () {
      _this.isNodes++;
    }, 500);
    var that = this;
    that.$store.commit("PRODUCT_TYPE", 'normal');
    var statusBarHeight = '';
    var pages = getCurrentPages();
    that.$set(that, 'chatConfig', that.$Cache.getItem('chatConfig'));
    //that.returnShow = pages.length === 1 ? false : true;
    //设置商品列表高度
    uni.getSystemInfo({
      success: function success(res) {
        that.height = res.windowHeight;
        statusBarHeight = res.statusBarHeight;
        //res.windowHeight:获取整个窗口高度为px，*2为rpx；98为头部占据的高度；
      }
    });

    this.navH = app.globalData.navHeight;
    var menuButtonInfo = uni.getMenuButtonBoundingClientRect();
    this.meunHeight = menuButtonInfo.height;
    this.backH = that.navH / 2 + this.meunHeight / 2;
    that.$set(that, 'theme', that.$Cache.get('theme')); //用户从分享卡片进入的场景下获取主题色配置

    if (!options.scene && !options.id) {
      this.showSkeleton = false;
      this.$util.Tips({
        title: '缺少参数无法查看商品'
      }, {
        url: '/pages/index/index'
      });
      return;
    }
    if (options.hasOwnProperty('id') || options.scene) {
      if (options.scene) {
        // 仅仅小程序扫码进入
        var qrCodeValue = this.$util.getUrlParams(decodeURIComponent(options.scene));
        var mapeMpQrCodeValue = this.$util.formatMpQrCodeData(qrCodeValue);
        app.globalData.spread = mapeMpQrCodeValue.spread;
        this.id = mapeMpQrCodeValue.id;
        //    setTimeout(()=>{
        //    	spread(mapeMpQrCodeValue.spread).then(res => {}).catch(res => {})
        //    },2000)
      } else {
        this.id = options.id;
      }
    }
    if (options.spread) app.globalData.spread = options.spread;
    if (this.isLogin) {
      this.getSeckillDetail();
    } else {
      this.$Cache.set('login_back_url',  true ? app.globalData.spread : undefined);
      (0, _login.toLogin)();
    }
    this.$nextTick(function () {
      var menuButton = uni.getMenuButtonBoundingClientRect();
      var query = uni.createSelectorQuery().in(_this);
      query.select('#home').boundingClientRect(function (data) {
        _this.homeTop = menuButton.top * 2 + menuButton.height - data.height;
      }).exec();
    });
    if (this.isLogin && app.globalData.spread) {
      (0, _utils.silenceBindingSpread)();
    }
  },
  methods: {
    // app分享
    kefuClick: function kefuClick() {
      if (this.chatConfig.telephone_service_switch === 'true') {
        uni.makePhoneCall({
          phoneNumber: this.chatConfig.consumer_hotline //仅为示例
        });
      } else {
        location.href = this.chatUrl;
      }
    },
    closePosters: function closePosters() {
      this.posters = false;
    },
    getProductReplyList: function getProductReplyList() {
      var _this2 = this;
      (0, _store.getReplyList)(this.storeInfo.productId, {
        page: 1,
        limit: 3,
        type: 0
      }).then(function (res) {
        _this2.reply = res.data.list ? [res.data.list[0]] : [];
      });
    },
    getProductReplyCount: function getProductReplyCount() {
      var that = this;
      (0, _store.getReplyConfig)(that.storeInfo.productId).then(function (res) {
        that.$set(that, 'replyChance', res.data.replyChance * 100);
        that.$set(that, 'replyCount', res.data.sumCount);
      });
    },
    /**
     * 购物车手动填写
     * 
    */
    iptCartNum: function iptCartNum(e) {
      this.$set(this.attribute.productSelect, 'cart_num', e ? e : 1);
      this.$set(this, "cart_num", e);
      if (e > 1) {
        return this.$util.Tips({
          title: "\u8BE5\u5546\u54C1\u6BCF\u6B21\u9650\u8D2D1".concat(this.storeInfo.unitName)
        });
      }
    },
    // 后退
    returns: function returns() {
      uni.navigateBack();
    },
    onLoadFun: function onLoadFun(data) {
      if (this.isAuto) {
        this.isAuto = false;
        this.isShowAuth = false;
        this.getSeckillDetail();
      }
    },
    getSeckillDetail: function getSeckillDetail() {
      var _this3 = this;
      var that = this;
      (0, _activity.getSeckillDetail)(that.id).then(function (res) {
        _this3.dataShow = 1;
        _this3.masterStatus = res.data.masterStatus;
        _this3.storeInfo = res.data.storeSeckill;
        _this3.userCollect = res.data.userCollect;
        _this3.status = _this3.storeInfo.seckillStatus;
        _this3.datatime = Number(_this3.storeInfo.timeSwap);
        _this3.imgUrls = JSON.parse(res.data.storeSeckill.sliderImage) || [];
        _this3.attribute.productAttr = res.data.productAttr;
        _this3.productValue = res.data.productValue;
        _this3.personNum = res.data.storeSeckill.quota;
        _this3.attribute.productSelect.num = res.data.storeSeckill.num;
        for (var key in res.data.productValue) {
          var obj = res.data.productValue[key];
          that.skuArr.push(obj);
        }
        _this3.$set(_this3, "selectSku", that.skuArr[0]);
        _this3.getProductReplyList();
        _this3.getProductReplyCount();
        var productAttr = res.data.productAttr.map(function (item) {
          return {
            attrName: item.attrName,
            attrValues: item.attrValues.split(','),
            id: item.id,
            isDel: item.isDel,
            productId: item.productId,
            type: item.type
          };
        });
        _this3.$set(_this3.attribute, 'productAttr', productAttr);
        that.getQrcode();
        that.imgTop = res.data.storeSeckill.image;
        that.downloadFilestoreImage();
        //that.downloadFilePromotionCode();

        that.DefaultSelect();
        setTimeout(function () {
          that.infoScroll();
        }, 1000);
        app.globalData.openPages = '/pages/activity/goods_seckill_details/index?id=' + that.id + '&spread=' + that.uid;
        setTimeout(function () {
          that.showSkeleton = false;
        }, 1000);
      }).catch(function (err) {
        that.$util.Tips({
          title: err
        }, {
          tab: 3
        });
      });
    },
    setShare: function setShare() {
      this.$wechat.isWeixin() && this.$wechat.wechatEvevt(["updateAppMessageShareData", "updateTimelineShareData", "onMenuShareAppMessage", "onMenuShareTimeline"], {
        desc: this.storeInfo.storeInfo,
        title: this.storeInfo.storeName,
        link: location.href,
        imgUrl: this.storeInfo.image
      }).then(function (res) {}).catch(function (err) {
        console.log(err);
      });
    },
    /**
     * 默认选中属性
     * 
     */
    DefaultSelect: function DefaultSelect() {
      var self = this,
        productAttr = self.attribute.productAttr,
        value = [];
      for (var key in self.productValue) {
        if (self.productValue[key].quota > 0) {
          value = productAttr.length ? key.split(",") : [];
          break;
        }
      }
      for (var i = 0; i < value.length; i++) {
        self.$set(productAttr[i], "index", value[i]);
      }
      //sort();排序函数:数字-英文-汉字；
      var productSelect = this.productValue[value.join(",")];
      if (productSelect && productAttr.length) {
        self.$set(self.attribute.productSelect, "storeName", self.storeInfo.storeName);
        self.$set(self.attribute.productSelect, "image", productSelect.image);
        self.$set(self.attribute.productSelect, "price", productSelect.price);
        self.$set(self.attribute.productSelect, "stock", productSelect.stock);
        self.$set(self.attribute.productSelect, "unique", productSelect.id);
        self.$set(self.attribute.productSelect, "quota", productSelect.quota);
        self.$set(self.attribute.productSelect, "quotaShow", productSelect.quotaShow);
        self.$set(self.attribute.productSelect, "cart_num", 1);
        self.$set(self, "attrValue", value.join(","));
        this.$set(self, "attrTxt", "已选择");
        self.attrValue = value.join(",");
      } else if (!productSelect && productAttr.length) {
        self.$set(self.attribute.productSelect, "storeName", self.storeInfo.storeName);
        self.$set(self.attribute.productSelect, "image", self.storeInfo.image);
        self.$set(self.attribute.productSelect, "price", self.storeInfo.price);
        self.$set(self.attribute.productSelect, "quota", 0);
        self.$set(self.attribute.productSelect, "quota", 0);
        self.$set(self.attribute.productSelect, "stock", 0);
        self.$set(self.attribute.productSelect, "unique", "");
        self.$set(self.attribute.productSelect, "cart_num", 0);
        self.$set(self, "attrValue", "");
        self.$set(self, "attrTxt", "请选择");
      } else if (!productSelect && !productAttr.length) {
        self.$set(self.attribute.productSelect, "storeName", self.storeInfo.storeName);
        self.$set(self.attribute.productSelect, "image", self.storeInfo.image);
        self.$set(self.attribute.productSelect, "price", self.storeInfo.price);
        self.$set(self.attribute.productSelect, "quota", self.storeInfo.quota);
        self.$set(self.attribute.productSelect, "quotaShow", self.storeInfo.quotaShow);
        self.$set(self.attribute.productSelect, "stock", self.storeInfo.stock);
        self.$set(self.attribute.productSelect, "unique", "");
        self.$set(self.attribute.productSelect, "cart_num", 1);
        self.$set(self, "attrValue", "");
        self.$set(self, "attrTxt", "请选择");
      }
    },
    selecAttr: function selecAttr() {
      this.attribute.cartAttr = true;
    },
    showNav: function showNav() {
      this.currentPage = !this.currentPage;
    },
    onMyEvent: function onMyEvent() {
      this.$set(this.attribute, 'cartAttr', false);
      this.$set(this, 'isOpen', false);
    },
    /**
     * 购物车数量加和数量减
     * 
     */
    ChangeCartNum: function ChangeCartNum(changeValue) {
      //changeValue:是否 加|减
      //获取当前变动属性
      var productSelect = this.productValue[this.attrValue];
      if (this.cart_num) {
        productSelect.cart_num = this.cart_num;
        this.attribute.productSelect.cart_num = this.cart_num;
      }
      //如果没有属性,赋值给商品默认库存
      if (productSelect === undefined && !this.attribute.productAttr.length) productSelect = this.attribute.productSelect;
      //无属性值即库存为0；不存在加减；
      if (productSelect === undefined) return;
      var stock = productSelect.stock || 0;
      var quota = productSelect.quota || 0;
      var num = this.attribute.productSelect;
      var nums = this.storeInfo.num || 0;
      //设置默认数据
      if (productSelect.cart_num == undefined) productSelect.cart_num = 1;
      if (changeValue) {
        if (num.cart_num === 1) {
          return this.$util.Tips({
            title: "\u8BE5\u5546\u54C1\u6BCF\u6B21\u9650\u8D2D1".concat(this.storeInfo.unitName)
          });
        }
        num.cart_num++;
        var arrMin = [];
        arrMin.push(nums);
        arrMin.push(quota);
        arrMin.push(stock);
        var minN = Math.min.apply(null, arrMin);
        if (num.cart_num >= minN) {
          this.$set(this.attribute.productSelect, "cart_num", minN ? minN : 1);
          this.$set(this, "cart_num", minN ? minN : 1);
        }
        this.$set(this, "cart_num", num.cart_num);
        this.$set(this.attribute.productSelect, "cart_num", num.cart_num);
      } else {
        num.cart_num--;
        if (num.cart_num < 1) {
          this.$set(this.attribute.productSelect, "cart_num", 1);
          this.$set(this, "cart_num", 1);
        }
        this.$set(this, "cart_num", num.cart_num);
        this.$set(this.attribute.productSelect, "cart_num", num.cart_num);
      }
    },
    attrVal: function attrVal(val) {
      this.attribute.productAttr[val.indexw].index = this.attribute.productAttr[val.indexw].attrValues[val.indexn];
    },
    /**
     * 属性变动赋值
     * 
     */
    ChangeAttr: function ChangeAttr(res) {
      this.$set(this, 'cart_num', 1);
      var productSelect = this.productValue[res];
      this.$set(this, "selectSku", productSelect);
      if (productSelect) {
        this.$set(this.attribute.productSelect, "image", productSelect.image);
        this.$set(this.attribute.productSelect, "price", productSelect.price);
        this.$set(this.attribute.productSelect, "stock", productSelect.stock);
        this.$set(this.attribute.productSelect, "unique", productSelect.id);
        this.$set(this.attribute.productSelect, "cart_num", 1);
        this.$set(this.attribute.productSelect, "quota", productSelect.quota);
        this.$set(this.attribute.productSelect, "quotaShow", productSelect.quotaShow);
        this.$set(this, "attrValue", res);
        this.attrTxt = "已选择";
      } else {
        this.$set(this.attribute.productSelect, "image", this.storeInfo.image);
        this.$set(this.attribute.productSelect, "price", this.storeInfo.price);
        this.$set(this.attribute.productSelect, "stock", 0);
        this.$set(this.attribute.productSelect, "unique", "");
        this.$set(this.attribute.productSelect, "cart_num", 0);
        this.$set(this.attribute.productSelect, "quota", 0);
        this.$set(this.attribute.productSelect, "quotaShow", 0);
        this.$set(this, "attrValue", "");
        this.attrTxt = "已选择";
      }
    },
    scroll: function scroll(e) {
      var that = this,
        scrollY = e.detail.scrollTop;
      var opacity = scrollY / 500;
      opacity = opacity > 1 ? 1 : opacity;
      that.opacity = opacity;
      that.scrollY = scrollY;
      if (that.lock) {
        that.lock = false;
        return;
      }
      for (var i = 0; i < that.topArr.length; i++) {
        if (scrollY < that.topArr[i] - app.globalData.navHeight / 2 + that.heightArr[i]) {
          that.navActive = i;
          break;
        }
      }
      if (that.currentPage == true) {
        that.$set(that, 'currentPage', false);
      }
    },
    tap: function tap(item, index) {
      var id = item.id;
      var index = index;
      var that = this;
      // if (!this.data.good_list.length && id == "past2") {
      //   id = "past3"
      // }
      this.toView = id;
      this.navActive = index;
      this.lock = true;
      this.scrollTop = index > 0 ? that.topArr[index] - app.globalData.navHeight / 2 : that.topArr[index];
    },
    infoScroll: function infoScroll() {
      var that = this,
        topArr = [],
        heightArr = [];
      for (var i = 0; i < that.navList.length; i++) {
        //productList
        //获取元素所在位置
        var query = wx.createSelectorQuery().in(this);
        var idView = "#past" + i;
        // if (!that.data.good_list.length && i == 2) {
        //   var idView = "#past" + 3;
        // }
        query.select(idView).boundingClientRect();
        query.exec(function (res) {
          var top = res[0].top;
          var height = res[0].height;
          topArr.push(top);
          heightArr.push(height);
          that.topArr = topArr;
          that.heightArr = heightArr;
        });
      }
      ;
    },
    /**
     * 收藏商品
     */
    setCollect: function setCollect() {
      var that = this;
      if (this.userCollect) {
        (0, _store.collectDel)(this.storeInfo.productId).then(function (res) {
          that.userCollect = !that.userCollect;
        });
      } else {
        (0, _store.collectAdd)(this.storeInfo.productId).then(function (res) {
          that.userCollect = !that.userCollect;
        });
      }
    },
    /*
     *  单独购买
     */
    openAlone: function openAlone() {
      uni.navigateTo({
        url: "/pages/goods_details/index?id=".concat(this.storeInfo.productId)
      });
    },
    /*
     *  下订单
     */
    goCat: function goCat() {
      var that = this;
      var productSelect = this.productValue[this.attrValue];
      var productSelect = this.productValue[this.attrValue];
      if (that.cart_num > 1) {
        return this.$util.Tips({
          title: "\u8BE5\u5546\u54C1\u6BCF\u4EBA\u9650\u8D2D1".concat(this.storeInfo.unitName)
        });
      }
      //打开属性
      if (this.isOpen) this.attribute.cartAttr = true;else this.attribute.cartAttr = !this.attribute.cartAttr;
      //只有关闭属性弹窗时进行加入购物车
      if (this.attribute.cartAttr === true && this.isOpen == false) return this.isOpen = true;
      //如果有属性,没有选择,提示用户选择
      if (this.attribute.productAttr.length && productSelect === undefined && this.isOpen == true) return app.$util.Tips({
        title: '请选择属性'
      });
      this.$Order.getPreOrder("buyNow", [{
        "attrValueId": parseFloat(this.attribute.productSelect.unique),
        "seckillId": parseFloat(this.id),
        "productNum": parseFloat(this.cart_num ? this.cart_num : this.attribute.productSelect.cart_num),
        "productId": parseFloat(this.storeInfo.productId)
      }]);
    },
    /**
     * 分享打开
     * 
     */
    listenerActionSheet: function listenerActionSheet() {
      if (this.isLogin === false) {
        (0, _login.toLogin)();
      } else {
        this.goPoster();
        this.posters = true;
      }
    },
    // 分享关闭
    listenerActionClose: function listenerActionClose() {
      this.posters = false;
    },
    //隐藏海报
    posterImageClose: function posterImageClose() {
      this.canvasStatus = false;
      this.posters = false;
    },
    //替换安全域名
    setDomain: function setDomain(url) {
      url = url ? url.toString() : '';
      //本地调试打开,生产请注销
      if (url.indexOf("https://") > -1) return url;else return url.replace('http://', 'https://');
    },
    //获取海报产品图
    downloadFilestoreImage: function downloadFilestoreImage() {
      var that = this;
      uni.downloadFile({
        url: that.setDomain(that.storeInfo.image),
        success: function success(res) {
          that.storeImage = res.tempFilePath;
        },
        fail: function fail() {
          return that.$util.Tips({
            title: ''
          });
          that.storeImage = '';
        }
      });
    },
    /**
     * 获取产品分销二维码
     * @param function successFn 下载完成回调
     * 
     */
    downloadFilePromotionCode: function downloadFilePromotionCode(successFn) {
      var that = this;
      (0, _activity.seckillCode)(that.id, {
        stop_time: that.datatime
      }).then(function (res) {
        uni.downloadFile({
          url: that.setDomain(res.data.code),
          success: function success(res) {
            that.$set(that, 'isDown', false);
            if (typeof successFn == 'function') successFn && successFn(res.tempFilePath);else that.$set(that, 'PromotionCode', res.tempFilePath);
          },
          fail: function fail() {
            that.$set(that, 'isDown', false);
            that.$set(that, 'PromotionCode', '');
          }
        });
      }).catch(function (err) {
        that.$set(that, 'isDown', false);
        that.$set(that, 'PromotionCode', '');
      });
    },
    getImageBase64: function getImageBase64(images) {
      var that = this;
      (0, _public.imageBase64)({
        url: images
      }).then(function (res) {
        that.$set(that, 'imgTop', res.data.code);
      });
    },
    // 小程序关闭分享弹窗；
    goFriend: function goFriend() {
      this.posters = false;
    },
    /**
     * 生成海报
     */
    goPoster: function goPoster() {
      var that = this;
      uni.showLoading({
        title: '海报生成中',
        mask: true
      });
      that.posters = false;
      var arrImagesUrl = '';
      var arrImagesUrlTop = '';
      if (!that.PromotionCode) {
        uni.hideLoading();
        that.$util.Tips({
          title: that.errT
        });
        return;
      }
      setTimeout(function () {
        if (!that.imgTop) {
          uni.hideLoading();
          that.$util.Tips({
            title: '无法生成商品海报！'
          });
          return;
        }
      }, 2000);
      uni.downloadFile({
        url: that.imgTop,
        //仅为示例，并非真实的资源
        success: function success(res) {
          arrImagesUrlTop = res.tempFilePath;
          var arrImages = [that.posterbackgd, arrImagesUrlTop, that.PromotionCode];
          var storeName = that.storeInfo.storeName;
          var price = that.storeInfo.price;
          setTimeout(function () {
            that.$util.PosterCanvas(arrImages, storeName, price, that.storeInfo.otPrice, function (tempFilePath) {
              that.posterImage = tempFilePath;
              that.canvasStatus = true;
              uni.hideLoading();
            });
          }, 500);
        }
      });
    },
    // 小程序二维码
    getQrcode: function getQrcode() {
      var that = this;
      var data = {
        pid: that.uid,
        id: that.id,
        path: 'pages/activity/goods_seckill_details/index'
      };
      (0, _api.getQrcode)(data).then(function (res) {
        (0, _base64src.base64src)(res.data.code, Date.now(), function (res) {
          that.PromotionCode = res;
        });
      }).catch(function (err) {
        that.errT = err;
      });
    },
    // 生成二维码；
    make: function make() {
      var _this4 = this;
      var href = location.href.split('?')[0] + "?id=" + this.id + "&spread=" + this.uid;
      _uqrcode.default.make({
        canvasId: 'qrcode',
        text: href,
        size: this.qrcodeSize,
        margin: 10,
        success: function success(res) {
          _this4.PromotionCode = res;
        },
        complete: function complete(res) {},
        fail: function fail(res) {
          _this4.$util.Tips({
            title: '海报二维码生成失败！'
          });
        }
      });
    },
    // 图片预览；
    getpreviewImage: function getpreviewImage() {
      if (this.posterImage) {
        var photoList = [];
        photoList.push(this.posterImage);
        uni.previewImage({
          urls: photoList,
          current: this.posterImage
        });
      } else {
        this.$util.Tips({
          title: '您的海报尚未生成'
        });
      }
    },
    /*
     * 保存到手机相册
     */

    savePosterPath: function savePosterPath() {
      var that = this;
      uni.getSetting({
        success: function success(res) {
          if (!res.authSetting['scope.writePhotosAlbum']) {
            uni.authorize({
              scope: 'scope.writePhotosAlbum',
              success: function success() {
                uni.saveImageToPhotosAlbum({
                  filePath: that.posterImage,
                  success: function success(res) {
                    that.posterImageClose();
                    that.$util.Tips({
                      title: '保存成功',
                      icon: 'success'
                    });
                  },
                  fail: function fail(res) {
                    that.$util.Tips({
                      title: '保存失败'
                    });
                  }
                });
              }
            });
          } else {
            uni.saveImageToPhotosAlbum({
              filePath: that.posterImage,
              success: function success(res) {
                that.posterImageClose();
                that.$util.Tips({
                  title: '保存成功',
                  icon: 'success'
                });
              },
              fail: function fail(res) {
                that.$util.Tips({
                  title: '保存失败'
                });
              }
            });
          }
        }
      });
    },
    setShareInfoStatus: function setShareInfoStatus() {
      var data = this.storeInfo;
      var href = location.href;
      if (this.$wechat.isWeixin()) {
        href = href.indexOf("?") === -1 ? href + "?spread=" + this.uid : href + "&spread=" + this.uid;
        var configAppMessage = {
          desc: data.storeInfo,
          title: data.storeName,
          link: href,
          imgUrl: data.image
        };
        this.$wechat.wechatEvevt(["updateAppMessageShareData", "updateTimelineShareData"], configAppMessage);
      }
    },
    hideNav: function hideNav() {
      this.currentPage = false;
    },
    //下拉导航页面跳转
    linkPage: function linkPage(url) {
      if (url == '/pages/index/index' || url == '/pages/order_addcart/order_addcart' || url == '/pages/user/index') {
        uni.switchTab({
          url: url
        });
      } else {
        uni.navigateTo({
          url: url
        });
      }
      this.currentPage = false;
    },
    showImg: function showImg(index) {
      this.$refs.cusPreviewImg.open(this.selectSku.suk);
    },
    changeSwitch: function changeSwitch(e) {
      var _this5 = this;
      var productSelect = this.skuArr[e];
      this.$set(this, 'selectSku', productSelect);
      var skuList = productSelect.suk.split(',');
      skuList.forEach(function (i, index) {
        _this5.$set(_this5.attribute.productAttr[index], 'index', skuList[index]);
      });
      if (productSelect) {
        this.$set(this.attribute.productSelect, "image", productSelect.image);
        this.$set(this.attribute.productSelect, "price", productSelect.price);
        this.$set(this.attribute.productSelect, "stock", productSelect.stock);
        this.$set(this.attribute.productSelect, "unique", productSelect.id);
        this.$set(this.attribute.productSelect, "quota", productSelect.quota);
        this.$set(this.attribute.productSelect, "quotaShow", productSelect.quotaShow);
        this.$set(this.attribute.productSelect, "cart_num", 1);
        this.$set(this, "attrTxt", "已选择");
        this.$set(this, "attrValue", productSelect.suk);
      }
    }
  },
  onShareAppMessage: function onShareAppMessage() {
    var that = this;
    return {
      title: that.storeInfo.title,
      path: app.globalData.openPages,
      imageUrl: that.storeInfo.image
    };
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"]))

/***/ }),

/***/ 611:
/*!********************************************************************************************************************************************************!*\
  !*** E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_seckill_details/index.vue?vue&type=style&index=0&id=1620faf6&scoped=true&lang=scss& ***!
  \********************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_1620faf6_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=1620faf6&scoped=true&lang=scss& */ 612);
/* harmony import */ var _D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_1620faf6_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_1620faf6_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_1620faf6_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_1620faf6_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_D_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_1620faf6_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 612:
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/ideaprojects/tyy-crmeb/BIZ-CRMEB-APP/pages/activity/goods_seckill_details/index.vue?vue&type=style&index=0&id=1620faf6&scoped=true&lang=scss& ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[605,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/activity/goods_seckill_details/index.js.map