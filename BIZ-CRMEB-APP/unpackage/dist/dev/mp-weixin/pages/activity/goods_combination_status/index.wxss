@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.generate-posters.data-v-35f14445 {
  width: 100%;
  height: 170rpx;
  background-color: #fff;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 300;
  -webkit-transform: translate3d(0, 100%, 0);
          transform: translate3d(0, 100%, 0);
  transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
  border-top: 1rpx solid #eee;
}
.generate-posters.on.data-v-35f14445 {
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
}
.generate-posters .item.data-v-35f14445 {
  flex: 1;
  text-align: center;
  font-size: 30rpx;
}
.generate-posters .item .iconfont.data-v-35f14445 {
  font-size: 80rpx;
  color: #5eae72;
}
.generate-posters .item .iconfont.icon-haibao.data-v-35f14445 {
  color: #5391f1;
}
.pinkT.data-v-35f14445 {
  position: relative;
}
.pinkT .chief.data-v-35f14445 {
  position: absolute;
  width: 72rpx;
  height: 30rpx;
  background-color: theme;
  border-radius: 15rpx;
  font-size: 20rpx;
  line-height: 30rpx;
  text-align: center;
  right: -24rpx;
  top: -16rpx;
  color: #fff;
}
[data-theme="theme1"] .pinkT .chief.data-v-35f14445 {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .pinkT .chief.data-v-35f14445 {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .pinkT .chief.data-v-35f14445 {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .pinkT .chief.data-v-35f14445 {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .pinkT .chief.data-v-35f14445 {
  background-color: #FF448F !important;
}
.bg-color-red.data-v-35f14445 {
  background-color: theme;
}
[data-theme="theme1"] .bg-color-red.data-v-35f14445 {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .bg-color-red.data-v-35f14445 {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .bg-color-red.data-v-35f14445 {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .bg-color-red.data-v-35f14445 {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .bg-color-red.data-v-35f14445 {
  background-color: #FF448F !important;
}
.canvas.data-v-35f14445 {
  position: fixed;
  opacity: 0;
}
.poster-pop.data-v-35f14445 {
  width: 594rpx;
  height: 850rpx;
  position: fixed;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  z-index: 999;
  top: 50%;
  margin-top: -466rpx;
}
.poster-pop image.data-v-35f14445 {
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 10rpx;
}
.poster-pop .close.data-v-35f14445 {
  text-align: center;
  margin-top: 55rpx;
  color: #fff;
  font-size: 52rpx;
}
.poster-pop .save-poster.data-v-35f14445 {
  background-color: #df2d0a;
  font-size: ：22rpx;
  color: #fff;
  text-align: center;
  height: 76rpx;
  line-height: 76rpx;
  width: 100%;
}
.poster-pop .keep.data-v-35f14445 {
  color: #fff;
  text-align: center;
  font-size: 25rpx;
  margin-top: 25rpx;
}
/*开团*/
.group-con .header.data-v-35f14445 {
  height: 186rpx;
  background-color: #fff;
  border-top: 1px solid #f5f5f5;
  margin: 20rpx 30rpx 0;
  border-radius: 14rpx;
  position: relative;
}
.group-con .header .iconfont.data-v-35f14445 {
  font-size: 100rpx;
  position: absolute;
  color: #ccc;
  right: 33rpx;
  bottom: 20rpx;
}
.group-con .header .pictrue.data-v-35f14445 {
  width: 140rpx;
  height: 140rpx;
}
.group-con .header .pictrue image.data-v-35f14445 {
  width: 100%;
  height: 100%;
  border-radius: 6rpx;
}
.group-con .header .text.data-v-35f14445 {
  width: 540rpx;
  font-size: 30rpx;
  color: #222;
}
.group-con .header .text .money.data-v-35f14445 {
  font-size: 24rpx;
  font-weight: bold;
  margin-top: 15rpx;
}
.group-con .header .text .money .num.data-v-35f14445 {
  font-size: 32rpx;
}
.group-con .header .text .money .team.data-v-35f14445 {
  padding: 1rpx 10rpx;
  font-weight: normal;
  border-radius: 50rpx;
  font-size: 20rpx;
  vertical-align: 4rpx;
  margin-left: 15rpx;
  color: theme;
  border: theme;
}
[data-theme="theme1"] .group-con .header .text .money .team.data-v-35f14445 {
  color: #e93323 !important;
}
[data-theme="theme2"] .group-con .header .text .money .team.data-v-35f14445 {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .group-con .header .text .money .team.data-v-35f14445 {
  color: #42CA4D !important;
}
[data-theme="theme4"] .group-con .header .text .money .team.data-v-35f14445 {
  color: #1DB0FC !important;
}
[data-theme="theme5"] .group-con .header .text .money .team.data-v-35f14445 {
  color: #FF448F !important;
}
[data-theme="theme1"] .group-con .header .text .money .team.data-v-35f14445 {
  border: 1px solid #e93323;
}
[data-theme="theme2"] .group-con .header .text .money .team.data-v-35f14445 {
  border: 1px solid #FE5C2D;
}
[data-theme="theme3"] .group-con .header .text .money .team.data-v-35f14445 {
  border: 1px solid #42CA4D;
}
[data-theme="theme4"] .group-con .header .text .money .team.data-v-35f14445 {
  border: 1px solid #1DB0FC;
}
[data-theme="theme5"] .group-con .header .text .money .team.data-v-35f14445 {
  border: 1px solid #FF448F;
}
.group-con .wrapper.data-v-35f14445 {
  background-color: #fff;
  margin: 20rpx 30rpx 0;
  border-radius: 14rpx;
}
.group-con .wrapper .title.data-v-35f14445 {
  margin-top: 30rpx;
  padding-top: 30rpx;
}
.group-con .wrapper .title .line.data-v-35f14445 {
  width: 136rpx;
  height: 1px;
  background-color: #ddd;
}
.group-con .wrapper .title .name.data-v-35f14445 {
  margin: 0 45rpx;
  font-size: 28rpx;
  color: #282828;
}
.group-con .wrapper .title .name .time.data-v-35f14445 {
  margin: 0 14rpx;
}
.group-con .wrapper .title .name .timeTxt.data-v-35f14445 {
  color: #fc4141;
}
.group-con .wrapper .title .name .time .styleAll.data-v-35f14445 {
  background-color: #ffcfcb;
  text-align: center;
  border-radius: 3rpx;
  font-size: 28rpx;
  font-weight: bold;
  display: inline-block;
  vertical-align: middle;
  color: #fc4141;
  padding: 2rpx 5rpx;
}
.group-con .wrapper .tips.data-v-35f14445 {
  font-size: 30rpx;
  font-weight: bold;
  text-align: center;
  margin-top: 30rpx;
  color: #999;
}
.group-con .wrapper .list.data-v-35f14445 {
  padding: 0 30rpx;
  margin-top: 45rpx;
}
.group-con .wrapper .list.result.data-v-35f14445 {
  max-height: 240rpx;
}
.group-con .wrapper .list.result.on.data-v-35f14445 {
  max-height: 2000rpx;
}
.group-con .wrapper .list .pictrue.data-v-35f14445 {
  width: 94rpx;
  height: 94rpx;
  margin: 0 0 29rpx 35rpx;
}
.group-con .wrapper .list .pictrue image.data-v-35f14445 {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: theme;
}
[data-theme="theme1"] .group-con .wrapper .list .pictrue image.data-v-35f14445 {
  border: 1px solid #e93323;
}
[data-theme="theme2"] .group-con .wrapper .list .pictrue image.data-v-35f14445 {
  border: 1px solid #FE5C2D;
}
[data-theme="theme3"] .group-con .wrapper .list .pictrue image.data-v-35f14445 {
  border: 1px solid #42CA4D;
}
[data-theme="theme4"] .group-con .wrapper .list .pictrue image.data-v-35f14445 {
  border: 1px solid #1DB0FC;
}
[data-theme="theme5"] .group-con .wrapper .list .pictrue image.data-v-35f14445 {
  border: 1px solid #FF448F;
}
.group-con .wrapper .list .pictrue image.img-none.data-v-35f14445 {
  border: none;
}
.group-con .wrapper .lookAll.data-v-35f14445 {
  font-size: 24rpx;
  color: #282828;
  padding-top: 10rpx;
}
.group-con .wrapper .lookAll .iconfont.data-v-35f14445 {
  font-size: 25rpx;
  margin: 2rpx 0 0 10rpx;
}
.group-con .wrapper .teamBnt.data-v-35f14445 {
  font-size: 30rpx;
  width: 620rpx;
  height: 86rpx;
  border-radius: 50rpx;
  text-align: center;
  line-height: 86rpx;
  color: #fff;
  margin: 21rpx auto 0 auto;
}
.group-con .wrapper .cancel.data-v-35f14445,
.group-con .wrapper .lookOrder.data-v-35f14445 {
  text-align: center;
  font-size: 24rpx;
  color: #282828;
  padding-top: 30rpx;
  padding-bottom: 30rpx;
}
.group-con .wrapper .cancel .iconfont.data-v-35f14445 {
  font-size: 35rpx;
  color: #2c2c2c;
  vertical-align: -4rpx;
  margin-right: 9rpx;
}
.group-con .wrapper .lookOrder .iconfont.data-v-35f14445 {
  font-size: 25rpx;
  color: #2c2c2c;
  margin-left: 10rpx;
}
.group-con .group-recommend.data-v-35f14445 {
  background-color: #fff;
  margin: 20rpx 30rpx 0;
  border-radius: 14rpx;
}
.group-con .group-recommend .title.data-v-35f14445 {
  padding-right: 30rpx;
  margin-left: 30rpx;
  height: 85rpx;
  border-bottom: 1px solid #eee;
  font-size: 28rpx;
  color: #282828;
}
.group-con .group-recommend .title .more.data-v-35f14445 {
  color: #808080;
}
.group-con .group-recommend .title .more .iconfont.data-v-35f14445 {
  margin-left: 13rpx;
  font-size: 28rpx;
}
.group-con .group-recommend .list.data-v-35f14445 {
  margin-top: 30rpx;
}
.group-con .group-recommend .list .item.data-v-35f14445 {
  width: 190rpx;
  margin: 0 0 25rpx 30rpx;
}
.group-con .group-recommend .list .item .pictrue.data-v-35f14445 {
  width: 100%;
  height: 190rpx;
  position: relative;
}
.group-con .group-recommend .list .item .pictrue image.data-v-35f14445 {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}
.group-con .group-recommend .list .item .pictrue .team.data-v-35f14445 {
  position: absolute;
  top: 28rpx;
  left: -5rpx;
  min-width: 100rpx;
  height: 36rpx;
  line-height: 36rpx;
  text-align: center;
  border-radius: 0 18rpx 18rpx 0;
  font-size: 20rpx;
  color: #fff;
  background-color: theme;
}
[data-theme="theme1"] .group-con .group-recommend .list .item .pictrue .team.data-v-35f14445 {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .group-con .group-recommend .list .item .pictrue .team.data-v-35f14445 {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .group-con .group-recommend .list .item .pictrue .team.data-v-35f14445 {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .group-con .group-recommend .list .item .pictrue .team.data-v-35f14445 {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .group-con .group-recommend .list .item .pictrue .team.data-v-35f14445 {
  background-color: #FF448F !important;
}
.group-con .group-recommend .list .item .name.data-v-35f14445 {
  font-size: 28rpx;
  color: #333;
  margin-top: 0.18rem;
}
.group-con .group-recommend .list .item .money.data-v-35f14445 {
  font-weight: bold;
  font-size: 28rpx;
}
.share-box.data-v-35f14445 {
  z-index: 1000;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.share-box image.data-v-35f14445 {
  width: 100%;
  height: 100%;
}
.font_price.data-v-35f14445 {
  color: theme;
}
[data-theme="theme1"] .font_price.data-v-35f14445 {
  color: #F93323 !important;
}
[data-theme="theme2"] .font_price.data-v-35f14445 {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .font_price.data-v-35f14445 {
  color: #FF7600 !important;
}
[data-theme="theme4"] .font_price.data-v-35f14445 {
  color: #FD502F !important;
}
[data-theme="theme5"] .font_price.data-v-35f14445 {
  color: #FF448F !important;
}

