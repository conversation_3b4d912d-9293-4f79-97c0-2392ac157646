@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  background-color: theme;
  height: 100%;
}
[data-theme="theme1"] page {
  background-color: #e93323 !important;
}
[data-theme="theme2"] page {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] page {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] page {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] page {
  background-color: #FF448F !important;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.pageInfo.data-v-51d94765 {
  background-color: theme;
  height: 100vh;
  overflow: auto;
}
[data-theme="theme1"] .pageInfo.data-v-51d94765 {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .pageInfo.data-v-51d94765 {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .pageInfo.data-v-51d94765 {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .pageInfo.data-v-51d94765 {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .pageInfo.data-v-51d94765 {
  background-color: #FF448F !important;
}
.mr9.data-v-51d94765 {
  margin-right: 9rpx;
}
.swipers.data-v-51d94765 {
  height: 100%;
  width: 76%;
  margin: auto;
  overflow: hidden;
  font-size: 22rpx;
  color: #fff;
}
.swipers image.data-v-51d94765 {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  overflow: hidden;
}
.swipers swiper.data-v-51d94765 {
  height: 100%;
  width: 100%;
  overflow: hidden;
}
.swipers .line1.data-v-51d94765 {
  width: 195rpx;
}
.bargain-list .icon-xiangzuo.data-v-51d94765 {
  font-size: 40rpx;
  color: #fff;
  position: fixed;
  left: 30rpx;
  z-index: 99;
  -webkit-transform: translateY(-20%);
          transform: translateY(-20%);
}
.bargain-list .header.data-v-51d94765 {
  babackground-repeat: no-repeat;
  background-size: 100% 100%;
  width: 750rpx;
  height: 420rpx;
}
.bargain-list .header .acea-row.data-v-51d94765 {
  height: 50rpx;
  line-height: 50rpx;
  left: 50rpx;
}
.bargain-list .header .acea-row .nickName.data-v-51d94765 {
  width: 65rpx;
  overflow: hidden;
  white-space: nowrap;
}
.bargain-list .header .pic.data-v-51d94765 {
  width: 478rpx;
  height: 50rpx;
  margin: 0 auto;
}
.bargain-list .header .tit.data-v-51d94765 {
  color: #FFFFFF;
  font-size: 24rpx;
  font-weight: 400;
  text-align: center;
  margin-top: 304rpx;
}
.bargain-list .list.data-v-51d94765 {
  padding: 0 30rpx;
}
.bargain-list .list .item.data-v-51d94765 {
  position: relative;
  height: 250rpx;
  background-color: #fff;
  border-radius: 14rpx;
  margin-bottom: 20rpx;
  padding: 30rpx 25rpx;
}
.bargain-list .list .item .pictrue.data-v-51d94765 {
  width: 190rpx;
  height: 190rpx;
}
.bargain-list .list .item .pictrue image.data-v-51d94765 {
  width: 100%;
  height: 100%;
  border-radius: 14rpx;
}
.bargain-list .list .item .text.data-v-51d94765 {
  width: 432rpx;
  font-size: 28rpx;
  color: #333333;
}
.bargain-list .list .item .text .txt.data-v-51d94765 {
  font-size: 22rpx;
  margin-left: 4rpx;
  color: #666666;
  line-height: 36rpx;
}
.bargain-list .list .item .text .name.data-v-51d94765 {
  width: 100%;
  height: 68rpx;
  line-height: 36rpx;
  font-size: 28rpx;
  margin-bottom: 26rpx;
}
.bargain-list .list .item .text .num.data-v-51d94765 {
  font-size: 26rpx;
  color: #999;
}
.bargain-list .list .item .text .num .iconfont.data-v-51d94765 {
  font-size: 35rpx;
  margin-right: 7rpx;
}
.bargain-list .list .item .text .money.data-v-51d94765 {
  font-size: 24rpx;
  font-weight: bold;
  color: theme;
}
[data-theme="theme1"] .bargain-list .list .item .text .money.data-v-51d94765 {
  color: #F93323 !important;
}
[data-theme="theme2"] .bargain-list .list .item .text .money.data-v-51d94765 {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .bargain-list .list .item .text .money.data-v-51d94765 {
  color: #FF7600 !important;
}
[data-theme="theme4"] .bargain-list .list .item .text .money.data-v-51d94765 {
  color: #FD502F !important;
}
[data-theme="theme5"] .bargain-list .list .item .text .money.data-v-51d94765 {
  color: #FF448F !important;
}
.bargain-list .list .item .text .money .price.data-v-51d94765 {
  font-size: 38rpx;
}
.bargain-list .list .item .cutBnt.data-v-51d94765 {
  position: absolute;
  width: 162rpx;
  height: 52rpx;
  border-radius: 50rpx;
  font-size: 24rpx;
  color: #fff;
  text-align: center;
  line-height: 52rpx;
  right: 24rpx;
  bottom: 30rpx;
}
.bg_color.data-v-51d94765 {
  background: theme;
}
[data-theme="theme1"] .bg_color.data-v-51d94765 {
  background: linear-gradient(90deg, #FF7931 0%, #E93323 100%);
}
[data-theme="theme2"] .bg_color.data-v-51d94765 {
  background: linear-gradient(90deg, #FF9445 0%, #FE5C2D 100%);
}
[data-theme="theme3"] .bg_color.data-v-51d94765 {
  background: linear-gradient(55deg, #70E038 0%, #42Ca4D 100%);
}
[data-theme="theme4"] .bg_color.data-v-51d94765 {
  background: linear-gradient(90deg, #40D1F4 0%, #1DB0FC 100%);
}
[data-theme="theme5"] .bg_color.data-v-51d94765 {
  background: linear-gradient(90deg, #FF448F 0%, #FF67AD 100%);
}
.bg-color-hui.data-v-51d94765 {
  background-color: #CDCBCC;
}
.bargain-list .list .item .cutBnt .iconfont.data-v-51d94765 {
  margin-right: 8rpx;
  font-size: 30rpx;
}
.bargain-list .list .load.data-v-51d94765 {
  font-size: 24rpx;
  height: 85rpx;
  text-align: center;
  line-height: 85rpx;
}
.flex-center.data-v-51d94765 {
  display: flex;
  justify-content: center;
  align-items: center;
}
.no_shop.data-v-51d94765 {
  margin: 0 30rpx 0;
  height: 700rpx;
  border-radius: 10rpx;
  background-color: #fff;
}

