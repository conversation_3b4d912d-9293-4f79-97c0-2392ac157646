<view data-theme="{{theme}}" class="data-v-7af2e664"><block wx:if="{{$root.g0>0}}"><block class="data-v-7af2e664"><view data-ref="container" class="bargain-record _div data-v-7af2e664 vue-ref"><block wx:for="{{bargain}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item borRadius14 _div data-v-7af2e664"><view class="picTxt acea-row row-between-wrapper _div data-v-7af2e664"><view class="pictrue _div data-v-7af2e664"><image src="{{item.image}}" class="data-v-7af2e664"></image></view><view class="text acea-row row-column-around _div data-v-7af2e664"><view class="line1 _div data-v-7af2e664" style="width:100%;">{{item.title}}</view><block wx:if="{{item.status===1}}"><count-down vue-id="{{'e368e410-1-'+index}}" justify-left="justify-content:left" bgColor="{{bgColor}}" is-day="{{true}}" tip-text="倒计时 " day-text="天" hour-text=" 时 " minute-text=" 分 " second-text=" 秒 " datatime="{{item.stopTime/1000}}" class="data-v-7af2e664" bind:__l="__l"></count-down></block><block wx:else><block wx:if="{{item.status===3&&item.isDel===false}}"><view class="successTxt font_color _div data-v-7af2e664">砍价成功</view></block><block wx:else><block wx:if="{{item.status===3&&item.isDel===true&&item.isPay===false}}"><view class="successTxt _div data-v-7af2e664">砍价失败</view></block><block wx:else><view class="endTxt _div data-v-7af2e664">活动已结束</view></block></block></block><view class="money _div data-v-7af2e664">已砍至<label class="symbol font_color _span data-v-7af2e664">￥</label><label class="num font_color _span data-v-7af2e664">{{item.surplusPrice}}</label></view></view></view><view class="bottom acea-row row-between-wrapper _div data-v-7af2e664"><block wx:if="{{item.status===1}}"><view class="purple _div data-v-7af2e664">活动进行中</view></block><block wx:else><block wx:if="{{item.status===3&&item.isDel===false}}"><view class="success _div data-v-7af2e664">砍价成功</view></block><block wx:else><block wx:if="{{item.status===3&&item.isDel===true&&item.isPay===false}}"><view class="end _div data-v-7af2e664">砍价失败</view></block><block wx:else><view class="end _div data-v-7af2e664">活动已结束</view></block></block></block><view class="acea-row row-middle row-right _div data-v-7af2e664"><block wx:if="{{item.status===3&&!item.isOrder}}"><view data-event-opts="{{[['tap',[['goConfirm',['$0'],[[['bargain','',index]]]]]]]}}" class="bnt bg_color _div data-v-7af2e664" bindtap="__e">去付款</view></block><block wx:if="{{item.status===3&&!item.isDel&&item.isOrder&&!item.isPay}}"><view data-event-opts="{{[['tap',[['goPay',['$0','$1'],[[['bargain','',index,'surplusPrice']],[['bargain','',index,'orderNo']]]]]]]}}" class="bnt bg_color _div data-v-7af2e664" bindtap="__e">立即付款</view></block><block wx:if="{{item.status===1}}"><view data-event-opts="{{[['tap',[['goDetail',['$0'],[[['bargain','',index,'id']]]]]]]}}" class="bnt bg_color _div data-v-7af2e664" bindtap="__e">继续砍价</view></block><block wx:if="{{item.status===2}}"><view data-event-opts="{{[['tap',[['goList',['$event']]]]]}}" class="bnt bg_color _div data-v-7af2e664" bindtap="__e">重开一个</view></block></view></view></view></block><loading vue-id="e368e410-2" loaded="{{status}}" loading="{{loadingList}}" class="data-v-7af2e664" bind:__l="__l"></loading></view></block></block><block wx:if="{{$root.g1==0}}"><block class="data-v-7af2e664"><empty-page vue-id="e368e410-3" title="暂无砍价记录～" class="data-v-7af2e664" bind:__l="__l"></empty-page></block></block><payment vue-id="e368e410-4" payMode="{{payMode}}" pay_close="{{pay_close}}" order_id="{{pay_order_id}}" totalPrice="{{totalPrice}}" data-event-opts="{{[['^onChangeFun',[['onChangeFun']]]]}}" bind:onChangeFun="__e" class="data-v-7af2e664" bind:__l="__l"></payment></view>