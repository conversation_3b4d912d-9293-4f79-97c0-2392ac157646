@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.productSort .header.data-v-1554e5b0 {
  width: 100%;
  height: 96rpx;
  background-color: #fff;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  z-index: 9;
  border-bottom: 1rpx solid #f5f5f5;
}
.productSort .header .input.data-v-1554e5b0 {
  width: 700rpx;
  height: 60rpx;
  background-color: #f5f5f5;
  border-radius: 50rpx;
  box-sizing: border-box;
  padding: 0 25rpx;
}
.productSort .header .input .iconfont.data-v-1554e5b0 {
  font-size: 26rpx;
  color: #555;
}
.productSort .header .input .placeholder.data-v-1554e5b0 {
  color: #999;
}
.productSort .header .input input.data-v-1554e5b0 {
  font-size: 26rpx;
  height: 100%;
  width: 597rpx;
}
.productSort .aside.data-v-1554e5b0 {
  position: fixed;
  width: 180rpx;
  left: 0;
  top: 0;
  background-color: #f7f7f7;
  overflow-y: scroll;
  overflow-x: hidden;
  height: auto;
  margin-top: 96rpx;
}
.productSort .aside .item.data-v-1554e5b0 {
  height: 100rpx;
  width: 100%;
  font-size: 26rpx;
  color: #424242;
  position: relative;
}
.productSort .aside .item.on.data-v-1554e5b0 {
  background-color: #fff;
  width: 100%;
  text-align: center;
  color: theme;
  font-weight: bold;
}
[data-theme="theme1"] .productSort .aside .item.on.data-v-1554e5b0 {
  color: #e93323 !important;
}
[data-theme="theme2"] .productSort .aside .item.on.data-v-1554e5b0 {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .productSort .aside .item.on.data-v-1554e5b0 {
  color: #42CA4D !important;
}
[data-theme="theme4"] .productSort .aside .item.on.data-v-1554e5b0 {
  color: #1DB0FC !important;
}
[data-theme="theme5"] .productSort .aside .item.on.data-v-1554e5b0 {
  color: #FF448F !important;
}
.productSort .aside .item.on.data-v-1554e5b0 ::before {
  content: '';
  width: 4rpx;
  height: 100rpx;
  position: absolute;
  left: 0;
  top: 0;
  background-color: theme;
}
[data-theme="theme1"] .productSort .aside .item.on.data-v-1554e5b0 ::before {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .productSort .aside .item.on.data-v-1554e5b0 ::before {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .productSort .aside .item.on.data-v-1554e5b0 ::before {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .productSort .aside .item.on.data-v-1554e5b0 ::before {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .productSort .aside .item.on.data-v-1554e5b0 ::before {
  background-color: #FF448F !important;
}
.productSort .conter.data-v-1554e5b0 {
  margin: 96rpx 0 0 180rpx;
  padding: 0 14rpx;
  background-color: #fff;
}
.productSort .conter .listw.data-v-1554e5b0 {
  padding-top: 20rpx;
}
.productSort .conter .listw .title.data-v-1554e5b0 {
  height: 90rpx;
}
.productSort .conter .listw .title .line.data-v-1554e5b0 {
  width: 100rpx;
  height: 2rpx;
  background-color: #f0f0f0;
}
.productSort .conter .listw .title .name.data-v-1554e5b0 {
  font-size: 28rpx;
  color: #333;
  margin: 0 30rpx;
  font-weight: bold;
}
.productSort .conter .list.data-v-1554e5b0 {
  flex-wrap: wrap;
}
.productSort .conter .list .item.data-v-1554e5b0 {
  width: 177rpx;
  margin-top: 26rpx;
}
.productSort .conter .list .item .picture.data-v-1554e5b0 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
}
.productSort .conter .list .item .picture image.data-v-1554e5b0 {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.productSort .conter .list .item .picture image ._div.data-v-1554e5b0 {
  background-color: #f7f7f7;
}
.productSort .conter .list .item .name.data-v-1554e5b0 {
  font-size: 24rpx;
  color: #333;
  height: 56rpx;
  line-height: 56rpx;
  width: 120rpx;
  text-align: center;
}

