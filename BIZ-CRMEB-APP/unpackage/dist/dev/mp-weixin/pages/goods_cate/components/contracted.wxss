@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.productSort .nav.data-v-289d3ae8 {
  padding: 0 30rpx;
  width: 100%;
  white-space: nowrap;
  box-sizing: border-box;
  height: 86rpx;
  background-color: #fff;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9;
}
.productSort .nav .item.data-v-289d3ae8 {
  display: inline-block;
  font-size: 30rpx;
  color: #282828;
  padding-right: 46rpx;
}
.productSort .nav .item.on.data-v-289d3ae8 {
  color: #4B56AA;
  font-weight: bold;
}
.productSort .nav .item .line.data-v-289d3ae8 {
  width: 40rpx;
  height: 4rpx;
  background-color: #4B56AA;
  margin: 10rpx auto 0 auto;
}
.productSort .list.data-v-289d3ae8 {
  margin-top: 86rpx;
  padding: 0 20rpx;
}
.productSort .list .item.data-v-289d3ae8 {
  width: 345rpx;
  margin-top: 20rpx;
  background-color: #fff;
  border-radius: 20rpx;
}
.productSort .list .item .pictrue.data-v-289d3ae8 {
  position: relative;
  width: 100%;
  height: 345rpx;
}
.productSort .list .item .pictrue image.data-v-289d3ae8 {
  width: 100%;
  height: 100%;
  border-radius: 20rpx 20rpx 0 0;
}
.productSort .list .item .text.data-v-289d3ae8 {
  padding: 20rpx 17rpx 26rpx 17rpx;
  font-size: 30rpx;
  color: #222;
}
.productSort .list .item .text .money.data-v-289d3ae8 {
  font-size: 26rpx;
  font-weight: bold;
  margin-top: 8rpx;
  color: theme;
}
[data-theme="theme1"] .productSort .list .item .text .money.data-v-289d3ae8 {
  color: #F93323 !important;
}
[data-theme="theme2"] .productSort .list .item .text .money.data-v-289d3ae8 {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .productSort .list .item .text .money.data-v-289d3ae8 {
  color: #FF7600 !important;
}
[data-theme="theme4"] .productSort .list .item .text .money.data-v-289d3ae8 {
  color: #FD502F !important;
}
[data-theme="theme5"] .productSort .list .item .text .money.data-v-289d3ae8 {
  color: #FF448F !important;
}
.productSort .list .item .text .money .num.data-v-289d3ae8 {
  font-size: 34rpx;
}
.scroll-Y.data-v-289d3ae8 {
  height: 100vh;
}

