@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.newsDetail.data-v-7abd4a12 {
  padding: 30rpx 0;
}
.newsDetail .title.data-v-7abd4a12 {
  padding: 0 30rpx;
  font-size: 34rpx;
  color: #282828;
  font-weight: bold;
  line-height: 1.5;
}
.newsDetail .list.data-v-7abd4a12 {
  margin: 28rpx 30rpx 0 30rpx;
  padding-bottom: 25rpx;
}
.newsDetail .list .label.data-v-7abd4a12 {
  font-size: 30rpx;
  color: #B1B2B3;
}
.newsDetail .list .item.data-v-7abd4a12 {
  margin-left: 27rpx;
  font-size: 30rpx;
  color: #B1B2B3;
}
.newsDetail .list .item .iconfont.data-v-7abd4a12 {
  font-size: 28rpx;
  margin-right: 10rpx;
}
.newsDetail .list .item .iconfont.icon-shenhezhong.data-v-7abd4a12 {
  font-size: 26rpx;
}
.newsDetail .picTxt.data-v-7abd4a12 {
  width: 690rpx;
  height: 200rpx;
  border-radius: 20rpx;
  border: 1px solid #e1e1e1;
  position: relative;
  margin: 30rpx auto 0 auto;
}
.newsDetail .picTxt .pictrue.data-v-7abd4a12 {
  width: 200rpx;
  height: 200rpx;
}
.newsDetail .picTxt .pictrue image.data-v-7abd4a12 {
  width: 100%;
  height: 100%;
  border-radius: 20rpx 0 0 20rpx;
  display: block;
}
.newsDetail .picTxt .text.data-v-7abd4a12 {
  width: 460rpx;
}
.newsDetail .picTxt .text .name.data-v-7abd4a12 {
  font-size: 30rpx;
  color: #282828;
}
.newsDetail .picTxt .text .money.data-v-7abd4a12 {
  font-size: 24rpx;
  margin-top: 40rpx;
  font-weight: bold;
}
.price_color.data-v-7abd4a12 {
  color: theme;
}
[data-theme="theme1"] .price_color.data-v-7abd4a12 {
  color: #F93323 !important;
}
[data-theme="theme2"] .price_color.data-v-7abd4a12 {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .price_color.data-v-7abd4a12 {
  color: #FF7600 !important;
}
[data-theme="theme4"] .price_color.data-v-7abd4a12 {
  color: #FD502F !important;
}
[data-theme="theme5"] .price_color.data-v-7abd4a12 {
  color: #FF448F !important;
}
.newsDetail .picTxt .text .money .num.data-v-7abd4a12 {
  font-size: 36rpx;
}
.newsDetail .picTxt .text .y_money.data-v-7abd4a12 {
  font-size: 26rpx;
  color: #999;
  text-decoration: line-through;
}
.newsDetail .picTxt .label.data-v-7abd4a12 {
  position: absolute;
  background-color: #303131;
  width: 160rpx;
  height: 50rpx;
  right: -7rpx;
  border-radius: 25rpx 0 6rpx 25rpx;
  text-align: center;
  line-height: 50rpx;
  bottom: 24rpx;
}
.newsDetail .picTxt .label .span.data-v-7abd4a12 {
  background-image: linear-gradient(to right, #fff71e 0%, #f9b513 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.newsDetail .picTxt .label.data-v-7abd4a12:after {
  content: " ";
  position: absolute;
  width: 0;
  height: 0;
  border-bottom: 8rpx solid #303131;
  border-right: 8rpx solid transparent;
  top: -7rpx;
  right: 0;
}
.newsDetail .bnt.data-v-7abd4a12 {
  color: #fff;
  font-size: 30rpx;
  width: 690rpx;
  height: 90rpx;
  border-radius: 45rpx;
  margin: 48rpx auto;
  text-align: center;
  line-height: 90rpx;
}
.bg_color.data-v-7abd4a12 {
  background-color: theme;
}
[data-theme="theme1"] .bg_color.data-v-7abd4a12 {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .bg_color.data-v-7abd4a12 {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .bg_color.data-v-7abd4a12 {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .bg_color.data-v-7abd4a12 {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .bg_color.data-v-7abd4a12 {
  background-color: #FF448F !important;
}

