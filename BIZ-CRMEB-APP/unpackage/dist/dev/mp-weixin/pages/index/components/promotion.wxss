@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.index-wrapper {
  border-radius: 14rpx;
  margin: 30rpx auto 110rpx;
}
.wrapper {
  margin: 30rpx auto 0;
  border-radius: 14rpx;
  background-color: #fff;
}
.title1 {
  padding-top: 20rpx;
  margin: 0 20rpx;
}
.title1 .text {
  display: flex;
}
.title1 .text .name {
  font-size: 34rpx;
  font-weight: bold;
}
.title1 .text .txt-btn {
  display: flex;
  align-items: flex-end;
  margin-bottom: 4rpx;
  margin-left: 12rpx;
  font-size: 24rpx;
  color: #999;
}
.title1 .more {
  font-size: 12px;
  color: #999;
}
.title1 .more .icon-jiantou {
  margin-left: 10rpx;
  font-size: 26rpx;
}
.wrapper .newProducts {
  white-space: nowrap;
  padding: 0rpx 20rpx 0rpx 20rpx;
  margin: 20rpx 0;
}
.wrapper .newProducts .item {
  display: inline-block;
  width: 240rpx;
  margin-right: 20rpx;
  border-radius: 20rpx;
}
.wrapper .newProducts .item:nth-last-child(1) {
  margin-right: 0;
}
.wrapper .newProducts .item .img-box {
  width: 100%;
  height: 240rpx;
  position: relative;
}
.wrapper .newProducts .item .img-box image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx 12rpx 0 0;
}
.wrapper .newProducts .item .pro-info {
  font-size: 28rpx;
  color: #333;
  text-align: center;
  padding: 19rpx 10rpx 0 10rpx;
}
.wrapper .newProducts .item .money {
  padding: 0 10rpx 18rpx 10rpx;
  text-align: center;
  font-size: 26rpx;
  font-weight: bold;
}
.empty-img {
  width: 640rpx;
  height: 300rpx;
  border-radius: 14rpx;
  margin: 26rpx auto 0 auto;
  background-color: #ccc;
  text-align: center;
  line-height: 300rpx;
}
.empty-img .iconfont {
  font-size: 50rpx;
}
.font-color {
  color: theme;
}
[data-theme="theme1"] .font-color {
  color: #e93323 !important;
}
[data-theme="theme2"] .font-color {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .font-color {
  color: #42CA4D !important;
}
[data-theme="theme4"] .font-color {
  color: #1DB0FC !important;
}
[data-theme="theme5"] .font-color {
  color: #FF448F !important;
}

