@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.coupons.data-v-3ac931cf {
  background-color: #fff;
  margin: 30rpx auto 30rpx;
  padding-bottom: 32rpx;
  border-radius: 14rpx;
  box-sizing: border-box;
}
.coupont_title.data-v-3ac931cf {
  display: flex;
  justify-content: space-between;
  padding: 24rpx 20rpx;
}
.coupont_title .left_con.data-v-3ac931cf {
  font-size: 32rpx;
  font-weight: 600;
  line-height: 45rpx;
  color: #333333;
}
.coupont_title .right_con.data-v-3ac931cf {
  font-size: 24rpx;
  font-weight: 400;
  line-height: 45rpx;
  color: #999999;
}
.conter.data-v-3ac931cf {
  width: 666rpx;
  border-radius: 12px;
  padding-left: 20rpx;
}
.conter .itemCon.data-v-3ac931cf {
  display: inline-block;
  width: 228rpx;
  height: 108rpx;
  margin-right: 20rpx;
  border-radius: 14rpx;
  background-repeat: no-repeat;
  background-position: right;
  position: relative;
}
.conter .itemCon.data-v-3ac931cf ::before {
  content: '';
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #fff;
  left: -10rpx;
  top: 44rpx;
}
.conter .itemCon_left.data-v-3ac931cf {
  width: 172rpx;
  height: 108rpx;
  border-radius: 14rpx;
  float: left;
  display: flex;
  justify-content: center;
  align-items: center;
}
.conter .itemCon_left .quan_text.data-v-3ac931cf {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
}
.conter .itemCon_left .quan_text .sm_txt.data-v-3ac931cf {
  display: inline-block;
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  text-align: center;
  font-size: 20rpx;
  margin-top: 10rpx;
  color: #fff;
}
.conter .itemCon_left .quan_text .price_num.data-v-3ac931cf {
  font-size: 44rpx;
  line-height: 44rpx;
  font-weight: 600;
}
.conter .itemCon_left .quan_text .man.data-v-3ac931cf {
  color: #999;
  font-size: 24rpx;
}
.conter .itemCon_right.data-v-3ac931cf {
  width: 56rpx;
  height: 108rpx;
  float: left;
  background: transparent;
}
.conter .itemCon_right .column.data-v-3ac931cf {
  height: 108rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 24rpx;
  border-radius: 0 14rpx 14rpx 0;
}
.conter .itemCon_right .column_dis.data-v-3ac931cf {
  height: 108rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
  color: #fff;
  border-radius: 0 14rpx 14rpx 0;
  background-color: #CCCCCC;
}
.flex-center.data-v-3ac931cf {
  display: flex;
  justify-content: center;
  align-items: center;
}
.listActive.data-v-3ac931cf {
  background: theme;
}
[data-theme="theme1"] .listActive.data-v-3ac931cf {
  background: linear-gradient(90deg, #FF7931 0%, #E93323 100%);
}
[data-theme="theme2"] .listActive.data-v-3ac931cf {
  background: linear-gradient(90deg, #FF9445 0%, #FE5C2D 100%);
}
[data-theme="theme3"] .listActive.data-v-3ac931cf {
  background: linear-gradient(55deg, #70E038 0%, #42Ca4D 100%);
}
[data-theme="theme4"] .listActive.data-v-3ac931cf {
  background: linear-gradient(90deg, #40D1F4 0%, #1DB0FC 100%);
}
[data-theme="theme5"] .listActive.data-v-3ac931cf {
  background: linear-gradient(90deg, #FF448F 0%, #FF67AD 100%);
}
.listActive .itemCon_left.data-v-3ac931cf {
  background-color: theme;
}
[data-theme="theme1"] .listActive .itemCon_left.data-v-3ac931cf {
  background-color: #FDD9D3 !important;
}
[data-theme="theme2"] .listActive .itemCon_left.data-v-3ac931cf {
  background-color: #FEE0D2 !important;
}
[data-theme="theme3"] .listActive .itemCon_left.data-v-3ac931cf {
  background-color: #DBF5D6 !important;
}
[data-theme="theme4"] .listActive .itemCon_left.data-v-3ac931cf {
  background-color: #D1F1FB !important;
}
[data-theme="theme5"] .listActive .itemCon_left.data-v-3ac931cf {
  background-color: #FFD8E7 !important;
}
.listActive .itemCon_left .quan_text.data-v-3ac931cf {
  color: theme;
}
[data-theme="theme1"] .listActive .itemCon_left .quan_text.data-v-3ac931cf {
  color: #e93323 !important;
}
[data-theme="theme2"] .listActive .itemCon_left .quan_text.data-v-3ac931cf {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .listActive .itemCon_left .quan_text.data-v-3ac931cf {
  color: #42CA4D !important;
}
[data-theme="theme4"] .listActive .itemCon_left .quan_text.data-v-3ac931cf {
  color: #1DB0FC !important;
}
[data-theme="theme5"] .listActive .itemCon_left .quan_text.data-v-3ac931cf {
  color: #FF448F !important;
}
.listActive .itemCon_left .quan_text .sm_txt.data-v-3ac931cf {
  background-color: theme;
}
[data-theme="theme1"] .listActive .itemCon_left .quan_text .sm_txt.data-v-3ac931cf {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .listActive .itemCon_left .quan_text .sm_txt.data-v-3ac931cf {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .listActive .itemCon_left .quan_text .sm_txt.data-v-3ac931cf {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .listActive .itemCon_left .quan_text .sm_txt.data-v-3ac931cf {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .listActive .itemCon_left .quan_text .sm_txt.data-v-3ac931cf {
  background-color: #FF448F !important;
}
.listActive .itemCon_left .quan_text .man.data-v-3ac931cf {
  color: theme;
  opacity: .7;
}
[data-theme="theme1"] .listActive .itemCon_left .quan_text .man.data-v-3ac931cf {
  color: #e93323 !important;
}
[data-theme="theme2"] .listActive .itemCon_left .quan_text .man.data-v-3ac931cf {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .listActive .itemCon_left .quan_text .man.data-v-3ac931cf {
  color: #42CA4D !important;
}
[data-theme="theme4"] .listActive .itemCon_left .quan_text .man.data-v-3ac931cf {
  color: #1DB0FC !important;
}
[data-theme="theme5"] .listActive .itemCon_left .quan_text .man.data-v-3ac931cf {
  color: #FF448F !important;
}
.listHui.data-v-3ac931cf {
  background-color: #ccc !important;
}
.listHui .itemCon_left.data-v-3ac931cf {
  background-color: #F6F6F6;
  color: #ccc;
}
.listHui .itemCon_left .quan_text.data-v-3ac931cf {
  color: #ccc;
}
.listHui .itemCon_left .quan_text .sm_txt.data-v-3ac931cf {
  background-color: #ccc;
}
.listHui .itemCon_left .quan_text .man.data-v-3ac931cf {
  color: #ccc;
}

