<view data-theme="{{theme}}"><block wx:if="{{$root.g0>2}}"><view class="hotList"><view class="title acea-row row-between-wrapper"><view class="text line1"><text class="iconfont icon-jingpintuijian1"></text><text class="label">商品排行榜</text></view><view class="more" hover-class="none" data-event-opts="{{[['tap',[['more']]]]}}" bindtap="__e">更多<text class="iconfont icon-jiantou"></text></view></view><view class="list"><block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({item})}}" class="{{['item','acea-row','row-middle',(index<2)?'lei':'']}}" bindtap="__e"><view class="img_box"><image class="pictrue" src="{{item.image}}"></image><block wx:if="{{index==0}}"><view class="rank_bdg top_1">热榜TOP1</view></block><block wx:else><block wx:if="{{index==1}}"><view class="rank_bdg top_2">热榜TOP2</view></block><block wx:else><block wx:if="{{index==2}}"><view class="rank_bdg top_3">热榜TOP3</view></block></block></block></view><view class="ml_11 flex-column justify-between flex-1"><view class="goods_name">{{item.storeName}}</view><view class="price flex justify-between"><view><text class="price_bdg">￥</text>{{item.price+''}}<text class="sales">{{"销量 "+item.sales+"件"}}</text></view><view class="cart_icon"><text class="iconfont icon-gouwuche7"></text></view></view></view></view></block></block></view></view></block></view>