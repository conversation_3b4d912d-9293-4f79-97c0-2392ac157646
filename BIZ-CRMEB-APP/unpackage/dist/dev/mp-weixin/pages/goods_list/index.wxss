@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.bg_color.data-v-8b1a97ba {
  background-color: theme;
}
[data-theme="theme1"] .bg_color.data-v-8b1a97ba {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .bg_color.data-v-8b1a97ba {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .bg_color.data-v-8b1a97ba {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .bg_color.data-v-8b1a97ba {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .bg_color.data-v-8b1a97ba {
  background-color: #FF448F !important;
}
.font_color.data-v-8b1a97ba {
  color: theme;
}
[data-theme="theme1"] .font_color.data-v-8b1a97ba {
  color: #e93323 !important;
}
[data-theme="theme2"] .font_color.data-v-8b1a97ba {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .font_color.data-v-8b1a97ba {
  color: #42CA4D !important;
}
[data-theme="theme4"] .font_color.data-v-8b1a97ba {
  color: #1DB0FC !important;
}
[data-theme="theme5"] .font_color.data-v-8b1a97ba {
  color: #FF448F !important;
}
.x-money.data-v-8b1a97ba {
  color: theme;
}
[data-theme="theme1"] .x-money.data-v-8b1a97ba {
  color: #F93323 !important;
}
[data-theme="theme2"] .x-money.data-v-8b1a97ba {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .x-money.data-v-8b1a97ba {
  color: #FF7600 !important;
}
[data-theme="theme4"] .x-money.data-v-8b1a97ba {
  color: #FD502F !important;
}
[data-theme="theme5"] .x-money.data-v-8b1a97ba {
  color: #FF448F !important;
}
.iconfont.data-v-8b1a97ba {
  color: #fff;
}
.listBox.data-v-8b1a97ba {
  padding: 20px 15px;
  margin-top: 154rpx;
}
.productList .search.data-v-8b1a97ba {
  width: 100%;
  height: 86rpx;
  padding-left: 23rpx;
  box-sizing: border-box;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 9;
}
.productList .search .input.data-v-8b1a97ba {
  height: 60rpx;
  background-color: #fff;
  border-radius: 50rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
}
.productList .search .input input.data-v-8b1a97ba {
  width: 548rpx;
  height: 100%;
  font-size: 26rpx;
}
.productList .search .input .placeholder.data-v-8b1a97ba {
  color: #999;
}
.productList .search .input .iconfont.data-v-8b1a97ba {
  font-size: 35rpx;
  color: #555;
}
.productList .search .icon-pailie.data-v-8b1a97ba,
.productList .search .icon-tupianpailie.data-v-8b1a97ba {
  color: #fff;
  width: 62rpx;
  font-size: 40rpx;
  height: 86rpx;
  line-height: 86rpx;
}
.productList .nav.data-v-8b1a97ba {
  height: 86rpx;
  color: #454545;
  position: fixed;
  left: 0;
  width: 100%;
  font-size: 28rpx;
  background-color: #fff;
  margin-top: 86rpx;
  top: 0;
  z-index: 9;
}
.productList .nav .item.data-v-8b1a97ba {
  width: 25%;
  text-align: center;
}
.productList .nav .item.font-color.data-v-8b1a97ba {
  font-weight: bold;
}
.productList .nav .item image.data-v-8b1a97ba {
  width: 15rpx;
  height: 19rpx;
  margin-left: 10rpx;
}
.productList .list.data-v-8b1a97ba {
  padding: 0 30rpx;
  margin-top: 192rpx;
}
.productList .list.on.data-v-8b1a97ba {
  border-radius: 14rpx;
  margin-top: 0 !important;
  background-color: #fff;
  padding: 40rpx 0 0 0;
}
.productList .list .item.data-v-8b1a97ba {
  width: 335rpx;
  background-color: #fff;
  border-radius: 14rpx;
  margin-bottom: 20rpx;
}
.productList .list .item.on.data-v-8b1a97ba {
  width: 100%;
  display: flex;
  padding: 0 24rpx 50rpx 24rpx;
  margin: 0;
  border-radius: 14rpx;
}
.productList .list .item .pictrue.data-v-8b1a97ba {
  position: relative;
  width: 100%;
  height: 335rpx;
}
.productList .list .item .pictrue.on.data-v-8b1a97ba {
  width: 180rpx;
  height: 180rpx;
}
.productList .list .item .pictrue image.data-v-8b1a97ba {
  width: 100%;
  height: 100%;
  border-radius: 20rpx 20rpx 0 0;
}
.productList .list .item .pictrue image.on.data-v-8b1a97ba {
  border-radius: 6rpx;
}
.productList .list .item .text.data-v-8b1a97ba {
  padding: 18rpx 20rpx;
  font-size: 30rpx;
  color: #222;
}
.productList .list .item .text.on.data-v-8b1a97ba {
  width: 456rpx;
  padding: 0 0 0 20rpx;
}
.productList .list .item .text .money.data-v-8b1a97ba {
  font-size: 26rpx;
  font-weight: bold;
  margin-top: 8rpx;
}
.productList .list .item .text .money.on.data-v-8b1a97ba {
  margin-top: 50rpx;
}
.productList .list .item .text .money .num.data-v-8b1a97ba {
  font-size: 34rpx;
}
.productList .list .item .text .vip.data-v-8b1a97ba {
  font-size: 22rpx;
  color: #aaa;
  margin-top: 7rpx;
}
.productList .list .item .text .vip.on.data-v-8b1a97ba {
  margin-top: 12rpx;
}
.productList .list .item .text .vip .vip-money.data-v-8b1a97ba {
  font-size: 24rpx;
  color: #282828;
  font-weight: bold;
}
.productList .list .item .text .vip .vip-money image.data-v-8b1a97ba {
  width: 46rpx;
  height: 21rpx;
  margin-left: 4rpx;
}
.noCommodity.data-v-8b1a97ba {
  background-color: #fff;
  padding-bottom: 30rpx;
  margin-top: 172rpx;
}

