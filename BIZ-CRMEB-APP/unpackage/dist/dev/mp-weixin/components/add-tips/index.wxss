@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
@-webkit-keyframes anScale-data-v-c324e33c {
from {
    -webkit-transform: scale3d(0.96, 0.96, 0.96);
    transform: scale3d(0.96, 0.96, 0.96);
}
50% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
}
to {
    -webkit-transform: scale3d(0.96, 0.96, 0.96);
    transform: scale3d(0.96, 0.96, 0.96);
}
}
@keyframes anScale-data-v-c324e33c {
from {
    -webkit-transform: scale3d(0.96, 0.96, 0.96);
    transform: scale3d(0.96, 0.96, 0.96);
}
50% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
}
to {
    -webkit-transform: scale3d(0.96, 0.96, 0.96);
    transform: scale3d(0.96, 0.96, 0.96);
}
}
.anScale.data-v-c324e33c {
  -webkit-animation: anScale-data-v-c324e33c 1s linear infinite;
          animation: anScale-data-v-c324e33c 1s linear infinite;
}
.tip_box.data-v-c324e33c {
  width: 70%;
  position: fixed;
  top: 0;
  right: 0;
  z-index: 100;
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  flex-direction: column;
}
.tip_box .arrow.data-v-c324e33c {
  width: 0;
  height: 0;
  border: 10rpx solid;
  border-color: transparent;
}
.tip_box .container.data-v-c324e33c {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 24rpx;
}
.tip_box .container .tips.data-v-c324e33c {
  flex: 1;
  padding-right: 12rpx;
}
.tip_box .container .close.data-v-c324e33c {
  height: 30rpx;
  width: 30rpx;
  font-size: 20rpx;
  line-height: 30rpx;
  color: #999;
}
.tip_box .container .close .closeImg.data-v-c324e33c {
  height: 100%;
  width: 100%;
}

