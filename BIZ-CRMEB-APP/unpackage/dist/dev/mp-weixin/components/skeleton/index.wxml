<block wx:if="{{show}}"><view style="{{'width:'+(systemInfo.width+'px')+';'+('height:'+(systemInfo.height+'px')+';')+('background-color:'+(bgcolor)+';')+('position:'+('absolute')+';')+('left:'+(0)+';')+('top:'+(0)+';')+('z-index:'+(9998)+';')}}"><block wx:for="{{skeletonRectLists}}" wx:for-item="item" wx:for-index="rect_idx"><view class="{{[loading=='chiaroscuro'?'chiaroscuro':'']}}" style="{{'width:'+(item.width+'px')+';'+('height:'+(item.height+'px')+';')+('background-color:'+('rgb(194, 207, 214,.3)')+';')+('position:'+('absolute')+';')+('left:'+(item.left+'px')+';')+('top:'+(item.top+'px')+';')}}"></view></block><block wx:for="{{skeletonCircleLists}}" wx:for-item="item" wx:for-index="circle_idx"><view class="{{[loading=='chiaroscuro'?'chiaroscuro':'']}}" style="{{'width:'+(item.width+'px')+';'+('height:'+(item.height+'px')+';')+('background-color:'+('rgb(194, 207, 214,.3)')+';')+('border-radius:'+(item.width+'px')+';')+('position:'+('absolute')+';')+('left:'+(item.left+'px')+';')+('top:'+(item.top+'px')+';')}}"></view></block><block wx:if="{{loading=='spin'}}"><view class="spinbox"><view class="spin"></view></view></block></view></block>