
.spinbox {
	position: fixed;
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100%;
	width: 100%;
	z-index: 9999
}
.spin {
	display: inline-block;
	width: 64rpx;
	height: 64rpx;
}
.spin:after {
	content: " ";
	display: block;
	width: 46rpx;
	height: 46rpx;
	margin: 1rpx;
	border-radius: 50%;
	border: 5rpx solid #409eff;
	border-color: #409eff transparent #409eff transparent;
	-webkit-animation: spin 1.2s linear infinite;
	        animation: spin 1.2s linear infinite;
}
@-webkit-keyframes spin {
0% {
		-webkit-transform: rotate(0deg);
		        transform: rotate(0deg);
}
100% {
		-webkit-transform: rotate(360deg);
		        transform: rotate(360deg);
}
}
@keyframes spin {
0% {
		-webkit-transform: rotate(0deg);
		        transform: rotate(0deg);
}
100% {
		-webkit-transform: rotate(360deg);
		        transform: rotate(360deg);
}
}
.chiaroscuro {
	width: 100%;
	height: 100%;
	background: rgb(194, 207, 214);
	-webkit-animation-duration: 2s;
	        animation-duration: 2s;
	-webkit-animation-name: blink;
	        animation-name: blink;
	-webkit-animation-iteration-count: infinite;
	        animation-iteration-count: infinite;
}
@-webkit-keyframes blink {
0% {
		opacity: .4;
}
50% {
		opacity: 1;
}
100% {
		opacity: .4;
}
}
@keyframes blink {
0% {
		opacity: .4;
}
50% {
		opacity: 1;
}
100% {
		opacity: .4;
}
}
@-webkit-keyframes flush {
0% {
		left: -100%;
}
50% {
		left: 0;
}
100% {
		left: 100%;
}
}
@keyframes flush {
0% {
		left: -100%;
}
50% {
		left: 0;
}
100% {
		left: 100%;
}
}
.shine {
	-webkit-animation: flush 2s linear infinite;
	        animation: flush 2s linear infinite;
	position: absolute;
	top: 0;
	bottom: 0;
	width: 100%;
	background: linear-gradient(to left,
			rgba(255, 255, 255, 0) 0%,
			rgba(255, 255, 255, .85) 50%,
			rgba(255, 255, 255, 0) 100%)
}

