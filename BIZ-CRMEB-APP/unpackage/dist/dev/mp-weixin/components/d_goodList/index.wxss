@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.goodsList {
  padding: 0 30rpx;
}
.goodsList .item {
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 63rpx;
}
.goodsList .item .pictrue {
  width: 100%;
  height: 216rpx;
  border-radius: 16rpx;
  position: relative;
}
.goodsList .item .pictrue image {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
}
.goodsList .item .text {
  font-size: 30rpx;
  font-family: PingFang SC;
  font-weight: bold;
  color: #282828;
  margin: 20rpx 0;
}
.goodsList .item .bottom .sales {
  font-size: 22rpx;
  color: #8E8E8E;
}
.goodsList .item .bottom .sales .money {
  font-size: 42rpx;
  font-weight: bold;
  margin-right: 18rpx;
  color: theme;
}
[data-theme="theme1"] .goodsList .item .bottom .sales .money {
  color: #F93323 !important;
}
[data-theme="theme2"] .goodsList .item .bottom .sales .money {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .goodsList .item .bottom .sales .money {
  color: #FF7600 !important;
}
[data-theme="theme4"] .goodsList .item .bottom .sales .money {
  color: #FD502F !important;
}
[data-theme="theme5"] .goodsList .item .bottom .sales .money {
  color: #FF448F !important;
}
.goodsList .item .bottom .sales .money .item_sales {
  font-size: 24rpx;
  font-family: PingFang SC;
  font-weight: 400;
  padding-left: 17rpx;
  color: #8e8e8e;
}
.goodsList .item .bottom .sales .money text {
  font-size: 28rpx;
}
.goodsList .item .bottom .cart {
  height: 56rpx;
}
.goodsList .item .bottom .cart .pictrue {
  color: #E93323;
  font-size: 46rpx;
  width: 50rpx;
  height: 50rpx;
  text-align: center;
  line-height: 50rpx;
}
.goodsList .item .bottom .cart .pictrue.icon-jiahao {
  background: linear-gradient(140deg, #FA6514 0%, #E93323 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.goodsList .item .bottom .cart .num {
  font-size: 30rpx;
  color: #282828;
  font-weight: bold;
  width: 80rpx;
  text-align: center;
}
.goodsList .item .bottom .bnt {
  padding: 0 30rpx;
  height: 56rpx;
  line-height: 56rpx;
  background-color: theme;
  border-radius: 42rpx;
  font-size: 26rpx;
  color: #fff;
  position: relative;
}
[data-theme="theme1"] .goodsList .item .bottom .bnt {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .goodsList .item .bottom .bnt {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .goodsList .item .bottom .bnt {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .goodsList .item .bottom .bnt {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .goodsList .item .bottom .bnt {
  background-color: #FF448F !important;
}
.goodsList .item .bottom .bnt .num {
  color: theme;
  border: theme;
  background: #fff;
  min-width: 12rpx;
  border-radius: 15px;
  position: absolute;
  right: -14rpx;
  top: -15rpx;
  font-size: 22rpx;
  padding: 0 10rpx;
  height: 34rpx;
  line-height: 34rpx;
}
[data-theme="theme1"] .goodsList .item .bottom .bnt .num {
  color: #e93323 !important;
}
[data-theme="theme2"] .goodsList .item .bottom .bnt .num {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .goodsList .item .bottom .bnt .num {
  color: #42CA4D !important;
}
[data-theme="theme4"] .goodsList .item .bottom .bnt .num {
  color: #1DB0FC !important;
}
[data-theme="theme5"] .goodsList .item .bottom .bnt .num {
  color: #FF448F !important;
}
[data-theme="theme1"] .goodsList .item .bottom .bnt .num {
  border: 1px solid #e93323;
}
[data-theme="theme2"] .goodsList .item .bottom .bnt .num {
  border: 1px solid #FE5C2D;
}
[data-theme="theme3"] .goodsList .item .bottom .bnt .num {
  border: 1px solid #42CA4D;
}
[data-theme="theme4"] .goodsList .item .bottom .bnt .num {
  border: 1px solid #1DB0FC;
}
[data-theme="theme5"] .goodsList .item .bottom .bnt .num {
  border: 1px solid #FF448F;
}
.goodsList .item .bottom .end {
  padding: 0 30rpx;
  height: 56rpx;
  line-height: 56rpx;
  border-radius: 42rpx;
  font-size: 26rpx;
  color: #fff;
  position: relative;
  background: #cbcbcb;
}

