<view class="orderGoods borRadius14 data-v-0d23a466"><view class="total data-v-0d23a466">{{"共"+(orderProNum?orderProNum:totalNmu)+"件商品"}}</view><view class="goodWrapper pad30 data-v-0d23a466"><block wx:for="{{cartInfo}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['jumpCon',['$0'],[[['cartInfo','',index,'productId']]]]]]]}}" class="item acea-row row-between-wrapper data-v-0d23a466" bindtap="__e"><view class="pictrue data-v-0d23a466"><image src="{{item.image}}" class="data-v-0d23a466"></image></view><view class="text data-v-0d23a466"><view class="acea-row row-between-wrapper data-v-0d23a466"><view class="name line1 data-v-0d23a466">{{item.productName?item.productName:item.storeName}}</view><view class="num data-v-0d23a466">{{"x "+(item.payNum?item.payNum:item.cartNum)}}</view></view><block wx:if="{{item.sku}}"><view class="attr line1 data-v-0d23a466">{{item.sku}}</view></block><view class="money data-v-0d23a466">·</view><block wx:if="{{item.isReply==0&&evaluate==2}}"><view data-event-opts="{{[['tap',[['evaluateTap',['$0'],[[['cartInfo','',index]]]]]]]}}" class="evaluate data-v-0d23a466" catchtap="__e">评价</view></block><block wx:else><block wx:if="{{item.isReply==1}}"><view class="evaluate data-v-0d23a466">已评价</view></block></block></view></view></block></view></view>