<view><view class="{{['cartList',cartData.iScart?'on':'']}}"><view class="title acea-row row-between-wrapper"><view class="name">已选商品</view><view data-event-opts="{{[['tap',[['subDel',['$event']]]]]}}" class="del acea-row row-middle" bindtap="__e"><view class="iconfont icon-shanchu1"></view>清空</view></view><view class="list"><block wx:for="{{cartData.cartList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item acea-row row-between-wrapper"><view class="pictrue"><image src="{{item.image}}"></image></view><view class="txtPic"><view class="{{['name','line2',item.attrStatus?'':'on']}}">{{item.storeName}}</view><block wx:if="{{item.attrStatus}}"><view><view class="info">{{item.suk}}</view><view class="bottom acea-row row-between-wrapper"><view class="money">￥<text class="num">{{item.vipPrice?item.vipPrice:item.price}}</text></view><view class="cartNum acea-row row-middle"><view data-event-opts="{{[['tap',[['leaveCart',[index]]]]]}}" class="reduce iconfont icon-jianhao1" bindtap="__e"></view><view class="num">{{item.cartNum}}</view><view data-event-opts="{{[['tap',[['joinCart',[index]]]]]}}" class="plus iconfont icon-jiahao1" bindtap="__e"></view></view></view></view></block><block wx:else><block wx:if="{{item.stock==0}}"><view class="noBnt">已售罄</view></block></block></view></view></block></view></view><block wx:if="{{cartData.iScart}}"><view data-event-opts="{{[['tap',[['closeList',['$event']]]]]}}" class="mask" bindtap="__e"></view></block></view>