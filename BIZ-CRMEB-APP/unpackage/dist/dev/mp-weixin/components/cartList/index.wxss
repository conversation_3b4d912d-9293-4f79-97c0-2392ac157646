@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.mask {
  z-index: 99;
}
.cartList {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  background-color: #fff;
  z-index: 100;
  padding: 0 30rpx 100rpx 30rpx;
  box-sizing: border-box;
  border-radius: 16rpx 16rpx 0 0;
  -webkit-transform: translate3d(0, 100%, 0);
          transform: translate3d(0, 100%, 0);
  transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
}
.cartList.on {
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
}
.cartList .title {
  height: 108rpx;
}
.cartList .title .name {
  font-size: 28rpx;
  color: #282828;
  font-weight: bold;
}
.cartList .title .del {
  font-size: 26rpx;
  color: theme;
}
[data-theme="theme1"] .cartList .title .del {
  color: #e93323 !important;
}
[data-theme="theme2"] .cartList .title .del {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .cartList .title .del {
  color: #42CA4D !important;
}
[data-theme="theme4"] .cartList .title .del {
  color: #1DB0FC !important;
}
[data-theme="theme5"] .cartList .title .del {
  color: #FF448F !important;
}
.cartList .title .del .iconfont {
  margin-right: 5rpx;
  font-size: 34rpx;
}
.cartList .list {
  max-height: 720rpx;
  overflow-x: hidden;
  overflow-y: auto;
}
.cartList .list .item {
  margin-bottom: 40rpx;
}
.cartList .list .item .pictrue {
  width: 176rpx;
  height: 176rpx;
  border-radius: 16rpx;
  position: relative;
}
.cartList .list .item .pictrue image {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
}
.cartList .list .item .pictrue .mantle {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.65);
  border-radius: 16rpx;
}
.cartList .list .item .txtPic {
  width: 486rpx;
}
.cartList .list .item .txtPic .name {
  font-size: 28rpx;
  color: #282828;
}
.cartList .list .item .txtPic .name.on {
  color: #A3A3A3;
}
.cartList .list .item .txtPic .noBnt {
  width: 126rpx;
  height: 44rpx;
  background: #f2f2f2;
  border-radius: 22rpx;
  text-align: center;
  line-height: 44rpx;
  font-size: 24rpx;
  color: #A3A3A3;
  margin-top: 10rpx;
}
.cartList .list .item .txtPic .delTxt {
  margin-top: 48rpx;
  font-size: 24rpx;
  color: #E93323;
}
.cartList .list .item .txtPic .delTxt text {
  width: 70rpx;
  height: 50rpx;
  text-align: center;
  line-height: 50rpx;
}
.cartList .list .item .txtPic .info {
  font-size: 23rpx;
  color: #989898;
  margin-top: 5rpx;
}
.cartList .list .item .txtPic .bottom {
  margin-top: 11rpx;
}
.cartList .list .item .txtPic .bottom .money {
  font-weight: bold;
  font-size: 26rpx;
  color: theme;
}
[data-theme="theme1"] .cartList .list .item .txtPic .bottom .money {
  color: #F93323 !important;
}
[data-theme="theme2"] .cartList .list .item .txtPic .bottom .money {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .cartList .list .item .txtPic .bottom .money {
  color: #FF7600 !important;
}
[data-theme="theme4"] .cartList .list .item .txtPic .bottom .money {
  color: #FD502F !important;
}
[data-theme="theme5"] .cartList .list .item .txtPic .bottom .money {
  color: #FF448F !important;
}
.cartList .list .item .txtPic .bottom .money .num {
  font-size: 34rpx;
}
.cartList .list .item .txtPic .bottom .cartNum {
  font-weight: bold;
}
.cartList .list .item .txtPic .bottom .cartNum .num {
  font-size: 34rpx;
  color: #282828;
  width: 120rpx;
  text-align: center;
}
.cartList .list .item .txtPic .bottom .cartNum .reduce {
  color: #282828;
  font-size: 24rpx;
  width: 60rpx;
  height: 60rpx;
  text-align: center;
  line-height: 60rpx;
}
.cartList .list .item .txtPic .bottom .cartNum .plus {
  color: #282828;
  font-size: 24rpx;
  width: 60rpx;
  height: 60rpx;
  text-align: center;
  line-height: 60rpx;
}

