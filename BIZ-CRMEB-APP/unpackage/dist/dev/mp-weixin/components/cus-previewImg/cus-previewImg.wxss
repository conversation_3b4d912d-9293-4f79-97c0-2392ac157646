@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.previewImg.data-v-4a9cdc64 {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 300;
  width: 100%;
  height: 100%;
}
.previewImg .mask.data-v-4a9cdc64 {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000;
  opacity: 1;
  z-index: 8;
  width: 100%;
  height: 100%;
}
.previewImg .mask-swiper.data-v-4a9cdc64 {
  width: 100%;
  height: 100%;
}
.previewImg .mask-swiper-img.data-v-4a9cdc64 {
  width: 100%;
}
.previewImg .pagebox.data-v-4a9cdc64 {
  position: absolute;
  width: 100%;
  bottom: 20rpx;
  z-index: 300;
  color: #fff;
  text-align: center;
}
.mask_sku.data-v-4a9cdc64 {
  color: #fff;
  max-width: 80%;
  z-index: 300;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 30rpx;
}
.mask_sku .sku_name.data-v-4a9cdc64 {
  font-size: 12px;
  border: 1px solid #fff;
  padding: 10rpx 30rpx 10rpx;
  border-radius: 40px;
  box-sizing: border-box;
}
.mask_sku .sku_price.data-v-4a9cdc64 {
  padding-top: 10px;
}
.font12.data-v-4a9cdc64 {
  font-size: 24rpx;
}
.share_btn.data-v-4a9cdc64 {
  position: absolute;
  top: 70rpx;
  right: 50rpx;
  font-size: 40rpx;
  color: #fff;
  z-index: 300;
}
.flex-column.data-v-4a9cdc64 {
  flex-direction: column;
}
.justify-center.data-v-4a9cdc64 {
  justify-content: center;
}
.align-center.data-v-4a9cdc64 {
  align-items: center;
}

