<block wx:if="{{showBox}}"><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="previewImg data-v-4a9cdc64" catchtouchmove="__e"><view data-event-opts="{{[['tap',[['close',['$event']]]]]}}" class="mask data-v-4a9cdc64" bindtap="__e"><swiper class="mask-swiper data-v-4a9cdc64" current="{{currentIndex}}" circular="{{circular}}" duration="{{duration}}" data-event-opts="{{[['change',[['changeSwiper',['$event']]]]]}}" bindchange="__e"><block wx:for="{{list}}" wx:for-item="src" wx:for-index="i" wx:key="i"><swiper-item class="flex flex-column justify-center align-center data-v-4a9cdc64"><image class="mask-swiper-img data-v-4a9cdc64" src="{{src.image}}" mode="widthFix"></image><view class="mask_sku data-v-4a9cdc64"><text class="sku_name data-v-4a9cdc64">{{src.suk}}</text><text class="sku_price data-v-4a9cdc64">{{"￥"+src.price}}</text></view></swiper-item></block></swiper></view><block wx:if="{{$root.g0>0}}"><view class="pagebox data-v-4a9cdc64">{{$root.m0+1+" / "+$root.g1}}</view></block></view></block>