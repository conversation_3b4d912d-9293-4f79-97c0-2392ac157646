@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.address-window.data-v-9b690cc8 {
  background-color: #fff;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 101;
  -webkit-transform: translate3d(0, 100%, 0);
          transform: translate3d(0, 100%, 0);
  transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
}
.font_color.data-v-9b690cc8 {
  color: theme;
}
[data-theme="theme1"] .font_color.data-v-9b690cc8 {
  color: #e93323 !important;
}
[data-theme="theme2"] .font_color.data-v-9b690cc8 {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .font_color.data-v-9b690cc8 {
  color: #42CA4D !important;
}
[data-theme="theme4"] .font_color.data-v-9b690cc8 {
  color: #1DB0FC !important;
}
[data-theme="theme5"] .font_color.data-v-9b690cc8 {
  color: #FF448F !important;
}
.bg_color.data-v-9b690cc8 {
  background-color: theme;
}
[data-theme="theme1"] .bg_color.data-v-9b690cc8 {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .bg_color.data-v-9b690cc8 {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .bg_color.data-v-9b690cc8 {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .bg_color.data-v-9b690cc8 {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .bg_color.data-v-9b690cc8 {
  background-color: #FF448F !important;
}
.address-window.on.data-v-9b690cc8 {
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
}
.address-window .title.data-v-9b690cc8 {
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  height: 123rpx;
  line-height: 123rpx;
  position: relative;
}
.address-window .title .iconfont.data-v-9b690cc8 {
  position: absolute;
  right: 30rpx;
  color: #8a8a8a;
  font-size: 35rpx;
}
.address-window .list .item.data-v-9b690cc8 {
  margin-left: 30rpx;
  padding-right: 30rpx;
  border-bottom: 1px solid #eee;
  height: 129rpx;
  font-size: 25rpx;
  color: #333;
}
.address-window .list .item .iconfont.data-v-9b690cc8 {
  font-size: 37rpx;
  color: #2c2c2c;
}
.address-window .list .item .iconfont.icon-complete.data-v-9b690cc8 {
  font-size: 30rpx;
  color: #fff;
}
.address-window .list .item .address.data-v-9b690cc8 {
  width: 560rpx;
}
.address-window .list .item .address .name.data-v-9b690cc8 {
  font-size: 28rpx;
  font-weight: bold;
  color: #282828;
  margin-bottom: 4rpx;
}
.address-window .list .item .address .name .phone.data-v-9b690cc8 {
  margin-left: 18rpx;
}
.address-window .addressBnt.data-v-9b690cc8 {
  font-size: 30rpx;
  font-weight: bold;
  color: #fff;
  width: 690rpx;
  height: 86rpx;
  border-radius: 43rpx;
  text-align: center;
  line-height: 86rpx;
  margin: 85rpx auto;
}
.address-window .pictrue.data-v-9b690cc8 {
  width: 414rpx;
  height: 336rpx;
  margin: 0 auto;
}
.address-window .pictrue image.data-v-9b690cc8 {
  width: 100%;
  height: 100%;
}

