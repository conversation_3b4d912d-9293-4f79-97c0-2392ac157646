@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.promotionGood.data-v-7b4f8cd0 {
  padding: 0 30rpx;
  display: flex;
  flex-wrap: wrap;
  padding: 15rpx 24rpx;
}
.promotionGood .item.data-v-7b4f8cd0 {
  width: 215rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 15rpx 9rpx;
}
.promotionGood .item .pictrue.data-v-7b4f8cd0 {
  height: 198rpx;
  border-radius: 12rpx;
}
.promotionGood .item .pictrue image.data-v-7b4f8cd0 {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}
.promotionGood .item .money.data-v-7b4f8cd0 {
  font-size: 30rpx;
  margin-top: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.promotionGood .item .money .rmb.data-v-7b4f8cd0 {
  font-weight: bold;
  color: theme;
  font-size: 20rpx;
}
[data-theme="theme1"] .promotionGood .item .money .rmb.data-v-7b4f8cd0 {
  color: #F93323 !important;
}
[data-theme="theme2"] .promotionGood .item .money .rmb.data-v-7b4f8cd0 {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .promotionGood .item .money .rmb.data-v-7b4f8cd0 {
  color: #FF7600 !important;
}
[data-theme="theme4"] .promotionGood .item .money .rmb.data-v-7b4f8cd0 {
  color: #FD502F !important;
}
[data-theme="theme5"] .promotionGood .item .money .rmb.data-v-7b4f8cd0 {
  color: #FF448F !important;
}
.promotionGood .item .money .price.data-v-7b4f8cd0 {
  color: theme;
  font-weight: bold;
}
[data-theme="theme1"] .promotionGood .item .money .price.data-v-7b4f8cd0 {
  color: #F93323 !important;
}
[data-theme="theme2"] .promotionGood .item .money .price.data-v-7b4f8cd0 {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .promotionGood .item .money .price.data-v-7b4f8cd0 {
  color: #FF7600 !important;
}
[data-theme="theme4"] .promotionGood .item .money .price.data-v-7b4f8cd0 {
  color: #FD502F !important;
}
[data-theme="theme5"] .promotionGood .item .money .price.data-v-7b4f8cd0 {
  color: #FF448F !important;
}
.promotionGood .item .money .ot-price.data-v-7b4f8cd0 {
  color: #999;
  text-decoration: line-through;
  font-size: 20rpx;
  margin-left: 4rpx;
}

