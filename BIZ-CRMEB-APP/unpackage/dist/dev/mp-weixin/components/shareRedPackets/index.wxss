@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.sharing-packets.data-v-1cb57b71 {
  position: fixed;
  left: 30rpx;
  z-index: 99;
  transition: all .2s linear;
}
.sharing-packets.hide_left.data-v-1cb57b71 {
  transition: all .2s linear;
  left: -110rpx;
  -webkit-transform: scale(0.6);
          transform: scale(0.6);
}
.sharing-packets .iconfont.data-v-1cb57b71 {
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  text-align: center;
  line-height: 44rpx;
  background-color: #999;
  font-size: 20rpx;
  color: #fff;
  margin: 0 auto;
  box-sizing: border-box;
  padding-left: 1px;
}
.sharing-packets .line.data-v-1cb57b71 {
  width: 2rpx;
  height: 40rpx;
  background-color: #999;
  margin: 0 auto;
}
.sharing-packets .sharing-con.data-v-1cb57b71 {
  width: 187rpx;
  height: 210rpx;
  position: relative;
  background-size: cover;
}
.sharing-packets .sharing-con .text.data-v-1cb57b71 {
  position: absolute;
  top: 30rpx;
  font-size: 20rpx;
  width: 100%;
  text-align: center;
}
.sharing-packets .sharing-con .text .money.data-v-1cb57b71 {
  font-size: 32rpx;
  font-weight: bold;
  margin-top: 5rpx;
}
.sharing-packets .sharing-con .text .money .label.data-v-1cb57b71 {
  font-size: 20rpx;
}
.sharing-packets .sharing-con .text .tip.data-v-1cb57b71 {
  font-size: 18rpx;
  color: #999;
  margin-top: 5rpx;
}
.sharing-packets .sharing-con .text .shareBut.data-v-1cb57b71 {
  font-size: 22rpx;
  color: #fff;
  margin-top: 28rpx;
  height: 50rpx;
  line-height: 50rpx;
}
.main_color.data-v-1cb57b71 {
  color: theme;
}
[data-theme="theme1"] .main_color.data-v-1cb57b71 {
  color: #e93323 !important;
}
[data-theme="theme2"] .main_color.data-v-1cb57b71 {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .main_color.data-v-1cb57b71 {
  color: #42CA4D !important;
}
[data-theme="theme4"] .main_color.data-v-1cb57b71 {
  color: #1DB0FC !important;
}
[data-theme="theme5"] .main_color.data-v-1cb57b71 {
  color: #FF448F !important;
}
.price.data-v-1cb57b71 {
  color: theme;
}
[data-theme="theme1"] .price.data-v-1cb57b71 {
  color: #F93323 !important;
}
[data-theme="theme2"] .price.data-v-1cb57b71 {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .price.data-v-1cb57b71 {
  color: #FF7600 !important;
}
[data-theme="theme4"] .price.data-v-1cb57b71 {
  color: #FD502F !important;
}
[data-theme="theme5"] .price.data-v-1cb57b71 {
  color: #FF448F !important;
}

