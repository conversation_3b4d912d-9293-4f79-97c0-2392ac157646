@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.recommend.data-v-72406dd4 {
  background-color: #fff;
}
.recommend .title.data-v-72406dd4 {
  height: 135rpx;
  line-height: 135rpx;
  font-size: 28rpx;
  color: #282828;
}
.recommend .title .name.data-v-72406dd4 {
  margin: 0 28rpx;
}
.recommend .title .iconfont.data-v-72406dd4 {
  font-size: 170rpx;
  color: #454545;
}
.recommend .title .iconfont.lefticon.data-v-72406dd4 {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}
.recommend .recommendList.data-v-72406dd4 {
  padding: 0 30rpx;
}
.recommend .recommendList .item.data-v-72406dd4 {
  width: 335rpx;
  margin-bottom: 30rpx;
}
.recommend .recommendList .item .pictrue.data-v-72406dd4 {
  position: relative;
  width: 100%;
  height: 335rpx;
}
.recommend .recommendList .item .pictrue image.data-v-72406dd4 {
  width: 100%;
  height: 100%;
  border-radius: 14rpx;
}
.recommend .recommendList .item .name.data-v-72406dd4 {
  font-size: 28rpx;
  color: #282828;
  margin-top: 20rpx;
}
.money.data-v-72406dd4 {
  font-size: 20rpx;
  margin-top: 8rpx;
  font-weight: 600;
  color: theme;
}
[data-theme="theme1"] .money.data-v-72406dd4 {
  color: #F93323 !important;
}
[data-theme="theme2"] .money.data-v-72406dd4 {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .money.data-v-72406dd4 {
  color: #FF7600 !important;
}
[data-theme="theme4"] .money.data-v-72406dd4 {
  color: #FD502F !important;
}
[data-theme="theme5"] .money.data-v-72406dd4 {
  color: #FF448F !important;
}
.recommend .recommendList .item .money .num.data-v-72406dd4 {
  font-size: 28rpx;
}

