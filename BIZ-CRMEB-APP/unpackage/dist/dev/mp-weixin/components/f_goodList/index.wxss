@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.goodsList {
  padding: 0 30rpx;
}
.goodsList .item {
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 63rpx;
}
.goodsList .item .pic {
  width: 140rpx;
  height: 140rpx;
  border-radius: 10rpx;
  position: relative;
  border-radius: 22rpx;
}
.goodsList .item .pic image {
  width: 100%;
  height: 100%;
  border-radius: 22rpx;
}
.goodsList .item .pictxt {
  width: 372rpx;
}
.goodsList .item .pictxt .text {
  font-size: 26rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #333333;
}
.goodsList .item .pictxt .bottom {
  margin-top: 22rpx;
}
.goodsList .item .pictxt .bottom .money {
  font-size: 34rpx;
  font-weight: 800;
  width: 212rpx;
  color: theme;
}
[data-theme="theme1"] .goodsList .item .pictxt .bottom .money {
  color: #F93323 !important;
}
[data-theme="theme2"] .goodsList .item .pictxt .bottom .money {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .goodsList .item .pictxt .bottom .money {
  color: #FF7600 !important;
}
[data-theme="theme4"] .goodsList .item .pictxt .bottom .money {
  color: #FD502F !important;
}
[data-theme="theme5"] .goodsList .item .pictxt .bottom .money {
  color: #FF448F !important;
}
.goodsList .item .pictxt .bottom .money .sign {
  font-size: 24rpx;
}
.goodsList .item .pictxt .bottom .otPrice {
  font-size: 20rpx;
  font-family: PingFang SC;
  font-weight: 400;
  line-height: 24rpx;
  padding-left: 14rpx;
  color: #999999;
}
.goodsList .item .pictxt .bottom .cart {
  height: 46rpx;
}
.goodsList .item .pictxt .bottom .cart .pictrue {
  color: #1DB0FC;
  font-size: 46rpx;
  width: 46rpx;
  height: 46rpx;
  text-align: center;
  line-height: 46rpx;
}
.goodsList .item .pictxt .bottom .cart .pictrue.icon-jiahao {
  color: #1DB0FC;
}
.goodsList .item .pictxt .bottom .cart .num {
  font-size: 30rpx;
  color: #333333;
  font-weight: bold;
  width: 60rpx;
  text-align: center;
}
.goodsList .item .pictxt .bottom .icon-gouwuche6 {
  width: 46rpx;
  height: 46rpx;
  background-color: #1DB0FC;
  border-radius: 50%;
  color: #ffffff;
  font-size: 30rpx;
}
.goodsList .item .pictxt .bottom .bnt {
  padding: 0 20rpx;
  height: 46rpx;
  line-height: 46rpx;
  background-color: theme;
  border-radius: 23rpx;
  font-size: 22rpx;
  color: #ffffff;
  position: relative;
}
[data-theme="theme1"] .goodsList .item .pictxt .bottom .bnt {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .goodsList .item .pictxt .bottom .bnt {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .goodsList .item .pictxt .bottom .bnt {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .goodsList .item .pictxt .bottom .bnt {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .goodsList .item .pictxt .bottom .bnt {
  background-color: #FF448F !important;
}
.goodsList .item .pictxt .bottom .bnt.end {
  background: #cccccc;
}
.goodsList .item .pictxt .bottom .bnt .num {
  min-width: 12rpx;
  color: theme;
  border: theme;
  background: #fff;
  border-radius: 15px;
  position: absolute;
  right: -13rpx;
  top: -11rpx;
  font-size: 16rpx;
  padding: 0 11rpx;
  height: 32rpx;
  line-height: 32rpx;
}
[data-theme="theme1"] .goodsList .item .pictxt .bottom .bnt .num {
  color: #e93323 !important;
}
[data-theme="theme2"] .goodsList .item .pictxt .bottom .bnt .num {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .goodsList .item .pictxt .bottom .bnt .num {
  color: #42CA4D !important;
}
[data-theme="theme4"] .goodsList .item .pictxt .bottom .bnt .num {
  color: #1DB0FC !important;
}
[data-theme="theme5"] .goodsList .item .pictxt .bottom .bnt .num {
  color: #FF448F !important;
}
[data-theme="theme1"] .goodsList .item .pictxt .bottom .bnt .num {
  border: 1px solid #e93323;
}
[data-theme="theme2"] .goodsList .item .pictxt .bottom .bnt .num {
  border: 1px solid #FE5C2D;
}
[data-theme="theme3"] .goodsList .item .pictxt .bottom .bnt .num {
  border: 1px solid #42CA4D;
}
[data-theme="theme4"] .goodsList .item .pictxt .bottom .bnt .num {
  border: 1px solid #1DB0FC;
}
[data-theme="theme5"] .goodsList .item .pictxt .bottom .bnt .num {
  border: 1px solid #FF448F;
}

