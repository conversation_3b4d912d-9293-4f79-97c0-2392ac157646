<view class="goodsList"><block wx:for="{{tempArr}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goDetail',['$0'],[[['tempArr','',index]]]]]]]}}" class="item acea-row row-between-wrapper" bindtap="__e"><view class="pic"><image src="{{item.image}}" mode></image></view><view class="pictxt"><view class="text line2">{{item.storeName}}</view><view class="bottom acea-row row-between-wrapper"><view class="money"><text class="sign">￥</text>{{item.price+''}}</view><block wx:if="{{item.stock>0}}"><view><view><view data-event-opts="{{[['tap',[['goCartDuo',['$0'],[[['tempArr','',index]]]]]]]}}" class="bnt" catchtap="__e">选规格<block wx:if="{{item.cartNum}}"><view class="num">{{item.cartNum}}</view></block></view></view></view></block><block wx:else><view class="bnt end">已售罄</view></block></view></view></view></block></view>