@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.product-window.data-v-286e5497 {
  position: fixed;
  bottom: 0;
  width: 100%;
  left: 0;
  background-color: #fff;
  z-index: 77;
  border-radius: 16rpx 16rpx 0 0;
  padding-bottom: 100rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) + 100rpx);
  -webkit-transform: translate3d(0, 100%, 0);
          transform: translate3d(0, 100%, 0);
  transition: all 0.2s cubic-bezier(0, 0, 0.25, 1);
}
.product-window.on.data-v-286e5497 {
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
}
.product-window.join.data-v-286e5497 {
  padding-bottom: 30rpx;
}
.product-window.joinCart.data-v-286e5497 {
  padding-bottom: 30rpx;
  z-index: 999;
}
.product-window .textpic.data-v-286e5497 {
  padding: 0 130rpx 0 30rpx;
  margin-top: 29rpx;
  position: relative;
}
.product-window .textpic .pictrue.data-v-286e5497 {
  width: 150rpx;
  height: 150rpx;
}
.product-window .textpic .pictrue image.data-v-286e5497 {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}
.product-window .textpic .text.data-v-286e5497 {
  width: 410rpx;
  font-size: 32rpx;
  color: #333333;
}
.product-window .textpic .text .money.data-v-286e5497 {
  font-size: 24rpx;
  margin-top: 23rpx;
  color: theme;
}
[data-theme="theme1"] .product-window .textpic .text .money.data-v-286e5497 {
  color: #F93323 !important;
}
[data-theme="theme2"] .product-window .textpic .text .money.data-v-286e5497 {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .product-window .textpic .text .money.data-v-286e5497 {
  color: #FF7600 !important;
}
[data-theme="theme4"] .product-window .textpic .text .money.data-v-286e5497 {
  color: #FD502F !important;
}
[data-theme="theme5"] .product-window .textpic .text .money.data-v-286e5497 {
  color: #FF448F !important;
}
.product-window .textpic .text .money .num.data-v-286e5497 {
  font-family: PingFang SC;
  font-size: 36rpx;
  font-weight: 600;
}
.product-window .textpic .text .money .stock.data-v-286e5497 {
  color: #999;
}
.product-window .textpic .iconfont.data-v-286e5497 {
  position: absolute;
  right: 30rpx;
  top: -5rpx;
  font-size: 35rpx;
  color: #8a8a8a;
}
.product-window .rollTop.data-v-286e5497 {
  max-height: 760rpx;
  overflow: auto;
  margin-top: 36rpx;
}
.product-window .productWinList .item ~ .item.data-v-286e5497 {
  margin-top: 36rpx;
}
.product-window .productWinList .item .title.data-v-286e5497 {
  font-size: 30rpx;
  color: #999;
  padding: 0 30rpx;
}
.product-window .productWinList .item .listn.data-v-286e5497 {
  padding: 0 30rpx 0 16rpx;
}
.product-window .productWinList .item .listn .itemn.data-v-286e5497 {
  border: 1px solid #F2F2F2;
  font-size: 26rpx;
  color: #282828;
  padding: 7rpx 33rpx;
  border-radius: 40rpx;
  margin: 20rpx 0 0 14rpx;
  background-color: #F2F2F2;
}
.product-window .productWinList .item .listn .itemn.on.data-v-286e5497 {
  color: theme;
  border: theme;
  background-color: theme;
}
[data-theme="theme1"] .product-window .productWinList .item .listn .itemn.on.data-v-286e5497 {
  color: #e93323 !important;
}
[data-theme="theme2"] .product-window .productWinList .item .listn .itemn.on.data-v-286e5497 {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .product-window .productWinList .item .listn .itemn.on.data-v-286e5497 {
  color: #42CA4D !important;
}
[data-theme="theme4"] .product-window .productWinList .item .listn .itemn.on.data-v-286e5497 {
  color: #1DB0FC !important;
}
[data-theme="theme5"] .product-window .productWinList .item .listn .itemn.on.data-v-286e5497 {
  color: #FF448F !important;
}
[data-theme="theme1"] .product-window .productWinList .item .listn .itemn.on.data-v-286e5497 {
  border: 1px solid #e93323;
}
[data-theme="theme2"] .product-window .productWinList .item .listn .itemn.on.data-v-286e5497 {
  border: 1px solid #FE5C2D;
}
[data-theme="theme3"] .product-window .productWinList .item .listn .itemn.on.data-v-286e5497 {
  border: 1px solid #42CA4D;
}
[data-theme="theme4"] .product-window .productWinList .item .listn .itemn.on.data-v-286e5497 {
  border: 1px solid #1DB0FC;
}
[data-theme="theme5"] .product-window .productWinList .item .listn .itemn.on.data-v-286e5497 {
  border: 1px solid #FF448F;
}
[data-theme="theme1"] .product-window .productWinList .item .listn .itemn.on.data-v-286e5497 {
  background-color: #FDEBE9 !important;
}
[data-theme="theme2"] .product-window .productWinList .item .listn .itemn.on.data-v-286e5497 {
  background-color: #FFEFEA !important;
}
[data-theme="theme3"] .product-window .productWinList .item .listn .itemn.on.data-v-286e5497 {
  background-color: #ECFAEE !important;
}
[data-theme="theme4"] .product-window .productWinList .item .listn .itemn.on.data-v-286e5497 {
  background-color: #E9F7FF !important;
}
[data-theme="theme5"] .product-window .productWinList .item .listn .itemn.on.data-v-286e5497 {
  background-color: #FFEDF4 !important;
}
.product-window .productWinList .item .listn .itemn.limit.data-v-286e5497 {
  color: #999;
  text-decoration: line-through;
}
.product-window .cart.data-v-286e5497 {
  margin-top: 50rpx;
  margin-bottom: 30rpx;
  padding: 0 30rpx;
}
.product-window .cart .title.data-v-286e5497 {
  font-size: 30rpx;
  color: #999;
}
.product-window .cart .carnum.data-v-286e5497 {
  height: 54rpx;
}
.product-window .cart .carnum view.data-v-286e5497 {
  width: 84rpx;
  text-align: center;
  height: 100%;
  line-height: 54rpx;
  color: #282828;
  font-size: 45rpx;
}
.product-window .cart .carnum .reduce.data-v-286e5497 {
  border-right: 0;
  border-radius: 6rpx 0 0 6rpx;
  line-height: 48rpx;
}
.product-window .cart .carnum .reduce.on.data-v-286e5497 {
  color: #DEDEDE;
  font-size: 44rpx;
}
.product-window .cart .carnum .plus.data-v-286e5497 {
  border-left: 0;
  border-radius: 0 6rpx 6rpx 0;
  line-height: 46rpx;
}
.product-window .cart .carnum .plus.on.data-v-286e5497 {
  border-color: #e3e3e3;
  color: #dedede;
}
.product-window .cart .carnum .num.data-v-286e5497 {
  background: #f2f2f2;
  color: #282828;
  font-size: 28rpx;
  border-radius: 12rpx;
  line-height: 29px;
  height: 54rpx;
}
.product-window .cart .carnum .num input.data-v-286e5497 {
  display: -webkit-inline-box;
}
.product-window .joinBnt.data-v-286e5497 {
  font-size: 30rpx;
  width: 620rpx;
  height: 86rpx;
  border-radius: 50rpx;
  text-align: center;
  line-height: 86rpx;
  color: #fff;
  margin: 21rpx auto 0 auto;
}
.align-baseline.data-v-286e5497 {
  align-items: baseline;
}
.bg_color.data-v-286e5497 {
  background-color: theme;
}
[data-theme="theme1"] .bg_color.data-v-286e5497 {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .bg_color.data-v-286e5497 {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .bg_color.data-v-286e5497 {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .bg_color.data-v-286e5497 {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .bg_color.data-v-286e5497 {
  background-color: #FF448F !important;
}
.product-window .joinBnt.on.data-v-286e5497 {
  background-color: #bbb;
  color: #fff;
}
.align-center.data-v-286e5497 {
  align-items: center;
}
.vip_icon.data-v-286e5497 {
  width: 44rpx;
  height: 28rpx;
}
.vip_money.data-v-286e5497 {
  background: #FFE7B9;
  border-radius: 4px;
  font-size: 22rpx;
  color: #333;
  line-height: 28rpx;
  text-align: center;
  padding: 0 6rpx;
  box-sizing: border-box;
  margin-left: -4rpx;
}
.pl-2.data-v-286e5497 {
  padding-left: 20rpx;
}

