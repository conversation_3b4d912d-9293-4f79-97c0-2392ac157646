@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.payment.data-v-15ef15de {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  border-radius: 16rpx 16rpx 0 0;
  background-color: #fff;
  padding-bottom: 60rpx;
  z-index: 99;
  transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
  -webkit-transform: translate3d(0, 100%, 0);
          transform: translate3d(0, 100%, 0);
}
.payment.on.data-v-15ef15de {
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
}
.payment .title.data-v-15ef15de {
  text-align: center;
  height: 123rpx;
  font-size: 32rpx;
  color: #282828;
  font-weight: bold;
  padding-right: 30rpx;
  margin-left: 30rpx;
  position: relative;
  border-bottom: 1rpx solid #eee;
}
.payment .title .iconfont.data-v-15ef15de {
  position: absolute;
  right: 30rpx;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  font-size: 43rpx;
  color: #8a8a8a;
  font-weight: normal;
}
.payment .item.data-v-15ef15de {
  border-bottom: 1rpx solid #eee;
  height: 130rpx;
  margin-left: 30rpx;
  padding-right: 30rpx;
}
.payment .item .left.data-v-15ef15de {
  width: 610rpx;
}
.payment .item .left .text.data-v-15ef15de {
  width: 540rpx;
}
.payment .item .left .text .name.data-v-15ef15de {
  font-size: 32rpx;
  color: #282828;
}
.payment .item .left .text .info.data-v-15ef15de {
  font-size: 24rpx;
  color: #999;
}
.payment .item .left .text .info .money.data-v-15ef15de {
  color: #ff9900;
}
.payment .item .left .iconfont.data-v-15ef15de {
  font-size: 45rpx;
  color: #09bb07;
}
.payment .item .left .iconfont.icon-zhifubao.data-v-15ef15de {
  color: #00aaea;
}
.payment .item .left .iconfont.icon-yuezhifu.data-v-15ef15de {
  color: #ff9900;
}
.payment .item .left .iconfont.icon-yuezhifu1.data-v-15ef15de {
  color: #eb6623;
}
.payment .item .iconfont.data-v-15ef15de {
  font-size: 0.3rpx;
  color: #999;
}

