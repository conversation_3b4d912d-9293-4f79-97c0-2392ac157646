@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.pl-20.data-v-f6fba62a {
  padding-left: 20rpx;
}
.cart_nav.data-v-f6fba62a {
  position: fixed;
  background-color: theme;
  top: 0;
  left: 0;
  z-index: 99;
  width: 100%;
}
[data-theme="theme1"] .cart_nav.data-v-f6fba62a {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .cart_nav.data-v-f6fba62a {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .cart_nav.data-v-f6fba62a {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .cart_nav.data-v-f6fba62a {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .cart_nav.data-v-f6fba62a {
  background-color: #FF448F !important;
}
.navbarCon.data-v-f6fba62a {
  position: absolute;
  bottom: 0;
  height: 100rpx;
  width: 100%;
}
.h5_back.data-v-f6fba62a {
  color: #fff;
  position: fixed;
  left: 20rpx;
  font-size: 32rpx;
  text-align: center;
  line-height: 58rpx;
}
.select_nav.data-v-f6fba62a {
  width: 170rpx !important;
  height: 60rpx !important;
  border-radius: 33rpx;
  background: rgba(255, 255, 255, 0.6);
  color: #000;
  position: fixed;
  font-size: 18px;
  line-height: 58rpx;
  z-index: 1000;
  left: 14rpx;
}
.px-20.data-v-f6fba62a {
  padding: 0 20rpx 0;
}
.nav_line.data-v-f6fba62a {
  content: '';
  display: inline-block;
  width: 1px;
  height: 34rpx;
  background: #fff;
  position: absolute;
  left: 0;
  right: 0;
  margin: auto;
}
.container_detail.data-v-f6fba62a {
  margin-top: 32rpx;
}
.tab_nav.data-v-f6fba62a {
  width: 100%;
  height: 48px;
  padding: 0 30rpx 0;
}
.nav_title.data-v-f6fba62a {
  width: 200rpx;
  height: 58rpx;
  line-height: 58rpx;
  color: #fff;
  font-size: 36rpx;
  position: fixed;
  text-align: center;
  left: 0;
  right: 0;
  margin: auto;
}
.right_select.data-v-f6fba62a {
  position: fixed;
  right: 20rpx;
  color: #fff;
  text-align: center;
  line-height: 58rpx;
}
.select_nav.data-v-f6fba62a {
  width: 170rpx !important;
  height: 60rpx !important;
  border-radius: 33rpx;
  background: rgba(255, 255, 255, 0.6);
  color: #000;
  position: fixed;
  font-size: 18px;
  line-height: 58rpx;
  z-index: 1000;
  left: 14rpx;
}
.px-20.data-v-f6fba62a {
  padding: 0 20rpx 0;
}
.justify-center.data-v-f6fba62a {
  justify-content: center;
}
.align-center.data-v-f6fba62a {
  align-items: center;
}
.dialog_nav.data-v-f6fba62a {
  position: fixed;
  left: 14rpx;
  width: 240rpx;
  background: #FFFFFF;
  box-shadow: 0px 0px 16rpx rgba(0, 0, 0, 0.08);
  z-index: 999;
  border-radius: 14rpx;
}
.dialog_nav.data-v-f6fba62a::before {
  content: '';
  width: 0;
  height: 0;
  position: absolute;
  left: 0;
  right: 0;
  margin: auto;
  top: -9px;
  border-bottom: 10px solid #fff;
  border-left: 10px solid transparent;
  /*transparent 表示透明*/
  border-right: 10px solid transparent;
}
.dialog_nav_item.data-v-f6fba62a {
  width: 100%;
  height: 84rpx;
  line-height: 84rpx;
  padding: 0 20rpx 0;
  box-sizing: border-box;
  border-bottom: #eee;
  font-size: 28rpx;
  color: #333;
  position: relative;
}
.dialog_nav_item .iconfont.data-v-f6fba62a {
  font-size: 32rpx;
}
.dialog_nav_item.data-v-f6fba62a::after {
  content: '';
  position: absolute;
  width: 86px;
  height: 1px;
  background-color: #EEEEEE;
  bottom: 0;
  right: 0;
}

