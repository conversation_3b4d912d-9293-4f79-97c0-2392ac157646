@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.pictrueBox.data-v-08ae13c6 {
  width: 130rpx;
  height: 120rpx;
}
/*返回主页按钮*/
.home.data-v-08ae13c6 {
  position: fixed;
  color: white;
  text-align: center;
  z-index: 9999;
  right: 15rpx;
  display: flex;
}
.home .homeCon.data-v-08ae13c6 {
  border-radius: 50rpx;
  opacity: 0;
  height: 0;
  color: #E93323;
  width: 0;
}
.home .homeCon.on.data-v-08ae13c6 {
  opacity: 1;
  -webkit-animation: bounceInRight 0.5s cubic-bezier(0.215, 0.61, 0.355, 1);
          animation: bounceInRight 0.5s cubic-bezier(0.215, 0.61, 0.355, 1);
  width: 300rpx;
  height: 86rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  /* background: #f44939 !important; */
  background-color: theme;
}
[data-theme="theme1"] .home .homeCon.on.data-v-08ae13c6 {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .home .homeCon.on.data-v-08ae13c6 {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .home .homeCon.on.data-v-08ae13c6 {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .home .homeCon.on.data-v-08ae13c6 {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .home .homeCon.on.data-v-08ae13c6 {
  background-color: #FF448F !important;
}
.home .homeCon .iconfont.data-v-08ae13c6 {
  font-size: 48rpx;
  color: #fff;
  display: inline-block;
  margin: 0 auto;
}
.home .pictrue.data-v-08ae13c6 {
  width: 86rpx;
  height: 86rpx;
  border-radius: 50%;
  margin: 0 auto;
}
.home .pictrue .image.data-v-08ae13c6 {
  background-color: theme;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
  ms-transform: rotate(90deg);
  moz-transform: rotate(90deg);
  webkit-transform: rotate(90deg);
  o-transform: rotate(90deg);
}
[data-theme="theme1"] .home .pictrue .image.data-v-08ae13c6 {
  background-color: #e93323 !important;
}
[data-theme="theme2"] .home .pictrue .image.data-v-08ae13c6 {
  background-color: #FE5C2D !important;
}
[data-theme="theme3"] .home .pictrue .image.data-v-08ae13c6 {
  background-color: #42CA4D !important;
}
[data-theme="theme4"] .home .pictrue .image.data-v-08ae13c6 {
  background-color: #1DB0FC !important;
}
[data-theme="theme5"] .home .pictrue .image.data-v-08ae13c6 {
  background-color: #FF448F !important;
}

