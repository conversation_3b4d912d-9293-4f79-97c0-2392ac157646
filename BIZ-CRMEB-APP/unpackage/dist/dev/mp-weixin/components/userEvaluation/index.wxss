@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*引入主题变色的scss文件*/
/* 颜色变量 */
/*
@mixin 指令允许我们定义一个可以在整个样式表中重复使用的样式。
*/
/*定义全局颜色变量*/
/*主色*/
/* 辅色*/
/* 价格色*/
/*左侧按钮*/
/*主渐变色*/
/*辅色渐变*/
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/*字体颜色(少部分其他,不限于字体)*/
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.evaluateWtapper .evaluateItem.data-v-52697636 {
  background-color: #fff;
  padding: 24rpx;
  border-bottom-left-radius: 14rpx;
  border-bottom-right-radius: 14rpx;
}
.evaluateWtapper .evaluateItem ~ .evaluateItem.data-v-52697636 {
  border-top: 1rpx solid #f5f5f5;
}
.evaluateWtapper .evaluateItem .pic-text.data-v-52697636 {
  font-size: 26rpx;
  color: #282828;
}
.evaluateWtapper .evaluateItem .pic-text .content.data-v-52697636 {
  width: 84%;
  margin-left: 20rpx;
}
.evaluateWtapper .evaluateItem .pic-text .pictrue.data-v-52697636 {
  width: 62rpx;
  height: 62rpx;
}
.evaluateWtapper .evaluateItem .pic-text .pictrue image.data-v-52697636 {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.evaluateWtapper .evaluateItem .pic-text .name.data-v-52697636 {
  max-width: 450rpx;
}
.evaluateWtapper .evaluateItem .time.data-v-52697636 {
  font-size: 24rpx;
  color: #999999;
}
.sku.data-v-52697636 {
  font-size: 24rpx;
  color: #999999;
  margin: 10rpx 0;
}
.evaluateWtapper .evaluateItem .evaluate-infor.data-v-52697636 {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 14rpx;
}
.evaluateWtapper .evaluateItem .imgList.data-v-52697636 {
  /* 
	padding: 0 24rpx;
	margin-top: 16rpx; */
}
.evaluateWtapper .evaluateItem .imgList .pictrue.data-v-52697636 {
  width: 102rpx;
  height: 102rpx;
  margin-right: 14rpx;
  border-radius: 14rpx;
  margin-bottom: 16rpx;
  /* margin: 0 0 15rpx 15rpx; */
}
.evaluateWtapper .evaluateItem .imgList .pictrue image.data-v-52697636 {
  width: 100%;
  height: 100%;
  background-color: #f7f7f7;
  border-radius: 14rpx;
}
.evaluateWtapper .evaluateItem .reply.data-v-52697636 {
  font-size: 26rpx;
  color: #454545;
  background-color: #f7f7f7;
  border-radius: 14rpx;
  margin: 20rpx 30rpx 0 0rpx;
  padding: 20rpx;
  position: relative;
}
.evaluateWtapper .evaluateItem .reply.data-v-52697636::before {
  content: "";
  width: 0;
  height: 0;
  border-left: 20rpx solid transparent;
  border-right: 20rpx solid transparent;
  border-bottom: 30rpx solid #f7f7f7;
  position: absolute;
  top: -14rpx;
  left: 40rpx;
}
.font_color.data-v-52697636 {
  color: theme;
}
[data-theme="theme1"] .font_color.data-v-52697636 {
  color: #e93323 !important;
}
[data-theme="theme2"] .font_color.data-v-52697636 {
  color: #FE5C2D !important;
}
[data-theme="theme3"] .font_color.data-v-52697636 {
  color: #42CA4D !important;
}
[data-theme="theme4"] .font_color.data-v-52697636 {
  color: #1DB0FC !important;
}
[data-theme="theme5"] .font_color.data-v-52697636 {
  color: #FF448F !important;
}

