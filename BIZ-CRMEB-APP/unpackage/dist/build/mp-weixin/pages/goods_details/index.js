(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/goods_details/index"],{"139b":function(t,e,i){"use strict";i.d(e,"b",(function(){return s})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){return o}));var o={jyfParser:function(){return Promise.all([i.e("common/vendor"),i.e("components/jyf-parser/jyf-parser")]).then(i.bind(null,"5d6d"))},cusPreviewImg:function(){return i.e("components/cus-previewImg/cus-previewImg").then(i.bind(null,"34c9"))}},s=function(){var t=this,e=t.$createElement,i=(t._self._c,Math.floor(t.productInfo.sales)+Math.floor(t.productInfo.ficti)||0),o=t.defaultCoupon.length>0&&"normal"==t.type,s=t.activityH5.length,n=t.skuArr.length,r=n>1?t.skuArr.slice(0,4):null,a=n>1?t.skuArr.length:null,c="normal"===t.type?Math.floor(t.CartCount):null;t._isMounted||(t.e0=function(e){t.H5ShareBox=!1}),t.$mp.data=Object.assign({},{$root:{g0:i,g1:o,g2:s,g3:n,l0:r,g4:a,g5:c}})},n=[]},"1ec9":function(t,e,i){},8225:function(t,e,i){"use strict";i.r(e);var o=i("139b"),s=i("df48");for(var n in s)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return s[t]}))}(n);i("f974");var r=i("828b"),a=Object(r["a"])(s["default"],o["b"],o["c"],!1,null,"2e0202d7",null,!1,o["a"],void 0);e["default"]=a.exports},a7ac:function(t,e,i){"use strict";(function(t,e){var o=i("47a9");i("9e89");o(i("3240"));var s=o(i("8225"));t.__webpack_require_UNI_MP_PLUGIN__=i,e(s.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},df48:function(t,e,i){"use strict";i.r(e);var o=i("f281"),s=i.n(o);for(var n in o)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(n);e["default"]=s.a},f281:function(t,e,i){"use strict";(function(t){var o=i("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var s=o(i("7eb4")),n=o(i("ee10")),r=o(i("7064")),a=(o(i("5908")),i("0a3e"),i("20ce"),i("d9cf")),c=i("ad96"),u=i("8d22"),h=i("2e55"),l=i("8f59"),d=i("84f5"),p=i("342a"),f=i("2df1"),g=i("fbb9"),m=i("8b39"),v=getApp(),$={components:{productConSwiper:function(){Promise.all([i.e("common/vendor"),i.e("components/productConSwiper/index")]).then(function(){return resolve(i("867f"))}.bind(null,i)).catch(i.oe)},couponListWindow:function(){Promise.all([i.e("common/vendor"),i.e("components/couponListWindow/index")]).then(function(){return resolve(i("e966"))}.bind(null,i)).catch(i.oe)},productWindow:function(){i.e("components/productWindow/index").then(function(){return resolve(i("a82f"))}.bind(null,i)).catch(i.oe)},userEvaluation:function(){i.e("components/userEvaluation/index").then(function(){return resolve(i("ca9c"))}.bind(null,i)).catch(i.oe)},shareRedPackets:function(){i.e("components/shareRedPackets/index").then(function(){return resolve(i("784d"))}.bind(null,i)).catch(i.oe)},cusPreviewImg:function(){i.e("components/cus-previewImg/cus-previewImg").then(function(){return resolve(i("34c9"))}.bind(null,i)).catch(i.oe)},"jyf-parser":function(){Promise.all([i.e("common/vendor"),i.e("components/jyf-parser/jyf-parser")]).then(function(){return resolve(i("5d6d"))}.bind(null,i)).catch(i.oe)},authorize:function(){Promise.all([i.e("common/vendor"),i.e("components/Authorize")]).then(function(){return resolve(i("8de5"))}.bind(null,i)).catch(i.oe)}},data:function(){return{showSkeleton:!0,isNodes:0,coupon:{coupon:!1,type:0,list:[],count:[]},attrTxt:"请选择",attrValue:"",animated:!1,id:0,replyCount:0,reply:[],productInfo:{},productValue:[],couponList:[],cart_num:1,isAuto:!1,isShowAuth:!1,isOpen:!1,actionSheetHidden:!0,storeImage:"",PromotionCode:"",posterbackgd:"/static/images/posterbackgd.png",sharePacket:{isState:!0,touchstart:!1},circular:!1,autoplay:!1,interval:3e3,duration:500,clientHeight:"",systemStore:{},good_list:[],replyChance:0,CartCount:0,isDown:!0,posters:!1,weixinStatus:!1,attr:{cartAttr:!1,productAttr:[],productSelect:{}},description:"",navActive:0,H5ShareBox:!1,activityH5:[],retunTop:!0,navH:"",navList:[],opacity:0,scrollY:0,topArr:[],toView:"",height:0,heightArr:[],lock:!1,scrollTop:0,tagStyle:{img:"width:100%;display:block;",table:"width:100%",video:"width:100%"},sliderImage:[],videoLink:"",qrcodeSize:600,canvasStatus:!1,imagePath:"",imgTop:"",errT:"",homeTop:20,navbarRight:0,userCollect:!1,options:null,returnShow:!0,type:"",theme:v.globalData.theme,indicatorBg:"",shareStatus:!0,skuArr:[],currentPage:!1,selectSku:{},selectNavList:[{name:"首页",icon:"icon-shouye8",url:"/pages/index/index",after:"dialog_after"},{name:"搜索",icon:"icon-sousuo6",url:"/pages/goods_search/index",after:"dialog_after"},{name:"购物车",icon:"icon-gouwuche7",url:"/pages/order_addcart/order_addcart",after:"dialog_after"},{name:"我的收藏",icon:"icon-shoucang3",url:"/pages/users/user_goods_collection/index",after:"dialog_after"},{name:"个人中心",icon:"icon-gerenzhongxin1",url:"/pages/user/index"}],chatConfig:{consumer_hotline:"",telephone_service_switch:"false"},defaultCoupon:[],couponDeaultType:[{useType:1}]}},computed:(0,l.mapGetters)(["isLogin","uid","chatUrl"]),watch:{isLogin:{handler:function(t,e){1==t&&(this.getCouponList(),this.getCartCount())},deep:!0},productInfo:{handler:function(){this.$nextTick((function(){}))},immediate:!0}},onLoad:function(e){this.options=e;var i=this,o=getCurrentPages();if(i.returnShow=1!==o.length,o.length<=1&&(i.retunTop=!1),i.navH=v.globalData.navHeight,i.$set(i,"chatConfig",i.$Cache.getItem("chatConfig")),i.$set(i,"theme",i.$Cache.get("theme")),t.getSystemInfo({success:function(t){i.height=t.windowHeight}}),!e.scene&&!e.id)return this.showSkeleton=!1,void this.$util.Tips({title:"缺少参数无法查看商品"},{url:"/pages/index/index"});if(e.hasOwnProperty("id")||e.scene){if(e.scene){var s=this.$util.getUrlParams(decodeURIComponent(e.scene)),n=this.$util.formatMpQrCodeData(s);v.globalData.spread=n.spread,this.id=n.id}else this.id=e.id;void 0==e.type||null==e.type?i.type="normal":i.type=e.type,i.$store.commit("PRODUCT_TYPE",i.type)}e.spread&&(v.globalData.spread=e.spread),this.getGoodsDetails(),this.getCouponList(),this.getCouponType(),this.getProductReplyList(),this.getProductReplyCount(),this.getGoods(),this.isLogin&&v.globalData.spread&&(0,p.silenceBindingSpread)(),this.indicatorBg=(0,g.setThemeColor)()},onReady:function(){this.isNodes++,this.$nextTick((function(){var e=this,i=t.getMenuButtonBoundingClientRect(),o=t.createSelectorQuery().in(this);o.select("#home").boundingClientRect((function(t){e.homeTop=2*i.top+i.height-t.height})).exec()}))},onShareAppMessage:function(t){return this.$set(this,"actionSheetHidden",!this.actionSheetHidden),{title:this.productInfo.storeName||"",imageUrl:this.productInfo.image||"",path:"/pages/goods_details/index?id="+this.id+"&spread="+this.uid}},methods:{kefuClick:function(){"true"===this.chatConfig.telephone_service_switch?t.makePhoneCall({phoneNumber:this.chatConfig.consumer_hotline}):location.href=this.chatUrl},goActivity:function(e){var i=e;"1"===i.type?t.navigateTo({url:"/pages/activity/goods_seckill_details/index?id=".concat(i.id)}):"2"===i.type?t.navigateTo({url:"/pages/activity/goods_bargain_details/index?id=".concat(i.id,"&startBargainUid=").concat(this.uid)}):t.navigateTo({url:"/pages/activity/goods_combination_details/index?id=".concat(i.id)})},iptCartNum:function(t){this.$set(this.attr.productSelect,"cart_num",t||1)},returns:function(){t.navigateBack()},showNav:function(){this.currentPage=!this.currentPage},tap:function(t){var e="past"+t;t=t;this.$set(this,"toView",e),this.$set(this,"navActive",t),this.$set(this,"lock",!0),this.$set(this,"scrollTop",t>0?this.topArr[t]-v.globalData.navHeight/2:this.topArr[t])},scroll:function(t){var e=t.detail.scrollTop,i=e/500;if(i=i>1?1:i,this.$set(this,"opacity",i),this.$set(this,"scrollY",e),this.lock)this.$set(this,"lock",!1);else{for(var o=0;o<this.topArr.length;o++)if(e<this.topArr[o]-v.globalData.navHeight/2+this.heightArr[o]){this.$set(this,"navActive",o);break}this.$set(this.sharePacket,"touchstart",!0)}},goDetail:function(e){e.activityH5&&0!=e.activityH5.length?e.activityH5&&2==e.activityH5.type?t.redirectTo({url:"/pages/activity/goods_bargain_details/index?id=".concat(e.activityH5.id,"&bargain=").concat(this.uid)}):e.activityH5&&3==e.activityH5.type?t.redirectTo({url:"/pages/activity/goods_combination_details/index?id=".concat(e.activityH5.id)}):e.activityH5&&1==e.activityH5.type&&t.redirectTo({url:"/pages/activity/goods_seckill_details/index?id=".concat(e.activityH5.id)}):t.redirectTo({url:"/pages/goods_details/index?id="+e.id})},onLoadFun:function(t){this.getCouponList(),this.getCartCount()},ChangCouponsClone:function(){this.$set(this.coupon,"coupon",!1)},ChangeCartNum:function(t){var e=this.productValue[this.attrValue];if(void 0!==e||this.attr.productAttr.length||(e=this.attr.productSelect),void 0!==e){var i=e.stock||0,o=this.attr.productSelect;t?(o.cart_num++,o.cart_num>i&&(this.$set(this.attr.productSelect,"cart_num",i),this.$set(this,"cart_num",i))):(o.cart_num--,o.cart_num<1&&(this.$set(this.attr.productSelect,"cart_num",1),this.$set(this,"cart_num",1)))}},attrVal:function(t){this.$set(this.attr.productAttr[t.indexw],"index",this.attr.productAttr[t.indexw].attrValues[t.indexn])},ChangeAttr:function(t){var e=this.productValue[t];this.$set(this,"selectSku",e),e?(this.$set(this.attr.productSelect,"image",e.image),this.$set(this.attr.productSelect,"price",e.price),this.$set(this.attr.productSelect,"stock",e.stock),this.$set(this.attr.productSelect,"unique",e.id),this.$set(this.attr.productSelect,"cart_num",1),this.$set(this.attr.productSelect,"vipPrice",e.vipPrice),this.$set(this.attr.productSelect,"otPrice",e.otPrice),this.$set(this,"attrValue",t),this.$set(this,"attrTxt","已选择")):(this.$set(this.attr.productSelect,"image",this.productInfo.image),this.$set(this.attr.productSelect,"price",this.productInfo.price),this.$set(this.attr.productSelect,"stock",0),this.$set(this.attr.productSelect,"unique",this.productInfo.id),this.$set(this.attr.productSelect,"cart_num",1),this.$set(this.attr.productSelect,"vipPrice",this.productInfo.vipPrice),this.$set(this.attr.productSelect,"otPrice",this.productInfo.otPrice),this.$set(this,"attrValue",""),this.$set(this,"attrTxt","请选择"))},ChangCoupons:function(t){var e=t,i=this.$util.ArrayRemove(this.couponList,"id",e.id);this.$set(this,"couponList",i),this.getCouponList()},setClientHeight:function(){var e=this;if(e.good_list.length){var i=t.createSelectorQuery().in(this).select("#list0");i.fields({size:!0},(function(t){e.$set(e,"clientHeight",t.height+20)})).exec()}},getGoods:function(){var t=this;(0,a.getProductGood)().then((function(e){for(var i=e.data.list||[],o=Math.ceil(i.length/6),s=new Array,n=0;n<o;n++){var r=i.slice(6*n,6*n+6);r.length&&s.push({list:r})}t.$set(t,"good_list",s);var a=["商品","评价","详情"];s.length&&a.splice(2,0,"推荐"),t.$set(t,"navList",a),t.$nextTick((function(){i.length&&t.setClientHeight()}))}))},getGoodsDetails:function(){var e=this,i=this;(0,a.getProductDetail)(i.id,i.type).then((function(o){var s=o.data.productInfo,n=s.sliderImage,r=JSON.parse(n);for(var a in"video"==i.getFileType(r[0])&&(e.$set(e,"videoLink",r[0]),r.splice(0,1)),i.$set(i,"sliderImage",r),i.$set(i,"productInfo",s),i.$set(i,"description",s.content),i.$set(i,"userCollect",o.data.userCollect),i.$set(i.attr,"productAttr",o.data.productAttr),i.$set(i,"productValue",o.data.productValue),o.data.productValue){var c=o.data.productValue[a];i.skuArr.push(c)}e.$set(e,"selectSku",i.skuArr[0]),i.$set(i.sharePacket,"priceName",o.data.priceName),i.$set(i.sharePacket,"isState","0"==o.data.priceName||null===o.data.priceName),i.$set(i,"activityH5",o.data.activityAllH5?o.data.activityAllH5:[]),t.setNavigationBarTitle({title:s.storeName.substring(0,7)+"..."});var u=e.attr.productAttr.map((function(t){return{attrName:t.attrName,attrValues:t.attrValues.split(","),id:t.id,isDel:t.isDel,productId:t.productId,type:t.type}}));e.$set(e.attr,"productAttr",u),i.isLogin&&(i.getCartCount(),i.getQrcode()),setTimeout((function(){i.infoScroll()}),500),i.imgTop=o.data.productInfo.image,i.downloadFilestoreImage(),i.DefaultSelect(),setTimeout((function(){e.showSkeleton=!1,e.defaultCoupon=e.coupon.list}),1e3)})).catch((function(t){i.$util.Tips({title:t.toString()},{tab:3,url:1}),setTimeout((function(){e.showSkeleton=!1}),500)}))},getProductReplyList:function(){var t=this;(0,a.getReplyProduct)(this.id).then((function(e){t.reply=e.data.productReply?[e.data.productReply]:[]}))},getProductReplyCount:function(){var t=this;(0,a.getReplyConfig)(t.id).then((function(e){t.$set(t,"replyChance",100*e.data.replyChance),t.$set(t,"replyCount",e.data.sumCount)}))},infoScroll:function(){for(var e=this,i=[],o=[],s=0;s<e.navList.length;s++){var n=t.createSelectorQuery().in(this),r="#past"+s;n.select(r).boundingClientRect(),n.exec((function(t){var s=t[0].top,n=t[0].height;i.push(s),o.push(n),e.$set(e,"topArr",i),e.$set(e,"heightArr",o)}))}},DefaultSelect:function(){var t=this.attr.productAttr,e=[];t.forEach((function(t){e.push(t.attrValues[0])}));for(var i=0;i<e.length;i++)this.$set(t[i],"index",e[i]);var o=this.productValue[e.join(",")];o&&t.length?(this.$set(this.attr.productSelect,"storeName",this.productInfo.storeName),this.$set(this.attr.productSelect,"image",o.image),this.$set(this.attr.productSelect,"price",o.price),this.$set(this.attr.productSelect,"stock",o.stock),this.$set(this.attr.productSelect,"unique",o.id),this.$set(this.attr.productSelect,"cart_num",1),this.$set(this.attr.productSelect,"vipPrice",o.vipPrice),this.$set(this.attr.productSelect,"otPrice",o.otPrice),this.$set(this,"attrValue",e.join(",")),this.$set(this,"attrTxt","已选择")):!o&&t.length?(this.$set(this.attr.productSelect,"storeName",this.productInfo.storeName),this.$set(this.attr.productSelect,"image",this.productInfo.image),this.$set(this.attr.productSelect,"price",this.productInfo.price),this.$set(this.attr.productSelect,"stock",0),this.$set(this.attr.productSelect,"unique",this.productInfo.id),this.$set(this.attr.productSelect,"cart_num",1),this.$set(this.attr.productSelect,"vipPrice",this.productInfo.vipPrice),this.$set(this.attr.productSelect,"otPrice",this.productInfo.otPrice),this.$set(this,"attrValue",""),this.$set(this,"attrTxt","请选择")):o||t.length||(this.$set(this.attr.productSelect,"storeName",this.productInfo.storeName),this.$set(this.attr.productSelect,"image",this.productInfo.image),this.$set(this.attr.productSelect,"price",this.productInfo.price),this.$set(this.attr.productSelect,"stock",this.productInfo.stock),this.$set(this.attr.productSelect,"unique",this.productInfo.id||""),this.$set(this.attr.productSelect,"cart_num",1),this.$set(this.attr.productSelect,"vipPrice",this.productInfo.vipPrice),this.$set(this.attr.productSelect,"otPrice",this.productInfo.otPrice),this.$set(this,"attrValue",""),this.$set(this,"attrTxt","请选择"))},getCouponList:function(t){var e=this,i={page:1,limit:20,productId:e.id,type:t};i.type=void 0!=t||null!=t?t:"",(0,c.getCoupons)(i).then((function(t){e.$set(e.coupon,"list",t.data)}))},getCouponType:function(){var t=this;return(0,n.default)(s.default.mark((function e(){var i;return s.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,c.getCoupons)({productId:t.id});case 2:i=e.sent,i.length&&(t.couponDeaultType=i.data,t.$set(t.coupon,"type",i));case 4:case"end":return e.stop()}}),e)})))()},tabCouponType:function(t){this.$set(this.coupon,"type",t),this.getCouponList(t)},ChangCouponsUseState:function(t){this.coupon.list[t].isUse=!0,this.$set(this.coupon,"list",this.coupon.list),this.$set(this.coupon,"coupon",!1)},setCollect:function(){var t=this;!1===this.isLogin?(0,h.toLogin)():this.userCollect?(0,a.collectDel)(this.productInfo.id).then((function(e){t.$set(t,"userCollect",!t.userCollect)})):(0,a.collectAdd)(this.productInfo.id).then((function(e){t.$set(t,"userCollect",!t.userCollect)}))},selecAttr:function(){this.$set(this.attr,"cartAttr",!0),this.$set(this,"isOpen",!0)},couponTap:function(){!1===this.isLogin?(0,h.toLogin)():(this.getCouponList(this.couponDeaultType[0].useType),this.$set(this.coupon,"coupon",!0))},onMyEvent:function(){this.$set(this.attr,"cartAttr",!1),this.$set(this,"isOpen",!1)},joinCart:function(t){!1===this.isLogin?(0,h.toLogin)():this.goCat(1)},goCat:function(t){var e=this,i=e.productValue[this.attrValue];if(e.attrValue?e.attr.cartAttr=!e.isOpen:e.isOpen?e.attr.cartAttr=!0:e.attr.cartAttr=!e.attr.cartAttr,!0===e.attr.cartAttr&&!1===e.isOpen)return e.isOpen=!0;if(e.attr.productAttr.length&&0===i.stock&&!0===e.isOpen)return e.$util.Tips({title:"产品库存不足，请选择其它"});if(1===t){var o={productId:parseFloat(e.id),cartNum:parseFloat(e.attr.productSelect.cart_num),isNew:!1,productAttrUnique:void 0!==e.attr.productSelect?e.attr.productSelect.unique:e.productInfo.id};(0,a.postCartAdd)(o).then((function(t){e.isOpen=!1,e.attr.cartAttr=!1,e.$util.Tips({title:"添加购物车成功",success:function(){e.getCartCount(!0)}})})).catch((function(t){return e.isOpen=!1,e.$util.Tips({title:t})}))}else this.getPreOrder()},getCartCount:function(t){var e=this,i=e.isLogin;i&&(0,u.getCartCounts)(!0,"total").then((function(i){e.CartCount=i.data.count,t&&(e.animated=!0,setTimeout((function(){e.animated=!1}),500))}))},goBuy:(0,m.Debounce)((function(t){!1===this.isLogin?(0,h.toLogin)():this.goCat(0)})),getPreOrder:function(){this.$Order.getPreOrder("normal"===this.type?"buyNow":"video",[{attrValueId:parseFloat(this.attr.productSelect.unique),productId:parseFloat(this.id),productNum:parseFloat(this.attr.productSelect.cart_num)}])},authColse:function(t){this.isShowAuth=t},listenerActionSheet:function(){!1===this.isLogin?(0,h.toLogin)():(this.goPoster(),this.posters=!0)},closePosters:function(){this.posters=!1,this.currentPage=!1},posterImageClose:function(){this.canvasStatus=!1,this.posters=!1},setDomain:function(t){return t=t?t.toString():"",t.indexOf("https://")>-1?t:t.replace("http://","https://")},downloadFilestoreImage:function(){var e=this;t.downloadFile({url:e.setDomain(e.productInfo.image),success:function(t){e.storeImage=t.tempFilePath},fail:function(){return e.$util.Tips({title:""})}})},goFriend:function(){this.posters=!1},getQrcode:function(){var t=this,e={pid:t.uid,id:t.id,path:"pages/goods_details/index"};(0,c.getQrcode)(e).then((function(e){(0,f.base64src)(e.data.code,Date.now(),(function(e){t.PromotionCode=e}))})).catch((function(e){t.errT=e}))},make:function(t){var e=this,i=location.href.split("?")[0]+"?id="+this.id+"&spread="+this.uid;r.default.make({canvasId:"qrcode",text:i,size:this.qrcodeSize,margin:10,success:function(t){e.PromotionCode=t},complete:function(){},fail:function(t){e.$util.Tips({title:"海报二维码生成失败！"})}})},getImageBase64:function(t){var e=this;(0,d.imageBase64)({url:t}).then((function(t){e.imgTop=t.data.code}))},downloadFilePromotionCode:function(e){var i=this;getProductCode(i.id).then((function(o){t.downloadFile({url:i.setDomain(o.data.code),success:function(t){i.$set(i,"isDown",!1),"function"==typeof e?e&&e(t.tempFilePath):i.$set(i,"PromotionCode",t.tempFilePath)},fail:function(){i.$set(i,"isDown",!1),i.$set(i,"PromotionCode","")}})})).catch((function(t){i.$set(i,"isDown",!1),i.$set(i,"PromotionCode","")}))},goPoster:function(){var e=this;t.showLoading({title:"海报生成中",mask:!0}),e.posters=!1;var i="";if(!e.PromotionCode)return t.hideLoading(),void e.$util.Tips({title:e.errT});setTimeout((function(){if(!e.imgTop)return t.hideLoading(),void e.$util.Tips({title:"无法生成商品海报！"})}),1e3),t.downloadFile({url:e.imgTop,success:function(o){i=o.tempFilePath;var s=[e.posterbackgd,i,e.PromotionCode],n=e.productInfo.storeName,r=e.productInfo.price;setTimeout((function(){e.$util.PosterCanvas(s,n,r,e.productInfo.otPrice,(function(i){e.imagePath=i,e.canvasStatus=!0,t.hideLoading()}))}),500)}})},getpreviewImage:function(){if(this.imagePath){var e=[];e.push(this.imagePath),t.previewImage({urls:e,current:this.imagePath})}else this.$util.Tips({title:"您的海报尚未生成"})},savePosterPath:function(){var e=this;t.getSetting({success:function(i){i.authSetting["scope.writePhotosAlbum"]?t.saveImageToPhotosAlbum({filePath:e.imagePath,success:function(t){e.posterImageClose(),e.$util.Tips({title:"保存成功",icon:"success"})},fail:function(t){e.$util.Tips({title:"保存失败"})}}):t.authorize({scope:"scope.writePhotosAlbum",success:function(){t.saveImageToPhotosAlbum({filePath:e.imagePath,success:function(t){e.posterImageClose(),e.$util.Tips({title:"保存成功",icon:"success"})},fail:function(t){e.$util.Tips({title:"保存失败"})}})}})}})},ShareInfo:function(){var t=this.productInfo,e=location.href;if(this.$wechat.isWeixin()){e=-1===e.indexOf("?")?e+"?spread="+this.uid:e+"&spread="+this.uid;var i={desc:t.storeInfo,title:t.storeName,link:e,imgUrl:t.image};this.$wechat.wechatEvevt(["updateAppMessageShareData","updateTimelineShareData","onMenuShareAppMessage","onMenuShareTimeline"],i).then((function(t){})).catch((function(t){console.log(t)}))}},showShare:function(t){this.$set(this.sharePacket,"touchstart",t)},hideNav:function(){this.currentPage=!1},linkPage:function(e){"/pages/index/index"==e||"/pages/order_addcart/order_addcart"==e||"/pages/user/index"==e?t.switchTab({url:e}):t.navigateTo({url:e}),this.currentPage=!1},showImg:function(t){this.$refs.cusPreviewImg.open(this.selectSku.suk)},changeSwitch:function(t){var e=this,i=this.skuArr[t];this.$set(this,"selectSku",i);var o=i.suk.split(",");o.forEach((function(t,i){e.$set(e.attr.productAttr[i],"index",o[i])})),i&&(this.$set(this.attr.productSelect,"image",i.image),this.$set(this.attr.productSelect,"price",i.price),this.$set(this.attr.productSelect,"stock",i.stock),this.$set(this.attr.productSelect,"unique",i.id),this.$set(this.attr.productSelect,"vipPrice",i.vipPrice),this.$set(this,"attrTxt","已选择"),this.$set(this,"attrValue",i.suk))},getFileType:function(t){var e="",i="";try{var o=t.split(".");e=o[o.length-1]}catch(s){e=""}if(!e)return!1;e=e.toLocaleLowerCase();if(i=["png","jpg","jpeg","bmp","gif"].find((function(t){return t===e})),i)return"image";return i=["mp4","m2v","mkv","rmvb","wmv","avi","flv","mov","m4v"].find((function(t){return t===e})),i?"video":"other"},videoPause:function(){}}};e.default=$}).call(this,i("df3c")["default"])},f974:function(t,e,i){"use strict";var o=i("1ec9"),s=i.n(o);s.a}},[["a7ac","common/runtime","common/vendor"]]]);