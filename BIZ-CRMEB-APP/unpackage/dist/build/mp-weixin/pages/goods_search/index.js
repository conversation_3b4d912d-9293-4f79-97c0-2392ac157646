(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/goods_search/index"],{"223d":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=(this._self._c,this.bastList.length),i=this.bastList.length,n=0==this.bastList.length&&this.isbastList,o=this.bastList.length;this.$mp.data=Object.assign({},{$root:{g0:e,g1:i,g2:n,g3:o}})},o=[]},"3d56":function(t,e,i){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i("d9cf"),o=getApp(),s={components:{goodList:function(){Promise.all([i.e("common/vendor"),i.e("components/goodList/index")]).then(function(){return resolve(i("7fa1"))}.bind(null,i)).catch(i.oe)},recommend:function(){Promise.all([i.e("common/vendor"),i.e("components/recommend/index")]).then(function(){return resolve(i("66cd"))}.bind(null,i)).catch(i.oe)}},data:function(){return{hostProduct:[],searchValue:"",focus:!0,bastList:[],hotSearchList:[],first:0,limit:8,page:1,loading:!1,loadend:!1,loadTitle:"加载更多",hotPage:1,isScroll:!0,isbastList:!1,theme:o.globalData.theme}},onShow:function(){this.getRoutineHotSearch(),this.getHostProduct()},onReachBottom:function(){this.bastList.length>0?this.getProductList():this.getHostProduct()},methods:{getRoutineHotSearch:function(){var t=this;(0,n.getSearchKeyword)().then((function(e){t.$set(t,"hotSearchList",e.data)}))},getProductList:function(){var t=this;t.loadend||t.loading||(t.loading=!0,t.loadTitle="",(0,n.getProductslist)({keyword:t.searchValue,page:t.page,limit:t.limit}).then((function(e){var i=e.data.list,n=i.length<t.limit;t.bastList=t.$util.SplitArray(i,t.bastList),t.$set(t,"bastList",t.bastList),t.loading=!1,t.loadend=n,t.loadTitle=n?"😕人家是有底线的~~":"加载更多",t.page=t.page+1,t.isbastList=!0})).catch((function(e){t.loading=!1,t.loadTitle="加载更多"})))},getHostProduct:function(){var t=this;this.isScroll&&(0,n.getProductHot)(t.hotPage,t.limit).then((function(e){t.isScroll=e.data.list.length>=t.limit,t.hostProduct=t.hostProduct.concat(e.data.list),t.hotPage+=1}))},setHotSearchValue:function(t){this.$set(this,"searchValue",t),this.page=1,this.loadend=!1,this.$set(this,"bastList",[]),this.getProductList()},setValue:function(t){this.$set(this,"searchValue",t.detail.value)},searchBut:function(){if(this.focus=!1,!(this.searchValue.length>0))return this.$util.Tips({title:"请输入要搜索的商品",icon:"none",duration:1e3,mask:!0});this.page=1,this.loadend=!1,this.$set(this,"bastList",[]),t.showLoading({title:"正在搜索中"}),this.getProductList(),t.hideLoading()}}};e.default=s}).call(this,i("df3c")["default"])},"3ef4":function(t,e,i){},"49a4":function(t,e,i){"use strict";var n=i("3ef4"),o=i.n(n);o.a},5262:function(t,e,i){"use strict";(function(t,e){var n=i("47a9");i("9e89");n(i("3240"));var o=n(i("edea"));t.__webpack_require_UNI_MP_PLUGIN__=i,e(o.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},"806f":function(t,e,i){"use strict";i.r(e);var n=i("3d56"),o=i.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);e["default"]=o.a},edea:function(t,e,i){"use strict";i.r(e);var n=i("223d"),o=i("806f");for(var s in o)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(s);i("49a4");var a=i("828b"),c=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=c.exports}},[["5262","common/runtime","common/vendor"]]]);