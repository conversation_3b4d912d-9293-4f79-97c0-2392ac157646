<view data-theme="{{theme}}"><view class="searchGood"><view class="search acea-row row-between-wrapper"><view class="input acea-row row-between-wrapper"><text class="iconfont icon-sousuo2"></text><input type="text" focus="{{focus}}" placeholder="点击搜索商品" placeholder-class="placeholder" maxlength="20" data-event-opts="{{[['input',[['setValue',['$event']]]]]}}" value="{{searchValue}}" bindinput="__e"/></view><view data-event-opts="{{[['tap',[['searchBut',['$event']]]]]}}" class="bnt" bindtap="__e">搜索</view></view><view class="title">热门搜索</view><view class="list acea-row"><block wx:for="{{hotSearchList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view data-event-opts="{{[['tap',[['setHotSearchValue',['$0'],[[['hotSearchList','',index,'title']]]]]]]}}" class="item" bindtap="__e">{{item.title}}</view></block></block></view><view class="line"></view><block wx:if="{{$root.g0>0}}"><good-list vue-id="756b170e-1" bastList="{{bastList}}" bind:__l="__l"></good-list></block><block wx:if="{{$root.g1>0}}"><view class="loadingicon acea-row row-center-wrapper"><text class="loading iconfont icon-jiazai" hidden="{{loading==false}}"></text>{{loadTitle+''}}</view></block></view><view class="noCommodity"><block wx:if="{{$root.g2}}"><view class="pictrue"><image src="../../static/images/noSearch.png"></image></view></block><block wx:if="{{$root.g3==0}}"><recommend vue-id="756b170e-2" hostProduct="{{hostProduct}}" bind:__l="__l"></recommend></block></view></view>