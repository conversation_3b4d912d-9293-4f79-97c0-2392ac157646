(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/goods_cate/components/contracted"],{"39da":function(t,e,i){"use strict";i.r(e);var n=i("67a9"),o=i("3a5a");for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);i("4ba8");var c=i("828b"),d=Object(c["a"])(o["default"],n["b"],n["c"],!1,null,"e7a77638",null,!1,n["a"],void 0);e["default"]=d.exports},"3a5a":function(t,e,i){"use strict";i.r(e);var n=i("f86b"),o=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=o.a},"4ba8":function(t,e,i){"use strict";var n=i("d16b"),o=i.n(n);o.a},"67a9":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=(this._self._c,this.productList.length),i=0==this.productList.length&&this.page>1;this.$mp.data=Object.assign({},{$root:{g0:e,g1:i}})},o=[]},"8d57":function(t,e,i){"use strict";(function(t,e){var n=i("47a9");i("9e89");n(i("3240"));var o=n(i("39da"));t.__webpack_require_UNI_MP_PLUGIN__=i,e(o.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},d16b:function(t,e,i){},f86b:function(t,e,i){"use strict";(function(t){var n=i("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=i("d9cf"),a=i("3f87"),c=i("8f59"),d=n(i("4bef")),u={computed:(0,c.mapGetters)(["uid"]),components:{recommend:function(){Promise.all([i.e("common/vendor"),i.e("components/recommend/index")]).then(function(){return resolve(i("66cd"))}.bind(null,i)).catch(i.oe)}},data:function(){return{navLists:[],productList:[],scrollLeft:0,active:0,loading:!1,loadend:!1,loadTitle:"加载更多",page:1,limit:10,cid:0,hostProduct:[]}},created:function(){this.getAllCategory(),this.getProductList(),this.get_host_product()},methods:{godDetail:function(e){(0,a.goShopDetail)(e,this.uid).then((function(i){t.navigateTo({animationType:d.default.type,animationDuration:d.default.duration,url:"/pages/goods_details/index?id=".concat(e.id)})}))},tabSelect:function(e,i){var n=this;this.active=e;var o=t.createSelectorQuery().in(this);o.select("#id"+e).boundingClientRect((function(t){n.scrollLeft=(e-1)*t.width})).exec(),this.cid=i,this.loadend=!1,this.page=1,this.$set(this,"productList",[]),this.getProductList()},getAllCategory:function(){var t=this;(0,o.getCategoryList)().then((function(e){t.navLists=e.data}))},getProductList:function(){var t=this;t.loadend||t.loading||(t.loading=!0,t.loadTitle="",(0,o.getProductslist)({page:t.page,limit:t.limit,cid:t.cid}).then((function(e){var i=e.data.list,n=i.length<t.limit;t.productList=t.$util.SplitArray(i,t.productList),t.$set(t,"productList",t.productList),t.loading=!1,t.loadend=n,t.loadTitle=n?"😕人家是有底线的~~":"加载更多",t.page=t.page+1})).catch((function(e){t.loading=!1,t.loadTitle="加载更多"})))},get_host_product:function(){var t=this;(0,o.getProductHot)(1,20).then((function(e){t.$set(t,"hostProduct",e.data.list)}))}}};e.default=u}).call(this,i("df3c")["default"])}},[["8d57","common/runtime","common/vendor"]]]);