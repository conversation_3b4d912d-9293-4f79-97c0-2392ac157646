<view class="goodCate"><view class="header acea-row row-center-wrapper"><navigator class="pageIndex" url="/pages/index/index" hover-class="none" open-type="switchTab"><text class="iconfont icon-shouye3"></text></navigator><navigator class="search acea-row row-center-wrapper" url="/pages/goods_search/index" hover-class="none"><text class="iconfont icon-xiazai5"></text>搜索商品</navigator></view><block wx:if="{{showSlide}}"><view class="conter"><view class="aside"><block wx:for="{{productList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['tapNav',[index,'$0'],[[['productList','',index]]]]]]]}}" class="{{['item','acea-row','row-center-wrapper',index==navActive?'on':'']}}" bindtap="__e"><text>{{item.name}}</text></view></block></view><view class="wrapper"><block wx:if="{{iSlong}}"><view class="bgcolor"><view class="longTab acea-row row-middle"><scroll-view style="white-space:nowrap;display:flex;height:44rpx;" scroll-x="true" scroll-with-animation="{{true}}" scroll-left="{{tabLeft}}" show-scrollbar="true"><block wx:for="{{categoryErList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['longClick',[index,'$0'],[[['categoryErList','',index]]]]]]]}}" class="{{['longItem',index===tabClick?'click':'']}}" style="{{('width:'+isWidth+'px')}}" bindtap="__e">{{item.name}}</view></block></scroll-view></view><view data-event-opts="{{[['tap',[['openTap',['$event']]]]]}}" class="openList" bindtap="__e"><text class="iconfont icon-xiala"></text></view></view></block><block wx:else><view><view class="downTab"><view class="title acea-row row-between-wrapper"><view>{{categoryTitle}}</view><view data-event-opts="{{[['tap',[['closeTap',['$event']]]]]}}" class="closeList" bindtap="__e"><text class="iconfont icon-xiala"></text></view></view><view class="children"><view class="acea-row row-middle"><block wx:for="{{categoryErList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['longClick',[index,'$0'],[[['categoryErList','',index]]]]]]]}}" class="{{['item','line1',index===tabClick?'click':'']}}" bindtap="__e">{{item.name}}</view></block></view></view></view><view data-event-opts="{{[['tap',[['closeTap',['$event']]]]]}}" class="mask" bindtap="__e"></view></view></block><good-list vue-id="5fe21c04-1" tempArr="{{tempArr}}" isLogin="{{isLogin}}" data-event-opts="{{[['^gocartduo',[['goCartDuo']]],['^detail',[['goDetail']]]]}}" bind:gocartduo="__e" bind:detail="__e" bind:__l="__l"></good-list><view class="loadingicon acea-row row-center-wrapper mb-2"><text class="loading iconfont icon-jiazai" hidden="{{loading==false}}"></text>{{loadTitle+''}}</view></view></view></block><block wx:else><view class="conter"><view class="hide_slide"><block wx:if="{{iSlong}}"><view class="bgcolor"><view class="hongTab acea-row row-middle"><scroll-view style="white-space:nowrap;display:flex;height:44rpx;" scroll-x="true" scroll-with-animation="{{true}}" scroll-left="{{tabLeft}}" show-scrollbar="true"><block wx:for="{{productList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['navSwitch',[index,'$0'],[[['productList','',index]]]]]]]}}" class="{{['longItem',index===tabClick?'click':'']}}" style="{{('width:'+isWidth+'px')}}" bindtap="__e">{{item.name}}</view></block></scroll-view></view><view data-event-opts="{{[['tap',[['openTap',['$event']]]]]}}" class="openList" bindtap="__e"><text class="iconfont icon-xiangxia"></text></view></view></block><block wx:else><view><view class="hownTab"><view class="title acea-row row-between-wrapper"><view>{{categoryTitle}}</view><view data-event-opts="{{[['tap',[['closeTap',['$event']]]]]}}" class="closeList" bindtap="__e"><text class="iconfont icon-xiangxia"></text></view></view><view class="children"><view class="acea-row row-middle"><block wx:for="{{productList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['navSwitch',[index,'$0'],[[['productList','',index]]]]]]]}}" class="{{['item','line1',index===tabClick?'click':'']}}" bindtap="__e">{{item.name}}</view></block></view></view></view><view data-event-opts="{{[['tap',[['closeTap',['$event']]]]]}}" class="mask" bindtap="__e"></view></view></block><view class="list_prod"><block wx:for="{{tempArr}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goDetail',['$0'],[[['tempArr','',index]]]]]]]}}" class="item acea-row row-between-wrapper" bindtap="__e"><view class="pic"><image src="{{item.image}}" mode="aspectFill"></image></view><view class="pictxt"><view class="text line2">{{item.storeName}}</view><view class="bottom acea-row row-between-wrapper"><view class="money"><text class="sign">￥</text><text class="price_num">{{item.price}}</text><block wx:if="{{item.sales}}"><label class="item_sales _span">{{"已售"+item.sales}}</label></block></view><block wx:if="{{item.stock>0}}"><view><view><view data-event-opts="{{[['tap',[['goCartDuo',['$0'],[[['tempArr','',index]]]]]]]}}" class="bnt" catchtap="__e">选规格<block wx:if="{{item.cartNum}}"><view class="num">{{item.cartNum}}</view></block></view></view></view></block><block wx:else><view class="bnt end">已售罄</view></block></view></view></view></block></view><view class="loadingicon acea-row row-center-wrapper mb-2"><text class="loading iconfont icon-jiazai" hidden="{{loading==false}}"></text>{{loadTitle+''}}</view></view></view></block><view class="footer acea-row row-between-wrapper"><block wx:if="{{$root.g0}}"><view data-event-opts="{{[['tap',[['getCartLists',[0]]]]]}}" class="cartIcon acea-row row-center-wrapper" bindtap="__e"><image src="../../../static/images/cart.png"></image><view class="num">{{cartCount}}</view></view></block><block wx:else><view class="cartIcon acea-row row-center-wrapper noCart"><image src="../../../static/images/no_cart.png"></image></view></block><view class="money acea-row row-middle"><view>￥<text class="num">{{totalPrice}}</text></view><view data-event-opts="{{[['tap',[['subOrder',['$event']]]]]}}" class="{{['bnt','gray_bg',(cartCount>0)?'main_bg':'']}}" bindtap="__e">去结算</view></view></view><cart-list vue-id="5fe21c04-2" cartData="{{cartData}}" data-event-opts="{{[['^closeList',[['closeList']]],['^ChangeCartNumDan',[['ChangeCartList']]],['^ChangeSubDel',[['ChangeSubDel']]],['^ChangeOneDel',[['ChangeOneDel']]]]}}" bind:closeList="__e" bind:ChangeCartNumDan="__e" bind:ChangeSubDel="__e" bind:ChangeOneDel="__e" bind:__l="__l"></cart-list><product-window vue-id="5fe21c04-3" attr="{{attr}}" isShow="{{1}}" iSplus="{{1}}" iScart="{{1}}" id="product-window" data-event-opts="{{[['^myevent',[['onMyEvent']]],['^ChangeAttr',[['ChangeAttr']]],['^ChangeCartNum',[['ChangeCartNumDuo']]],['^attrVal',[['attrVal']]],['^iptCartNum',[['iptCartNum']]],['^goCat',[['goCatNum']]]]}}" bind:myevent="__e" bind:ChangeAttr="__e" bind:ChangeCartNum="__e" bind:attrVal="__e" bind:iptCartNum="__e" bind:goCat="__e" bind:__l="__l"></product-window></view>