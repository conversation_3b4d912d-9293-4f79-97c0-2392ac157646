(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/goods_cate/components/fresh"],{"0e96":function(t,i,e){"use strict";Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var a={name:"d_goodList",props:{dataConfig:{type:Object,default:function(){}},tempArr:{type:Array,default:[]},isLogin:{type:Boolean,default:!1}},data:function(){return{}},created:function(){},mounted:function(){},methods:{goDetail:function(t){this.$emit("detail",t)},goCartDuo:function(t){this.$emit("gocartduo",t)},goCartDan:function(t,i){this.$emit("gocartdan",t,i)},CartNumDes:function(t,i){this.$emit("ChangeCartNumDan",!1,t,i)},CartNumAdd:function(t,i){this.$emit("ChangeCartNumDan",!0,t,i)}}};i.default=a},"2f33":function(t,i,e){"use strict";e.r(i);var a=e("f34a"),r=e("c731");for(var s in r)["default"].indexOf(s)<0&&function(t){e.d(i,t,(function(){return r[t]}))}(s);e("ba92");var c=e("828b"),n=Object(c["a"])(r["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);i["default"]=n.exports},"3d2b":function(t,i,e){"use strict";(function(t,i){var a=e("47a9");e("9e89");a(e("3240"));var r=a(e("2f33"));t.__webpack_require_UNI_MP_PLUGIN__=e,i(r.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},9299:function(t,i,e){},9545:function(t,i,e){"use strict";var a=e("9299"),r=e.n(a);r.a},"95a2":function(t,i,e){},"97cc":function(t,i,e){"use strict";(function(t){var a=e("47a9");Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var r=e("d9cf"),s=e("8d22"),c=a(e("be01")),n=e("8f59"),o=e("3f87"),u=e("2e55"),d=a(e("4bef")),h={computed:(0,n.mapGetters)(["isLogin","uid"]),components:{productWindow:function(){e.e("components/productWindow/index").then(function(){return resolve(e("a82f"))}.bind(null,e)).catch(e.oe)},goodList:c.default,cartList:function(){e.e("components/cartList/index").then(function(){return resolve(e("bd6d"))}.bind(null,e)).catch(e.oe)},home:function(){e.e("components/home/<USER>").then(function(){return resolve(e("1e4d"))}.bind(null,e)).catch(e.oe)}},props:{showSlide:{type:Boolean,default:!0}},data:function(){return{productList:[],navActive:0,categoryTitle:"",categoryErList:[],tabLeft:0,isWidth:0,tabClick:0,iSlong:!0,tempArr:[],loading:!1,loadend:!1,loadTitle:"加载更多",page:1,limit:10,cid:0,sid:0,isAuto:!1,isShowAuth:!1,attr:{cartAttr:!1,productAttr:[],productSelect:{}},productValue:[],attrValue:"",storeName:"",id:0,cartData:{cartList:[],iScart:!1},cartCount:0,totalPrice:0,lengthCart:0,theme:"theme1"}},created:function(){this.isLogin&&(this.getCartNum(),this.getCartLists(1)),this.getAllCategory();var i=this;i.lengthCart=i.cartData.cartList,t.getSystemInfo({success:function(t){i.isWidth=t.windowWidth/5}})},methods:{subOrder:function(){var t=this.cartData.cartList;if(!t.length)return this.$util.Tips({title:"请选择产品"});var i=t.map((function(t){return{shoppingCartId:Number(t.id)}}));this.$Order.getPreOrder("shoppingCart",i),this.cartData.iScart=!1},getTotalPrice:function(){var t=this,i=t.cartData.cartList,e=0;i.forEach((function(i){i.attrStatus&&(e=t.$util.$h.Add(e,t.$util.$h.Mul(i.cartNum,i.vipPrice?i.vipPrice:i.price)))})),t.$set(t,"totalPrice",e)},ChangeSubDel:function(t){var i=this,e=i.cartData.cartList,a=[];e.forEach((function(t){a.push(t.id)})),(0,s.cartDel)(a.join(",")).then((function(t){i.$set(i.cartData,"cartList",[]),i.cartData.iScart=!1,i.totalPrice=0,i.page=1,i.loadend=!1,i.tempArr=[],i.productslist(),i.getCartNum()}))},ChangeOneDel:function(t,i){var e=this,a=e.cartData.cartList;(0,s.cartDel)(t.toString()).then((function(t){a.splice(i,1),a.length||(e.cartData.iScart=!1,e.page=1,e.loadend=!1,e.tempArr=[],e.productslist()),e.getCartNum()}))},getCartLists:function(t){var i=this,e={page:1,limit:i.cartCount,isValid:!0};(0,s.getCartList)(e).then((function(e){i.$set(i.cartData,"cartList",e.data.list),e.data.list.length?i.$set(i.cartData,"iScart",!t&&!i.cartData.iScart):i.$set(i.cartData,"iScart",!1),i.getTotalPrice()}))},closeList:function(t){this.$set(this.cartData,"iScart",t),this.page=1,this.loadend=!1,this.tempArr=[],this.productslist()},getCartNum:function(){var t=this;(0,s.getCartCounts)(!0,"sum").then((function(i){t.$set(t,"cartCount",i.data.count)}))},onMyEvent:function(){this.$set(this.attr,"cartAttr",!1)},DefaultSelect:function(){var t=this.attr.productAttr,i=[];for(var e in this.productValue)if(this.productValue[e].stock>0){i=this.attr.productAttr.length?e.split(","):[];break}for(var a=0;a<t.length;a++)this.$set(t[a],"index",i[a]);var r=this.productValue[i.join(",")];r&&t.length?(this.$set(this.attr.productSelect,"storeName",this.storeName),this.$set(this.attr.productSelect,"image",r.image),this.$set(this.attr.productSelect,"price",r.price),this.$set(this.attr.productSelect,"stock",r.stock),this.$set(this.attr.productSelect,"unique",r.id),this.$set(this.attr.productSelect,"vipPrice",r.vipPrice),this.$set(this.attr.productSelect,"cart_num",1),this.$set(this,"attrValue",i.join(","))):!r&&t.length?(this.$set(this.attr.productSelect,"storeName",this.storeName),this.$set(this.attr.productSelect,"image",this.storeInfo.image),this.$set(this.attr.productSelect,"price",this.storeInfo.price),this.$set(this.attr.productSelect,"stock",0),this.$set(this.attr.productSelect,"unique",""),this.$set(this.attr.productSelect,"cart_num",0),this.$set(this,"attrValue","")):r||t.length||(this.$set(this.attr.productSelect,"storeName",this.storeName),this.$set(this.attr.productSelect,"image",this.storeInfo.image),this.$set(this.attr.productSelect,"price",this.storeInfo.price),this.$set(this.attr.productSelect,"stock",this.storeInfo.stock),this.$set(this.attr.productSelect,"unique",this.storeInfo.unique||""),this.$set(this.attr.productSelect,"cart_num",1),this.$set(this,"attrValue",""))},ChangeAttr:function(t){var i=this.productValue[t];i?(this.$set(this.attr.productSelect,"image",i.image),this.$set(this.attr.productSelect,"price",i.price),this.$set(this.attr.productSelect,"stock",i.stock),this.$set(this.attr.productSelect,"unique",i.id),this.$set(this.attr.productSelect,"cart_num",1),this.$set(this.attr.productSelect,"vipPrice",i.vipPrice),this.$set(this,"attrValue",t)):(this.$set(this.attr.productSelect,"image",this.productInfo.image),this.$set(this.attr.productSelect,"price",this.productInfo.price),this.$set(this.attr.productSelect,"stock",0),this.$set(this.attr.productSelect,"unique",this.productInfo.id),this.$set(this.attr.productSelect,"cart_num",1),this.$set(this.attr.productSelect,"vipPrice",this.productInfo.vipPrice),this.$set(this,"attrValue",""))},attrVal:function(t){this.$set(this.attr.productAttr[t.indexw],"index",this.attr.productAttr[t.indexw].attrValues[t.indexn])},iptCartNum:function(t){this.$set(this.attr.productSelect,"cart_num",t)},onLoadFun:function(){},productslist:function(){var t=this;t.loadend||t.loading||(t.loading=!0,t.loadTitle="",(0,r.getProductslist)({page:t.page,limit:t.limit,type:1,cid:t.sid}).then((function(i){var e=i.data.list,a=e.length<t.limit;t.tempArr=t.$util.SplitArray(e,t.tempArr),t.$set(t,"tempArr",t.tempArr),t.loading=!1,t.loadend=a,t.loadTitle=a?"😕人家是有底线的~~":"加载更多",t.page=t.page+1})).catch((function(i){t.loading=!1,t.loadTitle="加载更多"})))},ChangeCartNumDuo:function(t){var i=this.productValue[this.attrValue];if(void 0!==i||this.attr.productAttr.length||(i=this.attr.productSelect),void 0!==i){var e=i.stock||0,a=this.attr.productSelect;t?(a.cart_num++,a.cart_num>e&&(this.$set(this.attr.productSelect,"cart_num",e),this.$set(this,"cart_num",e))):(a.cart_num--,a.cart_num<1&&(this.$set(this.attr.productSelect,"cart_num",1),this.$set(this,"cart_num",1)))}},ChangeCartList:function(t,i){var e=this.cartData.cartList,a=e[i],r=e[i].stock;this.ChangeCartNum(t,a,r,0,a.productId,i,1),e.length||(this.cartData.iScart=!1,this.page=1,this.loadend=!1,this.tempArr=[],this.productslist())},ChangeCartNum:function(t,i){var e=this;t?i.cartNum>=i.stock?i.cartNum=i.stock:(i.cartNum++,(0,s.changeCartNum)(i.id,i.cartNum).then((function(t){e.getCartNum(!0),e.getTotalPrice()}))):(i.cartNum--,(0,s.changeCartNum)(i.id,i.cartNum).then((function(t){e.getCartNum(!0),e.getTotalPrice()})),0==i.cartNum&&(0,s.cartDel)(i.id).then((function(t){e.getCartLists(1),e.getTotalPrice(),e.productslist(),e.getCartNum()})))},goCatNum:function(){this.goCat(1)},goCat:function(t){var i=this,e=i.productValue[this.attrValue];if(i.attrValue?i.attr.cartAttr=!i.isOpen:i.isOpen?i.attr.cartAttr=!0:i.attr.cartAttr=!i.attr.cartAttr,!0===i.attr.cartAttr&&!1===i.isOpen)return i.isOpen=!0;if(i.attr.productAttr.length&&0===e.stock&&!0===i.isOpen)return i.$util.Tips({title:"产品库存不足，请选择其它"});if(1===t){var a={productId:parseFloat(i.id),cartNum:parseFloat(i.attr.productSelect.cart_num),isNew:!1,productAttrUnique:void 0!==i.attr.productSelect?i.attr.productSelect.unique:i.productInfo.id};(0,r.postCartAdd)(a).then((function(t){i.isOpen=!1,i.attr.cartAttr=!1,i.$util.Tips({title:"添加购物车成功",success:function(){setTimeout((function(){i.getCartNum(!0),i.getCartLists(1)}),100)}})})).catch((function(t){return i.isOpen=!1,i.$util.Tips({title:t})}))}else this.getPreOrder()},goCartDuo:function(i){this.isLogin?(t.showLoading({title:"加载中"}),this.storeName=i.storeName,this.getAttrs(i.id,i.storeName),this.$set(this,"id",i.id)):this.getIsLogin()},getIsLogin:function(){(0,u.toLogin)()},getAttrs:function(i){var e=this,a=this;(0,r.getAttr)(i).then((function(i){t.hideLoading(),a.$set(a.attr,"productAttr",i.data.productAttr),a.$set(a,"productValue",i.data.productValue);var r=a.attr.productAttr.map((function(t){return{attrName:t.attrName,attrValues:t.attrValues.split(","),id:t.id,isDel:t.isDel,productId:t.productId,type:t.type}}));e.$set(a.attr,"productAttr",r),e.$set(a.attr,"cartAttr",!0),a.DefaultSelect()}))},goDetail:function(i){this.isLogin?(0,o.goShopDetail)(i,this.uid).then((function(e){t.navigateTo({animationType:d.default.type,animationDuration:d.default.duration,url:"/pages/goods_details/index?id=".concat(i.id)})})):(0,u.toLogin)()},openTap:function(){this.iSlong=!1},closeTap:function(){this.iSlong=!0},getAllCategory:function(){var t=this;(0,r.getCategoryList)().then((function(i){i.data.forEach((function(t){t.child&&t.child.unshift({id:t.id,name:"全部"})}));var e=i.data;t.categoryTitle=e[0].name,t.sid=e[0].id,t.productList=e,t.categoryErList=i.data[0].child?i.data[0].child:[],t.page=1,t.loadend=!1,t.tempArr=[],t.productslist()}))},tapNav:function(t,i){var e=this.productList[t];this.navActive=t,this.categoryTitle=e.name,this.categoryErList=i.child?i.child:[],this.tabClick=0,this.tabLeft=0,this.sid=i.id,this.page=1,this.loadend=!1,this.tempArr=[],this.productslist()},longClick:function(t,i){this.productList.length>3&&(this.tabLeft=(t-1)*(this.isWidth+6)),this.tabClick=t,this.iSlong=!0,this.sid=i.id,this.page=1,this.loadend=!1,this.tempArr=[],this.productslist()},navSwitch:function(t,i){this.productList.length>3&&(this.tabLeft=(t-1)*(this.isWidth+6)),this.tabClick=t,this.iSlong=!0,this.sid=i.id,this.page=1,this.loadend=!1,this.tempArr=[],this.productslist()}}};i.default=h}).call(this,e("df3c")["default"])},ba92:function(t,i,e){"use strict";var a=e("95a2"),r=e.n(a);r.a},be01:function(t,i,e){"use strict";e.r(i);var a=e("cf4a"),r=e("e9a2");for(var s in r)["default"].indexOf(s)<0&&function(t){e.d(i,t,(function(){return r[t]}))}(s);e("9545");var c=e("828b"),n=Object(c["a"])(r["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);i["default"]=n.exports},c731:function(t,i,e){"use strict";e.r(i);var a=e("97cc"),r=e.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){e.d(i,t,(function(){return a[t]}))}(s);i["default"]=r.a},cf4a:function(t,i,e){"use strict";e.d(i,"b",(function(){return a})),e.d(i,"c",(function(){return r})),e.d(i,"a",(function(){}));var a=function(){var t=this.$createElement;this._self._c},r=[]},e9a2:function(t,i,e){"use strict";e.r(i);var a=e("0e96"),r=e.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){e.d(i,t,(function(){return a[t]}))}(s);i["default"]=r.a},f34a:function(t,i,e){"use strict";e.d(i,"b",(function(){return a})),e.d(i,"c",(function(){return r})),e.d(i,"a",(function(){}));var a=function(){var t=this.$createElement,i=(this._self._c,this.cartData.cartList.length);this.$mp.data=Object.assign({},{$root:{g0:i}})},r=[]}},[["3d2b","common/runtime","common/vendor"]]]);