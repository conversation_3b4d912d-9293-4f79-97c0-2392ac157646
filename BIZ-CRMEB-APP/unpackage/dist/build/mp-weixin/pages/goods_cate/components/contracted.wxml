<view class="productSort data-v-e7a77638"><view class="nav acea-row row-middle data-v-e7a77638"><scroll-view class="scroll-view_x data-v-e7a77638" style="width:auto;overflow:hidden;" scroll-x="{{true}}" scroll-with-animation="{{true}}" scroll-left="{{scrollLeft}}"><block wx:for="{{navLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['item','data-v-e7a77638',active==index?'on':'']}}" id="{{'id'+index}}" data-event-opts="{{[['tap',[['tabSelect',[index,'$0'],[[['navLists','',index,'id']]]]]]]}}" bindtap="__e"><view class="data-v-e7a77638">{{item.name}}</view><block wx:if="{{active==index}}"><view class="line data-v-e7a77638"></view></block></view></block></scroll-view></view><view class="list acea-row row-between-wrapper data-v-e7a77638"><block wx:for="{{productList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['godDetail',['$0'],[[['productList','',index]]]]]]]}}" class="item data-v-e7a77638" bindtap="__e"><view class="pictrue data-v-e7a77638"><image src="{{item.image}}" class="data-v-e7a77638"></image></view><view class="text data-v-e7a77638"><view class="name line1 data-v-e7a77638">{{item.storeName}}</view><view class="money data-v-e7a77638">￥<text class="num data-v-e7a77638">{{item.price}}</text></view><view class="vip acea-row row-between-wrapper data-v-e7a77638"><view class="data-v-e7a77638">{{"已售"+(item.sales+item.ficti)+"件"}}</view></view></view></view></block><block wx:if="{{$root.g0}}"><view class="loadingicon acea-row row-center-wrapper data-v-e7a77638"><text class="loading iconfont icon-jiazai data-v-e7a77638" hidden="{{loading==false}}"></text>{{loadTitle+''}}</view></block></view><block wx:if="{{$root.g1}}"><view class="noCommodity data-v-e7a77638"><view class="pictrue data-v-e7a77638"><image src="../../../static/images/noShopper.png" class="data-v-e7a77638"></image></view><recommend vue-id="eb09200c-1" hostProduct="{{hostProduct}}" class="data-v-e7a77638" bind:__l="__l"></recommend></view></block></view>