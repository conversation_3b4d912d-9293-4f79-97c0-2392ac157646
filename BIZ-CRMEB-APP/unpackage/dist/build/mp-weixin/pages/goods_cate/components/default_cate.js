(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/goods_cate/components/default_cate"],{"0bc9":function(t,e,i){},3691:function(t,e,i){"use strict";var n=i("0bc9"),a=i.n(n);a.a},"4b14":function(t,e,i){"use strict";i.r(e);var n=i("736d"),a=i("e4b1");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("3691");var c=i("828b"),r=Object(c["a"])(a["default"],n["b"],n["c"],!1,null,"030dd617",null,!1,n["a"],void 0);e["default"]=r.exports},"736d":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement;this._self._c},a=[]},"762b":function(t,e,i){"use strict";(function(t,e){var n=i("47a9");i("9e89");n(i("3240"));var a=n(i("4b14"));t.__webpack_require_UNI_MP_PLUGIN__=i,e(a.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},"963f":function(t,e,i){"use strict";(function(t){var n=i("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i("d9cf"),o=(n(i("2365")),n(i("4bef"))),c={data:function(){return{showSkeleton:!0,isNodes:0,navlist:[],productList:[{name:"占位占位",child:[{extra:""},{extra:""}]},{name:"占位占位",child:[{extra:""},{extra:""}]},{name:"占位占位",child:[{extra:""},{extra:""}]},{name:"占位占位"}],navActive:0,number:"",height:0,hightArr:[],toView:"",tabbarH:0,theme:"theme1"}},created:function(){var e=this,i=this;t.getStorage({key:"theme",success:function(t){i.theme=t.data}}),setTimeout((function(){e.isNodes++}),500),this.getAllCategory()},methods:{infoScroll:function(){var e=this,i=e.productList.length,n=e.productList[i-1]&&e.productList[i-1].child?e.productList[i-1].child:[];this.number=n?n.length:0,t.getSystemInfo({success:function(t){e.height=t.windowHeight*(750/t.windowWidth)-98}});for(var a=[],o=0;o<i;o++){var c=t.createSelectorQuery().in(this),r="#b"+o;c.select(r).boundingClientRect(),c.exec((function(t){var i=t[0].top;a.push(i),e.hightArr=a}))}},tap:function(t,e){this.toView=e,this.navActive=t},getAllCategory:function(){var t=this,e=this;(0,a.getCategoryList)().then((function(i){e.productList=i.data,setTimeout((function(){e.infoScroll()}),500),setTimeout((function(){t.showSkeleton=!1}),1e3)}))},scroll:function(t){for(var e=t.detail.scrollTop,i=this.hightArr,n=0;n<i.length;n++)e>=0&&e<i[1]-i[0]?this.navActive=0:e>=i[n]-i[0]&&e<i[n+1]-i[0]?this.navActive=n:e>=i[i.length-1]-i[0]&&(this.navActive=i.length-1)},searchSubmitValue:function(e){if(!(this.$util.trim(e.detail.value).length>0))return this.$util.Tips({title:"请填写要搜索的产品信息"});t.navigateTo({animationType:o.default.type,animationDuration:o.default.duration,url:"/pages/goods_list/index?searchValue="+e.detail.value})}}};e.default=c}).call(this,i("df3c")["default"])},e4b1:function(t,e,i){"use strict";i.r(e);var n=i("963f"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a}},[["762b","common/runtime","common/vendor"]]]);