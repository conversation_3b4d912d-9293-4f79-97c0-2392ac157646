(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/goods_cate/goods_cate"],{"094e":function(t,e,i){"use strict";i.r(e);var a=i("6fe8"),r=i("b8a5");for(var n in r)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(n);i("ec74");var s=i("828b"),c=Object(s["a"])(r["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=c.exports},"0ab1":function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={name:"d_goodList",props:{dataConfig:{type:Object,default:function(){}},tempArr:{type:Array,default:[]},isLogin:{type:Boolean,default:!1}},data:function(){return{}},created:function(){},mounted:function(){},methods:{goDetail:function(t){this.$emit("detail",t)},goCartDuo:function(t){this.$emit("gocartduo",t)}}};e.default=a},"0bc9":function(t,e,i){},"0e96":function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={name:"d_goodList",props:{dataConfig:{type:Object,default:function(){}},tempArr:{type:Array,default:[]},isLogin:{type:Boolean,default:!1}},data:function(){return{}},created:function(){},mounted:function(){},methods:{goDetail:function(t){this.$emit("detail",t)},goCartDuo:function(t){this.$emit("gocartduo",t)},goCartDan:function(t,e){this.$emit("gocartdan",t,e)},CartNumDes:function(t,e){this.$emit("ChangeCartNumDan",!1,t,e)},CartNumAdd:function(t,e){this.$emit("ChangeCartNumDan",!0,t,e)}}};e.default=a},"2f33":function(t,e,i){"use strict";i.r(e);var a=i("f34a"),r=i("c731");for(var n in r)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(n);i("ba92");var s=i("828b"),c=Object(s["a"])(r["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=c.exports},"35da":function(t,e,i){},3691:function(t,e,i){"use strict";var a=i("0bc9"),r=i.n(a);r.a},"39da":function(t,e,i){"use strict";i.r(e);var a=i("67a9"),r=i("3a5a");for(var n in r)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(n);i("4ba8");var s=i("828b"),c=Object(s["a"])(r["default"],a["b"],a["c"],!1,null,"e7a77638",null,!1,a["a"],void 0);e["default"]=c.exports},"3a5a":function(t,e,i){"use strict";i.r(e);var a=i("f86b"),r=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=r.a},"3be6":function(t,e,i){},"4b14":function(t,e,i){"use strict";i.r(e);var a=i("736d"),r=i("e4b1");for(var n in r)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(n);i("3691");var s=i("828b"),c=Object(s["a"])(r["default"],a["b"],a["c"],!1,null,"030dd617",null,!1,a["a"],void 0);e["default"]=c.exports},"4ba8":function(t,e,i){"use strict";var a=i("d16b"),r=i.n(a);r.a},5471:function(t,e,i){},"5b28":function(t,e,i){"use strict";(function(t){var a=i("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=i("d9cf"),n=i("8d22"),s=a(i("f8bb")),c=i("8f59"),o=i("3f87"),u=i("2e55"),d=a(i("4bef")),h={computed:(0,c.mapGetters)(["isLogin","uid"]),components:{productWindow:function(){i.e("components/productWindow/index").then(function(){return resolve(i("a82f"))}.bind(null,i)).catch(i.oe)},goodList:s.default,cartList:function(){i.e("components/cartList/index").then(function(){return resolve(i("bd6d"))}.bind(null,i)).catch(i.oe)},home:function(){i.e("components/home/<USER>").then(function(){return resolve(i("1e4d"))}.bind(null,i)).catch(i.oe)}},props:{showSlide:{type:Boolean,default:!0}},data:function(){return{productList:[],navActive:0,categoryTitle:"",categoryErList:[],tabLeft:0,isWidth:0,tabClick:0,iSlong:!0,tempArr:[],loading:!1,loadend:!1,loadTitle:"加载更多",page:1,limit:10,cid:0,sid:0,isAuto:!1,isShowAuth:!1,attr:{cartAttr:!1,productAttr:[],productSelect:{}},productValue:[],attrValue:"",storeName:"",id:0,cartData:{cartList:[],iScart:!1},cartCount:0,totalPrice:0,lengthCart:0}},created:function(){this.isLogin&&(this.getCartNum(),this.getCartLists(1)),this.getAllCategory();var e=this;e.lengthCart=e.cartData.cartList,t.getSystemInfo({success:function(t){e.isWidth=t.windowWidth/5}})},methods:{subOrder:function(){var t=this.cartData.cartList;if(!t.length)return this.$util.Tips({title:"请选择产品"});var e=t.map((function(t){return{shoppingCartId:Number(t.id)}}));this.$Order.getPreOrder("shoppingCart",e),this.cartData.iScart=!1},getTotalPrice:function(){var t=this,e=t.cartData.cartList,i=0;e.forEach((function(e){e.attrStatus&&(i=t.$util.$h.Add(i,t.$util.$h.Mul(e.cartNum,e.vipPrice?e.vipPrice:e.price)))})),t.$set(t,"totalPrice",i)},ChangeSubDel:function(t){var e=this,i=e.cartData.cartList,a=[];i.forEach((function(t){a.push(t.id)})),(0,n.cartDel)(a.join(",")).then((function(t){e.$set(e.cartData,"cartList",[]),e.cartData.iScart=!1,e.totalPrice=0,e.page=1,e.loadend=!1,e.tempArr=[],e.productslist(),e.getCartNum()}))},ChangeOneDel:function(t,e){var i=this,a=i.cartData.cartList;(0,n.cartDel)(t.toString()).then((function(t){a.splice(e,1),a.length||(i.cartData.iScart=!1,i.page=1,i.loadend=!1,i.tempArr=[],i.productslist()),i.getCartNum()}))},getCartLists:function(t){var e=this,i={page:1,limit:e.cartCount,isValid:!0};(0,n.getCartList)(i).then((function(i){e.$set(e.cartData,"cartList",i.data.list),i.data.list.length?e.$set(e.cartData,"iScart",!t&&!e.cartData.iScart):e.$set(e.cartData,"iScart",!1),e.getTotalPrice()}))},closeList:function(t){this.$set(this.cartData,"iScart",t),this.page=1,this.loadend=!1,this.tempArr=[],this.productslist()},getCartNum:function(){var t=this;(0,n.getCartCounts)(!0,"sum").then((function(e){t.$set(t,"cartCount",e.data.count)}))},onMyEvent:function(){this.$set(this.attr,"cartAttr",!1)},DefaultSelect:function(){var t=this.attr.productAttr,e=[];for(var i in this.productValue)if(this.productValue[i].stock>0){e=this.attr.productAttr.length?i.split(","):[];break}for(var a=0;a<t.length;a++)this.$set(t[a],"index",e[a]);var r=this.productValue[e.join(",")];r&&t.length?(this.$set(this.attr.productSelect,"storeName",this.storeName),this.$set(this.attr.productSelect,"image",r.image),this.$set(this.attr.productSelect,"price",r.price),this.$set(this.attr.productSelect,"stock",r.stock),this.$set(this.attr.productSelect,"unique",r.id),this.$set(this.attr.productSelect,"vipPrice",r.vipPrice),this.$set(this.attr.productSelect,"cart_num",1),this.$set(this,"attrValue",e.join(","))):!r&&t.length?(this.$set(this.attr.productSelect,"storeName",this.storeName),this.$set(this.attr.productSelect,"image",this.storeInfo.image),this.$set(this.attr.productSelect,"price",this.storeInfo.price),this.$set(this.attr.productSelect,"stock",0),this.$set(this.attr.productSelect,"unique",""),this.$set(this.attr.productSelect,"cart_num",0),this.$set(this,"attrValue","")):r||t.length||(this.$set(this.attr.productSelect,"storeName",this.storeName),this.$set(this.attr.productSelect,"image",this.storeInfo.image),this.$set(this.attr.productSelect,"price",this.storeInfo.price),this.$set(this.attr.productSelect,"stock",this.storeInfo.stock),this.$set(this.attr.productSelect,"unique",this.storeInfo.unique||""),this.$set(this.attr.productSelect,"cart_num",1),this.$set(this,"attrValue",""))},ChangeAttr:function(t){var e=this.productValue[t];e?(this.$set(this.attr.productSelect,"image",e.image),this.$set(this.attr.productSelect,"price",e.price),this.$set(this.attr.productSelect,"stock",e.stock),this.$set(this.attr.productSelect,"unique",e.id),this.$set(this.attr.productSelect,"cart_num",1),this.$set(this.attr.productSelect,"vipPrice",e.vipPrice),this.$set(this,"attrValue",t)):(this.$set(this.attr.productSelect,"image",this.productInfo.image),this.$set(this.attr.productSelect,"price",this.productInfo.price),this.$set(this.attr.productSelect,"stock",0),this.$set(this.attr.productSelect,"unique",this.productInfo.id),this.$set(this.attr.productSelect,"cart_num",1),this.$set(this.attr.productSelect,"vipPrice",this.productInfo.vipPrice),this.$set(this,"attrValue",""))},attrVal:function(t){this.$set(this.attr.productAttr[t.indexw],"index",this.attr.productAttr[t.indexw].attrValues[t.indexn])},iptCartNum:function(t){this.$set(this.attr.productSelect,"cart_num",t)},onLoadFun:function(){},productslist:function(){var t=this;t.loadend||t.loading||(t.loading=!0,t.loadTitle="",(0,r.getProductslist)({page:t.page,limit:t.limit,type:1,cid:t.sid}).then((function(e){var i=e.data.list,a=i.length<t.limit;t.tempArr=t.$util.SplitArray(i,t.tempArr),t.$set(t,"tempArr",t.tempArr),t.loading=!1,t.loadend=a,t.loadTitle=a?"😕人家是有底线的~~":"加载更多",t.page=t.page+1})).catch((function(e){t.loading=!1,t.loadTitle="加载更多"})))},ChangeCartNumDuo:function(t){var e=this.productValue[this.attrValue];if(void 0!==e||this.attr.productAttr.length||(e=this.attr.productSelect),void 0!==e){var i=e.stock||0,a=this.attr.productSelect;t?(a.cart_num++,a.cart_num>i&&(this.$set(this.attr.productSelect,"cart_num",i),this.$set(this,"cart_num",i))):(a.cart_num--,a.cart_num<1&&(this.$set(this.attr.productSelect,"cart_num",1),this.$set(this,"cart_num",1)))}},ChangeCartList:function(t,e){var i=this.cartData.cartList,a=i[e],r=i[e].stock;this.ChangeCartNum(t,a,r,0,a.productId,e,1),i.length||(this.cartData.iScart=!1,this.page=1,this.loadend=!1,this.tempArr=[],this.productslist())},ChangeCartNum:function(t,e){var i=this;t?e.cartNum>=e.stock?e.cartNum=e.stock:(e.cartNum++,(0,n.changeCartNum)(e.id,e.cartNum).then((function(t){i.getCartNum(!0),i.getTotalPrice()}))):(e.cartNum--,(0,n.changeCartNum)(e.id,e.cartNum).then((function(t){i.getCartNum(!0),i.getTotalPrice()})),0==e.cartNum&&(0,n.cartDel)(e.id).then((function(t){i.getCartLists(1),i.getTotalPrice(),i.productslist(),i.getCartNum()})))},goCatNum:function(){this.goCat(1)},goCat:function(t){var e=this,i=e.productValue[this.attrValue];if(e.attrValue?e.attr.cartAttr=!e.isOpen:e.isOpen?e.attr.cartAttr=!0:e.attr.cartAttr=!e.attr.cartAttr,!0===e.attr.cartAttr&&!1===e.isOpen)return e.isOpen=!0;if(e.attr.productAttr.length&&0===i.stock&&!0===e.isOpen)return e.$util.Tips({title:"产品库存不足，请选择其它"});if(1===t){var a={productId:parseFloat(e.id),cartNum:parseFloat(e.attr.productSelect.cart_num),isNew:!1,productAttrUnique:void 0!==e.attr.productSelect?e.attr.productSelect.unique:e.productInfo.id};(0,r.postCartAdd)(a).then((function(t){e.isOpen=!1,e.attr.cartAttr=!1,e.$util.Tips({title:"添加购物车成功",success:function(){setTimeout((function(){e.getCartNum(!0),e.getCartLists(1)}),100)}})})).catch((function(t){return e.isOpen=!1,e.$util.Tips({title:t})}))}else this.getPreOrder()},goCartDuo:function(e){this.isLogin?(t.showLoading({title:"加载中"}),this.storeName=e.storeName,this.getAttrs(e.id,e.storeName),this.$set(this,"id",e.id)):this.getIsLogin()},getIsLogin:function(){(0,u.toLogin)()},getAttrs:function(e){var i=this,a=this;(0,r.getAttr)(e).then((function(e){t.hideLoading(),a.$set(a.attr,"productAttr",e.data.productAttr),a.$set(a,"productValue",e.data.productValue);var r=a.attr.productAttr.map((function(t){return{attrName:t.attrName,attrValues:t.attrValues.split(","),id:t.id,isDel:t.isDel,productId:t.productId,type:t.type}}));i.$set(a.attr,"productAttr",r),i.$set(a.attr,"cartAttr",!0),a.DefaultSelect()}))},goDetail:function(e){this.isLogin?(0,o.goShopDetail)(e,this.uid).then((function(i){t.navigateTo({animationType:d.default.type,animationDuration:d.default.duration,url:"/pages/goods_details/index?id=".concat(e.id)})})):(0,u.toLogin)()},openTap:function(){this.iSlong=!1},closeTap:function(){this.iSlong=!0},getAllCategory:function(){var t=this;(0,r.getCategoryList)().then((function(e){e.data.forEach((function(t){t.child&&t.child.unshift({id:t.id,name:"全部"})}));var i=e.data;t.categoryTitle=i[0].name,t.sid=i[0].id,t.productList=i,t.categoryErList=e.data[0].child?e.data[0].child:[],t.page=1,t.loadend=!1,t.tempArr=[],t.productslist()}))},tapNav:function(t,e){var i=this.productList[t];this.navActive=t,this.categoryTitle=i.name,this.categoryErList=e.child?e.child:[],this.tabClick=0,this.tabLeft=0,this.sid=e.id,this.page=1,this.loadend=!1,this.tempArr=[],this.productslist()},navSwitch:function(t,e){this.productList.length>3&&(this.tabLeft=(t-1)*(this.isWidth+6)),this.tabClick=t,this.iSlong=!0,this.sid=e.id,this.page=1,this.loadend=!1,this.tempArr=[],this.productslist()},longClick:function(t,e){this.productList.length>3&&(this.tabLeft=(t-1)*(this.isWidth+6)),this.tabClick=t,this.iSlong=!0,this.sid=e.id,this.page=1,this.loadend=!1,this.tempArr=[],this.productslist()}}};e.default=h}).call(this,i("df3c")["default"])},"60e6":function(t,e,i){"use strict";var a=i("35da"),r=i.n(a);r.a},"67a9":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){}));var a=function(){var t=this.$createElement,e=(this._self._c,this.productList.length),i=0==this.productList.length&&this.page>1;this.$mp.data=Object.assign({},{$root:{g0:e,g1:i}})},r=[]},"6fe8":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){}));var a=function(){var t=this.$createElement,e=(this._self._c,this.cartData.cartList.length);this.$mp.data=Object.assign({},{$root:{g0:e}})},r=[]},"736d":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){}));var a=function(){var t=this.$createElement;this._self._c},r=[]},"7bd9":function(t,e,i){"use strict";i.r(e);var a=i("c630"),r=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=r.a},9299:function(t,e,i){},9545:function(t,e,i){"use strict";var a=i("9299"),r=i.n(a);r.a},"95a2":function(t,e,i){},"963f":function(t,e,i){"use strict";(function(t){var a=i("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=i("d9cf"),n=(a(i("2365")),a(i("4bef"))),s={data:function(){return{showSkeleton:!0,isNodes:0,navlist:[],productList:[{name:"占位占位",child:[{extra:""},{extra:""}]},{name:"占位占位",child:[{extra:""},{extra:""}]},{name:"占位占位",child:[{extra:""},{extra:""}]},{name:"占位占位"}],navActive:0,number:"",height:0,hightArr:[],toView:"",tabbarH:0,theme:"theme1"}},created:function(){var e=this,i=this;t.getStorage({key:"theme",success:function(t){i.theme=t.data}}),setTimeout((function(){e.isNodes++}),500),this.getAllCategory()},methods:{infoScroll:function(){var e=this,i=e.productList.length,a=e.productList[i-1]&&e.productList[i-1].child?e.productList[i-1].child:[];this.number=a?a.length:0,t.getSystemInfo({success:function(t){e.height=t.windowHeight*(750/t.windowWidth)-98}});for(var r=[],n=0;n<i;n++){var s=t.createSelectorQuery().in(this),c="#b"+n;s.select(c).boundingClientRect(),s.exec((function(t){var i=t[0].top;r.push(i),e.hightArr=r}))}},tap:function(t,e){this.toView=e,this.navActive=t},getAllCategory:function(){var t=this,e=this;(0,r.getCategoryList)().then((function(i){e.productList=i.data,setTimeout((function(){e.infoScroll()}),500),setTimeout((function(){t.showSkeleton=!1}),1e3)}))},scroll:function(t){for(var e=t.detail.scrollTop,i=this.hightArr,a=0;a<i.length;a++)e>=0&&e<i[1]-i[0]?this.navActive=0:e>=i[a]-i[0]&&e<i[a+1]-i[0]?this.navActive=a:e>=i[i.length-1]-i[0]&&(this.navActive=i.length-1)},searchSubmitValue:function(e){if(!(this.$util.trim(e.detail.value).length>0))return this.$util.Tips({title:"请填写要搜索的产品信息"});t.navigateTo({animationType:n.default.type,animationDuration:n.default.duration,url:"/pages/goods_list/index?searchValue="+e.detail.value})}}};e.default=s}).call(this,i("df3c")["default"])},"97cc":function(t,e,i){"use strict";(function(t){var a=i("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=i("d9cf"),n=i("8d22"),s=a(i("be01")),c=i("8f59"),o=i("3f87"),u=i("2e55"),d=a(i("4bef")),h={computed:(0,c.mapGetters)(["isLogin","uid"]),components:{productWindow:function(){i.e("components/productWindow/index").then(function(){return resolve(i("a82f"))}.bind(null,i)).catch(i.oe)},goodList:s.default,cartList:function(){i.e("components/cartList/index").then(function(){return resolve(i("bd6d"))}.bind(null,i)).catch(i.oe)},home:function(){i.e("components/home/<USER>").then(function(){return resolve(i("1e4d"))}.bind(null,i)).catch(i.oe)}},props:{showSlide:{type:Boolean,default:!0}},data:function(){return{productList:[],navActive:0,categoryTitle:"",categoryErList:[],tabLeft:0,isWidth:0,tabClick:0,iSlong:!0,tempArr:[],loading:!1,loadend:!1,loadTitle:"加载更多",page:1,limit:10,cid:0,sid:0,isAuto:!1,isShowAuth:!1,attr:{cartAttr:!1,productAttr:[],productSelect:{}},productValue:[],attrValue:"",storeName:"",id:0,cartData:{cartList:[],iScart:!1},cartCount:0,totalPrice:0,lengthCart:0,theme:"theme1"}},created:function(){this.isLogin&&(this.getCartNum(),this.getCartLists(1)),this.getAllCategory();var e=this;e.lengthCart=e.cartData.cartList,t.getSystemInfo({success:function(t){e.isWidth=t.windowWidth/5}})},methods:{subOrder:function(){var t=this.cartData.cartList;if(!t.length)return this.$util.Tips({title:"请选择产品"});var e=t.map((function(t){return{shoppingCartId:Number(t.id)}}));this.$Order.getPreOrder("shoppingCart",e),this.cartData.iScart=!1},getTotalPrice:function(){var t=this,e=t.cartData.cartList,i=0;e.forEach((function(e){e.attrStatus&&(i=t.$util.$h.Add(i,t.$util.$h.Mul(e.cartNum,e.vipPrice?e.vipPrice:e.price)))})),t.$set(t,"totalPrice",i)},ChangeSubDel:function(t){var e=this,i=e.cartData.cartList,a=[];i.forEach((function(t){a.push(t.id)})),(0,n.cartDel)(a.join(",")).then((function(t){e.$set(e.cartData,"cartList",[]),e.cartData.iScart=!1,e.totalPrice=0,e.page=1,e.loadend=!1,e.tempArr=[],e.productslist(),e.getCartNum()}))},ChangeOneDel:function(t,e){var i=this,a=i.cartData.cartList;(0,n.cartDel)(t.toString()).then((function(t){a.splice(e,1),a.length||(i.cartData.iScart=!1,i.page=1,i.loadend=!1,i.tempArr=[],i.productslist()),i.getCartNum()}))},getCartLists:function(t){var e=this,i={page:1,limit:e.cartCount,isValid:!0};(0,n.getCartList)(i).then((function(i){e.$set(e.cartData,"cartList",i.data.list),i.data.list.length?e.$set(e.cartData,"iScart",!t&&!e.cartData.iScart):e.$set(e.cartData,"iScart",!1),e.getTotalPrice()}))},closeList:function(t){this.$set(this.cartData,"iScart",t),this.page=1,this.loadend=!1,this.tempArr=[],this.productslist()},getCartNum:function(){var t=this;(0,n.getCartCounts)(!0,"sum").then((function(e){t.$set(t,"cartCount",e.data.count)}))},onMyEvent:function(){this.$set(this.attr,"cartAttr",!1)},DefaultSelect:function(){var t=this.attr.productAttr,e=[];for(var i in this.productValue)if(this.productValue[i].stock>0){e=this.attr.productAttr.length?i.split(","):[];break}for(var a=0;a<t.length;a++)this.$set(t[a],"index",e[a]);var r=this.productValue[e.join(",")];r&&t.length?(this.$set(this.attr.productSelect,"storeName",this.storeName),this.$set(this.attr.productSelect,"image",r.image),this.$set(this.attr.productSelect,"price",r.price),this.$set(this.attr.productSelect,"stock",r.stock),this.$set(this.attr.productSelect,"unique",r.id),this.$set(this.attr.productSelect,"vipPrice",r.vipPrice),this.$set(this.attr.productSelect,"cart_num",1),this.$set(this,"attrValue",e.join(","))):!r&&t.length?(this.$set(this.attr.productSelect,"storeName",this.storeName),this.$set(this.attr.productSelect,"image",this.storeInfo.image),this.$set(this.attr.productSelect,"price",this.storeInfo.price),this.$set(this.attr.productSelect,"stock",0),this.$set(this.attr.productSelect,"unique",""),this.$set(this.attr.productSelect,"cart_num",0),this.$set(this,"attrValue","")):r||t.length||(this.$set(this.attr.productSelect,"storeName",this.storeName),this.$set(this.attr.productSelect,"image",this.storeInfo.image),this.$set(this.attr.productSelect,"price",this.storeInfo.price),this.$set(this.attr.productSelect,"stock",this.storeInfo.stock),this.$set(this.attr.productSelect,"unique",this.storeInfo.unique||""),this.$set(this.attr.productSelect,"cart_num",1),this.$set(this,"attrValue",""))},ChangeAttr:function(t){var e=this.productValue[t];e?(this.$set(this.attr.productSelect,"image",e.image),this.$set(this.attr.productSelect,"price",e.price),this.$set(this.attr.productSelect,"stock",e.stock),this.$set(this.attr.productSelect,"unique",e.id),this.$set(this.attr.productSelect,"cart_num",1),this.$set(this.attr.productSelect,"vipPrice",e.vipPrice),this.$set(this,"attrValue",t)):(this.$set(this.attr.productSelect,"image",this.productInfo.image),this.$set(this.attr.productSelect,"price",this.productInfo.price),this.$set(this.attr.productSelect,"stock",0),this.$set(this.attr.productSelect,"unique",this.productInfo.id),this.$set(this.attr.productSelect,"cart_num",1),this.$set(this.attr.productSelect,"vipPrice",this.productInfo.vipPrice),this.$set(this,"attrValue",""))},attrVal:function(t){this.$set(this.attr.productAttr[t.indexw],"index",this.attr.productAttr[t.indexw].attrValues[t.indexn])},iptCartNum:function(t){this.$set(this.attr.productSelect,"cart_num",t)},onLoadFun:function(){},productslist:function(){var t=this;t.loadend||t.loading||(t.loading=!0,t.loadTitle="",(0,r.getProductslist)({page:t.page,limit:t.limit,type:1,cid:t.sid}).then((function(e){var i=e.data.list,a=i.length<t.limit;t.tempArr=t.$util.SplitArray(i,t.tempArr),t.$set(t,"tempArr",t.tempArr),t.loading=!1,t.loadend=a,t.loadTitle=a?"😕人家是有底线的~~":"加载更多",t.page=t.page+1})).catch((function(e){t.loading=!1,t.loadTitle="加载更多"})))},ChangeCartNumDuo:function(t){var e=this.productValue[this.attrValue];if(void 0!==e||this.attr.productAttr.length||(e=this.attr.productSelect),void 0!==e){var i=e.stock||0,a=this.attr.productSelect;t?(a.cart_num++,a.cart_num>i&&(this.$set(this.attr.productSelect,"cart_num",i),this.$set(this,"cart_num",i))):(a.cart_num--,a.cart_num<1&&(this.$set(this.attr.productSelect,"cart_num",1),this.$set(this,"cart_num",1)))}},ChangeCartList:function(t,e){var i=this.cartData.cartList,a=i[e],r=i[e].stock;this.ChangeCartNum(t,a,r,0,a.productId,e,1),i.length||(this.cartData.iScart=!1,this.page=1,this.loadend=!1,this.tempArr=[],this.productslist())},ChangeCartNum:function(t,e){var i=this;t?e.cartNum>=e.stock?e.cartNum=e.stock:(e.cartNum++,(0,n.changeCartNum)(e.id,e.cartNum).then((function(t){i.getCartNum(!0),i.getTotalPrice()}))):(e.cartNum--,(0,n.changeCartNum)(e.id,e.cartNum).then((function(t){i.getCartNum(!0),i.getTotalPrice()})),0==e.cartNum&&(0,n.cartDel)(e.id).then((function(t){i.getCartLists(1),i.getTotalPrice(),i.productslist(),i.getCartNum()})))},goCatNum:function(){this.goCat(1)},goCat:function(t){var e=this,i=e.productValue[this.attrValue];if(e.attrValue?e.attr.cartAttr=!e.isOpen:e.isOpen?e.attr.cartAttr=!0:e.attr.cartAttr=!e.attr.cartAttr,!0===e.attr.cartAttr&&!1===e.isOpen)return e.isOpen=!0;if(e.attr.productAttr.length&&0===i.stock&&!0===e.isOpen)return e.$util.Tips({title:"产品库存不足，请选择其它"});if(1===t){var a={productId:parseFloat(e.id),cartNum:parseFloat(e.attr.productSelect.cart_num),isNew:!1,productAttrUnique:void 0!==e.attr.productSelect?e.attr.productSelect.unique:e.productInfo.id};(0,r.postCartAdd)(a).then((function(t){e.isOpen=!1,e.attr.cartAttr=!1,e.$util.Tips({title:"添加购物车成功",success:function(){setTimeout((function(){e.getCartNum(!0),e.getCartLists(1)}),100)}})})).catch((function(t){return e.isOpen=!1,e.$util.Tips({title:t})}))}else this.getPreOrder()},goCartDuo:function(e){this.isLogin?(t.showLoading({title:"加载中"}),this.storeName=e.storeName,this.getAttrs(e.id,e.storeName),this.$set(this,"id",e.id)):this.getIsLogin()},getIsLogin:function(){(0,u.toLogin)()},getAttrs:function(e){var i=this,a=this;(0,r.getAttr)(e).then((function(e){t.hideLoading(),a.$set(a.attr,"productAttr",e.data.productAttr),a.$set(a,"productValue",e.data.productValue);var r=a.attr.productAttr.map((function(t){return{attrName:t.attrName,attrValues:t.attrValues.split(","),id:t.id,isDel:t.isDel,productId:t.productId,type:t.type}}));i.$set(a.attr,"productAttr",r),i.$set(a.attr,"cartAttr",!0),a.DefaultSelect()}))},goDetail:function(e){this.isLogin?(0,o.goShopDetail)(e,this.uid).then((function(i){t.navigateTo({animationType:d.default.type,animationDuration:d.default.duration,url:"/pages/goods_details/index?id=".concat(e.id)})})):(0,u.toLogin)()},openTap:function(){this.iSlong=!1},closeTap:function(){this.iSlong=!0},getAllCategory:function(){var t=this;(0,r.getCategoryList)().then((function(e){e.data.forEach((function(t){t.child&&t.child.unshift({id:t.id,name:"全部"})}));var i=e.data;t.categoryTitle=i[0].name,t.sid=i[0].id,t.productList=i,t.categoryErList=e.data[0].child?e.data[0].child:[],t.page=1,t.loadend=!1,t.tempArr=[],t.productslist()}))},tapNav:function(t,e){var i=this.productList[t];this.navActive=t,this.categoryTitle=i.name,this.categoryErList=e.child?e.child:[],this.tabClick=0,this.tabLeft=0,this.sid=e.id,this.page=1,this.loadend=!1,this.tempArr=[],this.productslist()},longClick:function(t,e){this.productList.length>3&&(this.tabLeft=(t-1)*(this.isWidth+6)),this.tabClick=t,this.iSlong=!0,this.sid=e.id,this.page=1,this.loadend=!1,this.tempArr=[],this.productslist()},navSwitch:function(t,e){this.productList.length>3&&(this.tabLeft=(t-1)*(this.isWidth+6)),this.tabClick=t,this.iSlong=!0,this.sid=e.id,this.page=1,this.loadend=!1,this.tempArr=[],this.productslist()}}};e.default=h}).call(this,i("df3c")["default"])},b8a5:function(t,e,i){"use strict";i.r(e);var a=i("5b28"),r=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=r.a},ba92:function(t,e,i){"use strict";var a=i("95a2"),r=i.n(a);r.a},be01:function(t,e,i){"use strict";i.r(e);var a=i("cf4a"),r=i("e9a2");for(var n in r)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(n);i("9545");var s=i("828b"),c=Object(s["a"])(r["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=c.exports},c630:function(t,e,i){"use strict";(function(t){var a=i("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=a(i("4b14")),n=a(i("094e")),s=a(i("39da")),c=a(i("2f33")),o=i("84f5"),u=i("8f59"),d=getApp(),h={data:function(){return{currentPage:"one",theme:d.globalData.theme,showSlide:!0,winHeight:"",configApi:{}}},computed:(0,u.mapGetters)(["isLogin","uid"]),onLoad:function(){var e=this,i=e.$Cache.getItem("categoryConfig");switch(e.showSlide="true"==i.isShowCategory,i.categoryConfig){case"1":e.$set(e,"currentPage","one"),t.showTabBar();break;case"2":e.$set(e,"currentPage","two"),t.showTabBar();break;case"3":e.$set(e,"currentPage","three"),t.hideTabBar();break;case"4":e.$set(e,"currentPage","four"),t.hideTabBar();break}t.getSystemInfo({success:function(t){e.winHeight=t.windowHeight}})},onShow:function(){var e=this;switch(this.currentPage){case"one":t.showTabBar();break;case"two":t.showTabBar();break;case"three":t.hideTabBar(),setTimeout((function(){e.isLogin&&(e.$refs.classThree.getCartNum(),e.$refs.classThree.getCartLists(1))}),500);break;case"four":t.hideTabBar(),setTimeout((function(){e.isLogin&&(e.$refs.classFour.getCartNum(),e.$refs.classFour.getCartLists(1))}),500);break}},components:{cate:r.default,optimization:n.default,contracted:s.default,fresh:c.default,home:function(){i.e("components/home/<USER>").then(function(){return resolve(i("1e4d"))}.bind(null,i)).catch(i.oe)}},methods:{shareApi:function(){var t=this;(0,o.getShare)().then((function(e){t.$set(t,"configApi",e.data)}))},setOpenShare:function(t){if(this.$wechat.isWeixin()){var e={desc:t.synopsis,title:t.title,link:location.href,imgUrl:t.img};this.$wechat.wechatEvevt(["updateAppMessageShareData","updateTimelineShareData"],e)}}},onReachBottom:function(){"two"==this.currentPage&&this.$refs.classTwo.getProductList(),"three"==this.currentPage&&this.$refs.classThree.productslist(),"four"==this.currentPage&&this.$refs.classFour.productslist()}};e.default=h}).call(this,i("df3c")["default"])},c731:function(t,e,i){"use strict";i.r(e);var a=i("97cc"),r=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=r.a},cf4a:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){}));var a=function(){var t=this.$createElement;this._self._c},r=[]},d16b:function(t,e,i){},ddcc:function(t,e,i){"use strict";i.r(e);var a=i("0ab1"),r=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=r.a},e1a1:function(t,e,i){"use strict";var a=i("3be6"),r=i.n(a);r.a},e1b2:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){}));var a=function(){var t=this.$createElement;this._self._c},r=[]},e4b1:function(t,e,i){"use strict";i.r(e);var a=i("963f"),r=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=r.a},e9a2:function(t,e,i){"use strict";i.r(e);var a=i("0e96"),r=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=r.a},ec74:function(t,e,i){"use strict";var a=i("5471"),r=i.n(a);r.a},f0d1:function(t,e,i){"use strict";i.r(e);var a=i("f3bf"),r=i("7bd9");for(var n in r)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(n);i("60e6");var s=i("828b"),c=Object(s["a"])(r["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=c.exports},f34a:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){}));var a=function(){var t=this.$createElement,e=(this._self._c,this.cartData.cartList.length);this.$mp.data=Object.assign({},{$root:{g0:e}})},r=[]},f3bf:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){}));var a=function(){var t=this.$createElement;this._self._c},r=[]},f86b:function(t,e,i){"use strict";(function(t){var a=i("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=i("d9cf"),n=i("3f87"),s=i("8f59"),c=a(i("4bef")),o={computed:(0,s.mapGetters)(["uid"]),components:{recommend:function(){Promise.all([i.e("common/vendor"),i.e("components/recommend/index")]).then(function(){return resolve(i("66cd"))}.bind(null,i)).catch(i.oe)}},data:function(){return{navLists:[],productList:[],scrollLeft:0,active:0,loading:!1,loadend:!1,loadTitle:"加载更多",page:1,limit:10,cid:0,hostProduct:[]}},created:function(){this.getAllCategory(),this.getProductList(),this.get_host_product()},methods:{godDetail:function(e){(0,n.goShopDetail)(e,this.uid).then((function(i){t.navigateTo({animationType:c.default.type,animationDuration:c.default.duration,url:"/pages/goods_details/index?id=".concat(e.id)})}))},tabSelect:function(e,i){var a=this;this.active=e;var r=t.createSelectorQuery().in(this);r.select("#id"+e).boundingClientRect((function(t){a.scrollLeft=(e-1)*t.width})).exec(),this.cid=i,this.loadend=!1,this.page=1,this.$set(this,"productList",[]),this.getProductList()},getAllCategory:function(){var t=this;(0,r.getCategoryList)().then((function(e){t.navLists=e.data}))},getProductList:function(){var t=this;t.loadend||t.loading||(t.loading=!0,t.loadTitle="",(0,r.getProductslist)({page:t.page,limit:t.limit,cid:t.cid}).then((function(e){var i=e.data.list,a=i.length<t.limit;t.productList=t.$util.SplitArray(i,t.productList),t.$set(t,"productList",t.productList),t.loading=!1,t.loadend=a,t.loadTitle=a?"😕人家是有底线的~~":"加载更多",t.page=t.page+1})).catch((function(e){t.loading=!1,t.loadTitle="加载更多"})))},get_host_product:function(){var t=this;(0,r.getProductHot)(1,20).then((function(e){t.$set(t,"hostProduct",e.data.list)}))}}};e.default=o}).call(this,i("df3c")["default"])},f8bb:function(t,e,i){"use strict";i.r(e);var a=i("e1b2"),r=i("ddcc");for(var n in r)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(n);i("e1a1");var s=i("828b"),c=Object(s["a"])(r["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=c.exports},fba5:function(t,e,i){"use strict";(function(t,e){var a=i("47a9");i("9e89");a(i("3240"));var r=a(i("f0d1"));t.__webpack_require_UNI_MP_PLUGIN__=i,e(r.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])}},[["fba5","common/runtime","common/vendor"]]]);