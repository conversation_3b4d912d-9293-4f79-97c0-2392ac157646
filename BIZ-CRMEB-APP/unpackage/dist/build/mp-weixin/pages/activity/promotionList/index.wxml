<view class="quality-recommend _div" data-theme="{{theme}}"><block wx:if="{{typeInfo.pic}}"><view class="saleBox"></view></block><block wx:if="{{typeInfo.pic}}"><view class="header skeleton-rect"><swiper indicator-dots="true" autoplay="true" circular="{{circular}}" interval="3000" duration="1500" indicator-color="rgba(255,255,255,0.6)" indicator-active-color="#fff"><block><swiper-item class="borRadius14"><image class="slide-image borRadius14" src="{{typeInfo.pic}}" lazy-load="{{true}}"></image></swiper-item></block></swiper></view></block><view class="title acea-row row-center-wrapper _div"><view class="line _div"></view><view class="name _div"><label class="iconfont icon-jingpintuijian _span"></label>{{''+typeInfo.name+''}}</view><view class="line _div"></view></view><view class="wrapper"><view class="list"><block wx:for="{{tempArr}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['tempArr','',index,'id']]]]]]]}}" class="item acea-row row-middle" bindtap="__e"><view class="img_box"><image class="pictrue" src="{{item.image}}"></image></view><view class="ml_11 flex-column justify-between"><view class="goods_name">{{item.storeName}}</view><view class="price flex justify-between"><view><text class="price_bdg">￥</text>{{item.price+''}}<text class="otPrice">{{"￥"+item.otPrice}}</text></view></view></view></view></block></view><block wx:if="{{goodScroll}}"><view class="loadingicon acea-row row-center-wrapper"><text class="loading iconfont icon-jiazai" hidden="{{loading==false}}"></text></view></block><block wx:if="{{$root.g0>0}}"><view class="txt-bar">😕人家是有底线的~~</view></block><block wx:if="{{$root.g1==0}}"><empty-page vue-id="a64f4cee-1" title="暂无数据~" bind:__l="__l"></empty-page></block></view></view>