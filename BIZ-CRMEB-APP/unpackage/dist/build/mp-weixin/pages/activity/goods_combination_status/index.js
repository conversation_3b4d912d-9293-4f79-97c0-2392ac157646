(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/activity/goods_combination_status/index"],{"8cc8":function(t,i,e){},b501:function(t,i,e){"use strict";e.r(i);var o=e("bb9a"),a=e("dd47");for(var s in a)["default"].indexOf(s)<0&&function(t){e.d(i,t,(function(){return a[t]}))}(s);e("e27d");var n=e("828b"),r=Object(n["a"])(a["default"],o["b"],o["c"],!1,null,"2b413c92",null,!1,o["a"],void 0);i["default"]=r.exports},bb9a:function(t,i,e){"use strict";e.d(i,"b",(function(){return o})),e.d(i,"c",(function(){return a})),e.d(i,"a",(function(){}));var o=function(){var t=this,i=t.$createElement,e=(t._self._c,t.pinkAll.length);t._isMounted||(t.e0=function(i){t.H5ShareBox=!1}),t.$mp.data=Object.assign({},{$root:{g0:e}})},a=[]},beb8:function(t,i,e){"use strict";(function(t){var o=e("47a9");Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var a=o(e("7064")),s=e("84f5"),n=e("2e55"),r=e("8f59"),c=e("8cd5"),u=(e("d9cf"),getApp()),h={name:"GroupRule",components:{CountDown:function(){e.e("components/countDown/index").then(function(){return resolve(e("4376"))}.bind(null,e)).catch(e.oe)},ProductWindow:function(){e.e("components/productWindow/index").then(function(){return resolve(e("a82f"))}.bind(null,e)).catch(e.oe)},home:function(){e.e("components/home/<USER>").then(function(){return resolve(e("1e4d"))}.bind(null,e)).catch(e.oe)},authorize:function(){Promise.all([e.e("common/vendor"),e.e("components/Authorize")]).then(function(){return resolve(e("8de5"))}.bind(null,e)).catch(e.oe)}},props:{},data:function(){return{bgColor:{bgColor:"#333333",Color:"#fff",width:"44rpx",timeTxtwidth:"16rpx",isDay:!0},currentPinkOrder:"",isOk:0,pinkBool:0,userBool:0,pinkAll:[],pinkT:[],storeCombination:{},storeCombinationHost:[],pinkId:0,count:0,iShidden:!1,isOpen:!1,attr:{cartAttr:!1,productSelect:{image:"",storeName:"",price:"",quota:0,unique:"",cart_num:1,quotaShow:0,stock:0,num:0},attrValue:"",productAttr:[]},cart_num:"",limit:10,page:1,loading:!1,loadend:!1,userInfo:{},posters:!1,H5ShareBox:!1,isAuto:!1,isShowAuth:!1,onceNum:0,timestamp:0,qrcodeSize:600,posterbackgd:"/static/images/canbj.png",PromotionCode:"",canvasStatus:!1,imgTop:"",imagePath:"",theme:u.globalData.theme,vacancyPic:e("2243")}},watch:{userData:{handler:function(t,i){t&&(this.userInfo=t,u.globalData.openPages="/pages/activity/goods_combination_status/index?id="+this.pinkId+"&spread="+this.uid)},deep:!0}},computed:(0,r.mapGetters)({isLogin:"isLogin",userData:"userInfo",uid:"uid"}),onLoad:function(t){switch(this.pinkId=t.id,0==this.isLogin?(0,n.toLogin)():(this.timestamp=(new Date).getTime(),this.getCombinationPink()),this.$set(this,"theme",this.$Cache.get("theme")),this.theme){case"theme1":this.posterbackgd="../../../static/images/bargain_post1.png";break;case"theme2":this.posterbackgd="../../../static/images/bargain_post2.png";break;case"theme3":this.posterbackgd="../../../static/images/bargain_post3.png";break;case"theme4":this.posterbackgd="../../../static/images/bargain_post4.png";break;case"theme5":this.posterbackgd="../../../static/images/bargain_post5.png";break}},onShow:function(){},mounted:function(){this.combinationMore()},onShareAppMessage:function(){return{title:"您的好友"+this.userInfo.nickname+"邀请您参团"+this.storeCombination.title,path:u.globalData.openPages,imageUrl:this.storeCombination.image}},methods:{listenerActionClose:function(){this.posters=!1,this.canvasStatus=!1},combinationMore:function(){var t=this;if(!t.loadend&&!t.loading){var i={page:t.page,limit:t.limit,comId:t.pinkId};this.loading=!0,(0,c.getCombinationMore)(i).then((function(i){var e=t.storeCombinationHost,o=t.limit;t.page++,t.loadend=o>i.data.length,t.storeCombinationHost=e.concat(i.data.list),t.page=t.data.page,t.loading=!1})).catch((function(i){t.loading=!1,t.$util.Tips({title:i})}))}},authColse:function(t){this.isShowAuth=t},onLoadFun:function(t){this.userInfo=t,u.globalData.openPages="/pages/activity/goods_combination_status/index?id="+this.pinkId,this.getCombinationPink()},iptCartNum:function(t){t>this.onceNum?(this.$util.Tips({title:"该商品每次限购".concat(this.onceNum).concat(this.storeCombination.unitName)}),this.$set(this.attr.productSelect,"cart_num",this.onceNum),this.$set(this,"cart_num",this.onceNum)):(this.$set(this.attr.productSelect,"cart_num",t),this.$set(this,"cart_num",t))},attrVal:function(t){this.attr.productAttr[t.indexw].index=this.attr.productAttr[t.indexw].attrValues[t.indexn]},onMyEvent:function(){this.$set(this.attr,"cartAttr",!1),this.$set(this,"isOpen",!1)},ChangeAttr:function(t){this.$set(this,"cart_num",1);var i=this.productValue[t];i?(this.$set(this.attr.productSelect,"image",i.image),this.$set(this.attr.productSelect,"price",i.price),this.$set(this.attr.productSelect,"quota",i.quota),this.$set(this.attr.productSelect,"unique",i.id),this.$set(this.attr.productSelect,"cart_num",1),this.$set(this.attr.productSelect,"stock",i.stock),this.$set(this.attr.productSelect,"quotaShow",i.quotaShow),this.attrValue=t,this.attrTxt="已选择"):(this.$set(this.attr.productSelect,"image",this.storeCombination.image),this.$set(this.attr.productSelect,"price",this.storeCombination.price),this.$set(this.attr.productSelect,"quota",0),this.$set(this.attr.productSelect,"unique",""),this.$set(this.attr.productSelect,"cart_num",0),this.$set(this.attr.productSelect,"quotaShow",0),this.$set(this.attr.productSelect,"stock",0),this.attrValue="",this.attrTxt="请选择")},ChangeCartNum:function(t){var i=this.productValue[this.attrValue];if(this.cart_num&&(i.cart_num=this.cart_num,this.attr.productSelect.cart_num=this.cart_num),void 0!==i||this.attr.productAttr.length||(i=this.attr.productSelect),void 0!==i){var e=i.stock||0,o=(i.quotaShow,i.quota||0),a=this.attr.productSelect,s=this.storeCombination.num||0;if(void 0==i.cart_num&&(i.cart_num=1),t){a.cart_num++;var n=[];n.push(s),n.push(o),n.push(e);var r=Math.min.apply(null,n);a.cart_num>=r&&(this.$set(this.attr.productSelect,"cart_num",r||1),this.$set(this,"cart_num",r||1)),this.$set(this,"cart_num",a.cart_num),this.$set(this.attr.productSelect,"cart_num",a.cart_num)}else a.cart_num--,a.cart_num<1&&(this.$set(this.attr.productSelect,"cart_num",1),this.$set(this,"cart_num",1)),this.$set(this,"cart_num",a.cart_num),this.$set(this.attr.productSelect,"cart_num",a.cart_num)}},DefaultSelect:function(){var t=this.attr.productAttr,i=[];for(var e in this.productValue)if(this.productValue[e].quota>0){i=this.attr.productAttr.length?e.split(","):[];break}for(var o=0;o<t.length;o++)this.$set(t[o],"index",i[o]);var a=this.productValue[i.join(",")];a&&t.length?(this.$set(this.attr.productSelect,"storeName",this.storeCombination.title),this.$set(this.attr.productSelect,"image",a.image),this.$set(this.attr.productSelect,"price",a.price),this.$set(this.attr.productSelect,"quota",a.quota),this.$set(this.attr.productSelect,"unique",a.id),this.$set(this.attr.productSelect,"cart_num",1),this.$set(this.attr.productSelect,"stock",a.stock),this.$set(this.attr.productSelect,"quotaShow",a.quotaShow),this.attrValue=i.join(","),this.attrTxt="已选择"):!a&&t.length?(this.$set(this.attr.productSelect,"storeName",this.storeCombination.title),this.$set(this.attr.productSelect,"image",this.storeCombination.image),this.$set(this.attr.productSelect,"price",this.storeCombination.price),this.$set(this.attr.productSelect,"quota",0),this.$set(this.attr.productSelect,"unique",""),this.$set(this.attr.productSelect,"cart_num",0),this.$set(this.attr.productSelect,"stock",0),this.$set(this.attr.productSelect,"quotaShow",0),this.attrValue="",this.attrTxt="请选择"):a||t.length||(this.$set(this.attr.productSelect,"storeName",this.storeCombination.title),this.$set(this.attr.productSelect,"image",this.storeCombination.image),this.$set(this.attr.productSelect,"price",this.storeCombination.price),this.$set(this.attr.productSelect,"quota",0),this.$set(this.attr.productSelect,"unique",this.storeCombination.id||""),this.$set(this.attr.productSelect,"cart_num",1),this.$set(this.attr.productSelect,"quotaShow",0),this.$set(this.attr.productSelect,"stock",0),this.attrValue="",this.attrTxt="请选择")},setProductSelect:function(){var t=this.attr;t.productSelect.image=this.storeCombination.image,t.productSelect.storeName=this.storeCombination.title,t.productSelect.price=this.storeCombination.price,t.productSelect.quota=0,t.productSelect.quotaShow=0,t.productSelect.stock=0,t.cartAttr=!1,this.$set(this,"attr",t)},pay:function(){this.attr.cartAttr=!0,this.isOpen=!0},goPay:function(){this.$Order.getPreOrder("buyNow",[{attrValueId:parseFloat(this.attr.productSelect.unique),combinationId:parseFloat(this.storeCombination.id),productNum:parseFloat(this.attr.productSelect.cart_num),productId:parseFloat(this.storeCombination.productId),pinkId:parseFloat(this.pinkId)}])},goPoster:function(){},goOrder:function(){t.navigateTo({url:"/pages/order_details/index?order_id="+this.currentPinkOrder})},goList:function(){t.navigateTo({url:"/pages/activity/goods_combination/index"})},goDetail:function(i){this.pinkId=i,t.navigateTo({url:"/pages/activity/goods_combination_details/index?id="+i})},getImageBase64:function(t){var i=this;(0,s.imageBase64)({url:t}).then((function(t){i.imgTop=t.data.code}))},make:function(){var t=this,i=location.protocol+"//"+location.host+"/pages/activity/goods_combination_status/index?id="+this.pinkId+"&spread="+this.uid;a.default.make({canvasId:"qrcode",text:i,size:this.qrcodeSize,margin:10,success:function(i){t.PromotionCode=i},complete:function(){},fail:function(i){t.$util.Tips({title:"海报二维码生成失败！"})}})},getCombinationPink:function(){var t=this;(0,c.getCombinationPink)(t.pinkId).then((function(i){var e=i.data.storeCombination;i.data.pinkT.stop_time=parseInt(i.data.pinkT.stopTime),t.$set(t,"storeCombination",e),t.$set(t.attr.productSelect,"num",e.totalNum),t.$set(t,"pinkT",i.data.pinkT),t.$set(t,"pinkAll",i.data.pinkAll),t.$set(t,"count",i.data.count),t.$set(t,"userBool",i.data.userBool),t.$set(t,"pinkBool",i.data.pinkBool),t.$set(t,"isOk",i.data.isOk),t.$set(t,"currentPinkOrder",i.data.currentPinkOrder),t.$set(t,"userInfo",i.data.userInfo),t.onceNum=e.onceNum,t.attr.productAttr=e.productAttr,t.productValue=e.productValue,t.setProductSelect(),0!=t.attr.productAttr&&t.DefaultSelect()})).catch((function(i){t.isLogin&&t.$util.Tips({title:i},{url:"/pages/index/index"})}))},getCombinationRemove:function(){var t=this;(0,c.postCombinationRemove)({id:t.pinkId,cid:t.storeCombination.id}).then((function(i){t.$util.Tips({title:i.msg},{tab:3})})).catch((function(i){t.$util.Tips({title:i})}))},lookAll:function(){this.iShidden=!this.iShidden}}};i.default=h}).call(this,e("df3c")["default"])},dd47:function(t,i,e){"use strict";e.r(i);var o=e("beb8"),a=e.n(o);for(var s in o)["default"].indexOf(s)<0&&function(t){e.d(i,t,(function(){return o[t]}))}(s);i["default"]=a.a},e27d:function(t,i,e){"use strict";var o=e("8cc8"),a=e.n(o);a.a},f61e:function(t,i,e){"use strict";(function(t,i){var o=e("47a9");e("9e89");o(e("3240"));var a=o(e("b501"));t.__webpack_require_UNI_MP_PLUGIN__=e,i(a.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])}},[["f61e","common/runtime","common/vendor"]]]);