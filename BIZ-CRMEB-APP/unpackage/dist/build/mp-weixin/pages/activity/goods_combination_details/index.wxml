<view data-theme="{{theme}}" class="data-v-55f003df"><skeleton vue-id="9e9198fe-1" show="{{showSkeleton}}" isNodes="{{isNodes}}" loading="chiaroscuro" selector="skeleton" bgcolor="#FFF" data-ref="skeleton" class="data-v-55f003df vue-ref" bind:__l="__l"></skeleton><view class="skeleton data-v-55f003df" style="{{'visibility:'+(showSkeleton?'hidden':'visible')+';'}}"><view class="{{['navbar','data-v-55f003df',opacity>0.6?'bgwhite':'']}}"><view class="navbarH data-v-55f003df" style="{{('height:'+navH+'rpx;')}}"><view class="navbarCon acea-row data-v-55f003df" style="{{'padding-right:'+(navbarRight+'px')+';'}}"><view class="select_nav flex justify-center align-center data-v-55f003df" style="{{'top:'+(homeTop+'rpx')+';'}}" id="home"><text data-event-opts="{{[['tap',[['returns',['$event']]]]]}}" class="iconfont icon-fanhui2 px-20 data-v-55f003df" bindtap="__e"></text><text data-event-opts="{{[['tap',[['showNav',['$event']]]]]}}" class="iconfont icon-gengduo5 px-20 data-v-55f003df" bindtap="__e"></text><text class="nav_line data-v-55f003df"></text></view><view class="header flex justify-between align-center data-v-55f003df"><block wx:for="{{navList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['tap',['$0',index],[[['navList','',index]]]]]]]}}" class="{{['item','data-v-55f003df',navActive===index?'on':'']}}" bindtap="__e">{{''+item+''}}</view></block></view></view></view></view><view hidden="{{!(currentPage)}}" class="dialog_nav data-v-55f003df" style="{{('top:'+navH+'rpx;')}}"><block wx:for="{{selectNavList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['linkPage',['$0'],[[['selectNavList','',index,'url']]]]]]]}}" class="{{['dialog_nav_item','data-v-55f003df',item.after]}}" bindtap="__e"><text class="{{['iconfont','data-v-55f003df',item.icon]}}"></text><text class="pl-20 data-v-55f003df">{{item.name}}</text></view></block></view><view class="product-con data-v-55f003df"><scroll-view style="{{('height:'+height+'px;')}}" scroll-top="{{scrollTop}}" scroll-y="true" scroll-with-animation="true" data-event-opts="{{[['scroll',[['scroll',['$event']]]]]}}" bindscroll="__e" class="data-v-55f003df"><view id="past0" class="data-v-55f003df"><product-con-swiper class="mb30 skeleton-rect data-v-55f003df" vue-id="9e9198fe-2" imgUrls="{{imgUrls}}" bind:__l="__l"></product-con-swiper><view class="pad30 data-v-55f003df"><view class="wrapper mb30 data-v-55f003df"><view class="share acea-row row-between row-bottom data-v-55f003df"><view class="money font_color data-v-55f003df">￥<text class="num skeleton-rect data-v-55f003df">{{attribute.productSelect.price||0}}</text><text class="y-money skeleton-rect data-v-55f003df">{{"￥"+(storeInfo.otPrice||0)}}</text></view><view data-event-opts="{{[['tap',[['listenerActionSheet',['$event']]]]]}}" class="iconfont icon-fenxiang data-v-55f003df" bindtap="__e"></view></view><view class="introduce line2 skeleton-rect data-v-55f003df">{{storeInfo.storeName}}</view><view class="label acea-row row-between-wrapper data-v-55f003df"><view class="stock skeleton-rect data-v-55f003df">{{"类型："+(storeInfo.people||0)+"人团"}}</view><view class="skeleton-rect data-v-55f003df">{{"累计销量："+($root.m0+$root.m1)+" "+(storeInfo.unitName||'')}}</view><view class="skeleton-rect data-v-55f003df">{{"限购: "+(attribute.productSelect.quotaShow?attribute.productSelect.quotaShow:0)+"\n\t\t\t\t\t\t\t\t\t"+(storeInfo.unitName||'')+''}}</view></view></view><view data-event-opts="{{[['tap',[['selecAttr',['$event']]]]]}}" class="attribute mb30 borRadius14 data-v-55f003df" bindtap="__e"><view class="acea-row row-between-wrapper data-v-55f003df"><view class="line1 skeleton-rect data-v-55f003df">{{attrTxt+'：'}}<text class="atterTxt data-v-55f003df">{{attrValue}}</text></view><view class="iconfont icon-jiantou data-v-55f003df"></view></view><block wx:if="{{$root.g0>1}}"><view class="acea-row row-between-wrapper data-v-55f003df" style="margin-top:7px;padding-left:55px;"><view class="flex data-v-55f003df"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><image class="attrImg data-v-55f003df" src="{{item.image}}"></image></block></view><view class="switchTxt data-v-55f003df">{{"共"+$root.g1+"种规格可选"}}</view></view></block></view><block wx:if="{{$root.m2>0}}"><view class="notice acea-row row-middle mb30 borRadius14 data-v-55f003df"><view class="num font-color skeleton-rect data-v-55f003df"><text class="iconfont icon-laba data-v-55f003df"></text>{{'已拼'+pinkOkSum+"件"}}<text class="line data-v-55f003df">|</text></view><view class="swiper data-v-55f003df"><swiper indicator-dots="{{indicatorDots}}" autoplay="{{autoplay}}" interval="2500" duration="500" vertical="true" circular="true" class="data-v-55f003df"><block wx:for="{{itemNew}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block class="data-v-55f003df"><swiper-item class="data-v-55f003df"><view class="line1 data-v-55f003df">{{item.nickname+"拼团成功"}}</view></swiper-item></block></block></swiper></view></view></block><block wx:if="{{attribute.productSelect.quota>0}}"><view class="assemble mb30 borRadius14 data-v-55f003df"><block wx:for="{{pink}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{index<AllIndex}}"><view class="item acea-row row-between-wrapper data-v-55f003df"><view class="pictxt acea-row row-between-wrapper data-v-55f003df"><view class="pictrue data-v-55f003df"><image src="{{item.avatar}}" class="data-v-55f003df"></image></view><view class="text line1 data-v-55f003df">{{item.nickname}}</view></view><view class="right acea-row row-middle data-v-55f003df"><view class="data-v-55f003df"><view class="lack data-v-55f003df">还差<text class="font-color data-v-55f003df">{{item.count}}</text>人成团</view><view class="time acea-row data-v-55f003df"><count-down vue-id="{{'9e9198fe-3-'+index}}" is-day="{{false}}" tip-text="剩余 " day-text=" " hour-text=":" minute-text=":" second-text=" " datatime="{{item.stopTime/1000}}" bgColor="{{bgColor}}" class="data-v-55f003df" bind:__l="__l"></count-down></view></view><navigator class="spellBnt data-v-55f003df" hover-class="none" url="{{'/pages/activity/goods_combination_status/index?id='+item.id}}">去拼单<text class="iconfont icon-jiantou data-v-55f003df"></text></navigator></view></view></block></block><block wx:if="{{$root.g2}}"><block wx:if="{{$root.g3>AllIndex}}"><view data-event-opts="{{[['tap',[['showAll',['$event']]]]]}}" class="more data-v-55f003df" bindtap="__e">查看更多<text class="iconfont icon-xiangxia data-v-55f003df"></text></view></block><block wx:else><block wx:if="{{$root.g4}}"><view data-event-opts="{{[['tap',[['hideAll',['$event']]]]]}}" class="more data-v-55f003df" bindtap="__e">收起<text class="iconfont icon-xiangshang data-v-55f003df"></text></view></block></block></block></view></block><view class="playWay mb30 borRadius14 data-v-55f003df"><view class="title acea-row row-between row-middle data-v-55f003df"><view class="data-v-55f003df">拼团玩法</view></view><view class="way acea-row row-middle data-v-55f003df"><view class="item acea-row row-middle data-v-55f003df"><text class="num mb-02 data-v-55f003df">①</text>开团/参团</view><view class="iconfont icon-arrow data-v-55f003df"></view><view class="item data-v-55f003df"><text class="num data-v-55f003df">②</text>邀请好友</view><view class="iconfont icon-arrow data-v-55f003df"></view><view class="item data-v-55f003df"><view class="data-v-55f003df"><text class="num data-v-55f003df">③</text>满员发货</view></view></view></view><view class="userEvaluation borRadius14 data-v-55f003df" id="past1"><view class="title acea-row row-between-wrapper data-v-55f003df" style="{{(replyCount==0?'border-bottom-left-radius:14rpx;border-bottom-right-radius:14rpx;':'')}}"><view class="data-v-55f003df">用户评价<view class="_i data-v-55f003df">{{"("+replyCount+")"}}</view></view><navigator class="praise data-v-55f003df" hover-class="none" url="{{'/pages/users/goods_comment_list/index?productId='+storeInfo.productId}}"><view class="_i data-v-55f003df">好评</view><text class="font_color pl-14 data-v-55f003df">{{(replyChance||0)+"%"}}</text><text class="iconfont icon-jiantou data-v-55f003df"></text></navigator></view><block wx:if="{{$root.g5>0}}"><user-evaluation vue-id="9e9198fe-4" reply="{{reply}}" class="data-v-55f003df" bind:__l="__l"></user-evaluation></block></view></view></view><view class="product-intro data-v-55f003df" id="past2"><view class="title data-v-55f003df"><image src="../../../static/images/xzuo.png" class="data-v-55f003df"></image><label class="sp _span data-v-55f003df">产品详情</label><image src="../../../static/images/xyou.png" class="data-v-55f003df"></image></view><view class="conter data-v-55f003df"><jyf-parser vue-id="9e9198fe-5" html="{{storeInfo.content}}" tag-style="{{tagStyle}}" data-ref="article" class="data-v-55f003df vue-ref" bind:__l="__l"></jyf-parser></view></view><view style="height:120rpx;" class="data-v-55f003df"></view></scroll-view><view class="footer acea-row row-between-wrapper data-v-55f003df"><block wx:if="{{chatConfig.telephone_service_switch==='true'}}"><button class="item skeleton-rect data-v-55f003df" hover-class="none" data-event-opts="{{[['tap',[['kefuClick',['$event']]]]]}}" bindtap="__e"><view class="iconfont icon-kefu data-v-55f003df"></view><view class="data-v-55f003df">客服</view></button></block><block wx:else><button class="item skeleton-rect data-v-55f003df" open-type="contact" hover-class="none"><view class="iconfont icon-kefu data-v-55f003df"></view><view class="data-v-55f003df">客服</view></button></block><view data-event-opts="{{[['tap',[['setCollect',['$event']]]]]}}" class="item skeleton-rect data-v-55f003df" bindtap="__e"><block wx:if="{{userCollect}}"><view class="iconfont icon-shoucang1 data-v-55f003df"></view></block><block wx:else><view class="iconfont icon-shoucang data-v-55f003df"></view></block><view class="data-v-55f003df">收藏</view></view><view class="bnt acea-row skeleton-rect data-v-55f003df"><block wx:if="{{masterStatus!=='soldOut'}}"><view data-event-opts="{{[['tap',[['goProduct',['$event']]]]]}}" class="joinCart bnts data-v-55f003df" bindtap="__e">单独购买</view></block><block wx:if="{{masterStatus=='soldOut'}}"><view class="bg-color-hui bnts radius data-v-55f003df">单独购买</view></block><block wx:if="{{attribute.productSelect.quota>0}}"><view data-event-opts="{{[['tap',[['goCat',['$event']]]]]}}" class="buy bnts data-v-55f003df" bindtap="__e">立即开团</view></block><block wx:if="{{!dataShow}}"><view class="buy bnts bg-color-hui data-v-55f003df">立即开团</view></block><block wx:if="{{attribute.productSelect.quota<=0}}"><view class="buy bnts bg-color-hui data-v-55f003df">已售罄</view></block></view></view></view><share-red-packets vue-id="9e9198fe-6" sharePacket="{{sharePacket}}" data-event-opts="{{[['^listenerActionSheet',[['listenerActionSheet']]],['^closeChange',[['closeChange']]]]}}" bind:listenerActionSheet="__e" bind:closeChange="__e" class="data-v-55f003df" bind:__l="__l"></share-red-packets><view class="{{['generate-posters','data-v-55f003df',posters?'on':'']}}"><view class="generateCon acea-row row-middle data-v-55f003df"><button class="item data-v-55f003df" open-type="share" hover-class="none"><view class="pictrue data-v-55f003df"><image src="/static/images/weixin.png" class="data-v-55f003df"></image></view><view class="data-v-55f003df">分享给好友</view></button><view data-event-opts="{{[['tap',[['getpreviewImage',['$event']]]]]}}" class="item data-v-55f003df" bindtap="__e"><view class="pictrue data-v-55f003df"><image src="/static/images/changan.png" class="data-v-55f003df"></image></view><view class="data-v-55f003df">预览发图</view></view><button class="item data-v-55f003df" hover-class="none" data-event-opts="{{[['tap',[['savePosterPath',['$event']]]]]}}" bindtap="__e"><view class="pictrue data-v-55f003df"><image src="/static/images/haibao.png" class="data-v-55f003df"></image></view><view class="data-v-55f003df">保存海报</view></button></view><view data-event-opts="{{[['tap',[['posterImageClose',['$event']]]]]}}" class="generateClose acea-row row-center-wrapper data-v-55f003df" bindtap="__e">取消</view></view><cus-preview-img vue-id="9e9198fe-7" list="{{skuArr}}" data-ref="cusPreviewImg" data-event-opts="{{[['^changeSwitch',[['changeSwitch']]]]}}" bind:changeSwitch="__e" class="data-v-55f003df vue-ref" bind:__l="__l"></cus-preview-img><block wx:if="{{posters}}"><view data-event-opts="{{[['tap',[['closePosters',['$event']]]]]}}" class="mask data-v-55f003df" bindtap="__e"></view></block><block wx:if="{{canvasStatus}}"><view data-event-opts="{{[['tap',[['listenerActionClose',['$event']]]]]}}" class="mask data-v-55f003df" bindtap="__e"></view></block><block wx:if="{{currentPage}}"><view data-event-opts="{{[['touchmove',[['hideNav',['$event']]]],['tap',[['hideNav']]]]}}" class="mask_transparent data-v-55f003df" bindtouchmove="__e" bindtap="__e"></view></block><block wx:if="{{canvasStatus}}"><view class="poster-pop data-v-55f003df"><image src="{{posterImage}}" class="data-v-55f003df"></image></view></block><block wx:else><view class="canvas data-v-55f003df"><canvas style="width:750px;height:1190px;" canvas-id="firstCanvas" class="data-v-55f003df"></canvas><canvas style="{{'width:'+(qrcodeSize+'px')+';'+('height:'+(qrcodeSize+'px')+';')}}" canvas-id="qrcode" class="data-v-55f003df"></canvas></view></block><block wx:if="{{H5ShareBox}}"><view class="share-box data-v-55f003df"><image src="/static/images/share-info.png" data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" bindtap="__e" class="data-v-55f003df"></image></view></block><product-window vue-id="9e9198fe-8" attr="{{attribute}}" limitNum="{{1}}" data-event-opts="{{[['^myevent',[['onMyEvent']]],['^ChangeAttr',[['ChangeAttr']]],['^ChangeCartNum',[['ChangeCartNum']]],['^iptCartNum',[['iptCartNum']]],['^attrVal',[['attrVal']]],['^getImg',[['showImg']]]]}}" bind:myevent="__e" bind:ChangeAttr="__e" bind:ChangeCartNum="__e" bind:iptCartNum="__e" bind:attrVal="__e" bind:getImg="__e" class="data-v-55f003df" bind:__l="__l"></product-window></view></view>