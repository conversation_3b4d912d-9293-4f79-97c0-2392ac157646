(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/activity/bargain/index"],{"231a":function(t,n,i){"use strict";var a=i("aaa0"),e=i.n(a);e.a},"4af0":function(t,n,i){"use strict";var a=i("6304"),e=i.n(a);e.a},6304:function(t,n,i){},a455:function(t,n,i){"use strict";i.r(n);var a=i("a5db"),e=i("b494");for(var o in e)["default"].indexOf(o)<0&&function(t){i.d(n,t,(function(){return e[t]}))}(o);i("4af0"),i("231a");var s=i("828b"),r=Object(s["a"])(e["default"],a["b"],a["c"],!1,null,"5fcb228c",null,!1,a["a"],void 0);n["default"]=r.exports},a5db:function(t,n,i){"use strict";i.d(n,"b",(function(){return a})),i.d(n,"c",(function(){return e})),i.d(n,"a",(function(){}));var a=function(){var t=this.$createElement,n=(this._self._c,this.bargain.length),i=this.bargain.length;this.$mp.data=Object.assign({},{$root:{g0:n,g1:i}})},e=[]},aaa0:function(t,n,i){},b494:function(t,n,i){"use strict";i.r(n);var a=i("e69b"),e=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(n,t,(function(){return a[t]}))}(o);n["default"]=e.a},b63e:function(t,n,i){"use strict";(function(t,n){var a=i("47a9");i("9e89");a(i("3240"));var e=a(i("a455"));t.__webpack_require_UNI_MP_PLUGIN__=i,n(e.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},e69b:function(t,n,i){"use strict";(function(t){var a=i("47a9");Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e=i("8cd5"),o=i("8f59"),s=i("fbb9"),r=a(i("4bef")),u=getApp(),c={name:"BargainRecord",components:{CountDown:function(){i.e("components/countDown/index").then(function(){return resolve(i("4376"))}.bind(null,i)).catch(i.oe)},Loading:function(){i.e("components/Loading/index").then(function(){return resolve(i("00d1"))}.bind(null,i)).catch(i.oe)},emptyPage:function(){i.e("components/emptyPage").then(function(){return resolve(i("7180"))}.bind(null,i)).catch(i.oe)},home:function(){i.e("components/home/<USER>").then(function(){return resolve(i("1e4d"))}.bind(null,i)).catch(i.oe)},payment:function(){Promise.all([i.e("common/vendor"),i.e("components/payment/index")]).then(function(){return resolve(i("65d9"))}.bind(null,i)).catch(i.oe)}},props:{},computed:(0,o.mapGetters)(["isLogin","userInfo","uid"]),data:function(){return{bgColor:{bgColor:"",Color:"#E93323",width:"40rpx",timeTxtwidth:"28rpx",isDay:!1},bargain:[],status:!1,loadingList:!1,page:1,limit:20,payMode:[{name:"微信支付",icon:"icon-weixinzhifu",value:"weixin",title:"微信快捷支付"},{name:"余额支付",icon:"icon-yuezhifu",value:"yue",title:"可用余额:",number:0}],pay_close:!1,pay_order_id:"",totalPrice:"0",theme:u.globalData.theme}},onShow:function(){this.isLogin?(this.payMode[1].number=this.userInfo.nowMoney,this.$set(this,"payMode",this.payMode),this.getBargainUserList(),this.bgColor.Color=(0,s.setThemeColor)()):toLogin()},methods:{goPay:function(t,n){this.$set(this,"pay_close",!0),this.$set(this,"pay_order_id",n),this.$set(this,"totalPrice",t)},onChangeFun:function(t){var n=t,i=n.action||null,a=void 0!=n.value?n.value:null;i&&this[i]&&this[i](a)},payClose:function(){this.pay_close=!1},pay_complete:function(){this.status=!1,this.page=1,this.$set(this,"bargain",[]),this.$set(this,"pay_close",!1),this.getBargainUserList()},pay_fail:function(){this.pay_close=!1},goConfirm:function(n){!1===this.isLogin?toLogin():t.navigateTo({animationType:r.default.type,animationDuration:r.default.duration,url:"/pages/activity/goods_bargain_details/index?id=".concat(n.id,"&startBargainUid=").concat(this.uid,"&storeBargainId=").concat(n.bargainUserId)})},goDetail:function(n){t.navigateTo({animationType:r.default.type,animationDuration:r.default.duration,url:"/pages/activity/goods_bargain_details/index?id=".concat(n,"&startBargainUid=").concat(this.uid)})},goList:function(){t.navigateTo({animationType:r.default.type,animationDuration:r.default.duration,url:"/pages/activity/goods_bargain/index"})},getBargainUserList:function(){var t=this;t.loadingList||t.status||(0,e.getBargainUserList)({page:t.page,limit:t.limit}).then((function(n){t.status=n.data.list.length<t.limit,t.bargain.push.apply(t.bargain,n.data.list),t.page++,t.loadingList=!1})).catch((function(n){t.$dialog.error(n)}))},getBargainUserCancel:function(t){var n=this;(0,e.getBargainUserCancel)({bargainId:t}).then((function(t){n.status=!1,n.loadingList=!1,n.page=1,n.bargain=[],n.getBargainUserList(),n.$util.Tips({title:t})})).catch((function(t){n.$util.Tips({title:t})}))}},onReachBottom:function(){this.getBargainUserList()}};n.default=c}).call(this,i("df3c")["default"])}},[["b63e","common/runtime","common/vendor"]]]);