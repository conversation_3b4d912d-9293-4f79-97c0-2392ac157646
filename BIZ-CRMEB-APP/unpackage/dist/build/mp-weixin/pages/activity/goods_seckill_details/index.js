(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/activity/goods_seckill_details/index"],{"0043":function(t,e,i){"use strict";(function(t,s){var o=i("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=o(i("7064")),a=(i("0a3e"),i("8f59")),n=i("8cd5"),u=i("d9cf"),c=i("2df1"),h=i("ad96"),l=i("84f5"),d=i("2e55"),p=i("342a"),f=(i("20ce"),getApp()),m={data:function(){return{showSkeleton:!0,isNodes:0,bgColor:{bgColor:"#333333",Color:"#fff",isDay:!0,width:"44rpx",timeTxtwidth:"16rpx"},dataShow:0,id:0,time:0,countDownHour:"00",countDownMinute:"00",countDownSecond:"00",storeInfo:{},imgUrls:[],parameter:{navbar:"1",return:"1",title:"抢购详情页",color:!1},attribute:{cartAttr:!1,productAttr:[],productSelect:{}},productValue:[],isOpen:!1,attr:"请选择",attrValue:"",status:1,isAuto:!1,isShowAuth:!1,iShidden:!1,limitNum:1,personNum:0,iSplus:!1,replyCount:0,reply:[],replyChance:0,navH:"",navList:["商品","评价","详情"],opacity:0,scrollY:0,topArr:[],toView:"",height:0,heightArr:[],lock:!1,scrollTop:0,tagStyle:{img:"width:100%;display:block;",table:"width:100%",video:"width:100%"},datatime:0,navActive:0,meunHeight:0,backH:"",posters:!1,weixinStatus:!1,posterImageStatus:!1,canvasStatus:!1,storeImage:"",PromotionCode:"",posterImage:"",posterbackgd:"/static/images/posterbackgd.png",actionSheetHidden:!1,cart_num:"",attrTxt:"",qrcodeSize:600,imagePath:"",imgTop:"",H5ShareBox:!1,sharePacket:{isState:!0},buyNum:1,errT:"",returnShow:!0,homeTop:20,navbarRight:0,userCollect:!1,theme:f.globalData.theme,skuArr:[],currentPage:!1,selectSku:{},selectNavList:[{name:"首页",icon:"icon-shouye8",url:"/pages/index/index",after:"dialog_after"},{name:"搜索",icon:"icon-sousuo6",url:"/pages/goods_search/index",after:"dialog_after"},{name:"购物车",icon:"icon-gouwuche7",url:"/pages/order_addcart/order_addcart",after:"dialog_after"},{name:"我的收藏",icon:"icon-shoucang3",url:"/pages/users/user_goods_collection/index",after:"dialog_after"},{name:"个人中心",icon:"icon-gerenzhongxin1",url:"/pages/user/index"}],chatConfig:{consumer_hotline:"",telephone_service_switch:"false"},masterStatus:""}},components:{shareRedPackets:function(){i.e("components/shareRedPackets/index").then(function(){return resolve(i("784d"))}.bind(null,i)).catch(i.oe)},productConSwiper:function(){Promise.all([i.e("common/vendor"),i.e("components/productConSwiper/index")]).then(function(){return resolve(i("867f"))}.bind(null,i)).catch(i.oe)},productWindow:function(){i.e("components/productWindow/index").then(function(){return resolve(i("a82f"))}.bind(null,i)).catch(i.oe)},userEvaluation:function(){i.e("components/userEvaluation/index").then(function(){return resolve(i("ca9c"))}.bind(null,i)).catch(i.oe)},cusPreviewImg:function(){i.e("components/cus-previewImg/cus-previewImg").then(function(){return resolve(i("34c9"))}.bind(null,i)).catch(i.oe)},"jyf-parser":function(){Promise.all([i.e("common/vendor"),i.e("components/jyf-parser/jyf-parser")]).then(function(){return resolve(i("5d6d"))}.bind(null,i)).catch(i.oe)},home:function(){i.e("components/home/<USER>").then(function(){return resolve(i("1e4d"))}.bind(null,i)).catch(i.oe)},countDown:function(){i.e("components/countDown/index").then(function(){return resolve(i("4376"))}.bind(null,i)).catch(i.oe)},authorize:function(){Promise.all([i.e("common/vendor"),i.e("components/Authorize")]).then(function(){return resolve(i("8de5"))}.bind(null,i)).catch(i.oe)}},computed:(0,a.mapGetters)(["isLogin","uid","chatUrl"]),watch:{isLogin:{handler:function(t,e){t&&this.getSeckillDetail()},deep:!0}},onLoad:function(e){var i=this;setTimeout((function(){i.isNodes++}),500);var s=this;s.$store.commit("PRODUCT_TYPE","normal");getCurrentPages();s.$set(s,"chatConfig",s.$Cache.getItem("chatConfig")),t.getSystemInfo({success:function(t){s.height=t.windowHeight,t.statusBarHeight}}),this.navH=f.globalData.navHeight;var o=t.getMenuButtonBoundingClientRect();if(this.meunHeight=o.height,this.backH=s.navH/2+this.meunHeight/2,s.$set(s,"theme",s.$Cache.get("theme")),!e.scene&&!e.id)return this.showSkeleton=!1,void this.$util.Tips({title:"缺少参数无法查看商品"},{url:"/pages/index/index"});if(e.hasOwnProperty("id")||e.scene)if(e.scene){var r=this.$util.getUrlParams(decodeURIComponent(e.scene)),a=this.$util.formatMpQrCodeData(r);f.globalData.spread=a.spread,this.id=a.id}else this.id=e.id;e.spread&&(f.globalData.spread=e.spread),this.isLogin?this.getSeckillDetail():(this.$Cache.set("login_back_url",f.globalData.spread),(0,d.toLogin)()),this.$nextTick((function(){var e=t.getMenuButtonBoundingClientRect(),s=t.createSelectorQuery().in(i);s.select("#home").boundingClientRect((function(t){i.homeTop=2*e.top+e.height-t.height})).exec()})),this.isLogin&&f.globalData.spread&&(0,p.silenceBindingSpread)()},methods:{kefuClick:function(){"true"===this.chatConfig.telephone_service_switch?t.makePhoneCall({phoneNumber:this.chatConfig.consumer_hotline}):location.href=this.chatUrl},closePosters:function(){this.posters=!1},getProductReplyList:function(){var t=this;(0,u.getReplyList)(this.storeInfo.productId,{page:1,limit:3,type:0}).then((function(e){t.reply=e.data.list?[e.data.list[0]]:[]}))},getProductReplyCount:function(){var t=this;(0,u.getReplyConfig)(t.storeInfo.productId).then((function(e){t.$set(t,"replyChance",100*e.data.replyChance),t.$set(t,"replyCount",e.data.sumCount)}))},iptCartNum:function(t){if(this.$set(this.attribute.productSelect,"cart_num",t||1),this.$set(this,"cart_num",t),t>1)return this.$util.Tips({title:"该商品每次限购1".concat(this.storeInfo.unitName)})},returns:function(){t.navigateBack()},onLoadFun:function(t){this.isAuto&&(this.isAuto=!1,this.isShowAuth=!1,this.getSeckillDetail())},getSeckillDetail:function(){var t=this,e=this;(0,n.getSeckillDetail)(e.id).then((function(i){for(var s in t.dataShow=1,t.masterStatus=i.data.masterStatus,t.storeInfo=i.data.storeSeckill,t.userCollect=i.data.userCollect,t.status=t.storeInfo.seckillStatus,t.datatime=Number(t.storeInfo.timeSwap),t.imgUrls=JSON.parse(i.data.storeSeckill.sliderImage)||[],t.attribute.productAttr=i.data.productAttr,t.productValue=i.data.productValue,t.personNum=i.data.storeSeckill.quota,t.attribute.productSelect.num=i.data.storeSeckill.num,i.data.productValue){var o=i.data.productValue[s];e.skuArr.push(o)}t.$set(t,"selectSku",e.skuArr[0]),t.getProductReplyList(),t.getProductReplyCount();var r=i.data.productAttr.map((function(t){return{attrName:t.attrName,attrValues:t.attrValues.split(","),id:t.id,isDel:t.isDel,productId:t.productId,type:t.type}}));t.$set(t.attribute,"productAttr",r),e.getQrcode(),e.imgTop=i.data.storeSeckill.image,e.downloadFilestoreImage(),e.DefaultSelect(),setTimeout((function(){e.infoScroll()}),1e3),f.globalData.openPages="/pages/activity/goods_seckill_details/index?id="+e.id+"&spread="+e.uid,setTimeout((function(){e.showSkeleton=!1}),1e3)})).catch((function(t){e.$util.Tips({title:t},{tab:3})}))},setShare:function(){this.$wechat.isWeixin()&&this.$wechat.wechatEvevt(["updateAppMessageShareData","updateTimelineShareData","onMenuShareAppMessage","onMenuShareTimeline"],{desc:this.storeInfo.storeInfo,title:this.storeInfo.storeName,link:location.href,imgUrl:this.storeInfo.image}).then((function(t){})).catch((function(t){console.log(t)}))},DefaultSelect:function(){var t=this.attribute.productAttr,e=[];for(var i in this.productValue)if(this.productValue[i].quota>0){e=t.length?i.split(","):[];break}for(var s=0;s<e.length;s++)this.$set(t[s],"index",e[s]);var o=this.productValue[e.join(",")];o&&t.length?(this.$set(this.attribute.productSelect,"storeName",this.storeInfo.storeName),this.$set(this.attribute.productSelect,"image",o.image),this.$set(this.attribute.productSelect,"price",o.price),this.$set(this.attribute.productSelect,"stock",o.stock),this.$set(this.attribute.productSelect,"unique",o.id),this.$set(this.attribute.productSelect,"quota",o.quota),this.$set(this.attribute.productSelect,"quotaShow",o.quotaShow),this.$set(this.attribute.productSelect,"cart_num",1),this.$set(this,"attrValue",e.join(",")),this.$set(this,"attrTxt","已选择"),this.attrValue=e.join(",")):!o&&t.length?(this.$set(this.attribute.productSelect,"storeName",this.storeInfo.storeName),this.$set(this.attribute.productSelect,"image",this.storeInfo.image),this.$set(this.attribute.productSelect,"price",this.storeInfo.price),this.$set(this.attribute.productSelect,"quota",0),this.$set(this.attribute.productSelect,"quota",0),this.$set(this.attribute.productSelect,"stock",0),this.$set(this.attribute.productSelect,"unique",""),this.$set(this.attribute.productSelect,"cart_num",0),this.$set(this,"attrValue",""),this.$set(this,"attrTxt","请选择")):o||t.length||(this.$set(this.attribute.productSelect,"storeName",this.storeInfo.storeName),this.$set(this.attribute.productSelect,"image",this.storeInfo.image),this.$set(this.attribute.productSelect,"price",this.storeInfo.price),this.$set(this.attribute.productSelect,"quota",this.storeInfo.quota),this.$set(this.attribute.productSelect,"quotaShow",this.storeInfo.quotaShow),this.$set(this.attribute.productSelect,"stock",this.storeInfo.stock),this.$set(this.attribute.productSelect,"unique",""),this.$set(this.attribute.productSelect,"cart_num",1),this.$set(this,"attrValue",""),this.$set(this,"attrTxt","请选择"))},selecAttr:function(){this.attribute.cartAttr=!0},showNav:function(){this.currentPage=!this.currentPage},onMyEvent:function(){this.$set(this.attribute,"cartAttr",!1),this.$set(this,"isOpen",!1)},ChangeCartNum:function(t){var e=this.productValue[this.attrValue];if(this.cart_num&&(e.cart_num=this.cart_num,this.attribute.productSelect.cart_num=this.cart_num),void 0!==e||this.attribute.productAttr.length||(e=this.attribute.productSelect),void 0!==e){var i=e.stock||0,s=e.quota||0,o=this.attribute.productSelect,r=this.storeInfo.num||0;if(void 0==e.cart_num&&(e.cart_num=1),t){if(1===o.cart_num)return this.$util.Tips({title:"该商品每次限购1".concat(this.storeInfo.unitName)});o.cart_num++;var a=[];a.push(r),a.push(s),a.push(i);var n=Math.min.apply(null,a);o.cart_num>=n&&(this.$set(this.attribute.productSelect,"cart_num",n||1),this.$set(this,"cart_num",n||1)),this.$set(this,"cart_num",o.cart_num),this.$set(this.attribute.productSelect,"cart_num",o.cart_num)}else o.cart_num--,o.cart_num<1&&(this.$set(this.attribute.productSelect,"cart_num",1),this.$set(this,"cart_num",1)),this.$set(this,"cart_num",o.cart_num),this.$set(this.attribute.productSelect,"cart_num",o.cart_num)}},attrVal:function(t){this.attribute.productAttr[t.indexw].index=this.attribute.productAttr[t.indexw].attrValues[t.indexn]},ChangeAttr:function(t){this.$set(this,"cart_num",1);var e=this.productValue[t];this.$set(this,"selectSku",e),e?(this.$set(this.attribute.productSelect,"image",e.image),this.$set(this.attribute.productSelect,"price",e.price),this.$set(this.attribute.productSelect,"stock",e.stock),this.$set(this.attribute.productSelect,"unique",e.id),this.$set(this.attribute.productSelect,"cart_num",1),this.$set(this.attribute.productSelect,"quota",e.quota),this.$set(this.attribute.productSelect,"quotaShow",e.quotaShow),this.$set(this,"attrValue",t),this.attrTxt="已选择"):(this.$set(this.attribute.productSelect,"image",this.storeInfo.image),this.$set(this.attribute.productSelect,"price",this.storeInfo.price),this.$set(this.attribute.productSelect,"stock",0),this.$set(this.attribute.productSelect,"unique",""),this.$set(this.attribute.productSelect,"cart_num",0),this.$set(this.attribute.productSelect,"quota",0),this.$set(this.attribute.productSelect,"quotaShow",0),this.$set(this,"attrValue",""),this.attrTxt="已选择")},scroll:function(t){var e=t.detail.scrollTop,i=e/500;if(i=i>1?1:i,this.opacity=i,this.scrollY=e,this.lock)this.lock=!1;else{for(var s=0;s<this.topArr.length;s++)if(e<this.topArr[s]-f.globalData.navHeight/2+this.heightArr[s]){this.navActive=s;break}1==this.currentPage&&this.$set(this,"currentPage",!1)}},tap:function(t,e){var i=t.id;e=e;this.toView=i,this.navActive=e,this.lock=!0,this.scrollTop=e>0?this.topArr[e]-f.globalData.navHeight/2:this.topArr[e]},infoScroll:function(){for(var t=this,e=[],i=[],o=0;o<t.navList.length;o++){var r=s.createSelectorQuery().in(this),a="#past"+o;r.select(a).boundingClientRect(),r.exec((function(s){var o=s[0].top,r=s[0].height;e.push(o),i.push(r),t.topArr=e,t.heightArr=i}))}},setCollect:function(){var t=this;this.userCollect?(0,u.collectDel)(this.storeInfo.productId).then((function(e){t.userCollect=!t.userCollect})):(0,u.collectAdd)(this.storeInfo.productId).then((function(e){t.userCollect=!t.userCollect}))},openAlone:function(){t.navigateTo({url:"/pages/goods_details/index?id=".concat(this.storeInfo.productId)})},goCat:function(){var t=this.productValue[this.attrValue];t=this.productValue[this.attrValue];return this.cart_num>1?this.$util.Tips({title:"该商品每人限购1".concat(this.storeInfo.unitName)}):(this.isOpen?this.attribute.cartAttr=!0:this.attribute.cartAttr=!this.attribute.cartAttr,!0===this.attribute.cartAttr&&0==this.isOpen?this.isOpen=!0:this.attribute.productAttr.length&&void 0===t&&1==this.isOpen?f.$util.Tips({title:"请选择属性"}):void this.$Order.getPreOrder("buyNow",[{attrValueId:parseFloat(this.attribute.productSelect.unique),seckillId:parseFloat(this.id),productNum:parseFloat(this.cart_num?this.cart_num:this.attribute.productSelect.cart_num),productId:parseFloat(this.storeInfo.productId)}]))},listenerActionSheet:function(){!1===this.isLogin?(0,d.toLogin)():(this.goPoster(),this.posters=!0)},listenerActionClose:function(){this.posters=!1},posterImageClose:function(){this.canvasStatus=!1,this.posters=!1},setDomain:function(t){return t=t?t.toString():"",t.indexOf("https://")>-1?t:t.replace("http://","https://")},downloadFilestoreImage:function(){var e=this;t.downloadFile({url:e.setDomain(e.storeInfo.image),success:function(t){e.storeImage=t.tempFilePath},fail:function(){return e.$util.Tips({title:""})}})},downloadFilePromotionCode:function(e){var i=this;(0,n.seckillCode)(i.id,{stop_time:i.datatime}).then((function(s){t.downloadFile({url:i.setDomain(s.data.code),success:function(t){i.$set(i,"isDown",!1),"function"==typeof e?e&&e(t.tempFilePath):i.$set(i,"PromotionCode",t.tempFilePath)},fail:function(){i.$set(i,"isDown",!1),i.$set(i,"PromotionCode","")}})})).catch((function(t){i.$set(i,"isDown",!1),i.$set(i,"PromotionCode","")}))},getImageBase64:function(t){var e=this;(0,l.imageBase64)({url:t}).then((function(t){e.$set(e,"imgTop",t.data.code)}))},goFriend:function(){this.posters=!1},goPoster:function(){var e=this;t.showLoading({title:"海报生成中",mask:!0}),e.posters=!1;var i="";if(!e.PromotionCode)return t.hideLoading(),void e.$util.Tips({title:e.errT});setTimeout((function(){if(!e.imgTop)return t.hideLoading(),void e.$util.Tips({title:"无法生成商品海报！"})}),2e3),t.downloadFile({url:e.imgTop,success:function(s){i=s.tempFilePath;var o=[e.posterbackgd,i,e.PromotionCode],r=e.storeInfo.storeName,a=e.storeInfo.price;setTimeout((function(){e.$util.PosterCanvas(o,r,a,e.storeInfo.otPrice,(function(i){e.posterImage=i,e.canvasStatus=!0,t.hideLoading()}))}),500)}})},getQrcode:function(){var t=this,e={pid:t.uid,id:t.id,path:"pages/activity/goods_seckill_details/index"};(0,h.getQrcode)(e).then((function(e){(0,c.base64src)(e.data.code,Date.now(),(function(e){t.PromotionCode=e}))})).catch((function(e){t.errT=e}))},make:function(){var t=this,e=location.href.split("?")[0]+"?id="+this.id+"&spread="+this.uid;r.default.make({canvasId:"qrcode",text:e,size:this.qrcodeSize,margin:10,success:function(e){t.PromotionCode=e},complete:function(t){},fail:function(e){t.$util.Tips({title:"海报二维码生成失败！"})}})},getpreviewImage:function(){if(this.posterImage){var e=[];e.push(this.posterImage),t.previewImage({urls:e,current:this.posterImage})}else this.$util.Tips({title:"您的海报尚未生成"})},savePosterPath:function(){var e=this;t.getSetting({success:function(i){i.authSetting["scope.writePhotosAlbum"]?t.saveImageToPhotosAlbum({filePath:e.posterImage,success:function(t){e.posterImageClose(),e.$util.Tips({title:"保存成功",icon:"success"})},fail:function(t){e.$util.Tips({title:"保存失败"})}}):t.authorize({scope:"scope.writePhotosAlbum",success:function(){t.saveImageToPhotosAlbum({filePath:e.posterImage,success:function(t){e.posterImageClose(),e.$util.Tips({title:"保存成功",icon:"success"})},fail:function(t){e.$util.Tips({title:"保存失败"})}})}})}})},setShareInfoStatus:function(){var t=this.storeInfo,e=location.href;if(this.$wechat.isWeixin()){e=-1===e.indexOf("?")?e+"?spread="+this.uid:e+"&spread="+this.uid;var i={desc:t.storeInfo,title:t.storeName,link:e,imgUrl:t.image};this.$wechat.wechatEvevt(["updateAppMessageShareData","updateTimelineShareData"],i)}},hideNav:function(){this.currentPage=!1},linkPage:function(e){"/pages/index/index"==e||"/pages/order_addcart/order_addcart"==e||"/pages/user/index"==e?t.switchTab({url:e}):t.navigateTo({url:e}),this.currentPage=!1},showImg:function(t){this.$refs.cusPreviewImg.open(this.selectSku.suk)},changeSwitch:function(t){var e=this,i=this.skuArr[t];this.$set(this,"selectSku",i);var s=i.suk.split(",");s.forEach((function(t,i){e.$set(e.attribute.productAttr[i],"index",s[i])})),i&&(this.$set(this.attribute.productSelect,"image",i.image),this.$set(this.attribute.productSelect,"price",i.price),this.$set(this.attribute.productSelect,"stock",i.stock),this.$set(this.attribute.productSelect,"unique",i.id),this.$set(this.attribute.productSelect,"quota",i.quota),this.$set(this.attribute.productSelect,"quotaShow",i.quotaShow),this.$set(this.attribute.productSelect,"cart_num",1),this.$set(this,"attrTxt","已选择"),this.$set(this,"attrValue",i.suk))}},onShareAppMessage:function(){return{title:this.storeInfo.title,path:f.globalData.openPages,imageUrl:this.storeInfo.image}}};e.default=m}).call(this,i("df3c")["default"],i("3223")["default"])},"0435":function(t,e,i){"use strict";var s=i("a743"),o=i.n(s);o.a},"2cb2":function(t,e,i){"use strict";i.r(e);var s=i("ba17"),o=i("c470");for(var r in o)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(r);i("0435");var a=i("828b"),n=Object(a["a"])(o["default"],s["b"],s["c"],!1,null,"624358a5",null,!1,s["a"],void 0);e["default"]=n.exports},a743:function(t,e,i){},ba17:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return s}));var s={jyfParser:function(){return Promise.all([i.e("common/vendor"),i.e("components/jyf-parser/jyf-parser")]).then(i.bind(null,"5d6d"))},cusPreviewImg:function(){return i.e("components/cus-previewImg/cus-previewImg").then(i.bind(null,"34c9"))}},o=function(){var t=this,e=t.$createElement,i=(t._self._c,parseFloat(t.storeInfo.sales)+parseFloat(t.storeInfo.ficti)||0),s=t.skuArr.length,o=s>1?t.skuArr.slice(0,4):null,r=s>1?t.skuArr.length:null,a=2==t.status&&t.attribute.productSelect.quota>0&&t.datatime>(new Date).getTime()/1e3,n=2==t.status&&t.attribute.productSelect.quota<=0&&t.datatime>(new Date).getTime()/1e3,u=2==t.status&&(new Date).getTime()/1e3-t.datatime>=0;t._isMounted||(t.e0=function(e){t.H5ShareBox=!1}),t.$mp.data=Object.assign({},{$root:{m0:i,g0:s,l0:o,g1:r,g2:a,g3:n,g4:u}})},r=[]},c470:function(t,e,i){"use strict";i.r(e);var s=i("0043"),o=i.n(s);for(var r in s)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return s[t]}))}(r);e["default"]=o.a},c9c8:function(t,e,i){"use strict";(function(t,e){var s=i("47a9");i("9e89");s(i("3240"));var o=s(i("2cb2"));t.__webpack_require_UNI_MP_PLUGIN__=i,e(o.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])}},[["c9c8","common/runtime","common/vendor"]]]);