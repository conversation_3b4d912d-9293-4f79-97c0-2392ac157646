<view data-theme="{{theme}}" class="data-v-2962edfa"><skeleton vue-id="349d6104-1" show="{{showSkeleton}}" isNodes="{{isNodes}}" loading="chiaroscuro" selector="skeleton" bgcolor="#FFF" data-ref="skeleton" class="data-v-2962edfa vue-ref" bind:__l="__l"></skeleton><view class="skeleton data-v-2962edfa" style="{{'visibility:'+(showSkeleton?'hidden':'visible')+';'}}"><view class="combinationBj data-v-2962edfa"></view><view class="flash-sale data-v-2962edfa"><view class="saleBox data-v-2962edfa"></view><block wx:if="{{$root.g0}}"><view class="header skeleton-rect data-v-2962edfa"><swiper indicator-dots="true" autoplay="true" circular="{{circular}}" interval="3000" duration="1500" indicator-color="rgba(255,255,255,0.6)" indicator-active-color="#fff" class="data-v-2962edfa"><block wx:for="{{dataList[active].slide}}" wx:for-item="items" wx:for-index="index" wx:key="index"><block class="data-v-2962edfa"><swiper-item class="borRadius14 data-v-2962edfa"><image class="slide-image borRadius14 data-v-2962edfa" src="{{items.sattDir}}" lazy-load="{{true}}"></image></swiper-item></block></block></swiper></view></block><view class="seckillList acea-row row-between-wrapper data-v-2962edfa"><view class="priceTag skeleton-rect data-v-2962edfa"><image src="/static/images/priceTag.png" class="data-v-2962edfa"></image></view><view class="timeLsit data-v-2962edfa"><scroll-view class="scroll-view_x data-v-2962edfa" style="width:auto;overflow:hidden;" scroll-x="{{true}}" scroll-with-animation="{{true}}" scroll-left="{{scrollLeft}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block class="data-v-2962edfa"><view data-event-opts="{{[['tap',[['settimeList',['$0',index],[[['dataList','',index]]]]]]]}}" class="{{['item','data-v-2962edfa',active==index?'on':'']}}" bindtap="__e"><view class="time data-v-2962edfa">{{item.g1[0]}}</view><view class="state data-v-2962edfa">{{item.$orig.statusName}}</view></view></block></block></scroll-view></view></view><block wx:if="{{$root.g2>0}}"><view class="list pad30 data-v-2962edfa"><block wx:for="{{seckillList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block class="data-v-2962edfa"><view data-event-opts="{{[['tap',[['goDetails',['$0'],[[['seckillList','',index]]]]]]]}}" class="item acea-row row-between-wrapper data-v-2962edfa" bindtap="__e"><view class="pictrue skeleton-rect data-v-2962edfa"><image src="{{item.image}}" class="data-v-2962edfa"></image></view><view class="text acea-row row-column-around data-v-2962edfa"><view class="name line1 skeleton-rect data-v-2962edfa">{{item.title}}</view><view class="money skeleton-rect data-v-2962edfa"><text class="font_color data-v-2962edfa">￥</text><text class="num font_color data-v-2962edfa">{{item.price}}</text><text class="y_money data-v-2962edfa">{{"￥"+item.otPrice}}</text></view><view class="limit skeleton-rect data-v-2962edfa">限量<text class="limitPrice data-v-2962edfa">{{item.quota+" "+item.unitName}}</text></view><view class="progress skeleton-rect data-v-2962edfa"><view class="bg-reds data-v-2962edfa" style="{{('width:'+item.percent+'%;')}}"></view><view class="piece data-v-2962edfa">{{"已抢"+item.percent+"%"}}</view></view></view><block wx:if="{{status==2}}"><view class="grab bg_color data-v-2962edfa">马上抢</view></block><block wx:else><block wx:if="{{status==1}}"><view class="grab bg_color data-v-2962edfa">未开始</view></block><block wx:else><view class="grab bg-color-hui data-v-2962edfa">已结束</view></block></block></view></block></block></view></block></view><block wx:if="{{$root.g3}}"><view class="noCommodity data-v-2962edfa"><view class="pictrue data-v-2962edfa"><image src="../../../static/images/noShopper.png" class="data-v-2962edfa"></image></view></view></block></view></view>