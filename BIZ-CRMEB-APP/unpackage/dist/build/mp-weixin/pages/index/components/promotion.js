(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/index/components/promotion"],{"0a06":function(t,e,n){"use strict";(function(t){var a=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=a(n("7eb4")),i=a(n("ee10")),r=n("d9cf"),u=a(n("4bef")),c=(getApp(),{name:"promotion",props:{tabData:{type:Array,default:[]},showType:{type:Number,default:1}},components:{promotionGood:function(){n.e("components/promotionGood/index").then(function(){return resolve(n("8f83"))}.bind(null,n)).catch(n.oe)}},created:function(){var t=this;setTimeout((function(){t.reloadData()}),1e3)},data:function(){return{tempArr:[],params:{page:1,limit:6}}},methods:{productslist:function(t){var e=this;return new Promise((function(n,a){(0,r.getGroomList)(t.type,e.params).then((function(t){var e=t.data;n(e.list)}))}))},reloadData:function(){var t=this;return(0,i.default)(o.default.mark((function e(){var n,a;return o.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:n=0;case 1:if(!(n<t.tabData.length)){e.next=9;break}return e.next=4,t.productslist(t.tabData[n]);case 4:a=e.sent,t.tempArr.push(a);case 6:n++,e.next=1;break;case 9:case"end":return e.stop()}}),e)})))()},gopage:function(e){t.navigateTo({animationType:u.default.type,animationDuration:u.default.duration,url:"/pages/activity/promotionList/index?type="+JSON.stringify(e)})},goDetail:function(e){t.navigateTo({animationType:u.default.type,animationDuration:u.default.duration,url:"/pages/goods_details/index?id=".concat(e)})}}});e.default=c}).call(this,n("df3c")["default"])},"61ab":function(t,e,n){"use strict";n.r(e);var a=n("0a06"),o=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);e["default"]=o.a},"8d30":function(t,e,n){},"9a64":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){}));var a=function(){var t=this.$createElement;this._self._c},o=[]},b5fe:function(t,e,n){"use strict";var a=n("8d30"),o=n.n(a);o.a},ed30:function(t,e,n){"use strict";n.r(e);var a=n("9a64"),o=n("61ab");for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);n("b5fe");var r=n("828b"),u=Object(r["a"])(o["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=u.exports},ff1f:function(t,e,n){"use strict";(function(t,e){var a=n("47a9");n("9e89");a(n("3240"));var o=a(n("ed30"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(o.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])}},[["ff1f","common/runtime","common/vendor"]]]);