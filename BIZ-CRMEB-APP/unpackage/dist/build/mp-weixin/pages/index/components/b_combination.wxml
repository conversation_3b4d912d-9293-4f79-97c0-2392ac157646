<block wx:if="{{$root.g0}}"><view class="{{['data-v-46656336',(isBorader)?'borderShow':'']}}"><view class="combination data-v-46656336"><view class="title acea-row row-between data-v-46656336"><view class="spike-bd data-v-46656336"><block wx:if="{{$root.g1>0}}"><view class="activity_pic data-v-46656336"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="picture data-v-46656336" style="{{(index===2?'position: relative':'position: static')}}"><label class="avatar _span data-v-46656336" style="{{('background-image: url('+item.$orig+')')}}"></label><block wx:if="{{item.m0}}"><label class="mengceng _span data-v-46656336"><view class="_i data-v-46656336">···</view></label></block></view></block><text class="pic_count data-v-46656336">{{assistUserCount+"人参与"}}</text></view></block></view><view data-event-opts="{{[['tap',[['toCombinationList']]]]}}" class="more acea-row row-center-wrapper data-v-46656336" bindtap="__e">GO<text class="iconfont icon-xiangyou data-v-46656336"></text></view></view><view class="conter acea-row data-v-46656336"><scroll-view style="white-space:nowrap;vertical-align:middle;" scroll-x="true" show-scrollbar="false" class="data-v-46656336"><block wx:for="{{combinationList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goDetail',['$0'],[[['combinationList','',index]]]]]]]}}" class="itemCon data-v-46656336" bindtap="__e"><view class="item data-v-46656336"><view class="pictrue data-v-46656336"><image src="{{item.image}}" class="data-v-46656336"></image></view><view class="text lines1 data-v-46656336"><view class="name line1 data-v-46656336">{{item.title}}</view><view class="x-money data-v-46656336">¥<text class="num data-v-46656336">{{item.price}}</text></view><view class="y_money data-v-46656336">{{"¥"+item.otPrice}}</view></view></view></view></block></scroll-view></view></view></view></block>