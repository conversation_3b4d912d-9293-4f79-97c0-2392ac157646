<view><view hidden="{{!(showType==2)}}" class="index-wrapper"><block wx:for="{{tabData}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="wrapper" index="{{true}}"><view class="title1 acea-row row-between-wrapper"><view class="text"><view class="name line1">{{item.name}}</view><view class="line1 txt-btn">{{item.info}}</view></view><view data-event-opts="{{[['tap',[['gopage',['$0'],[[['tabData','',index]]]]]]]}}" class="more" bindtap="__e">更多<text class="iconfont icon-jiantou"></text></view></view><view class="newProducts"><scroll-view class="scroll-view_x" style="width:auto;overflow:hidden;" scroll-x="{{true}}"><block wx:for="{{tempArr[index]}}" wx:for-item="item1" wx:for-index="index1" wx:key="index1"><block><view data-event-opts="{{[['tap',[['goDetail',['$0'],[[['tempArr.'+index+'','',index1,'id']]]]]]]}}" class="item" bindtap="__e"><view class="img-box"><image src="{{item1.image}}"></image></view><view class="pro-info line1">{{item1.storeName}}</view><view class="money font-color">{{"￥"+item1.price}}</view></view></block></block></scroll-view></view></view></block></view><view hidden="{{!(showType==3)}}" class="index-wrapper"><block wx:for="{{tabData}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="wrapper" index="{{true}}"><view class="title1 acea-row row-between-wrapper"><view class="text"><view class="name line1">{{item.name}}</view><view class="line1 txt-btn">{{item.info}}</view></view><view data-event-opts="{{[['tap',[['gopage',['$0'],[[['tabData','',index]]]]]]]}}" class="more" bindtap="__e">更多<text class="iconfont icon-jiantou"></text></view></view><promotion-good vue-id="{{'21124974-1-'+index}}" benefit="{{tempArr[index]}}" bind:__l="__l"></promotion-good></view></block></view></view>