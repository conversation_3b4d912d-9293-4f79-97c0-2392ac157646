<block wx:if="{{$root.g0}}"><view class="data-v-2831dac5"><view class="seckill data-v-2831dac5"><view class="title acea-row row-between-wrapper data-v-2831dac5"><view class="acea-row row-middle data-v-2831dac5"><view class="pictrue skeleton-rect data-v-2831dac5"><image src="/static/images/seckillTitle.png" class="data-v-2831dac5"></image></view><view class="lines data-v-2831dac5"></view><view class="point skeleton-rect data-v-2831dac5">{{point+" 场"}}</view><count-down vue-id="2b69f758-1" is-day="{{false}}" tip-text=" " day-text=" " hour-text=" : " minute-text=" : " second-text=" " datatime="{{datatime}}" is-col="{{true}}" bgColor="{{bgColor}}" class="data-v-2831dac5" bind:__l="__l"></count-down></view><view data-event-opts="{{[['tap',[['toSeckillList']]]]}}" class="more acea-row row-center-wrapper skeleton-rect data-v-2831dac5" bindtap="__e">GO<text class="iconfont icon-xiangyou data-v-2831dac5"></text></view></view><view class="conter data-v-2831dac5"><scroll-view style="white-space:nowrap;vertical-align:middle;" scroll-x="true" show-scrollbar="false" class="data-v-2831dac5"><block wx:for="{{spikeList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goDetail',['$0'],[[['spikeList','',index]]]]]]]}}" class="itemCon data-v-2831dac5" bindtap="__e"><view class="item data-v-2831dac5"><view class="pictrue skeleton-rect data-v-2831dac5"><image src="{{item.image}}" class="data-v-2831dac5"></image></view><view class="name line2 skeleton-rect data-v-2831dac5">{{item.title}}</view><view class="x_money line1 skeleton-rect data-v-2831dac5">¥<text class="num data-v-2831dac5">{{item.price}}</text></view><view class="y_money line1 data-v-2831dac5">{{"¥"+item.otPrice}}</view></view></view></block></scroll-view></view></view></view></block>