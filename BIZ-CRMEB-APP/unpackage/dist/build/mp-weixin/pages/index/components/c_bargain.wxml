<block wx:if="{{$root.g0}}"><view class="{{['data-v-03772950',(isBorader)?'borderShow':'']}}"><view class="combination data-v-03772950"><view class="title acea-row row-between data-v-03772950"><view class="acea-row row-column data-v-03772950"></view><view data-event-opts="{{[['tap',[['toBragainList']]]]}}" class="more acea-row row-center-wrapper data-v-03772950" bindtap="__e">GO<text class="iconfont icon-xiangyou data-v-03772950"></text></view></view><view class="conter acea-row data-v-03772950"><scroll-view style="white-space:nowrap;vertical-align:middle;" scroll-x="true" show-scrollbar="false" class="data-v-03772950"><block wx:for="{{bargList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['bargDetail',['$0'],[[['bargList','',index]]]]]]]}}" class="itemCon data-v-03772950" bindtap="__e"><view class="item data-v-03772950"><view class="pictrue data-v-03772950"><image src="{{item.image}}" class="data-v-03772950"></image></view><view class="text lines1 data-v-03772950"><view class="name line1 data-v-03772950">{{item.title}}</view><view class="x-money data-v-03772950">¥<text class="num data-v-03772950">{{item.minPrice}}</text></view><view class="btn data-v-03772950">参与砍价</view></view></view></view></block></scroll-view></view></view></view></block>