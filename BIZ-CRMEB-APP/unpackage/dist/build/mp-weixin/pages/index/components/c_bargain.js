(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/index/components/c_bargain"],{"29b1":function(t,a,i){"use strict";i.r(a);var n=i("5459"),e=i("53e8");for(var r in e)["default"].indexOf(r)<0&&function(t){i.d(a,t,(function(){return e[t]}))}(r);i("db7b9");var o=i("828b"),u=Object(o["a"])(e["default"],n["b"],n["c"],!1,null,"03772950",null,!1,n["a"],void 0);a["default"]=u.exports},"377d":function(t,a,i){},"53e8":function(t,a,i){"use strict";i.r(a);var n=i("7ef5"),e=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(a,t,(function(){return n[t]}))}(r);a["default"]=e.a},5459:function(t,a,i){"use strict";i.d(a,"b",(function(){return n})),i.d(a,"c",(function(){return e})),i.d(a,"a",(function(){}));var n=function(){var t=this.$createElement,a=(this._self._c,this.bargList.length);this.$mp.data=Object.assign({},{$root:{g0:a}})},e=[]},"7ef5":function(t,a,i){"use strict";(function(t){var n=i("47a9");Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;i("2e55");var e=i("8cd5"),r=i("8f59"),o=n(i("4bef")),u=(getApp(),{name:"c_bargain",computed:(0,r.mapGetters)({userData:"userInfo",uid:"uid"}),data:function(){return{bargList:[{image:"",title:"",price:"",otPrice:""},{image:"",title:"",price:"",otPrice:""},{image:"",title:"",price:"",otPrice:""}],isBorader:!1}},created:function(){this.getBargainList()},mounted:function(){},methods:{getBargainList:function(){var t=this;(0,e.getBargainIndexApi)().then((function(a){t.bargList=a.data?a.data.productList:[]}))},bargDetail:function(a){t.navigateTo({animationType:o.default.type,animationDuration:o.default.duration,url:"/pages/activity/goods_bargain_details/index?id=".concat(a.id,"&startBargainUid=").concat(this.uid)})},toBragainList:function(){t.navigateTo({animationType:o.default.type,animationDuration:o.default.duration,url:"/pages/activity/goods_bargain/index"})}}});a.default=u}).call(this,i("df3c")["default"])},db7b9:function(t,a,i){"use strict";var n=i("377d"),e=i.n(n);e.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'pages/index/components/c_bargain-create-component',
    {
        'pages/index/components/c_bargain-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("29b1"))
        })
    },
    [['pages/index/components/c_bargain-create-component']]
]);
