(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/index/components/b_combination"],{"386b":function(t,n,i){"use strict";i.d(n,"b",(function(){return a})),i.d(n,"c",(function(){return e})),i.d(n,"a",(function(){}));var a=function(){var t=this,n=t.$createElement,i=(t._self._c,t.combinationList.length),a=i?t.assistUserList.length:null,e=i&&a>0?t.__map(t.assistUserList,(function(n,i){var a=t.__get_orig(n),e=2===i&&Number(t.assistUserCount)>3;return{$orig:a,m0:e}})):null;t.$mp.data=Object.assign({},{$root:{g0:i,g1:a,l0:e}})},e=[]},"45e8":function(t,n,i){"use strict";(function(t,n){var a=i("47a9");i("9e89");a(i("3240"));var e=a(i("56b9"));t.__webpack_require_UNI_MP_PLUGIN__=i,n(e.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},"56b9":function(t,n,i){"use strict";i.r(n);var a=i("386b"),e=i("9dfc");for(var o in e)["default"].indexOf(o)<0&&function(t){i.d(n,t,(function(){return e[t]}))}(o);i("6a19");var s=i("828b"),u=Object(s["a"])(e["default"],a["b"],a["c"],!1,null,"46656336",null,!1,a["a"],void 0);n["default"]=u.exports},"6a19":function(t,n,i){"use strict";var a=i("ed8f"),e=i.n(a);e.a},"7d0a":function(t,n,i){"use strict";(function(t){var a=i("47a9");Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e=i("8cd5"),o=a(i("4bef")),s=(getApp(),{name:"b_combination",data:function(){return{combinationList:[],isBorader:!1,assistUserList:[],assistUserCount:0}},created:function(){this.getCombinationList()},mounted:function(){},methods:{getCombinationList:function(){var t=this;(0,e.getCombinationIndexApi)().then((function(n){t.combinationList=n.data.productList,t.assistUserList=n.data.avatarList,t.assistUserCount=n.data.totalPeople})).catch((function(n){return t.$util.Tips({title:n})}))},goDetail:function(n){t.navigateTo({animationType:o.default.type,animationDuration:o.default.duration,url:"/pages/activity/goods_combination_details/index?id=".concat(n.id)})},toCombinationList:function(){t.navigateTo({animationType:o.default.type,animationDuration:o.default.duration,url:"/pages/activity/goods_combination/index"})}}});n.default=s}).call(this,i("df3c")["default"])},"9dfc":function(t,n,i){"use strict";i.r(n);var a=i("7d0a"),e=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(n,t,(function(){return a[t]}))}(o);n["default"]=e.a},ed8f:function(t,n,i){}},[["45e8","common/runtime","common/vendor"]]]);