<view data-theme="{{theme}}" class="data-v-ad2762e0"><skeleton vue-id="8dd740cc-1" show="{{showSkeleton}}" isNodes="{{isNodes}}" loading="chiaroscuro" selector="skeleton" bgcolor="#FFF" data-ref="skeleton" class="data-v-ad2762e0 vue-ref" bind:__l="__l"></skeleton><view class="{{['page-index','skeleton','data-v-ad2762e0',(navIndex>0)?'bgf':'']}}" style="{{'visibility:'+(showSkeleton?'hidden':'visible')+';'}}"><view class="mp-header data-v-ad2762e0"><view class="sys-head skeleton-rect data-v-ad2762e0" style="{{'height:'+(statusBarHeight)+';'}}"></view><view class="serch-box skeleton-rect data-v-ad2762e0" style="height:40px;"><view class="serch-wrapper flex data-v-ad2762e0"><view class="logo data-v-ad2762e0"><image src="{{logoUrl}}" mode class="data-v-ad2762e0"></image></view><navigator class="input data-v-ad2762e0" url="/pages/goods_search/index" hover-class="none"><text class="iconfont icon-xiazai5 data-v-ad2762e0"></text>搜索商品</navigator></view></view></view><block wx:if="{{navIndex==0}}"><view class="page_content skeleton data-v-ad2762e0" style="{{('margin-top:'+marTop+'px;')}}"><view class="mp-bg data-v-ad2762e0"></view><view id="pageIndex" class="data-v-ad2762e0"><view data-event-opts="{{[['tap',[['bindEdit',['indexBanner']]]]]}}" class="swiper skeleton-rect data-v-ad2762e0" bindtap="__e"><swiper indicator-dots="true" autoplay="{{true}}" circular="{{circular}}" interval="{{interval}}" duration="{{duration}}" indicator-color="rgba(255,255,255,0.6)" indicator-active-color="#fff" class="data-v-ad2762e0"><block wx:for="{{imgUrls}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block class="data-v-ad2762e0"><swiper-item class="data-v-ad2762e0"><view data-event-opts="{{[['tap',[['menusTap',['$0'],[[['imgUrls','',index,'url']]]]]]]}}" bindtap="__e" class="data-v-ad2762e0"><image class="slide-image data-v-ad2762e0" src="{{item.pic}}" lazy-load="{{true}}"></image></view></swiper-item></block></block></swiper></view><view data-event-opts="{{[['tap',[['bindEdit',['indexMenu']]]]]}}" class="nav acea-row data-v-ad2762e0" bindtap="__e"><block wx:for="{{menus}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block class="data-v-ad2762e0"><view data-event-opts="{{[['tap',[['menusTap',['$0'],[[['menus','',index,'url']]]]]]]}}" class="item data-v-ad2762e0" bindtap="__e"><view class="pictrue data-v-ad2762e0"><image class="skeleton-radius data-v-ad2762e0" src="{{item.pic}}"></image></view><view class="menu-txt skeleton-rect data-v-ad2762e0">{{item.name}}</view></view></block></block></view></view><coupons class="skeleton skeleton-rect data-v-ad2762e0" vue-id="8dd740cc-2" bind:__l="__l"></coupons><seckill class="skeleton skeleton-rect data-v-ad2762e0" vue-id="8dd740cc-3" bind:__l="__l"></seckill><combination class="skeleton skeleton-rect data-v-ad2762e0" vue-id="8dd740cc-4" bind:__l="__l"></combination><bargain class="skeleton-rect data-v-ad2762e0" vue-id="8dd740cc-5" bind:__l="__l"></bargain><goods-rank class="skeleton skeleton-rect data-v-ad2762e0" vue-id="8dd740cc-6" bind:__l="__l"></goods-rank><block wx:if="{{cardShow!==1}}"><promotion vue-id="8dd740cc-7" tabData="{{explosiveMoney}}" showType="{{cardShow}}" class="data-v-ad2762e0" bind:__l="__l"></promotion></block><block wx:if="{{cardShow==1}}"><view class="{{['index-product-wrapper','data-v-ad2762e0',iSshowH?'on':'']}}"><view class="{{['list-box','animated','data-v-ad2762e0',$root.g0>0?'fadeIn on':'']}}"><block wx:for="{{tempArr}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goDetail',['$0'],[[['tempArr','',index]]]]]]]}}" class="item data-v-ad2762e0" bindtap="__e"><view class="pictrue data-v-ad2762e0"><block wx:if="{{item.activityH5&&item.activityH5.type==='1'}}"><label class="pictrue_log pictrue_log_class _span data-v-ad2762e0">秒杀</label></block><block wx:if="{{item.activityH5&&item.activityH5.type==='2'}}"><label class="pictrue_log pictrue_log_class _span data-v-ad2762e0">砍价</label></block><block wx:if="{{item.activityH5&&item.activityH5.type==='3'}}"><label class="pictrue_log pictrue_log_class _span data-v-ad2762e0">拼团</label></block><image src="{{item.image}}" mode class="data-v-ad2762e0"></image></view><view class="text-info data-v-ad2762e0"><view class="title line1 data-v-ad2762e0">{{item.storeName}}</view><view class="old-price data-v-ad2762e0"><text class="data-v-ad2762e0">{{"¥"+item.otPrice}}</text></view><block wx:if="{{item.vipPrice&&item.vipPrice>0}}"><view class="price data-v-ad2762e0"><text class="data-v-ad2762e0">￥</text>{{item.vipPrice}}</view></block><block wx:else><view class="price data-v-ad2762e0"><text class="data-v-ad2762e0">￥</text>{{item.price}}</view></block></view></view></block></view><block wx:if="{{goodScroll}}"><view class="loadingicon acea-row row-center-wrapper data-v-ad2762e0"><text class="loading iconfont icon-jiazai data-v-ad2762e0" hidden="{{loading==false}}"></text></view></block><block wx:if="{{!goodScroll}}"><view class="mores-txt flex data-v-ad2762e0"><text class="data-v-ad2762e0">我是有底线的</text></view></block></view></block></view></block><a-tip vue-id="8dd740cc-8" isCustom="{{true}}" text="{{wxText}}" borderR="{{5}}" class="data-v-ad2762e0" bind:__l="__l"></a-tip><block wx:if="{{locationStatus}}"><at-model vue-id="8dd740cc-9" locationType="{{true}}" content="{{locationContent}}" data-event-opts="{{[['^closeModel',[['modelCancel']]],['^confirmModel',[['confirmModel']]]]}}" bind:closeModel="__e" bind:confirmModel="__e" class="data-v-ad2762e0" bind:__l="__l"></at-model></block></view></view>