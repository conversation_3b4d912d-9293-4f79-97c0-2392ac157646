<view data-theme="{{theme}}" class="data-v-622598d7"><view class="order-details data-v-622598d7"><view class="{{['header','bg_color','data-v-622598d7',isGoodsReturn?'on':'']}}"><view class="picTxt acea-row row-middle data-v-622598d7"><block wx:if="{{isGoodsReturn==false}}"><view class="pictrue data-v-622598d7"><image src="{{orderInfo.statusPic}}" class="data-v-622598d7"></image></view></block><view class="{{['data','data-v-622598d7',isGoodsReturn?'on':'']}}"><view class="state data-v-622598d7">{{orderInfo.orderStatusMsg}}</view><block wx:if="{{orderInfo.refundReasonTime!==null}}"><view class="data-v-622598d7">{{orderInfo.refundReasonTime}}</view></block><block wx:else><view class="data-v-622598d7">{{orderInfo.payTime?orderInfo.payTime:''}}</view></block></view></view></view><block wx:if="{{isGoodsReturn==false}}"><view class="pad30 data-v-622598d7"><view class="nav data-v-622598d7"><view class="navCon acea-row row-between-wrapper data-v-622598d7"><view class="{{['data-v-622598d7',!orderInfo.paid?'on':'']}}">待付款</view><view class="{{['data-v-622598d7',orderInfo.paid&&orderInfo.status==0?'on':'']}}">{{''+(orderInfo.shippingType==1?'待发货':'待核销')}}</view><block wx:if="{{orderInfo.shippingType==1}}"><view class="{{['data-v-622598d7',orderInfo.status==1?'on':'']}}">待收货</view></block><view class="{{['data-v-622598d7',orderInfo.status==2?'on':'']}}">待评价</view><view class="{{['data-v-622598d7',orderInfo.status==3?'on':'']}}">已完成</view></view><view class="progress acea-row row-between-wrapper data-v-622598d7"><view class="{{['iconfont','data-v-622598d7',(!orderInfo.paid?'icon-webicon318':'icon-yuandianxiao')+' '+(orderInfo.paid?'font_color':'')]}}"></view><view class="{{['line','data-v-622598d7',orderInfo.paid>0?'bg_color':'']}}"></view><view class="{{['iconfont','data-v-622598d7',(orderInfo.status==0?'icon-webicon318':'icon-yuandianxiao')+' '+(orderInfo.status>=0?'font_color':'')]}}"></view><block wx:if="{{orderInfo.shippingType==1}}"><view class="{{['line','data-v-622598d7',orderInfo.status>0?'bg_color':'']}}"></view></block><block wx:if="{{orderInfo.shippingType==1}}"><view class="{{['iconfont','data-v-622598d7',(orderInfo.status==1?'icon-webicon318':'icon-yuandianxiao')+' '+(orderInfo.status>=1?'font_color':'')]}}"></view></block><view class="{{['line','data-v-622598d7',orderInfo.status>1?'bg_color':'']}}"></view><view class="{{['iconfont','data-v-622598d7',(orderInfo.status==2?'icon-webicon318':'icon-yuandianxiao')+' '+(orderInfo.status>=2?'font_color':'')]}}"></view><view class="{{['line','data-v-622598d7',orderInfo.status>2?'bg_color':'']}}"></view><view class="{{['iconfont','data-v-622598d7',(orderInfo.status==3?'icon-webicon318':'icon-yuandianxiao')+' '+(orderInfo.status>=3?'font_color':'')]}}"></view></view></view><block wx:if="{{orderInfo.shippingType==2&&orderInfo.paid}}"><view class="writeOff borRadius14 data-v-622598d7"><view class="title data-v-622598d7">核销信息</view><view class="grayBg data-v-622598d7"><view class="pictrue data-v-622598d7"><image src="{{codeImg}}" class="data-v-622598d7"></image></view></view><view class="gear data-v-622598d7"><image src="../../static/images/writeOff.jpg" class="data-v-622598d7"></image></view><view class="num data-v-622598d7">{{orderInfo.verifyCode}}</view><block wx:if="{{orderInfo.systemStore}}"><view class="rules data-v-622598d7"><view class="item data-v-622598d7"><view class="rulesTitle acea-row row-middle data-v-622598d7"><text class="iconfont icon-shijian data-v-622598d7"></text>核销时间</view><view class="info data-v-622598d7">每日：<text class="time data-v-622598d7">{{$root.g0}}</text></view></view><view class="item data-v-622598d7"><view class="rulesTitle acea-row row-middle data-v-622598d7"><text class="iconfont icon-shuoming1 data-v-622598d7"></text>使用说明</view><view class="info data-v-622598d7">可将二维码出示给店员扫描或提供数字核销码</view></view></view></block></view></block><block wx:if="{{orderInfo.refundReason}}"><view class="refund borRadius14 data-v-622598d7"><view class="title data-v-622598d7"><image src="/static/images/shuoming.png" mode class="data-v-622598d7"></image>商家拒绝退款</view><view class="con data-v-622598d7">{{"拒绝原因："+orderInfo.refundReason}}</view></view></block><block wx:if="{{orderInfo.shippingType==2}}"><view class="map acea-row row-between-wrapper borRadius14 data-v-622598d7"><view class="data-v-622598d7">自提地址信息</view><view data-event-opts="{{[['tap',[['showMaoLocation',['$event']]]]]}}" class="place cart-color acea-row row-center-wrapper data-v-622598d7" bindtap="__e"><text class="iconfont icon-weizhi data-v-622598d7"></text>查看位置</view></view></block><block wx:if="{{orderInfo.shippingType===1}}"><view class="address borRadius14 data-v-622598d7"><view class="name data-v-622598d7">{{orderInfo.realName}}<text class="phone data-v-622598d7">{{orderInfo.userPhone}}</text></view><view class="data-v-622598d7">{{orderInfo.userAddress}}</view></view></block><block wx:else><view class="address data-v-622598d7" style="margin-top:15rpx;"><view data-event-opts="{{[['tap',[['makePhone',['$event']]]]]}}" class="name data-v-622598d7" bindtap="__e">{{orderInfo.systemStore?orderInfo.systemStore.name:''}}<text class="phone data-v-622598d7">{{orderInfo.systemStore?orderInfo.systemStore.phone:''}}</text><text class="iconfont icon-tonghua font-color data-v-622598d7"></text></view><view class="data-v-622598d7">{{orderInfo.systemStore?orderInfo.systemStore.address+orderInfo.systemStore.detailedAddress:''}}</view></view></block><order-goods vue-id="7e8fe08a-1" evaluate="{{evaluate}}" productType="{{orderInfo.type}}" orderId="{{order_id}}" ids="{{id}}" uniId="{{uniId}}" cartInfo="{{cartInfo}}" jump="{{true}}" class="data-v-622598d7" bind:__l="__l"></order-goods><view class="goodCall borRadius14 _div data-v-622598d7"><block wx:if="{{chatConfig.telephone_service_switch==='true'}}"><button hover-class="none" data-event-opts="{{[['tap',[['kefuClick',['$event']]]]]}}" bindtap="__e" class="data-v-622598d7"><label class="iconfont icon-kefu _span data-v-622598d7"></label><label class="_span data-v-622598d7">联系客服</label></button></block><block wx:else><button open-type="contact" hover-class="none" class="data-v-622598d7"><label class="iconfont icon-kefu _span data-v-622598d7"></label><label class="_span data-v-622598d7">联系客服</label></button></block></view></view></block><view class="pad30 data-v-622598d7"><view class="wrapper borRadius14 data-v-622598d7"><view class="item acea-row row-between data-v-622598d7"><view class="data-v-622598d7">订单编号：</view><view class="conter acea-row row-middle row-right data-v-622598d7"><text class="text-overflow data-v-622598d7">{{orderInfo.orderId}}</text><text data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" class="copy data-v-622598d7" bindtap="__e">复制</text></view></view><view class="item acea-row row-between data-v-622598d7"><view class="data-v-622598d7">下单时间：</view><view class="conter data-v-622598d7">{{orderInfo.createTime||0}}</view></view><view class="item acea-row row-between data-v-622598d7"><view class="data-v-622598d7">支付状态：</view><block wx:if="{{orderInfo.paid}}"><view class="conter data-v-622598d7">已支付</view></block><block wx:else><view class="conter data-v-622598d7">未支付</view></block></view><view class="item acea-row row-between data-v-622598d7"><view class="data-v-622598d7">支付方式：</view><view class="conter data-v-622598d7">{{orderInfo.payTypeStr}}</view></view><block wx:if="{{$root.g1}}"><view class="item flex justify-between align-center data-v-622598d7"><view class="data-v-622598d7">买家留言：</view><view class="conter data-v-622598d7">{{orderInfo.mark}}</view></view></block><block wx:if="{{$root.g2}}"><view class="item flex justify-between data-v-622598d7"><view class="data-v-622598d7">买家留言：</view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" hidden="{{!(isShow)}}" class="flex align-center data-v-622598d7" bindtap="__e"><view class="conter data-v-622598d7">{{orderInfo.mark}}</view><text class="iconfont icon-xiangyou data-v-622598d7" style="font-size:12px;"></text></view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" hidden="{{!(!isShow)}}" bindtap="__e" class="data-v-622598d7"><view class="mark_show data-v-622598d7">{{orderInfo.mark}}</view></view></view></block></view><block wx:if="{{isGoodsReturn}}"><view class="wrapper borRadius14 data-v-622598d7"><view class="item acea-row row-between data-v-622598d7"><view class="data-v-622598d7">收货人：</view><view class="conter data-v-622598d7">{{orderInfo.realName}}</view></view><view class="item acea-row row-between data-v-622598d7"><view class="data-v-622598d7">联系电话：</view><view class="conter data-v-622598d7">{{orderInfo.userPhone}}</view></view><view class="item acea-row row-between data-v-622598d7"><view class="data-v-622598d7">收货地址：</view><view class="conter data-v-622598d7">{{orderInfo.userAddress}}</view></view><block wx:if="{{orderInfo.refundReasonWap}}"><view class="item acea-row row-between data-v-622598d7"><view class="data-v-622598d7">退款原因：</view><view class="conter data-v-622598d7">{{orderInfo.refundReasonWap}}</view></view></block><block wx:if="{{orderInfo.refundReasonWapExplain}}"><view class="item acea-row row-between data-v-622598d7"><view class="data-v-622598d7">退款说明：</view><view class="conter data-v-622598d7">{{orderInfo.refundReasonWapExplain}}</view></view></block></view></block><block wx:if="{{orderInfo.status>0}}"><view class="data-v-622598d7"><block wx:if="{{orderInfo.deliveryType=='express'}}"><view class="wrapper borRadius14 data-v-622598d7"><view class="item acea-row row-between data-v-622598d7"><view class="data-v-622598d7">配送方式：</view><view class="conter data-v-622598d7">发货</view></view><view class="item acea-row row-between data-v-622598d7"><view class="data-v-622598d7">快递公司：</view><view class="conter data-v-622598d7">{{orderInfo.deliveryName||''}}</view></view><view class="item acea-row row-between data-v-622598d7"><view class="data-v-622598d7">快递号：</view><view class="conter data-v-622598d7">{{orderInfo.deliveryId||''}}<text data-event-opts="{{[['tap',[['copyDeliveryId',['$event']]]]]}}" class="copy data-v-622598d7" style="font-size:20rpx;color:#333;border-radius:20rpx;border:1rpx solid #666;padding:3rpx 15rpx;" bindtap="__e">复制</text></view></view></view></block><block wx:else><block wx:if="{{orderInfo.deliveryType=='send'}}"><view class="wrapper borRadius14 data-v-622598d7"><view class="item acea-row row-between data-v-622598d7"><view class="data-v-622598d7">配送方式：</view><view class="conter data-v-622598d7">送货</view></view><view class="item acea-row row-between data-v-622598d7"><view class="data-v-622598d7">配送人姓名：</view><view class="conter data-v-622598d7">{{orderInfo.deliveryName||''}}</view></view><view class="item acea-row row-between data-v-622598d7"><view class="data-v-622598d7">联系电话：</view><view class="conter acea-row row-middle row-right data-v-622598d7">{{orderInfo.deliveryId||''}}<text data-event-opts="{{[['tap',[['goTel',['$event']]]]]}}" class="copy data-v-622598d7" bindtap="__e">拨打</text></view></view></view></block><block wx:else><block wx:if="{{orderInfo.deliveryType=='fictitious'}}"><view class="wrapper borRadius14 data-v-622598d7"><view class="item acea-row row-between data-v-622598d7"><view class="data-v-622598d7">虚拟发货：</view><view class="conter data-v-622598d7">已发货，请注意查收</view></view></view></block></block></block></view></block><view class="wrapper borRadius14 data-v-622598d7"><block wx:if="{{orderInfo.payPostage>0}}"><view class="item acea-row row-between data-v-622598d7"><view class="data-v-622598d7">运费：</view><view class="conter data-v-622598d7">{{"￥"+orderInfo.payPostage}}</view></view></block><block wx:if="{{orderInfo.couponId}}"><view class="item acea-row row-between data-v-622598d7"><view class="data-v-622598d7">优惠券抵扣：</view><view class="conter data-v-622598d7">{{"-￥"+orderInfo.couponPrice}}</view></view></block><block wx:if="{{orderInfo.useIntegral>0}}"><view class="item acea-row row-between data-v-622598d7"><view class="data-v-622598d7">积分抵扣：</view><view class="conter data-v-622598d7">{{"-￥"+orderInfo.deductionPrice}}</view></view></block></view><view style="height:120rpx;" class="data-v-622598d7"></view><block wx:if="{{isGoodsReturn==false}}"><view class="footer acea-row row-right row-middle data-v-622598d7"><block wx:if="{{!orderInfo.paid}}"><view data-event-opts="{{[['tap',[['cancelOrder',['$event']]]]]}}" class="qs-btn data-v-622598d7" catchtap="__e">取消订单</view></block><block wx:if="{{!orderInfo.paid}}"><view data-event-opts="{{[['tap',[['pay_open',['$0'],['orderInfo.orderId']]]]]}}" class="bnt bg_color data-v-622598d7" bindtap="__e">立即付款</view></block><block wx:else><block wx:if="{{orderInfo.paid===true&&orderInfo.refundStatus===0&&orderInfo.type!==1}}"><navigator class="bnt cancel data-v-622598d7" hover-class="none" url="{{'/pages/users/goods_return/index?orderId='+orderInfo.orderId}}">申请退款</navigator></block></block><block wx:if="{{orderInfo.combinationId>0}}"><view data-event-opts="{{[['tap',[['goJoinPink',['$event']]]]]}}" class="bnt bg_color data-v-622598d7" bindtap="__e">查看拼团</view></block><block wx:if="{{orderInfo.deliveryType=='express'&&orderInfo.status>0}}"><navigator class="bnt cancel data-v-622598d7" hover-class="none" url="{{'/pages/users/goods_logistics/index?orderId='+orderInfo.orderId}}">查看物流</navigator></block><block wx:if="{{orderInfo.status==1}}"><view data-event-opts="{{[['tap',[['confirmOrder',['$event']]]]]}}" class="bnt bg_color data-v-622598d7" bindtap="__e">确认收货</view></block><block wx:if="{{orderInfo.status==3}}"><view data-event-opts="{{[['tap',[['delOrder',['$event']]]]]}}" class="bnt cancel data-v-622598d7" bindtap="__e">删除订单</view></block><block wx:if="{{orderInfo.status==3&&orderInfo.type!==1&&againStatus!==1}}"><view data-event-opts="{{[['tap',[['goOrderConfirm',['$event']]]]]}}" class="bnt bg_color data-v-622598d7" bindtap="__e">再次购买</view></block></view></block></view></view><payment vue-id="7e8fe08a-2" payMode="{{payMode}}" pay_close="{{pay_close}}" order_id="{{pay_order_id}}" totalPrice="{{totalPrice}}" data-event-opts="{{[['^onChangeFun',[['onChangeFun']]]]}}" bind:onChangeFun="__e" class="data-v-622598d7" bind:__l="__l"></payment></view>