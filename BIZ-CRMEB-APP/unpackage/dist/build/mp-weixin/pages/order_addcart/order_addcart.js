(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/order_addcart/order_addcart"],{"1cfc":function(t,e,i){"use strict";var a=i("f7aa"),n=i.n(a);n.a},"2e11":function(t,e,i){"use strict";i.r(e);var a=i("eb7d"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},"5a61":function(t,e,i){"use strict";(function(t,e){var a=i("47a9");i("9e89");a(i("3240"));var n=a(i("9de4"));t.__webpack_require_UNI_MP_PLUGIN__=i,e(n.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},"9de4":function(t,e,i){"use strict";i.r(e);var a=i("cf88"),n=i("2e11");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("1cfc");var c=i("828b"),s=Object(c["a"])(n["default"],a["b"],a["c"],!1,null,"2222cdcc",null,!1,a["a"],void 0);e["default"]=s.exports},cf88:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=(t._self._c,0===t.cartList.valid.length&&0===t.cartList.invalid.length||t.cartList.valid.length>0),a=i?t.cartList.valid.length>0||t.cartList.invalid.length>0:null,n=t.cartList.valid.length>0||t.cartList.invalid.length>0,r=n?t.cartList.invalid.length:null,c=n&&r>0?0===t.cartList.valid.length&&t.cartList.invalid.length>0:null,s=n&&r>0?t.cartList.invalid.length>1||t.cartList.valid.length>0:null,u=n?t.cartList.invalid.length&&t.loadend:null,o=0==t.cartList.valid.length&&0==t.cartList.invalid.length&&t.canShow||!t.isLogin,l=t.cartList.valid.length,d=l>0?t.selectValue.length:null;t.$mp.data=Object.assign({},{$root:{g0:i,g1:a,g2:n,g3:r,g4:c,g5:s,g6:u,g7:o,g8:l,g9:d}})},n=[]},eb7d:function(t,e,i){"use strict";(function(t){var a=i("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n,r=a(i("7eb4")),c=a(i("7ca3")),s=a(i("ee10")),u=i("8d22"),o=i("d9cf"),l=i("84f5"),d=i("8f59"),h=a(i("4bef")),f=getApp(),p={components:{recommend:function(){Promise.all([i.e("common/vendor"),i.e("components/recommend/index")]).then(function(){return resolve(i("66cd"))}.bind(null,i)).catch(i.oe)},productWindow:function(){i.e("components/productWindow/index").then(function(){return resolve(i("a82f"))}.bind(null,i)).catch(i.oe)},authorize:function(){Promise.all([i.e("common/vendor"),i.e("components/Authorize")]).then(function(){return resolve(i("8de5"))}.bind(null,i)).catch(i.oe)}},data:function(){return{cartCount:0,goodsHidden:!1,footerswitch:!0,hostProduct:[],cartList:{valid:[],invalid:[]},isAllSelect:!1,selectValue:[],selectCountPrice:0,isAuto:!1,isShowAuth:!1,hotScroll:!1,hotPage:1,hotLimit:10,loading:!1,loadend:!1,loadTitle:"加载更多",page:1,limit:20,loadingInvalid:!1,loadendInvalid:!1,loadTitleInvalid:"加载更多",pageInvalid:1,limitInvalid:20,attr:{cartAttr:!1,productAttr:[],productSelect:{}},productValue:[],productInfo:{},attrValue:"",attrTxt:"请选择",cartId:0,product_id:0,sysHeight:0,canShow:!1,configApi:{},theme:f.globalData.theme,navH:"",homeTop:20,currentPage:!1,selectNavList:[{name:"首页",icon:"icon-shouye8",url:"/pages/index/index"},{name:"搜索",icon:"icon-sousuo6",url:"/pages/goods_search/index"},{name:"我的收藏",icon:"icon-shoucang3",url:"/pages/users/user_goods_collection/index"},{name:"个人中心",icon:"icon-gerenzhongxin1",url:"/pages/user/index"}]}},computed:(0,d.mapGetters)(["isLogin"]),onLoad:function(t){console.log(f.globalData);this.navH=f.globalData.navHeight},onReady:function(){this.$nextTick((function(){var e=this,i=t.getMenuButtonBoundingClientRect(),a=t.createSelectorQuery().in(this);a.select("#home").boundingClientRect((function(t){e.homeTop=2*i.top+i.height-t.height})).exec()}))},onShow:function(){this.canShow=!1,1==this.isLogin&&(this.hotPage=1,this.hostProduct=[],this.hotScroll=!1,this.loadend=!1,this.page=1,this.cartList.valid=[],this.getCartList(),this.loadendInvalid=!1,this.pageInvalid=1,this.cartList.invalid=[],this.getInvalidList(),this.footerswitch=!0,this.hotScroll=!1,this.hotPage=1,this.hotLimit=10,this.cartList={valid:[],invalid:[]},this.isAllSelect=!1,this.selectValue=[],this.selectCountPrice=0,this.cartCount=0,this.isShowAuth=!1),t.showTabBar()},methods:(n={authColse:function(t){this.isShowAuth=t},reGoCat:function(){var t=this,e=t.productValue[this.attrValue];if(t.attr.productAttr.length&&void 0===e)return t.$util.Tips({title:"产品库存不足，请选择其它"});var i={id:t.cartId,productId:t.product_id,num:t.attr.productSelect.cart_num,unique:void 0!==t.attr.productSelect?t.attr.productSelect.unique:t.productInfo.id};(0,u.getResetCart)(i).then((function(e){t.attr.cartAttr=!1,t.$util.Tips({title:"添加购物车成功",success:function(){t.loadend=!1,t.page=1,t.cartList.valid=[],t.getCartList(),t.getCartNum()}})})).catch((function(e){return t.$util.Tips({title:e})}))},onMyEvent:function(){this.$set(this.attr,"cartAttr",!1)},reElection:function(t){this.getGoodsDetails(t)},getGoodsDetails:function(e){var i=this;t.showLoading({title:"加载中",mask:!0});var a=this;a.cartId=e.id,a.product_id=e.productId,(0,o.getProductDetail)(e.productId).then((function(e){t.hideLoading(),a.attr.cartAttr=!0;var n=e.data.productInfo;a.$set(a,"productInfo",n),a.$set(a,"productValue",e.data.productValue);var r=e.data.productAttr.map((function(t){return{attrName:t.attrName,attrValues:t.attrValues.split(","),id:t.id,isDel:t.isDel,productId:t.productId,type:t.type}}));i.$set(a.attr,"productAttr",r),a.DefaultSelect()})).catch((function(e){t.hideLoading()}))},ChangeAttr:function(t){var e=this.productValue[t];e&&e.stock>0?(this.$set(this.attr.productSelect,"image",e.image),this.$set(this.attr.productSelect,"price",e.price),this.$set(this.attr.productSelect,"stock",e.stock),this.$set(this.attr.productSelect,"unique",e.id),this.$set(this.attr.productSelect,"cart_num",1),this.$set(this,"attrValue",t),this.$set(this,"attrTxt","已选择")):(this.$set(this.attr.productSelect,"image",this.productInfo.image),this.$set(this.attr.productSelect,"price",this.productInfo.price),this.$set(this.attr.productSelect,"stock",0),this.$set(this.attr.productSelect,"unique",this.productInfo.id),this.$set(this.attr.productSelect,"cart_num",0),this.$set(this,"attrValue",""),this.$set(this,"attrTxt","请选择"))},DefaultSelect:function(){var t=this.attr.productAttr,e=[];for(var i in this.productValue)if(this.productValue[i].stock>0){e=this.attr.productAttr.length?i.split(","):[];break}for(var a=0;a<t.length;a++)this.$set(t[a],"index",e[a]);var n=this.productValue[e.sort().join(",")];n&&t.length?(this.$set(this.attr.productSelect,"storeName",this.productInfo.storeName),this.$set(this.attr.productSelect,"image",n.image),this.$set(this.attr.productSelect,"price",n.price),this.$set(this.attr.productSelect,"stock",n.stock),this.$set(this.attr.productSelect,"unique",n.id),this.$set(this.attr.productSelect,"cart_num",1),this.$set(this,"attrValue",e.sort().join(",")),this.$set(this,"attrTxt","已选择")):!n&&t.length?(this.$set(this.attr.productSelect,"storeName",this.productInfo.storeName),this.$set(this.attr.productSelect,"image",this.productInfo.image),this.$set(this.attr.productSelect,"price",this.productInfo.price),this.$set(this.attr.productSelect,"stock",0),this.$set(this.attr.productSelect,"unique",this.productInfo.id),this.$set(this.attr.productSelect,"cart_num",0),this.$set(this,"attrValue",""),this.$set(this,"attrTxt","请选择")):n||t.length||(this.$set(this.attr.productSelect,"storeName",this.productInfo.storeName),this.$set(this.attr.productSelect,"image",this.productInfo.image),this.$set(this.attr.productSelect,"price",this.productInfo.price),this.$set(this.attr.productSelect,"stock",this.productInfo.stock),this.$set(this.attr.productSelect,"unique",this.productInfo.id||""),this.$set(this.attr.productSelect,"cart_num",1),this.$set(this,"attrValue",""),this.$set(this,"attrTxt","请选择"))},attrVal:function(t){this.$set(this.attr.productAttr[t.indexw],"index",this.attr.productAttr[t.indexw].attrValues[t.indexn])},ChangeCartNum:function(t){var e=this.productValue[this.attrValue];if(void 0!==e||this.attr.productAttr.length||(e=this.attr.productSelect),void 0!==e){var i=e.stock||0,a=this.attr.productSelect;t?(a.cart_num++,a.cart_num>i&&(this.$set(this.attr.productSelect,"cart_num",i||1),this.$set(this,"cart_num",i||1))):(a.cart_num--,a.cart_num<1&&(this.$set(this.attr.productSelect,"cart_num",1),this.$set(this,"cart_num",1)))}},iptCartNum:function(t){this.$set(this.attr.productSelect,"cart_num",t)},subDel:function(t){var e=this,i=e.selectValue;if(!(i.length>0))return e.$util.Tips({title:"请选择产品"});(0,u.cartDel)(i).then((function(t){e.loadend=!1,e.page=1,e.cartList.valid=[],e.getCartList(),e.getCartNum()}))},getSelectValueProductId:function(){var t=this.cartList.valid,e=this.selectValue,i=[];if(e.length>0)for(var a in t)this.inArray(t[a].id,e)&&i.push(t[a].productId);return i},subCollect:function(t){var e=this,i=e.selectValue;if(!(i.length>0))return e.$util.Tips({title:"请选择产品"});e.getSelectValueProductId();(0,o.collectAll)(e.getSelectValueProductId()).then((function(t){return e.$util.Tips({title:"收藏成功",icon:"success"})})).catch((function(t){return e.$util.Tips({title:t})}))},subOrder:function(t){var e=this.selectValue;if(!(e.length>0))return this.$util.Tips({title:"请选择产品"});this.getPreOrder()},getPreOrder:function(){var t=this.selectValue.map((function(t){return{shoppingCartId:Number(t)}}));this.$Order.getPreOrder("shoppingCart",t)},checkboxAllChange:function(t){var e=t.detail.value;e.length>0?this.setAllSelectValue(1):this.setAllSelectValue(0)},setAllSelectValue:function(t){var e=this,i=[],a=e.cartList.valid;if(a.length>0){var n=a.map((function(a){return t?(e.footerswitch?a.attrStatus?(a.checked=!0,i.push(a.id)):a.checked=!1:(a.checked=!0,i.push(a.id)),e.isAllSelect=!0):(a.checked=!1,e.isAllSelect=!1),a}));e.$set(e.cartList,"valid",n),e.selectValue=i,e.switchSelect()}},checkboxChange:function(t){var e=this,i=t.detail.value,a=e.cartList.valid,n=[],r=[],c=[],s=a.map((function(t){return e.inArray(t.id,i)?e.footerswitch?t.attrStatus?(t.checked=!0,n.push(t)):t.checked=!1:(t.checked=!0,n.push(t)):(t.checked=!1,r.push(t)),t}));e.footerswitch&&(c=r.filter((function(t){return!t.attrStatus}))),e.$set(e.cartList,"valid",s),e.isAllSelect=s.length===n.length+c.length,e.selectValue=i,e.switchSelect()},inArray:function(t,e){for(var i in e)if(e[i]==t)return!0;return!1},switchSelect:function(){var t=this.cartList.valid,e=this.selectValue,i=0;if(e.length<1)this.selectCountPrice=i;else{for(var a in t)this.inArray(t[a].id,e)&&(i=this.$util.$h.Add(i,this.$util.$h.Mul(t[a].cartNum,t[a].vipPrice?t[a].vipPrice:t[a].price)));this.selectCountPrice=i}}},(0,c.default)(n,"iptCartNum",(function(t){var e=this.cartList.valid[t];e.cartNum&&this.setCartNum(e.id,e.cartNum),this.switchSelect()})),(0,c.default)(n,"blurInput",(function(t){var e=this.cartList.valid[t];e.cartNum||(e.cartNum=1,this.$set(this.cartList,"valid",this.cartList.valid))})),(0,c.default)(n,"subCart",(function(t){var e=this,i=!1,a=e.cartList.valid[t];a.cartNum=Number(a.cartNum)-1,a.cartNum<1&&(i=!0),a.cartNum<=1?(a.cartNum=1,a.numSub=!0):(a.numSub=!1,a.numAdd=!1),0==i&&e.setCartNum(a.id,a.cartNum,(function(i){e.cartList.valid[t]=a,e.switchSelect(),e.getCartNum()}))})),(0,c.default)(n,"addCart",(function(t){var e=this,i=e.cartList.valid[t];i.cartNum=Number(i.cartNum)+1;i.cartNum>=i.stock?(i.cartNum=i.stock,i.numAdd=!0,i.numSub=!1):(i.numAdd=!1,i.numSub=!1),e.setCartNum(i.id,i.cartNum,(function(a){e.cartList.valid[t]=i,e.switchSelect(),e.getCartNum()}))})),(0,c.default)(n,"setCartNum",(function(t,e,i){(0,u.changeCartNum)(t,e).then((function(t){i&&i(t.data)}))})),(0,c.default)(n,"getCartNum",(function(){var t=this;(0,u.getCartCounts)(!0,"sum").then((function(e){t.cartCount=e.data.count}))})),(0,c.default)(n,"getCartData",(function(t){return new Promise((function(e,i){(0,u.getCartList)(t).then((function(t){e(t.data)})).catch((function(t){this.loading=!1,this.canShow=!0,this.$util.Tips({title:t})}))}))})),(0,c.default)(n,"getCartList",(function(){var e=this;return(0,s.default)(r.default.mark((function i(){var a,n;return r.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:t.showLoading({title:"加载中",mask:!0}),a=e,n={page:a.page,limit:a.limit,isValid:!0},(0,u.getCartCounts)(!0,"sum").then(function(){var i=(0,s.default)(r.default.mark((function i(c){var s,u,o,l,d,h,f,p;return r.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:a.cartCount=c.data.count,0===c.data.count&&a.getHostProduct(),s=0;case 3:if(!(s<Math.ceil(a.cartCount/a.limit))){i.next=21;break}return i.next=6,e.getCartData(n);case 6:if(u=i.sent,o=u.list,l=a.$util.SplitArray(o,a.cartList.valid),[{numSub:!0},{numSub:!1}],[{numAdd:!0},{numAdd:!1}],d=[],l.length>0)for(h in l)1==l[h].cartNum?l[h].numSub=!0:l[h].numSub=!1,l[h],f=l[h].stock?l[h].stock:0,l[h].cartNum==f||l[h].cartNum==l[h].stock?l[h].numAdd=!0:l[h].numAdd=!1,l[h].attrStatus?(l[h].checked=!0,d.push(l[h].id)):l[h].checked=!1;a.$set(a.cartList,"valid",l),n.page+=1,a.selectValue=d,p=l.filter((function(t){return t.attrStatus})),a.isAllSelect=p.length==d.length&&p.length,a.switchSelect();case 18:s++,i.next=3;break;case 21:a.loading=!1,a.canShow=!0,t.hideLoading();case 24:case"end":return i.stop()}}),i)})));return function(t){return i.apply(this,arguments)}}());case 4:case"end":return i.stop()}}),i)})))()})),(0,c.default)(n,"getInvalidList",(function(){var t=this;if(this.loadendInvalid)return!1;if(this.loadingInvalid)return!1;var e={page:t.pageInvalid,limit:t.limitInvalid,isValid:!1};(0,u.getCartList)(e).then((function(e){var i=e.data.list,a=i.length<t.limitInvalid,n=t.$util.SplitArray(i,t.cartList.invalid);t.$set(t.cartList,"invalid",n),t.loadendInvalid=a,t.loadTitleInvalid=a?"我也是有底线的":"加载更多",t.pageInvalid=t.pageInvalid+1,t.loadingInvalid=!1})).catch((function(e){t.loadingInvalid=!1,t.loadTitleInvalid="加载更多"}))})),(0,c.default)(n,"getHostProduct",(function(){var t=this;t.hotScroll||(0,o.getProductHot)(t.hotPage,t.hotLimit).then((function(e){t.hotPage++,t.hotScroll=e.data.list.length<t.hotLimit,t.hostProduct=t.hostProduct.concat(e.data.list)}))})),(0,c.default)(n,"goodsOpen",(function(){this.goodsHidden=!this.goodsHidden})),(0,c.default)(n,"manage",(function(){var t=this;t.footerswitch=!t.footerswitch;var e=[],i=[],a=t.cartList.valid.map((function(a){return t.footerswitch?a.attrStatus?a.checked&&e.push(a.id):(a.checked=!1,i.push(a)):a.checked&&e.push(a.id),a}));t.cartList.valid=a,t.footerswitch?t.isAllSelect=a.length===e.length+i.length:t.isAllSelect=a.length===e.length,t.selectValue=e,t.switchSelect()})),(0,c.default)(n,"unsetCart",(function(){for(var t=this,e=[],i=0,a=t.cartList.invalid.length;i<a;i++)e.push(t.cartList.invalid[i].id);(0,u.cartDel)(e).then((function(e){t.$util.Tips({title:"清除成功"}),t.$set(t.cartList,"invalid",[]),t.getHostProduct()})).catch((function(t){}))})),(0,c.default)(n,"shareApi",(function(){var t=this;(0,l.getShare)().then((function(e){t.$set(t,"configApi",e.data)}))})),(0,c.default)(n,"setOpenShare",(function(t){if(this.$wechat.isWeixin()){var e={desc:t.synopsis,title:t.title,link:location.href,imgUrl:t.img};this.$wechat.wechatEvevt(["updateAppMessageShareData","updateTimelineShareData"],e)}})),(0,c.default)(n,"returns",(function(){t.switchTab({url:"/pages/index/index"})})),(0,c.default)(n,"showNav",(function(){this.currentPage=!this.currentPage})),(0,c.default)(n,"linkPage",(function(e){"/pages/index/index"==e||"/pages/user/index"==e?t.switchTab({url:e}):t.navigateTo({animationType:h.default.type,animationDuration:h.default.duration,url:e}),this.currentPage=!1})),(0,c.default)(n,"touchStart",(function(){this.currentPage=!1})),n),onReachBottom:function(){this.loadend&&this.getInvalidList(),0==this.cartList.valid.length&&0==this.cartList.invalid.length&&1!=this.hotPage&&this.getHostProduct()}};e.default=p}).call(this,i("df3c")["default"])},f7aa:function(t,e,i){}},[["5a61","common/runtime","common/vendor"]]]);