<view data-theme="{{theme}}" class="data-v-2222cdcc"><view class="cart_nav data-v-2222cdcc" style="{{('height:'+navH+'rpx;')}}"><view class="navbarCon acea-row data-v-2222cdcc"><view class="select_nav flex justify-center align-center data-v-2222cdcc" style="{{'top:'+(homeTop+'rpx')+';'}}" id="home"><text data-event-opts="{{[['tap',[['returns',['$event']]]]]}}" class="iconfont icon-fanhui2 px-20 data-v-2222cdcc" bindtap="__e"></text><text data-event-opts="{{[['tap',[['showNav',['$event']]]]]}}" class="iconfont icon-gengduo5 px-20 data-v-2222cdcc" catchtap="__e"></text><text class="nav_line data-v-2222cdcc"></text></view><view class="nav_title data-v-2222cdcc" style="{{'top:'+(homeTop+'rpx')+';'}}">购物车</view></view></view><view hidden="{{!(currentPage)}}" class="dialog_nav data-v-2222cdcc" style="{{('top:'+navH+'rpx;')}}"><block wx:for="{{selectNavList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['linkPage',['$0'],[[['selectNavList','',index,'url']]]]]]]}}" class="dialog_nav_item data-v-2222cdcc" bindtap="__e"><text class="{{['iconfont','data-v-2222cdcc',item.icon]}}"></text><text class="pl-20 data-v-2222cdcc">{{item.name}}</text></view></block></view><view data-event-opts="{{[['touchstart',[['touchStart',['$event']]]]]}}" class="shoppingCart copy-data data-v-2222cdcc" style="{{('top:'+navH+'rpx;')}}" bindtouchstart="__e"><view class="labelNav acea-row row-around data-v-2222cdcc"><view class="item data-v-2222cdcc"><text class="iconfont icon-xuanzhong data-v-2222cdcc"></text>100%正品保证</view><view class="item data-v-2222cdcc"><text class="iconfont icon-xuanzhong data-v-2222cdcc"></text>所有商品精挑细选</view><view class="item data-v-2222cdcc"><text class="iconfont icon-xuanzhong data-v-2222cdcc"></text>售后无忧</view></view><view class="borRadius14 cartBox data-v-2222cdcc"><block wx:if="{{$root.g0}}"><view class="nav acea-row row-between-wrapper data-v-2222cdcc"><view class="data-v-2222cdcc">购物数量<text class="num font_color data-v-2222cdcc">{{cartCount}}</text></view><block wx:if="{{$root.g1}}"><view data-event-opts="{{[['tap',[['manage',['$event']]]]]}}" class="administrate acea-row row-center-wrapper data-v-2222cdcc" bindtap="__e">{{(footerswitch?'管理':'取消')+''}}</view></block></view></block><block wx:if="{{$root.g2}}"><view class="pad30 data-v-2222cdcc"><view class="list data-v-2222cdcc"><checkbox-group data-event-opts="{{[['change',[['checkboxChange',['$event']]]]]}}" bindchange="__e" class="data-v-2222cdcc"><block wx:for="{{cartList.valid}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block class="data-v-2222cdcc"><view class="item acea-row row-between-wrapper data-v-2222cdcc"><checkbox value="{{item.id}}" checked="{{item.checked}}" disabled="{{!item.attrStatus&&footerswitch}}" class="data-v-2222cdcc"></checkbox><navigator class="picTxt acea-row row-between-wrapper data-v-2222cdcc" url="{{'/pages/goods_details/index?id='+item.productId}}" hover-class="none"><view class="pictrue data-v-2222cdcc"><image src="{{item.image}}" class="data-v-2222cdcc"></image></view><view class="text data-v-2222cdcc"><view class="{{['line1','data-v-2222cdcc',item.attrStatus?'':'reColor']}}">{{item.storeName+''}}</view><block wx:if="{{item.suk}}"><view class="infor line1 data-v-2222cdcc">{{"属性："+item.suk}}</view></block><block wx:if="{{item.attrStatus}}"><view class="money mt-28 data-v-2222cdcc">{{"￥"+(item.vipPrice?item.vipPrice:item.price)}}</view></block><block wx:else><view class="reElection acea-row row-between-wrapper data-v-2222cdcc"><view class="title data-v-2222cdcc">请重新选择商品规格</view><view data-event-opts="{{[['tap',[['reElection',['$0'],[[['cartList.valid','',index]]]]]]]}}" class="reBnt cart-color acea-row row-center-wrapper data-v-2222cdcc" catchtap="__e">重选</view></view></block></view><block wx:if="{{item.attrStatus}}"><view class="carnum acea-row row-center-wrapper data-v-2222cdcc"><view data-event-opts="{{[['tap',[['subCart',[index]]]]]}}" class="{{['reduce','data-v-2222cdcc',item.numSub?'on':'']}}" catchtap="__e">-</view><view class="num data-v-2222cdcc">{{item.cartNum}}</view><view data-event-opts="{{[['tap',[['addCart',[index]]]]]}}" class="{{['plus','data-v-2222cdcc',item.numAdd?'on':'']}}" catchtap="__e">+</view></view></block></navigator></view></block></block></checkbox-group></view><block wx:if="{{$root.g3>0}}"><view class="invalidGoods borRadius14 data-v-2222cdcc" style="{{($root.g4?'position: relative;z-index: 111;top: -120rpx;':'position: static;')}}"><view class="goodsNav acea-row row-between-wrapper data-v-2222cdcc"><block wx:if="{{$root.g5}}"><view data-event-opts="{{[['tap',[['goodsOpen',['$event']]]]]}}" bindtap="__e" class="data-v-2222cdcc"><text class="{{['iconfont','data-v-2222cdcc',goodsHidden==true?'icon-xiangxia':'icon-xiangshang']}}"></text>失效商品</view></block><block wx:else><view class="data-v-2222cdcc">失效商品</view></block><view data-event-opts="{{[['tap',[['unsetCart',['$event']]]]]}}" class="del data-v-2222cdcc" bindtap="__e"><text class="iconfont icon-shanchu1 data-v-2222cdcc"></text>清空</view></view><view class="goodsList data-v-2222cdcc" hidden="{{goodsHidden}}"><block wx:for="{{cartList.invalid}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block class="data-v-2222cdcc"><view class="item acea-row row-between-wrapper data-v-2222cdcc"><view class="invalid data-v-2222cdcc">失效</view><view class="picTxt acea-row row-between-wrapper data-v-2222cdcc"><view class="pictrue data-v-2222cdcc"><image src="{{item.image}}" class="data-v-2222cdcc"></image></view><view class="text acea-row row-column-between data-v-2222cdcc"><view class="line1 name data-v-2222cdcc">{{item.storeName}}</view><block wx:if="{{item.suk}}"><view class="infor line1 data-v-2222cdcc">{{"属性："+item.suk}}</view></block><view class="acea-row row-between-wrapper data-v-2222cdcc"><view class="end data-v-2222cdcc">该商品已失效</view></view></view></view></view></block></block></view></view></block><view style="height:120rpx;" class="data-v-2222cdcc"></view><block wx:if="{{$root.g6}}"><view class="loadingicon acea-row row-center-wrapper data-v-2222cdcc"><text class="loading iconfont icon-jiazai data-v-2222cdcc" hidden="{{loadingInvalid==false}}"></text>{{loadTitleInvalid+''}}</view></block></view></block><block wx:if="{{$root.g7}}"><view class="noCart data-v-2222cdcc"><view class="pictrue data-v-2222cdcc"><image src="../../static/images/noCart.png" class="data-v-2222cdcc"></image></view><recommend vue-id="0fa464c4-1" hostProduct="{{hostProduct}}" class="data-v-2222cdcc" bind:__l="__l"></recommend></view></block></view></view><block wx:if="{{$root.g8>0}}"><view class="footer acea-row row-between-wrapper data-v-2222cdcc"><view class="data-v-2222cdcc"><checkbox-group data-event-opts="{{[['change',[['checkboxAllChange',['$event']]]]]}}" bindchange="__e" class="data-v-2222cdcc"><checkbox value="all" checked="{{!!isAllSelect}}" class="data-v-2222cdcc"></checkbox><text class="checkAll data-v-2222cdcc">{{"全选("+$root.g9+")"}}</text></checkbox-group></view><block wx:if="{{footerswitch==true}}"><view class="money acea-row row-middle data-v-2222cdcc"><text class="price-color data-v-2222cdcc">{{"￥"+selectCountPrice}}</text><form report-submit="true" data-event-opts="{{[['submit',[['subOrder',['$event']]]]]}}" bindsubmit="__e" class="data-v-2222cdcc"><button class="placeOrder bg_color data-v-2222cdcc" formType="submit">立即下单</button></form></view></block><block wx:else><view class="button acea-row row-middle data-v-2222cdcc"><form report-submit="true" data-event-opts="{{[['submit',[['subCollect',['$event']]]]]}}" bindsubmit="__e" class="data-v-2222cdcc"><button class="btn_cart_color data-v-2222cdcc" formType="submit">收藏</button></form><form report-submit="true" data-event-opts="{{[['submit',[['subDel',['$event']]]]]}}" bindsubmit="__e" class="data-v-2222cdcc"><button class="bnt data-v-2222cdcc" formType="submit">删除</button></form></view></block></view></block><product-window vue-id="0fa464c4-2" attr="{{attr}}" isShow="{{1}}" iSplus="{{1}}" iScart="{{1}}" id="product-window" data-event-opts="{{[['^myevent',[['onMyEvent']]],['^ChangeAttr',[['ChangeAttr']]],['^ChangeCartNum',[['ChangeCartNum']]],['^attrVal',[['attrVal']]],['^iptCartNum',[['iptCartNum']]],['^goCat',[['reGoCat']]]]}}" bind:myevent="__e" bind:ChangeAttr="__e" bind:ChangeCartNum="__e" bind:attrVal="__e" bind:iptCartNum="__e" bind:goCat="__e" class="data-v-2222cdcc" bind:__l="__l"></product-window><view class="uni-p-b-96 data-v-2222cdcc"></view><view class="uni-p-b-98 data-v-2222cdcc"></view></view>