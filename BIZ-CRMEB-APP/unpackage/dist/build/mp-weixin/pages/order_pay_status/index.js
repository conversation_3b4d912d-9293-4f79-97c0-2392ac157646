(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/order_pay_status/index"],{"19dd":function(t,e,i){},"3ce4":function(t,e,i){"use strict";var n=i("19dd"),a=i.n(n);a.a},"6cb6":function(t,e,i){"use strict";(function(t,e){var n=i("47a9");i("9e89");n(i("3240"));var a=n(i("e662"));t.__webpack_require_UNI_MP_PLUGIN__=i,e(a.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},"750f":function(t,e,i){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i("8d22"),a=i("e32c"),o=i("2e55"),r=i("8f59"),d=getApp(),u={components:{authorize:function(){Promise.all([i.e("common/vendor"),i.e("components/Authorize")]).then(function(){return resolve(i("8de5"))}.bind(null,i)).catch(i.oe)}},data:function(){return{orderId:"",order_pay_info:{paid:0,_status:{}},isAuto:!1,isShowAuth:!1,status:0,msg:"",errMsg:!1,payResult:"订单查询中...",theme:d.globalData.theme}},computed:(0,r.mapGetters)(["isLogin"]),watch:{isLogin:{handler:function(t,e){t&&this.getOrderPayInfo()},deep:!0}},onLoad:function(t){if(!t.order_id)return this.$util.Tips({title:"缺少参数无法查看订单支付状态"},{tab:3,url:1});this.orderId=t.order_id,this.status=t.status||0,this.isLogin?this.getOrderPayInfo():(0,o.toLogin)()},methods:{wechatQueryPay:function(){var e=this;(0,n.wechatQueryPayResult)(this.orderId).then((function(i){e.payResult="支付成功",t.setNavigationBarTitle({title:"支付成功"}),e.order_pay_info.paid=1,t.hideLoading()})).catch((function(i){e.order_pay_info.paid=2,e.errMsg=!0,e.msg=i,t.hideLoading(),e.$util.Tips({title:i})}))},onLoadFun:function(){this.getOrderPayInfo()},getOrderPayInfo:function(){var e=this,i=this;t.showLoading({title:"正在加载中"}),(0,n.getOrderDetail)(i.orderId).then((function(n){i.$set(i,"order_pay_info",n.data),"weixin"===n.data.payType&&!1===n.data.paid&&2!=e.status?setTimeout((function(){i.wechatQueryPay()}),2e3):(t.setNavigationBarTitle({title:n.data.paid?"支付成功":"未支付"}),n.data.paid?(e.payResult="支付成功",e.order_pay_info.paid=1):(e.payResult="支付失败",e.order_pay_info.paid=2),t.hideLoading())})).catch((function(e){t.hideLoading()}))},goIndex:function(e){t.switchTab({url:"/pages/index/index"})},goPink:function(e){t.navigateTo({url:"/pages/activity/goods_combination_status/index?id="+e})},goOrderDetails:function(e){var i=this;t.showLoading({title:"正在加载"}),(0,a.openOrderSubscribe)().then((function(e){t.hideLoading(),t.navigateTo({url:"/pages/order_details/index?order_id="+i.orderId})})).catch((function(){t.hideLoading()}))}}};e.default=u}).call(this,i("df3c")["default"])},9054:function(t,e,i){"use strict";i.r(e);var n=i("750f"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},e662:function(t,e,i){"use strict";i.r(e);var n=i("ef01"),a=i("9054");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("3ce4");var r=i("828b"),d=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=d.exports},ef01:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement;this._self._c},a=[]}},[["6cb6","common/runtime","common/vendor"]]]);