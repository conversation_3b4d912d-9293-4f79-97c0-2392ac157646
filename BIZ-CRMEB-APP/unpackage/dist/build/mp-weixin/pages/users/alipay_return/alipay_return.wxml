<view data-theme="{{theme}}"><view class="payment-status"><block wx:if="{{order_pay_info.paid===1}}"><view class="iconfont icons icon-duihao2 bg_color"></view></block><block wx:if="{{order_pay_info.paid===2}}"><view class="iconfont icons icon-iconfontguanbi"></view></block><view class="status">{{payResult}}</view><view class="wrapper"><view class="item acea-row row-between-wrapper"><view>订单编号</view><view class="itemCom">{{orderId}}</view></view><view class="item acea-row row-between-wrapper"><view>下单时间</view><view class="itemCom">{{order_pay_info.createTime?order_pay_info.createTime:'-'}}</view></view><view class="item acea-row row-between-wrapper"><view>支付方式</view><view class="itemCom">支付宝支付</view></view><view class="item acea-row row-between-wrapper"><view>支付金额</view><view class="itemCom">{{order_pay_info.payPrice}}</view></view><block wx:if="{{order_pay_info.paid===2}}"><view class="item acea-row row-between-wrapper"><view>失败原因</view><view class="itemCom">{{msg}}</view></view></block></view><view data-event-opts="{{[['tap',[['goOrderDetails',['$event']]]]]}}" bindtap="__e"><button class="returnBnt bg_color" formType="submit" hover-class="none">查看订单</button></view><button class="returnBnt cart-color" formType="submit" hover-class="none" data-event-opts="{{[['tap',[['goIndex',['$event']]]]]}}" bindtap="__e">返回首页</button></view></view>