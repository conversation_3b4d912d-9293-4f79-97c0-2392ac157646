(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/users/user_address/index"],{"0d22":function(i,t,e){"use strict";e.r(t);var n=e("8cdb"),s=e("72bc");for(var r in s)["default"].indexOf(r)<0&&function(i){e.d(t,i,(function(){return s[i]}))}(r);e("a89b");var a=e("828b"),c=Object(a["a"])(s["default"],n["b"],n["c"],!1,null,"518da161",null,!1,n["a"],void 0);t["default"]=c.exports},"72bc":function(i,t,e){"use strict";e.r(t);var n=e("8556"),s=e.n(n);for(var r in n)["default"].indexOf(r)<0&&function(i){e.d(t,i,(function(){return n[i]}))}(r);t["default"]=s.a},8556:function(i,t,e){"use strict";(function(i){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=e("20ce"),s=e("342a"),r=(e("ad96"),e("2e55")),a=e("8f59"),c=e("8b39"),d=getApp(),o={components:{authorize:function(){Promise.all([e.e("common/vendor"),e.e("components/Authorize")]).then(function(){return resolve(e("8de5"))}.bind(null,e)).catch(e.oe)},home:function(){e.e("components/home/<USER>").then(function(){return resolve(e("1e4d"))}.bind(null,e)).catch(e.oe)}},data:function(){return{cartId:"",pinkId:0,couponId:0,id:0,userAddress:{isDefault:!1},region:["省","市","区"],valueRegion:[0,0,0],isAuto:!1,isShowAuth:!1,district:[],multiArray:[],multiIndex:[0,0,0],cityId:0,bargain:!1,combination:!1,secKill:!1,theme:d.globalData.theme,showLoading:!1}},computed:(0,a.mapGetters)(["isLogin"]),watch:{isLogin:{handler:function(i,t){i&&this.getUserAddress()},deep:!0}},onLoad:function(t){var e=this;this.$Cache.getItem("cityList")?(this.district=this.$Cache.getItem("cityList"),this.initialize()):(this.showLoading=!0,i.showLoading({title:"数据加载中..."}),(0,s.getCityList)().then((function(t){e.district=t,e.initialize(),i.hideLoading(),e.showLoading=!1}))),this.isLogin?(this.preOrderNo=t.preOrderNo||0,this.id=t.id||0,i.setNavigationBarTitle({title:t.id?"修改地址":"添加地址"}),this.getUserAddress()):(0,r.toLogin)()},methods:{getUserAddress:function(){if(!this.id)return!1;var i=this;(0,n.getAddressDetail)(this.id).then((function(t){if(t.data){var e=[t.data.province,t.data.city,t.data.district];i.$set(i,"userAddress",t.data),i.$set(i,"region",e),i.city_id=t.data.cityId}}))},initialize:function(){var i=this,t=[],e=[],n=[];if(i.district.length){var s=i.district[0].child||[];s.length&&s[0].child;i.district.forEach((function(e,n){t.push(e.name),e.name===i.region[0]&&(i.valueRegion[0]=n,i.multiIndex[0]=n)})),i.district[this.valueRegion[0]].child.forEach((function(t,n){e.push(t.name),i.region[1]==t.name&&(i.valueRegion[1]=n,i.multiIndex[1]=n)})),i.district[this.valueRegion[0]].child[this.valueRegion[1]].child.forEach((function(t,e){n.push(t.name),i.region[2]==t.name&&(i.valueRegion[2]=e,i.multiIndex[2]=e)})),this.multiArray=[t,e,n]}},bindRegionChange:function(i){var t=this.multiIndex,e=this.district[t[0]]||{child:[]},n=e.child[t[1]]||{child:[]},s=n.child[t[2]]||{cityId:0},r=this.multiArray,a=i.detail.value;this.region=[r[0][a[0]],r[1][a[1]],r[2][a[2]]],this.cityId=s.cityId,this.valueRegion=[0,0,0],this.initialize()},bindMultiPickerColumnChange:function(i){var t=i.detail.column,e=i.detail.value,n=this.district[e]||{child:[]},s=this.multiArray,r=this.multiIndex;switch(r[t]=e,t){case 0:var a=n.child[0]||{child:[]};s[1]=n.child.map((function(i){return i.name})),s[2]=a.child.map((function(i){return i.name}));break;case 1:var c=this.district[r[0]].child[r[1]].child||[];s[2]=c.map((function(i){return i.name}));break;case 2:break}this.$set(this.multiArray,0,s[0]),this.$set(this.multiArray,1,s[1]),this.$set(this.multiArray,2,s[2]),this.multiIndex=r},onLoadFun:function(){this.getUserAddress()},authColse:function(i){this.isShowAuth=i},toggleTab:function(i){this.$refs[i].show()},onConfirm:function(i){this.region=i.checkArr[0]+"-"+i.checkArr[1]+"-"+i.checkArr[2]},chooseLocation:function(){var t=this;i.chooseLocation({success:function(i){console.log(i),t.$set(t.userAddress,"detail",i.name)}})},getWxAddress:function(){var t=this;i.authorize({scope:"scope.address",success:function(e){i.chooseAddress({success:function(e){var s={};s.province=e.provinceName,s.city=e.cityName,s.district=e.countyName,s.cityId=0,(0,n.editAddress)({address:s,isDefault:1,realName:e.userName,postCode:e.postalCode,phone:e.telNumber,detail:e.detailInfo,id:0}).then((function(e){return setTimeout((function(){if(t.cartId){var n=t.cartId,s=t.pinkId,r=t.couponId;t.cartId="",t.pinkId="",t.couponId="",i.navigateTo({url:"/pages/users/order_confirm/index?cartId="+n+"&addressId="+(t.id?t.id:e.data.id)+"&pinkId="+s+"&couponId="+r+"&secKill="+t.secKill+"&combination="+t.combination+"&bargain="+t.bargain})}else i.navigateBack({delta:1})}),1e3),t.$util.Tips({title:"添加成功",icon:"success"})})).catch((function(i){return t.$util.Tips({title:i})}))},fail:function(i){if("chooseAddress:cancel"==i.errMsg)return t.$util.Tips({title:"取消选择"})}})},fail:function(e){i.showModal({title:"您已拒绝导入微信地址权限",content:"是否进入权限管理，调整授权？",success:function(e){if(e.confirm)i.openSetting({success:function(i){}});else if(e.cancel)return t.$util.Tips({title:"已取消！"})}})}})},getAddress:function(){var t=this,e=this;e.$wechat.openAddress().then((function(s){(0,n.editAddress)({id:t.id,realName:s.userName,phone:s.telNumber,address:{province:s.provinceName,city:s.cityName,district:s.countryName,cityId:0},detail:s.detailInfo,isDefault:1,postCode:s.postalCode}).then((function(){setTimeout((function(){if(e.cartId){var t=e.cartId,n=e.pinkId,s=e.couponId;e.cartId="",e.pinkId="",e.couponId="",i.navigateTo({url:"/pages/users/order_confirm/index?cartId="+t+"&addressId="+(e.id?e.id:res.data.id)+"&pinkId="+n+"&couponId="+s+"&secKill="+e.secKill+"&combination="+e.combination+"&bargain="+e.bargain})}else i.navigateTo({url:"/pages/users/user_address_list/index"})}),1e3),e.$util.Tips({title:"添加成功",icon:"success"})})).catch((function(i){return e.$util.Tips({title:i||"添加失败"})}))})).catch((function(i){console.log(i)}))},formSubmit:(0,c.Debounce)((function(t){var e=this,s=t.detail.value;if(!s.realName)return e.$util.Tips({title:"请填写收货人姓名"});if(!s.phone)return e.$util.Tips({title:"请填写联系电话"});if(!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(s.phone))return e.$util.Tips({title:"请输入正确的手机号码"});if("省-市-区"==e.region)return e.$util.Tips({title:"请选择所在地区"});if(!s.detail)return e.$util.Tips({title:"请填写详细地址"});s.id=e.id;var r=e.region;s.address={province:r[0],city:r[1],district:r[2],cityId:e.cityId},s.isDefault=e.userAddress.isDefault,i.showLoading({title:"保存中",mask:!0}),(0,n.editAddress)(s).then((function(t){e.id?e.$util.Tips({title:"修改成功",icon:"success"}):e.$util.Tips({title:"添加成功",icon:"success"}),setTimeout((function(){if(!(e.preOrderNo>0))return i.navigateBack({delta:1});i.redirectTo({url:"/pages/users/order_confirm/index?preOrderNo="+e.preOrderNo+"&addressId="+(e.id?e.id:t.data.id)})}),1e3)})).catch((function(i){return e.$util.Tips({title:i})}))})),ChangeIsDefault:function(i){this.$set(this.userAddress,"isDefault",!this.userAddress.isDefault)}}};t.default=o}).call(this,e("df3c")["default"])},"8cdb":function(i,t,e){"use strict";e.d(t,"b",(function(){return n})),e.d(t,"c",(function(){return s})),e.d(t,"a",(function(){}));var n=function(){var i=this.$createElement;this._self._c},s=[]},a89b:function(i,t,e){"use strict";var n=e("f9a8"),s=e.n(n);s.a},f9a8:function(i,t,e){},fdbe:function(i,t,e){"use strict";(function(i,t){var n=e("47a9");e("9e89");n(e("3240"));var s=n(e("0d22"));i.__webpack_require_UNI_MP_PLUGIN__=e,t(s.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])}},[["fdbe","common/runtime","common/vendor"]]]);