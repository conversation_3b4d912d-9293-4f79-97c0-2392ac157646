<view data-theme="{{theme}}" class="data-v-518da161"><form report-submit="true" data-event-opts="{{[['submit',[['formSubmit',['$event']]]]]}}" bindsubmit="__e" class="data-v-518da161"><view class="addAddress pad30 data-v-518da161"><view class="list borRadius14 data-v-518da161"><view class="item acea-row row-between-wrapper data-v-518da161" style="border:none;"><view class="name data-v-518da161">姓名</view><input type="text" placeholder="请输入姓名" placeholder-style="color:#ccc;" name="realName" placeholder-class="placeholder" maxlength="20" value="{{userAddress.realName}}" class="data-v-518da161"/></view><view class="item acea-row row-between-wrapper data-v-518da161"><view class="name data-v-518da161">联系电话</view><input type="number" placeholder="请输入联系电话" placeholder-style="color:#ccc;" name="phone" placeholder-class="placeholder" maxlength="11" value="{{userAddress.phone}}" class="data-v-518da161"/></view><view class="item acea-row row-between-wrapper relative data-v-518da161"><view class="name data-v-518da161">所在地区</view><view class="address data-v-518da161"><picker mode="multiSelector" value="{{valueRegion}}" range="{{multiArray}}" data-event-opts="{{[['change',[['bindRegionChange',['$event']]]],['columnchange',[['bindMultiPickerColumnChange',['$event']]]]]}}" bindchange="__e" bindcolumnchange="__e" class="data-v-518da161"><view class="acea-row data-v-518da161"><view class="picker line1 data-v-518da161">{{region[0]+"，"+region[1]+"，"+region[2]}}</view><view class="iconfont icon-xiangyou abs_right data-v-518da161"></view></view></picker></view></view><view class="item acea-row row-between-wrapper relative data-v-518da161"><view class="name data-v-518da161">详细地址</view><input type="text" placeholder="请填写具体地址" placeholder-style="color:#ccc;" name="detail" placeholder-class="placeholder" maxlength="14" data-event-opts="{{[['input',[['__set_model',['$0','detail','$event',[]],['userAddress']]]]]}}" value="{{userAddress.detail}}" bindinput="__e" class="data-v-518da161"/><view data-event-opts="{{[['tap',[['chooseLocation',['$event']]]]]}}" class="iconfont icon-dizhi font_color abs_right data-v-518da161" bindtap="__e"></view></view></view><view class="default acea-row row-middle borRadius14 data-v-518da161"><checkbox-group data-event-opts="{{[['change',[['ChangeIsDefault',['$event']]]]]}}" bindchange="__e" class="data-v-518da161"><checkbox checked="{{userAddress.isDefault}}" class="data-v-518da161"></checkbox>设置为默认地址</checkbox-group></view><button class="keepBnt bg_color data-v-518da161" form-type="submit">立即保存</button><block wx:if="{{!id}}"><view data-event-opts="{{[['tap',[['getWxAddress',['$event']]]]]}}" class="wechatAddress data-v-518da161" bindtap="__e">导入微信地址</view></block></view></form><view hidden="{{!(showLoading)}}" class="bg-fixed data-v-518da161"></view></view>