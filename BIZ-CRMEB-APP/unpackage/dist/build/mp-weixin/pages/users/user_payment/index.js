(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/users/user_payment/index"],{"0447":function(e,t,i){"use strict";var n=i("9bbb"),a=i.n(n);a.a},6183:function(e,t,i){"use strict";(function(e,t){var n=i("47a9");i("9e89");n(i("3240"));var a=n(i("ce3d"));e.__webpack_require_UNI_MP_PLUGIN__=i,t(a.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},6613:function(e,t,i){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i("20ce"),a=i("8d22"),r=i("2e55"),c=i("8f59"),o=i("8b39"),u=getApp(),s={components:{authorize:function(){Promise.all([i.e("common/vendor"),i.e("components/Authorize")]).then(function(){return resolve(i("8de5"))}.bind(null,i)).catch(i.oe)},home:function(){i.e("components/home/<USER>").then(function(){return resolve(i("1e4d"))}.bind(null,i)).catch(i.oe)}},data:function(){return{now_money:0,navRecharge:["账户充值","佣金转入"],active:0,number:"",placeholder:"0.00",from:"",isAuto:!1,isShowAuth:!1,picList:[],activePic:0,money:"",numberPic:"",rechar_id:0,rechargeAttention:[],theme:u.globalData.theme,cartArr:[{name:"微信支付",icon:"icon-weixin2",value:"weixin",title:"微信快捷支付",payStatus:1}],payType:"weixin",openType:1,curActive:0,animated:!1,formContent:""}},computed:(0,c.mapGetters)(["isLogin","systemPlatform","userInfo"]),watch:{isLogin:{handler:function(e,t){e&&this.getRecharge()},deep:!0}},onLoad:function(e){this.isLogin?(this.getRecharge(),this.payConfig()):(0,r.toLogin)()},methods:{picCharge:function(e,t){this.activePic=e,void 0===t?(this.rechar_id=0,this.numberPic=""):(this.money="",this.rechar_id=t.id,this.numberPic=t.price)},getRecharge:function(){var e=this;(0,n.getRechargeApi)().then((function(t){e.picList=t.data.rechargeQuota,e.picList[0]&&(e.rechar_id=e.picList[0].id,e.numberPic=e.picList[0].price),e.rechargeAttention=t.data.rechargeAttention||[]})).catch((function(t){e.$dialog.toast({mes:t})}))},payConfig:function(){var e=this;(0,a.getPayConfig)().then((function(t){e.cartArr[0].payStatus=1===parseInt(t.data.payWeixinOpen)?1:0,e.$wechat.isWeixin()&&e.cartArr.pop()}))},onLoadFun:function(){this.getRecharge()},authColse:function(e){this.isShowAuth=e},navRecharges:function(e){this.active=e},payItem:function(e){var t=e;this.curActive=t,this.animated=!0,this.payType=this.cartArr[t].value},submitSub:(0,o.Debounce)((function(t){var i=this,a=t.detail.value.number?t.detail.value.number:i.numberPic;if(i.active){if(parseFloat(a)<0||NaN==parseFloat(a)||void 0==a||""==a)return i.$util.Tips({title:"请输入金额"});e.showModal({title:"转入余额",content:"转入余额后无法再次转出，确认是否转入余额",success:function(e){if(e.confirm)(0,n.transferIn)({price:parseFloat(a)}).then((function(e){return i.$store.commit("changInfo",{amount1:"brokeragePrice",amount2:i.$util.$h.Sub(i.userInfo.brokeragePrice,parseFloat(a))}),i.$util.Tips({title:"转入成功",icon:"success"},{tab:5,url:"/pages/users/user_money/index"})})).catch((function(e){return i.$util.Tips({title:e})}));else if(e.cancel)return i.$util.Tips({title:"已取消"})}})}else{e.showLoading({title:"正在支付"});var r=parseFloat(this.money);if(0==this.rechar_id){if(Number.isNaN(r))return i.$util.Tips({title:"充值金额必须为数字"});if(r<=0)return i.$util.Tips({title:"充值金额不能为0"});if(r>5e4)return i.$util.Tips({title:"充值金额最大值为50000"})}else r=this.numberPic;switch(i.payType){case"weixin":(0,n.rechargeRoutine)({price:r,type:0,rechar_id:this.rechar_id}).then((function(t){e.hideLoading();var n=t.data.data.jsConfig;e.requestPayment({timeStamp:n.timeStamp,nonceStr:n.nonceStr,package:n.packages,signType:n.signType,paySign:n.paySign,success:function(e){return i.$store.commit("changInfo",{amount1:"nowMoney",amount2:i.$util.$h.Add(a,i.userInfo.nowMoney)}),i.$util.Tips({title:"支付成功",icon:"success"},{tab:5,url:"/pages/users/user_money/index"})},fail:function(e){return i.$util.Tips({title:"支付失败"})},complete:function(e){if("requestPayment:cancel"==e.errMsg)return i.$util.Tips({title:"取消支付"})}})})).catch((function(t){return e.hideLoading(),i.$util.Tips({title:t})}));break;case"alipay":break}}})),addMoney:function(){this.money=this.money.replace(/[^\d]/g,"").replace(/^0{1,}/g,"")}}};t.default=s}).call(this,i("df3c")["default"])},"96cf":function(e,t,i){"use strict";i.d(t,"b",(function(){return n})),i.d(t,"c",(function(){return a})),i.d(t,"a",(function(){}));var n=function(){var e=this,t=e.$createElement,i=(e._self._c,e.active?null:parseFloat(e.activePic)),n=e.active?null:parseFloat(e.picList.length),a=e.active?null:parseFloat(e.activePic),r=e.active?null:parseFloat(e.picList.length);e.$mp.data=Object.assign({},{$root:{m0:i,m1:n,m2:a,m3:r}})},a=[]},"9bbb":function(e,t,i){},ce3d:function(e,t,i){"use strict";i.r(t);var n=i("96cf"),a=i("fbb9f");for(var r in a)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(r);i("0447");var c=i("828b"),o=Object(c["a"])(a["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);t["default"]=o.exports},fbb9f:function(e,t,i){"use strict";i.r(t);var n=i("6613"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);t["default"]=a.a}},[["6183","common/runtime","common/vendor"]]]);