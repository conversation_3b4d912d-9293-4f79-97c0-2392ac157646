<view class="user_payment" data-theme="{{theme}}"><form report-submit="true" data-event-opts="{{[['submit',[['submitSub',['$event']]]]]}}" bindsubmit="__e"><view class="payment-top acea-row row-column row-center-wrapper"><label class="name1 _span">我的余额</label><view class="pic">￥<label class="pic-font _span">{{userInfo.nowMoney||0}}</label></view></view><view class="payment"><view class="nav acea-row row-around row-middle"><block wx:for="{{navRecharge}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['navRecharges',[index]]]]]}}" class="{{['item',active==index?'on':'']}}" bindtap="__e">{{item}}</view></block></view><block wx:if="{{!active}}"><view class="tip picList"><block wx:for="{{picList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['picCharge',[index,'$0'],[[['picList','',index]]]]]]]}}" class="{{['pic-box','pic-box-color','acea-row','row-center-wrapper','row-column',activePic===index?'pic-box-color-active':'']}}" bindtap="__e"><view class="pic-number-pic">{{''+item.price}}<label class="pic-number _span">元</label></view><view class="pic-number">{{"赠送："+item.giveMoney+" 元"}}</view></view></block><view data-event-opts="{{[['tap',[['picCharge',['$0'],['picList.length']]]]]}}" class="{{['pic-box','pic-box-color','acea-row','row-center-wrapper',$root.m0===$root.m1?'pic-box-color-active':'']}}" bindtap="__e"><input class="{{['pic-box-money','pic-number-pic','uni-input',$root.m2===$root.m3?'pic-box-color-active':'']}}" type="number" placeholder="其他" maxlength="5" data-event-opts="{{[['blur',[['addMoney']]],['input',[['__set_model',['','money','$event',[]]]]]]}}" value="{{money}}" bindblur="__e" bindinput="__e"/></view><view class="tips-box"><view class="tips mt-30">注意事项：</view><block wx:for="{{rechargeAttention}}" wx:for-item="item" wx:for-index="__i0__" wx:key="*this"><view class="tips-samll">{{''+item+''}}</view></block></view></view></block><block wx:else><view class="tip"><view class="input"><text>￥</text><input placeholder="0.00" type="number" placeholder-class="placeholder" name="number" value="{{number}}"/></view><view class="tips-title"><view style="font-weight:bold;font-size:26rpx;">提示：</view><view style="margin-top:10rpx;">当前佣金为<text class="font-color">{{"￥"+(userInfo.brokeragePrice||0)}}</text></view></view><view class="tips-box"><view class="tips mt-30">注意事项：</view><block wx:for="{{rechargeAttention}}" wx:for-item="item" wx:for-index="__i1__" wx:key="*this"><view class="tips-samll">{{''+item+''}}</view></block></view></view></block><button class="but" formType="submit">{{''+(active?'立即转入':'立即充值')}}</button><view class="alipaysubmit"><rich-text nodes="{{formContent}}"></rich-text></view></view></form></view>