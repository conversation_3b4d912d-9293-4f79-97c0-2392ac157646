(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/users/wechat_login/index"],{"22af":function(e,t,n){"use strict";n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){}));var o=function(){var e=this.$createElement;this._self._c},i=[]},"2fe1":function(e,t,n){"use strict";n.r(t);var o=n("22af"),i=n("dc6f");for(var a in i)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(a);n("a8bd");var c=n("828b"),u=Object(c["a"])(i["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);t["default"]=u.exports},"555e":function(e,t,n){"use strict";(function(e){var o=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;n("8f59");var i=n("84f5"),a=(n("6152"),n("20ce")),c=o(n("f0ff")),u=(o(n("50a1")),getApp()),s=e.getSystemInfoSync().statusBarHeight+"px",r={data:function(){return{isUp:!1,phone:"",statusBarHeight:s,isHome:!1,isPhoneBox:!1,logoUrl:"",code:"",authKey:"",options:"",userInfo:{},codeNum:0,theme:u.globalData.theme,getPhoneContent:"申请获取您的手机号用于注册，完成后可使用商城更多功能"}},components:{mobileLogin:function(){Promise.all([n.e("common/vendor"),n.e("components/login_mobile/index")]).then(function(){return resolve(n("c211"))}.bind(null,n)).catch(n.oe)},atModel:function(){Promise.all([n.e("common/vendor"),n.e("components/accredit/index")]).then(function(){return resolve(n("7667"))}.bind(null,n)).catch(n.oe)}},onLoad:function(e){var t=this;(0,i.getLogo)().then((function(e){t.logoUrl=e.data.logoUrl}));getCurrentPages()},methods:{back:function(){e.navigateBack()},home:function(){e.switchTab({url:"/pages/index/index"})},modelCancel:function(){this.isPhoneBox=!1},maskClose:function(){},bindPhoneClose:function(e){e.isStatus?(this.isPhoneBox=!1,this.$util.Tips({title:"登录成功",icon:"success"},{tab:3})):this.isPhoneBox=!1},getUserInfo:function(){var t=this;(0,a.getUserInfo)().then((function(n){e.hideLoading(),t.userInfo=n.data,t.$store.commit("UPDATE_USERINFO",n.data),t.$util.Tips({title:"登录成功",icon:"success"},{tab:3})}))},getUserProfile:function(){var t=this;e.showLoading({title:"正在登录中"}),c.default.getUserProfile().then((function(n){c.default.getCode().then((function(e){t.getWxUser(e,n)})).catch((function(t){e.hideLoading()}))})).catch((function(t){e.hideLoading()}))},getWxUser:function(t,n){var o=this,i=n.userInfo;i.code=t,i.spread_spid=u.globalData.spread,i.avatar=i.userInfo.avatarUrl,i.city=i.userInfo.city,i.country=i.userInfo.country,i.nickName=i.userInfo.nickName,i.province=i.userInfo.province,i.sex=i.userInfo.gender,i.type="routine",c.default.authUserInfo(i.code,i).then((function(t){o.authKey=t.data.key,"register"===t.data.type&&(e.hideLoading(),o.isPhoneBox=!0),"login"===t.data.type&&(e.hideLoading(),o.$store.commit("LOGIN",{token:t.data.token}),o.$store.commit("SETUID",t.data.uid),o.getUserInfo(),u.globalData.spread&&(0,a.spread)(u.globalData.spread).then((function(e){})),o.$util.Tips({title:t,icon:"success"},{tab:3}))})).catch((function(t){e.hideLoading(),e.showToast({title:t,icon:"none",duration:2e3})}))}}};t.default=r}).call(this,n("df3c")["default"])},"85a8":function(e,t,n){"use strict";(function(e,t){var o=n("47a9");n("9e89");o(n("3240"));var i=o(n("2fe1"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(i.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},a8bd:function(e,t,n){"use strict";var o=n("d2fe"),i=n.n(o);i.a},d2fe:function(e,t,n){},dc6f:function(e,t,n){"use strict";n.r(t);var o=n("555e"),i=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(a);t["default"]=i.a}},[["85a8","common/runtime","common/vendor"]]]);