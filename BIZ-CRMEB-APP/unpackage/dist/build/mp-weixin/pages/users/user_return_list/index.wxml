<view class="data-v-b52ead34"><block wx:if="{{$root.g0}}"><view class="return-list pad30 data-v-b52ead34"><block wx:for="{{orderList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goOrderDetails',['$0'],[[['orderList','',index,'orderId']]]]]]]}}" class="goodWrapper borRadius14 data-v-b52ead34" bindtap="__e"><block wx:if="{{item.refundStatus==1||item.refundStatus==3}}"><view class="iconfont icon-tuikuanzhong powder data-v-b52ead34"></view></block><block wx:if="{{item.refundStatus==2}}"><view class="iconfont icon-yituikuan data-v-b52ead34"></view></block><view class="orderNum data-v-b52ead34">{{"订单号："+item.orderId}}</view><block wx:for="{{item.orderInfoList}}" wx:for-item="items" wx:for-index="index" wx:key="index"><view class="item acea-row row-between-wrapper data-v-b52ead34"><view class="pictrue data-v-b52ead34"><image src="{{items.image}}" class="data-v-b52ead34"></image></view><view class="text data-v-b52ead34"><view class="acea-row row-between-wrapper data-v-b52ead34"><view class="name line1 data-v-b52ead34">{{items.storeName}}</view><view class="num data-v-b52ead34">{{"x "+items.cartNum}}</view></view><block wx:if="{{items.suk}}"><view class="attr line1 data-v-b52ead34">{{items.suk}}</view></block><block wx:else><view class="attr line1 data-v-b52ead34">{{items.storeName}}</view></block><view class="money data-v-b52ead34">{{"￥"+items.price}}</view></view></view></block><view class="totalSum data-v-b52ead34">{{"共"+(item.totalNum||0)+'件商品，总金额'}}<text class="price data-v-b52ead34">{{"￥"+item.payPrice}}</text></view></view></block></view></block><block wx:if="{{$root.g1}}"><view class="loadingicon acea-row row-center-wrapper data-v-b52ead34"><text class="loading iconfont icon-jiazai data-v-b52ead34" hidden="{{loading==false}}"></text>{{loadTitle+''}}</view></block><block wx:if="{{$root.g2==0}}"><view class="data-v-b52ead34"><empty-page vue-id="6b8d880a-1" title="暂无订单~" class="data-v-b52ead34" bind:__l="__l"></empty-page></view></block></view>