(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/users/user_return_list/index"],{"05f4":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=(this._self._c,this.orderList.length),n=this.orderList.length,i=this.orderList.length;this.$mp.data=Object.assign({},{$root:{g0:e,g1:n,g2:i}})},o=[]},"174c":function(t,e,n){"use strict";(function(t,e){var i=n("47a9");n("9e89");i(n("3240"));var o=i(n("cca8"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(o.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"1cf8":function(t,e,n){"use strict";var i=n("d8a2"),o=n.n(i);o.a},7591:function(t,e,n){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n("8d22"),o=n("2e55"),r=n("8f59"),a={components:{emptyPage:function(){n.e("components/emptyPage").then(function(){return resolve(n("7180"))}.bind(null,n)).catch(n.oe)},home:function(){n.e("components/home/<USER>").then(function(){return resolve(n("1e4d"))}.bind(null,n)).catch(n.oe)},authorize:function(){Promise.all([n.e("common/vendor"),n.e("components/Authorize")]).then(function(){return resolve(n("8de5"))}.bind(null,n)).catch(n.oe)}},data:function(){return{loading:!1,loadend:!1,loadTitle:"加载更多",orderList:[],orderStatus:-3,page:1,limit:20,isAuto:!1,isShowAuth:!1}},computed:(0,r.mapGetters)(["isLogin"]),watch:{isLogin:{handler:function(t,e){t&&this.getOrderList()},deep:!0}},onLoad:function(){this.isLogin?this.getOrderList():(0,o.toLogin)()},onReachBottom:function(){this.getOrderList()},methods:{onLoadFun:function(){this.getOrderList()},authColse:function(t){this.isShowAuth=t},goOrderDetails:function(e){if(!e)return that.$util.Tips({title:"缺少订单号无法查看订单详情"});t.navigateTo({url:"/pages/order_details/index?order_id="+e+"&isReturen=1"})},getOrderList:function(){var t=this;t.loadend||t.loading||(t.loading=!0,t.loadTitle="",(0,i.getOrderList)({type:t.orderStatus,page:t.page,limit:t.limit}).then((function(e){var n=e.data.list||[],i=n.length<t.limit;t.orderList=t.$util.SplitArray(n,t.orderList),t.$set(t,"orderList",t.orderList),t.loadend=i,t.loading=!1,t.loadTitle=i?"我也是有底线的":"加载更多",t.page=t.page+1})).catch((function(e){t.loading=!1,t.loadTitle="加载更多"})))}}};e.default=a}).call(this,n("df3c")["default"])},cca8:function(t,e,n){"use strict";n.r(e);var i=n("05f4"),o=n("d857");for(var r in o)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(r);n("1cf8");var a=n("828b"),u=Object(a["a"])(o["default"],i["b"],i["c"],!1,null,"b52ead34",null,!1,i["a"],void 0);e["default"]=u.exports},d857:function(t,e,n){"use strict";n.r(e);var i=n("7591"),o=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=o.a},d8a2:function(t,e,n){}},[["174c","common/runtime","common/vendor"]]]);