(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/users/user_integral/index"],{"14e3":function(t,n,e){"use strict";e.r(n);var i=e("9fd4"),a=e("fed8");for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);e("6f17");var r=e("828b"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"78b2bbbe",null,!1,i["a"],void 0);n["default"]=s.exports},"6f17":function(t,n,e){"use strict";var i=e("caa5"),a=e.n(i);a.a},"8f7c":function(t,n,e){"use strict";(function(t,n){var i=e("47a9");e("9e89");i(e("3240"));var a=i(e("14e3"));t.__webpack_require_UNI_MP_PLUGIN__=e,n(a.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},"9fd4":function(t,n,e){"use strict";e.d(n,"b",(function(){return i})),e.d(n,"c",(function(){return a})),e.d(n,"a",(function(){}));var i=function(){var t=this.$createElement,n=(this._self._c,this.integralList.length),e=this.integralList.length;this.$mp.data=Object.assign({},{$root:{g0:n,g1:e}})},a=[]},ba99:function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var i=e("20ce"),a=e("2e55"),o=e("8f59"),r=getApp(),s={components:{authorize:function(){Promise.all([e.e("common/vendor"),e.e("components/Authorize")]).then(function(){return resolve(e("8de5"))}.bind(null,e)).catch(e.oe)},emptyPage:function(){e.e("components/emptyPage").then(function(){return resolve(e("7180"))}.bind(null,e)).catch(e.oe)}},data:function(){return{navList:[{name:"分值明细",icon:"icon-mingxi"},{name:"分值提升",icon:"icon-tishengfenzhi"}],current:0,page:1,limit:10,integralList:[],integral:{},loadend:!1,loading:!1,loadTitle:"加载更多",isAuto:!1,isShowAuth:!1,theme:r.globalData.theme}},computed:(0,o.mapGetters)(["isLogin"]),watch:{isLogin:{handler:function(t,n){t&&(this.getUserInfo(),this.getIntegralList())},deep:!0}},onLoad:function(){this.isLogin?(this.getUserInfo(),this.getIntegralList()):(0,a.toLogin)()},onReachBottom:function(){this.getIntegralList()},methods:{onLoadFun:function(){this.getUserInfo(),this.getIntegralList()},authColse:function(t){this.isShowAuth=t},getUserInfo:function(){var t=this;(0,i.postIntegralUser)().then((function(n){t.$set(t,"integral",n.data)}))},getIntegralList:function(){var t=this;t.loading||t.loadend||(t.loading=!0,t.loadTitle="",(0,i.getIntegralList)({page:t.page,limit:t.limit}).then((function(n){var e=n.data.list,i=e.length<t.limit;t.integralList=t.$util.SplitArray(e,t.integralList),t.$set(t,"integralList",t.integralList),t.page=t.page+1,t.loading=!1,t.loadend=i,t.loadTitle=i?"哼~😕我也是有底线的~":"加载更多"}),(function(n){this.loading=!1,t.loadTitle="加载更多"})))},nav:function(t){this.current=t}}};n.default=s},caa5:function(t,n,e){},fed8:function(t,n,e){"use strict";e.r(n);var i=e("ba99"),a=e.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);n["default"]=a.a}},[["8f7c","common/runtime","common/vendor"]]]);