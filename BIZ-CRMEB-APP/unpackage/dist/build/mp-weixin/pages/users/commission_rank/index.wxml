<view data-theme="{{theme}}" class="data-v-50426cc2"><view class="CommissionRank data-v-50426cc2"><view class="header data-v-50426cc2"><block wx:if="{{position}}"><view class="rank data-v-50426cc2">您目前的排名<text class="num data-v-50426cc2">{{position}}</text>名</view></block><block wx:else><view class="rank data-v-50426cc2">您目前暂无排名</view></block></view><view class="wrapper data-v-50426cc2"><view class="nav acea-row row-around data-v-50426cc2"><block wx:for="{{navList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['switchTap',[index]]]]]}}" class="{{['item','data-v-50426cc2',active==index?'font_color':'']}}" bindtap="__e">{{''+item+''}}</view></block></view><view class="list data-v-50426cc2"><block wx:for="{{rankList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item acea-row row-between-wrapper data-v-50426cc2"><block wx:if="{{index<=2}}"><view class="num data-v-50426cc2"><image src="{{'/static/images/medal0'+(index+1)+'.png'}}" class="data-v-50426cc2"></image></view></block><block wx:else><view class="num data-v-50426cc2">{{''+(index+1)+''}}</view></block><view class="picTxt acea-row row-between-wrapper data-v-50426cc2"><view class="pictrue data-v-50426cc2"><image src="{{item.avatar}}" class="data-v-50426cc2"></image></view><view class="text line1 data-v-50426cc2">{{item.nickname}}</view></view><view class="people font_color data-v-50426cc2">{{"￥"+item.brokeragePrice}}</view></view></block></view><block wx:if="{{$root.g0}}"><view class="noCommodity data-v-50426cc2"><empty-page vue-id="092f89da-1" title="暂无排行～" class="data-v-50426cc2" bind:__l="__l"></empty-page></view></block></view></view></view>