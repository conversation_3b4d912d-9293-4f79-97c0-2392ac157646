<view data-theme="{{theme}}" class="data-v-2a16372b"><block wx:if="{{$root.g0}}"><view class="collectionGoods data-v-2a16372b"><view class="nav acea-row row-between-wrapper data-v-2a16372b"><view class="data-v-2a16372b">当前共<text class="num font_color data-v-2a16372b">{{totals}}</text>件商品</view><view data-event-opts="{{[['tap',[['manage',['$event']]]]]}}" class="administrate acea-row row-center-wrapper data-v-2a16372b" bindtap="__e">{{(footerswitch?'管理':'取消')+''}}</view></view><view class="list data-v-2a16372b"><checkbox-group data-event-opts="{{[['change',[['checkboxChange',['$event']]]]]}}" class="centent data-v-2a16372b" bindchange="__e"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item acea-row row-middle data-v-2a16372b"><block wx:if="{{!footerswitch}}"><checkbox style="margin-right:10rpx;" value="{{item.g1}}" checked="{{item.$orig.checked}}" class="data-v-2a16372b"></checkbox></block><navigator class="acea-row data-v-2a16372b" url="{{'/pages/goods_details/index?id='+item.$orig.productId}}" hover-class="none"><view class="pictrue data-v-2a16372b"><image src="{{item.$orig.image}}" class="data-v-2a16372b"></image></view><view class="data-v-2a16372b"><view class="name line1 data-v-2a16372b">{{item.$orig.storeName}}</view><view class="money data-v-2a16372b">{{"￥"+item.$orig.price}}</view></view></navigator></view></block></checkbox-group></view><view class="loadingicon acea-row row-center-wrapper data-v-2a16372b"><text class="loading iconfont icon-jiazai data-v-2a16372b" hidden="{{loading==false}}"></text>{{loadTitle+''}}</view><block wx:if="{{!footerswitch}}"><view class="footer acea-row row-between-wrapper data-v-2a16372b"><view class="data-v-2a16372b"><checkbox-group data-event-opts="{{[['change',[['checkboxAllChange',['$event']]]]]}}" bindchange="__e" class="data-v-2a16372b"><checkbox value="all" checked="{{!!isAllSelect}}" class="data-v-2a16372b"></checkbox><text class="checkAll data-v-2a16372b">全选</text></checkbox-group></view><view class="button acea-row row-middle data-v-2a16372b"><form report-submit="true" data-event-opts="{{[['submit',[['delCollectionAll',['$event']]]]]}}" bindsubmit="__e" class="data-v-2a16372b"><button class="bnt cart-color data-v-2a16372b" formType="submit">取消收藏</button></form></view></view></block></view></block><block wx:else><block wx:if="{{$root.g2}}"><view class="noCommodity data-v-2a16372b"><view class="pictrue data-v-2a16372b"><image src="../static/noCollection.png" class="data-v-2a16372b"></image></view><recommend vue-id="dc8d4fd0-1" hostProduct="{{hostProduct}}" class="data-v-2a16372b" bind:__l="__l"></recommend></view></block></block></view>