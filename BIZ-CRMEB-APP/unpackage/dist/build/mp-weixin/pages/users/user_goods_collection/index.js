(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/users/user_goods_collection/index"],{"26ff":function(t,e,o){"use strict";o.r(e);var c=o("bb72"),l=o("bb1d");for(var i in l)["default"].indexOf(i)<0&&function(t){o.d(e,t,(function(){return l[t]}))}(i);o("eef5");var n=o("828b"),s=Object(n["a"])(l["default"],c["b"],c["c"],!1,null,"2a16372b",null,!1,c["a"],void 0);e["default"]=s.exports},"64ff":function(t,e,o){"use strict";(function(t,e){var c=o("47a9");o("9e89");c(o("3240"));var l=c(o("26ff"));t.__webpack_require_UNI_MP_PLUGIN__=o,e(l.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])},bb1d:function(t,e,o){"use strict";o.r(e);var c=o("ea8c"),l=o.n(c);for(var i in c)["default"].indexOf(i)<0&&function(t){o.d(e,t,(function(){return c[t]}))}(i);e["default"]=l.a},bb72:function(t,e,o){"use strict";o.d(e,"b",(function(){return c})),o.d(e,"c",(function(){return l})),o.d(e,"a",(function(){}));var c=function(){var t=this,e=t.$createElement,o=(t._self._c,t.collectProductList.length),c=o?t.__map(t.collectProductList,(function(e,o){var c=t.__get_orig(e),l=t.footerswitch?null:e.id.toString();return{$orig:c,g1:l}})):null,l=o?null:!t.collectProductList.length&&t.page>1;t.$mp.data=Object.assign({},{$root:{g0:o,l0:c,g2:l}})},l=[]},ea8c:function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var c=o("d9cf"),l=o("8f59"),i=o("2e55"),n=getApp(),s={components:{recommend:function(){Promise.all([o.e("common/vendor"),o.e("components/recommend/index")]).then(function(){return resolve(o("66cd"))}.bind(null,o)).catch(o.oe)},authorize:function(){Promise.all([o.e("common/vendor"),o.e("components/Authorize")]).then(function(){return resolve(o("8de5"))}.bind(null,o)).catch(o.oe)},home:function(){o.e("components/home/<USER>").then(function(){return resolve(o("1e4d"))}.bind(null,o)).catch(o.oe)}},data:function(){return{footerswitch:!0,hostProduct:[],loadTitle:"加载更多",loading:!1,loadend:!1,collectProductList:[],limit:8,page:1,isAuto:!1,isShowAuth:!1,hotScroll:!1,hotPage:1,hotLimit:10,isAllSelect:!1,selectValue:[],delBtnWidth:80,totals:0,theme:n.globalData.theme}},computed:(0,l.mapGetters)(["isLogin"]),onLoad:function(){this.isLogin?(this.loadend=!1,this.page=1,this.collectProductList=[],this.get_user_collect_product()):(0,i.toLogin)()},onShow:function(){this.loadend=!1,this.page=1,this.collectProductList=[],this.get_user_collect_product()},methods:{manage:function(){this.footerswitch=!this.footerswitch},checkboxChange:function(t){for(var e=this.collectProductList,o=t.detail.value,c=0,l=e.length;c<l;++c){var i=e[c];o.includes(i.id.toString())?this.$set(i,"checked",!0):this.$set(i,"checked",!1)}this.selectValue=o.toString(),this.isAllSelect=e.length===o.length},checkboxAllChange:function(t){var e=t.detail.value;e.length>0?this.setAllSelectValue(1):this.setAllSelectValue(0)},setAllSelectValue:function(t){var e=this,o=[];this.collectProductList.length>0&&(this.collectProductList.map((function(c){t?(e.$set(c,"checked",!0),o.push(c.id),e.isAllSelect=!0):(e.$set(c,"checked",!1),e.isAllSelect=!1)})),this.selectValue=o.toString())},onLoadFun:function(){this.get_user_collect_product(),this.get_host_product()},authColse:function(t){this.isShowAuth=t},get_user_collect_product:function(){var t=this;this.loading||this.loadend||(t.loading=!0,t.loadTitle="",(0,c.getCollectUserList)({page:t.page,limit:t.limit}).then((function(e){e.data.list.map((function(e){t.$set(e,"right",0)})),t.totals=e.data.total;var o=e.data.list,c=o.length<t.limit;t.collectProductList=t.$util.SplitArray(o,t.collectProductList),t.$set(t,"collectProductList",t.collectProductList),0===t.collectProductList.length&&t.get_host_product(),t.loadend=c,t.loadTitle=c?"我也是有底线的":"加载更多",t.page=t.page+1,t.loading=!1})).catch((function(e){t.loading=!1,t.loadTitle="加载更多"})))},delCollection:function(t,e){this.selectValue=t,this.del({ids:this.selectValue.toString()})},delCollectionAll:function(){if(!this.selectValue||0==this.selectValue.length)return this.$util.Tips({title:"请选择商品"});this.del({ids:this.selectValue})},del:function(t){var e=this;(0,c.collectDelete)(t).then((function(t){e.$util.Tips({title:"取消收藏成功",icon:"success"}),e.collectProductList=[],e.loadend=!1,e.page=1,e.get_user_collect_product()})).catch((function(t){return e.$util.Tips({title:t})}))},get_host_product:function(){var t=this;t.hotScroll||(0,c.getProductHot)(t.hotPage,t.hotLimit).then((function(e){t.hotPage++,t.hotScroll=e.data.list.length<t.hotLimit,t.hostProduct=t.hostProduct.concat(e.data.list)}))}},onReachBottom:function(){this.get_user_collect_product(),this.get_host_product()}};e.default=s},ec61:function(t,e,o){},eef5:function(t,e,o){"use strict";var c=o("ec61"),l=o.n(c);l.a}},[["64ff","common/runtime","common/vendor"]]]);