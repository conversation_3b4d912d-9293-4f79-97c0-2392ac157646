(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/users/login/index"],{"0c4d":function(t,e,n){"use strict";(function(t){var i=n("47a9"),a=n("3b2d");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=i(n("7eb4")),r=i(n("ee10")),c=(i(n("bac5")),i(n("bb5a"))),u=n("20ce"),s=(function(t,e){if(!e&&t&&t.__esModule)return t;if(null===t||"object"!==a(t)&&"function"!==typeof t)return{default:t};var n=p(e);if(n&&n.has(t))return n.get(t);var i={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var r in t)if("default"!==r&&Object.prototype.hasOwnProperty.call(t,r)){var c=o?Object.getOwnPropertyDescriptor(t,r):null;c&&(c.get||c.set)?Object.defineProperty(i,r,c):i[r]=t[r]}i.default=t,n&&n.set(t,i)}(n("8b39")),n("74bd"),n("84f5")),l=n("342a"),f=(i(n("f0ff")),n("8b39"));function p(t){if("function"!==typeof WeakMap)return null;var e=new WeakMap,n=new WeakMap;return(p=function(t){return t?n:e})(t)}var d=getApp(),h={name:"Login",mixins:[c.default],data:function(){return{navList:["快速登录","账号登录"],current:1,account:"",password:"",captcha:"",formItem:1,type:"login",logoUrl:"",keyCode:"",codeUrl:"",codeVal:"",isShowCode:!1,platform:"",appLoginStatus:!1,appUserInfo:null,appleLoginStatus:!1,appleUserInfo:null,appleShow:!1,theme:d.globalData.theme}},watch:{formItem:function(t,e){this.type=1==t?"login":"register"}},mounted:function(){this.getCode(),this.getLogoImage()},onLoad:function(){var e=this;t.getSystemInfo({success:function(t){"ios"==t.platform.toLowerCase()&&t.system.split(" ")[1]>=13&&(e.appleShow=!0)}})},methods:{appleLogin:function(){var e=this;this.account="",this.captcha="",t.showLoading({title:"登录中"}),t.login({provider:"apple",timeout:1e4,success:function(n){t.getUserInfo({provider:"apple",success:function(t){e.appleUserInfo=t.userInfo,e.appleLoginApi()},fail:function(){t.hideLoading(),t.showToast({title:"获取用户信息失败",icon:"none",duration:2e3})},complete:function(){t.hideLoading()}})},fail:function(e){t.hideLoading(),console.log(e)}})},appleLoginApi:function(){var e=this;(0,s.appleLogin)({openId:this.appleUserInfo.openId,email:void 0==this.appleUserInfo.email?"":this.appleUserInfo.email,identityToken:this.appleUserInfo.identityToken||""}).then((function(t){e.$store.commit("LOGIN",{token:t.data.token}),e.getUserInfo(t.data)})).catch((function(e){t.hideLoading(),t.showModal({title:"提示",content:"错误信息".concat(e),success:function(t){t.confirm?console.log("用户点击确定"):t.cancel&&console.log("用户点击取消")}})}))},wxLogin:(0,f.Debounce)((function(){var e=this;this.account="",this.captcha="",t.showLoading({title:"登录中"}),t.login({provider:"weixin",success:function(n){t.getUserInfo({provider:"weixin",success:function(n){t.hideLoading(),e.appUserInfo=n.userInfo,e.appUserInfo.type="ios"===e.platform?"iosWx":"androidWx",e.wxLoginGo(e.appUserInfo)},fail:function(){t.hideLoading(),t.showToast({title:"获取用户信息失败",icon:"none",duration:2e3})},complete:function(){t.hideLoading()}})},fail:function(){t.hideLoading(),t.showToast({title:"登录失败",icon:"none",duration:2e3})}})})),wxLoginGo:function(e){var n=this;(0,s.appAuth)(e).then((function(e){"register"===e.data.type&&t.navigateTo({url:"/pages/users/app_login/index?authKey="+e.data.key}),"login"===e.data.type&&(n.$store.commit("LOGIN",{token:e.data.token}),n.getUserInfo(e.data))})).catch((function(t){n.$util.Tips({title:t})}))},again:function(){this.codeUrl=l.VUE_APP_API_URL+"/sms_captcha?key="+this.keyCode+Date.parse(new Date)},getCode:function(){},getLogoImage:function(){var t=this;return(0,r.default)(o.default.mark((function e(){var n;return o.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:n=t,(0,s.getLogo)().then((function(t){n.logoUrl=t.data.logoUrl?t.data.logoUrl:"/static/images/logo2.png"}));case 2:case"end":return e.stop()}}),e)})))()},loginMobile:(0,f.Debounce)((function(){var t=this,e=this;return e.account?/^1(3|4|5|7|8|9|6)\d{9}$/i.test(e.account)?e.captcha?/^[\w\d]+$/i.test(e.captcha)?void(0,u.loginMobile)({phone:e.account,captcha:e.captcha,spread_spid:e.$Cache.get("spread")}).then((function(n){var i=n.data;Math.round(new Date/1e3);t.$store.commit("LOGIN",{token:n.data.token}),e.getUserInfo(i)})).catch((function(t){e.$util.Tips({title:t})})):e.$util.Tips({title:"请输入正确的验证码"}):e.$util.Tips({title:"请填写验证码"}):e.$util.Tips({title:"请输入正确的手机号码"}):e.$util.Tips({title:"请填写手机号码"})})),register:function(){var t=this;return(0,r.default)(o.default.mark((function e(){var n;return o.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=t,n.account){e.next=3;break}return e.abrupt("return",n.$util.Tips({title:"请填写手机号码"}));case 3:if(/^1(3|4|5|7|8|9|6)\d{9}$/i.test(n.account)){e.next=5;break}return e.abrupt("return",n.$util.Tips({title:"请输入正确的手机号码"}));case 5:if(n.captcha){e.next=7;break}return e.abrupt("return",n.$util.Tips({title:"请填写验证码"}));case 7:if(/^[\w\d]+$/i.test(n.captcha)){e.next=9;break}return e.abrupt("return",n.$util.Tips({title:"请输入正确的验证码"}));case 9:if(n.password){e.next=11;break}return e.abrupt("return",n.$util.Tips({title:"请填写密码"}));case 11:if(/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,16}$/i.test(n.password)){e.next=13;break}return e.abrupt("return",n.$util.Tips({title:"您输入的密码过于简单"}));case 13:(0,u.register)({account:n.account,captcha:n.captcha,password:n.password,spread_spid:n.$Cache.get("spread")}).then((function(t){n.$util.Tips({title:t}),n.formItem=1})).catch((function(t){n.$util.Tips({title:t})}));case 14:case"end":return e.stop()}}),e)})))()},code:function(){var t=this;return(0,r.default)(o.default.mark((function e(){var n;return o.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=t,n.account){e.next=3;break}return e.abrupt("return",n.$util.Tips({title:"请填写手机号码"}));case 3:if(/^1(3|4|5|7|8|9|6)\d{9}$/i.test(n.account)){e.next=5;break}return e.abrupt("return",n.$util.Tips({title:"请输入正确的手机号码"}));case 5:return 2==n.formItem&&(n.type="register"),e.next=8,(0,u.registerVerify)(n.account).then((function(t){n.$util.Tips({title:t.message}),n.sendCode()})).catch((function(t){return n.$util.Tips({title:t})}));case 8:case"end":return e.stop()}}),e)})))()},navTap:function(t){this.current=t},submit:(0,f.Debounce)((function(){var t=this,e=this;return e.account?/^[\w\d]{5,16}$/i.test(e.account)?e.password?void(0,u.loginH5)({account:e.account,password:e.password,spread_spid:e.$Cache.get("spread")}).then((function(n){var i=n.data;t.$store.commit("LOGIN",{token:i.token}),e.getUserInfo(i)})).catch((function(t){e.$util.Tips({title:t})})):e.$util.Tips({title:"请填写密码"}):e.$util.Tips({title:"请输入正确的账号"}):e.$util.Tips({title:"请填写账号"})})),getUserInfo:function(e){var n=this;this.$store.commit("SETUID",e.uid),(0,u.getUserInfo)().then((function(e){n.$store.commit("UPDATE_USERINFO",e.data);var i=n.$Cache.get("login_back_url")||"/pages/index/index";-1!==i.indexOf("/pages/users/login/index")&&(i="/pages/index/index"),t.reLaunch({url:i})}))}}};e.default=h}).call(this,n("df3c")["default"])},"3b5df":function(t,e,n){"use strict";(function(t,e){var i=n("47a9");n("9e89");i(n("3240"));var a=i(n("6324"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(a.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},6044:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement;t._self._c;t._isMounted||(t.e0=function(e){t.current=1},t.e1=function(e){t.current=0})},a=[]},6324:function(t,e,n){"use strict";n.r(e);var i=n("6044"),a=n("f6b0");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("7610");var r=n("828b"),c=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"60e0b2db",null,!1,i["a"],void 0);e["default"]=c.exports},7610:function(t,e,n){"use strict";var i=n("9c7d"),a=n.n(i);a.a},"9c7d":function(t,e,n){},f6b0:function(t,e,n){"use strict";n.r(e);var i=n("0c4d"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a}},[["3b5df","common/runtime","common/vendor"]]]);