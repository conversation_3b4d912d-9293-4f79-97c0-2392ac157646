(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/users/user_pwd_edit/index"],{"0801":function(t,e,n){"use strict";var i=n("4f5f"),r=n.n(i);r.a},"4f5f":function(t,e,n){},"55d1":function(t,e,n){"use strict";n.r(e);var i=n("fb03"),r=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=r.a},"852b":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){}));var i=function(){var t=this.$createElement;this._self._c},r=[]},"89f4":function(t,e,n){"use strict";(function(t,e){var i=n("47a9");n("9e89");i(n("3240"));var r=i(n("d85d"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(r.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},d85d:function(t,e,n){"use strict";n.r(e);var i=n("852b"),r=n("55d1");for(var a in r)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(a);n("0801");var o=n("828b"),u=Object(o["a"])(r["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=u.exports},fb03:function(t,e,n){"use strict";(function(t){var i=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=i(n("7eb4")),a=i(n("ee10")),o=i(n("bb5a")),u=n("ad96"),s=n("20ce"),f=n("2e55"),c=n("8f59"),l=n("fbb9"),d=getApp(),h={mixins:[o.default],data:function(){return{userInfo:{},phone:"",password:"",captcha:"",qr_password:"",isAuto:!1,isShowAuth:!1,theme:d.globalData.theme,bgColor:""}},computed:(0,c.mapGetters)(["isLogin"]),watch:{isLogin:{handler:function(t,e){t&&this.getUserInfo()},deep:!0}},onLoad:function(){this.isLogin?this.getUserInfo():(0,f.toLogin)();this.bgColor=(0,l.setThemeColor)(),t.setNavigationBarColor({frontColor:"#ffffff",backgroundColor:this.bgColor})},methods:{onLoadFun:function(t){this.getUserInfo()},authColse:function(t){this.isShowAuth=t},getUserInfo:function(){var t=this;(0,s.getUserInfo)().then((function(e){var n=e.data.phone,i=n.substr(0,3)+"****"+n.substr(7);t.$set(t,"userInfo",e.data),t.phone=i}))},code:function(){var t=this;return(0,a.default)(r.default.mark((function e(){var n;return r.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=t,n.userInfo.phone){e.next=3;break}return e.abrupt("return",n.$util.Tips({title:"手机号码不存在,无法发送验证码！"}));case 3:return e.next=5,(0,u.registerVerify)(n.userInfo.phone).then((function(t){n.$util.Tips({title:t.message}),n.sendCode()})).catch((function(t){return n.$util.Tips({title:t})}));case 5:case"end":return e.stop()}}),e)})))()},checkPasd:function(t){var e=t.detail.value;if(this.password=e,!/^[a-zA-Z]\w{5,17}$/i.test(e))return this.$util.Tips({title:"密码规则为6-18位字母加数字"})},checkPassword:function(t){var e=t.detail.value;if(e!=this.password)return this.$util.Tips({title:"两次输入的密码不一致！"})},editPwd:function(t){var e=this,n=t.detail.value.password,i=t.detail.value.qr_password,r=t.detail.value.captcha;return n?i?void(0,u.phoneRegisterReset)({account:e.userInfo.phone,captcha:r,password:n}).then((function(t){return e.$util.Tips({title:t.message},{tab:3,url:1})})).catch((function(t){return e.$util.Tips({title:t})})):e.$util.Tips({title:"请确认新密码"}):e.$util.Tips({title:"请输入新密码"})}}};e.default=h}).call(this,n("df3c")["default"])}},[["89f4","common/runtime","common/vendor"]]]);