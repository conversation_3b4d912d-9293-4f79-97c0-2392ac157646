<view class="page data-v-2e54ca24"><view class="distribution-posters data-v-2e54ca24"><swiper indicator-dots="{{indicatorDots}}" autoplay="{{autoplay}}" circular="{{circular}}" interval="{{interval}}" duration="{{duration}}" previous-margin="40px" next-margin="40px" data-event-opts="{{[['change',[['bindchange',['$event']]]]]}}" bindchange="__e" class="data-v-2e54ca24"><block wx:for="{{spreadList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block class="data-v-2e54ca24"><swiper-item class="data-v-2e54ca24"><image class="{{['slide-image','data-v-2e54ca24',swiperIndex==index?'active':'quiet']}}" src="{{item.pic}}" mode="aspectFill"></image></swiper-item></block></block></swiper><view data-event-opts="{{[['tap',[['savePhoto',['$0'],['spreadList.'+swiperIndex+'.pic']]]]]}}" class="keep data-v-2e54ca24" style="{{'background-color:'+(bgColor)+';'}}" bindtap="__e">保存海报</view></view><block wx:if="{{canvasStatus}}"><view class="canvas data-v-2e54ca24"><canvas style="width:750px;height:1190px;" canvas-id="canvasOne" class="data-v-2e54ca24"></canvas><canvas style="{{'width:'+(qrcodeSize+'px')+';'+('height:'+(qrcodeSize+'px')+';')}}" canvas-id="qrcode" class="data-v-2e54ca24"></canvas></view></block></view>