<view data-theme="{{theme}}" class="data-v-b30a7386"><view class="ChangePassword data-v-b30a7386"><view class="list data-v-b30a7386"><block wx:if="{{isNew}}"><view class="item data-v-b30a7386"><input type="number" disabled="true" placeholder="填写手机号码1" placeholder-class="placeholder" data-event-opts="{{[['input',[['__set_model',['$0','phone','$event',[]],['userInfo']]]]]}}" value="{{userInfo.phone}}" bindinput="__e" class="data-v-b30a7386"/></view></block><block wx:if="{{!isNew}}"><view class="item data-v-b30a7386"><input type="number" placeholder="填写手机号码" placeholder-class="placeholder" maxlength="11" data-event-opts="{{[['input',[['__set_model',['','phone','$event',[]]]]]]}}" value="{{phone}}" bindinput="__e" class="data-v-b30a7386"/></view></block></view><block wx:if="{{isNew}}"><button class="confirmBnt bg_color data-v-b30a7386" form-type="submit" data-event-opts="{{[['tap',[['next',['$event']]]]]}}" bindtap="__e">下一步</button></block><block wx:if="{{!isNew}}"><button class="confirmBnt bg_color data-v-b30a7386" form-type="submit" data-event-opts="{{[['tap',[['editPwd',['$event']]]]]}}" bindtap="__e">保存</button></block></view></view>