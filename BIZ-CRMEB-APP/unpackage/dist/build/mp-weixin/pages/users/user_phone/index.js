(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/users/user_phone/index"],{1889:function(t,e,n){"use strict";(function(t,e){var i=n("47a9");n("9e89");i(n("3240"));var s=i(n("3690"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(s.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},3690:function(t,e,n){"use strict";n.r(e);var i=n("83b6"),s=n("fd59");for(var u in s)["default"].indexOf(u)<0&&function(t){n.d(e,t,(function(){return s[t]}))}(u);n("51f2");var r=n("828b"),o=Object(r["a"])(s["default"],i["b"],i["c"],!1,null,"b30a7386",null,!1,i["a"],void 0);e["default"]=o.exports},"51f2":function(t,e,n){"use strict";var i=n("9f49"),s=n.n(i);s.a},"83b6":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return s})),n.d(e,"a",(function(){}));var i=function(){var t=this.$createElement;this._self._c},s=[]},"9f49":function(t,e,n){},e7bf:function(t,e,n){"use strict";(function(t){var i=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var s=i(n("7eb4")),u=i(n("ee10")),r=i(n("bb5a")),o=n("ad96"),a=n("2e55"),c=n("8f59"),f=n("fbb9"),l=getApp(),h={mixins:[r.default],data:function(){return{phone:"",captcha:"",isAuto:!1,isShowAuth:!1,key:"",isNew:!0,timer:"",text:"获取验证码",nums:60,theme:l.globalData.theme,bgColor:""}},mounted:function(){},computed:(0,c.mapGetters)(["isLogin","userInfo"]),onLoad:function(){this.bgColor=(0,f.setThemeColor)(),t.setNavigationBarColor({frontColor:"#ffffff",backgroundColor:this.bgColor}),this.isLogin||(0,a.toLogin)()},methods:{getTimes:function(){this.nums=this.nums-1,this.text="剩余 "+this.nums+"s",this.nums<0&&clearInterval(this.timer),this.text="剩余 "+this.nums+"s",this.text<"剩余 0s"&&(this.disabled=!1,this.text="重新获取")},onLoadFun:function(){},authColse:function(t){this.isShowAuth=t},next:function(){var t=this;(0,o.bindingVerify)({phone:this.userInfo.phone,captcha:this.captcha}).then((function(e){t.isNew=!1,t.captcha="",clearInterval(t.timer),t.disabled=!1,t.text="获取验证码"})).catch((function(e){return t.$util.Tips({title:e})}))},editPwd:function(){var e=this;return e.phone?/^1(3|4|5|7|8|9|6)\d{9}$/i.test(e.phone)?void t.showModal({title:"是否更换绑定账号",confirmText:"绑定",success:function(t){if(t.confirm)(0,o.bindingPhone)({phone:e.phone,captcha:e.captcha}).then((function(t){return e.$util.Tips({title:t.message,icon:"success"},{tab:5,url:"/pages/users/user_info/index"})})).catch((function(t){return e.$util.Tips({title:t})}));else if(t.cancel)return e.$util.Tips({title:"您已取消更换绑定！"},{tab:5,url:"/pages/users/user_info/index"})}}):e.$util.Tips({title:"请输入正确的手机号码！"}):e.$util.Tips({title:"请填写手机号码！"})},code:function(){var e=this;return(0,u.default)(s.default.mark((function n(){var i;return s.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(e.nums=60,t.showLoading({title:"加载中",mask:!0}),i=e,i.isNew){n.next=8;break}if(i.phone){n.next=6;break}return n.abrupt("return",i.$util.Tips({title:"请填写手机号码！"}));case 6:if(/^1(3|4|5|7|8|9|6)\d{9}$/i.test(i.phone)){n.next=8;break}return n.abrupt("return",i.$util.Tips({title:"请输入正确的手机号码！"}));case 8:return n.next=10,(0,o.registerVerify)(i.isNew?i.userInfo.phone:i.phone).then((function(e){i.$util.Tips({title:e.message}),i.timer=setInterval(i.getTimes,1e3),i.disabled=!0,t.hideLoading()})).catch((function(t){return i.$util.Tips({title:t})}));case 10:case"end":return n.stop()}}),n)})))()}}};e.default=h}).call(this,n("df3c")["default"])},fd59:function(t,e,n){"use strict";n.r(e);var i=n("e7bf"),s=n.n(i);for(var u in i)["default"].indexOf(u)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(u);e["default"]=s.a}},[["1889","common/runtime","common/vendor"]]]);