<view data-theme="{{theme}}"><view class="sign-record"><block wx:for="{{signList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="list pad30"><view class="item"><view class="data">{{item.month}}</view><view class="listn borRadius14"><block wx:for="{{item.list}}" wx:for-item="itemn" wx:for-index="indexn" wx:key="indexn"><view class="itemn acea-row row-between-wrapper"><view><view class="name line1">{{itemn.title}}</view><view>{{itemn.createDay}}</view></view><view class="num font_color">{{"+"+itemn.number}}</view></view></block></view></view></view></block><view class="loadingicon acea-row row-center-wrapper"><text class="loading iconfont icon-jiazai" hidden="{{loading==false}}"></text>{{loadtitle+''}}</view></view></view>