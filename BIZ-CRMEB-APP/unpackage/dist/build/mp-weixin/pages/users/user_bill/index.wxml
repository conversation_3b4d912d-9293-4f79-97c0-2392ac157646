<view data-theme="{{theme}}" class="data-v-3488b09a"><view class="bill-details data-v-3488b09a"><view class="nav acea-row data-v-3488b09a"><view data-event-opts="{{[['tap',[['changeType',['all']]]]]}}" class="{{['item','data-v-3488b09a',type==='all'?'on':'']}}" bindtap="__e">全部</view><view data-event-opts="{{[['tap',[['changeType',['expenditure']]]]]}}" class="{{['item','data-v-3488b09a',type==='expenditure'?'on':'']}}" bindtap="__e">消费</view><view data-event-opts="{{[['tap',[['changeType',['income']]]]]}}" class="{{['item','data-v-3488b09a',type==='income'?'on':'']}}" bindtap="__e">充值</view></view><view class="sign-record data-v-3488b09a"><block wx:for="{{userBillList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="list pad30 data-v-3488b09a"><view class="item data-v-3488b09a"><view class="data data-v-3488b09a">{{item.date}}</view><view class="listn borRadius14 data-v-3488b09a"><block wx:for="{{item.list}}" wx:for-item="vo" wx:for-index="indexn" wx:key="indexn"><view class="itemn acea-row row-between-wrapper data-v-3488b09a"><view class="data-v-3488b09a"><view class="name line1 data-v-3488b09a">{{vo.title}}</view><view class="data-v-3488b09a">{{vo.add_time}}</view></view><block wx:if="{{vo.pm}}"><view class="num font_color data-v-3488b09a">{{"+"+vo.number}}</view></block><block wx:else><view class="num data-v-3488b09a">{{"-"+vo.number}}</view></block></view></block></view></view></view></block><block wx:if="{{$root.g0>0}}"><view class="loadingicon acea-row row-center-wrapper data-v-3488b09a"><text class="loading iconfont icon-jiazai data-v-3488b09a" hidden="{{loading==false}}"></text>{{loadTitle+''}}</view></block><block wx:if="{{$root.g1==0}}"><view class="data-v-3488b09a"><empty-page vue-id="7e4d3d90-1" title="暂无账单的记录哦～" class="data-v-3488b09a" bind:__l="__l"></empty-page></view></block></view></view></view>