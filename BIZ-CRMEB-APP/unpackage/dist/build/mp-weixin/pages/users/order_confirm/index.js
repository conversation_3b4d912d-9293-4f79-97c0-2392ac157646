(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/users/order_confirm/index"],{"221e":function(e,t,n){"use strict";var r=n("b034"),o=n.n(r);o.a},"3d92":function(e,t,n){"use strict";n.r(t);var r=n("893d"),o=n("a61e");for(var i in o)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(i);n("221e");var s=n("828b"),a=Object(s["a"])(o["default"],r["b"],r["c"],!1,null,"058a5866",null,!1,r["a"],void 0);t["default"]=a.exports},"59a5":function(e,t,n){"use strict";(function(e,t){var r=n("47a9");n("9e89");r(n("3240"));var o=r(n("3d92"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(o.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"893d":function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){}));var r=function(){var e=this.$createElement,t=(this._self._c,0!=this.shippingType?this.storeList.length:null);this.$mp.data=Object.assign({},{$root:{g0:t}})},o=[]},"9c37":function(e,t,n){"use strict";(function(e){var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(n("7ca3")),i=n("8d22"),s=n("20ce"),a=n("e32c"),d=n("d9cf"),u=(n("6152"),n("2e55")),c=n("8f59");n("8b39");function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){(0,o.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var h=getApp(),f={components:{navBar:function(){Promise.all([n.e("common/vendor"),n.e("components/navBar")]).then(function(){return resolve(n("b165"))}.bind(null,n)).catch(n.oe)},couponListWindow:function(){Promise.all([n.e("common/vendor"),n.e("components/couponListWindow/index")]).then(function(){return resolve(n("e966"))}.bind(null,n)).catch(n.oe)},addressWindow:function(){n.e("components/addressWindow/index").then(function(){return resolve(n("7183"))}.bind(null,n)).catch(n.oe)},orderGoods:function(){n.e("components/orderGoods/index").then(function(){return resolve(n("b5d5"))}.bind(null,n)).catch(n.oe)},home:function(){n.e("components/home/<USER>").then(function(){return resolve(n("1e4d"))}.bind(null,n)).catch(n.oe)},authorize:function(){Promise.all([n.e("common/vendor"),n.e("components/Authorize")]).then(function(){return resolve(n("8de5"))}.bind(null,n)).catch(n.oe)}},onReady:function(){this.$nextTick((function(){var t=this,n=e.getMenuButtonBoundingClientRect(),r=e.createSelectorQuery().in(this);r.select("#home").boundingClientRect((function(e){t.homeTop=2*n.top+n.height-e.height})).exec()}))},data:function(){return{marTop:0,navTitle:"提交订单",homeTop:20,orderShow:"orderShow",textareaStatus:!0,cartArr:[{name:"微信支付",icon:"icon-weixin2",value:"weixin",title:"微信快捷支付",payStatus:1},{name:"余额支付",icon:"icon-yuezhifu",value:"yue",title:"可用余额:",payStatus:1}],payType:"weixin",openType:1,active:0,coupon:{coupon:!1,list:[],statusTile:"立即使用"},address:{address:!1,addressId:0},addressInfo:{},addressId:0,couponId:0,cartId:"",userInfo:{},mark:"",couponTitle:"请选择",coupon_price:0,useIntegral:!1,integral_price:0,integral:0,ChangePrice:0,formIds:[],status:0,is_address:!1,toPay:!1,shippingType:0,system_store:{},storePostage:0,contacts:"",contactsTel:"",mydata:{},storeList:[],store_self_mention:0,cartInfo:[],priceGroup:{},animated:!1,totalPrice:0,integralRatio:"0",pagesUrl:"",orderKey:"",offlinePostage:"",isAuto:!1,isShowAuth:!1,payChannel:"",news:!0,again:!1,addAgain:!1,bargain:!1,combination:!1,secKill:!1,orderInfoVo:{},addressList:[],orderProNum:0,preOrderNo:"",theme:h.globalData.theme,formContent:"",sendAddress:""}},computed:p(p({},(0,c.mapGetters)(["isLogin","systemPlatform","productType"])),{},{markNum:function(){var e=0;if(this.mark)return e=150-this.mark.length,e}}),watch:{isLogin:{handler:function(e,t){e&&this.getloadPreOrder()},deep:!0}},onLoad:function(e){this.payChannel="routine",this.preOrderNo=e.preOrderNo||0,this.addressId=e.addressId||0,this.is_address=!!e.is_address,this.isLogin?this.getloadPreOrder():(0,u.toLogin)()},onShow:function(){var t=this;this.textareaStatus=!0,this.isLogin&&this.toPay,e.$on("handClick",(function(n){n&&(t.system_store=n.address),e.$off("handClick")}))},methods:{getNavH:function(e){this.marTop=e},add:function(e){},subtract:function(e){this.count<=0?(alert("受不了啦，宝贝不能再减少啦"),this.count=0):this.count-=1},getloadPreOrder:function(){var t=this;(0,i.loadPreOrderApi)(this.preOrderNo).then((function(e){var n=e.data.orderInfoVo;t.orderInfoVo=n,t.cartInfo=n.orderDetailList,t.orderProNum=n.orderProNum,t.address.addressId=t.addressId?t.addressId:n.addressId,t.cartArr[1].title="可用余额:"+n.userBalance,t.cartArr[1].payStatus=1===parseInt(e.data.yuePayStatus)?1:2,t.cartArr[0].payStatus=1===parseInt(e.data.payWeixinOpen)?1:0,t.store_self_mention=("1"==e.data.storeSelfMention||"true"==e.data.storeSelfMention)&&"normal"===t.productType,t.$nextTick((function(){this.$refs.addressWindow.getAddressList()}))})).catch((function(t){e.navigateTo({url:"/pages/users/order_list/index"})}))},bindHideKeyAddressinfo1:function(e){this.sendAddress=e.detail.value},onLoadFun:function(){},getList:function(){var t=this,n=e.getStorageSync("user_longitude"),r=e.getStorageSync("user_latitude"),o={latitude:r,longitude:n,page:1,limit:10};(0,d.storeListApi)(o).then((function(e){var n=e.data.list||[];t.$set(t,"storeList",n),t.$set(t,"system_store",n[0])})).catch((function(e){return t.$util.Tips({title:e})}))},changeClose:function(){this.$set(this.address,"address",!1)},showStoreList:function(){this.storeList.length>0&&e.navigateTo({url:"/pages/users/goods_details_store/index"})},computedPrice:function(){var e=this,t=this.shippingType;(0,i.postOrderComputed)({addressId:this.address.addressId,useIntegral:!!this.useIntegral,couponId:this.couponId,shippingType:parseInt(t)+1,preOrderNo:this.preOrderNo}).then((function(t){var n=t.data;e.orderInfoVo.couponFee=n.couponFee,e.orderInfoVo.userIntegral=n.surplusIntegral,e.orderInfoVo.deductionPrice=n.deductionPrice,e.orderInfoVo.freightFee=n.freightFee,e.orderInfoVo.payFee=n.payFee,e.orderInfoVo.proTotalFee=n.proTotalFee,e.orderInfoVo.useIntegral=n.useIntegral,e.orderInfoVo.usedIntegral=n.usedIntegral,e.orderInfoVo.surplusIntegral=n.surplusIntegral})).catch((function(t){return e.$util.Tips({title:t})}))},computedPrice1:function(e){var t=this;this.orderInfoVo.freightFee=e.detail.value;var n=this.shippingType;(0,i.postOrderComputed)({addressId:this.address.addressId,useIntegral:!!this.useIntegral,couponId:this.couponId,freightFee:this.orderInfoVo.freightFee,shippingType:parseInt(n)+1,preOrderNo:this.preOrderNo}).then((function(e){var n=e.data;t.orderInfoVo.couponFee=n.couponFee,t.orderInfoVo.userIntegral=n.surplusIntegral,t.orderInfoVo.deductionPrice=n.deductionPrice,t.orderInfoVo.freightFee=n.freightFee,t.orderInfoVo.payFee=n.payFee,t.orderInfoVo.proTotalFee=n.proTotalFee,t.orderInfoVo.useIntegral=n.useIntegral,t.orderInfoVo.usedIntegral=n.usedIntegral,t.orderInfoVo.surplusIntegral=n.surplusIntegral})).catch((function(e){return t.$util.Tips({title:e})}))},addressType:function(e){var t=e;this.shippingType=parseInt(t),this.computedPrice(),1==t&&this.getList()},bindPickerChange:function(e){var t=e.detail.value;this.shippingType=t,this.computedPrice()},ChangCouponsClone:function(){this.$set(this.coupon,"coupon",!1)},changeTextareaStatus:function(){for(var e=0,t=this.coupon.list.length;e<t;e++)this.coupon.list[e].use_title="",this.coupon.list[e].is_use=0;this.textareaStatus=!0,this.status=0,this.$set(this.coupon,"list",this.coupon.list)},ChangCoupons:function(e){for(var t=e,n=this.coupon.list,r="请选择",o=0,i=0,s=n.length;i<s;i++)i!=t&&(n[i].use_title="",n[i].isUse=0);n[t].isUse?(n[t].use_title="",n[t].isUse=0):(n[t].use_title="不使用",n[t].isUse=1,r=n[t].name,o=n[t].id),this.couponTitle=r,this.couponId=o,this.$set(this.coupon,"coupon",!1),this.$set(this.coupon,"list",n),this.computedPrice()},ChangeIntegral:function(){this.useIntegral=!this.useIntegral,this.computedPrice()},OnDefaultAddress:function(e){this.addressInfo=e,this.address.addressId=e.id,this.addressInfo&&this.computedPrice()},OnChangeAddress:function(e){this.addressInfo=e,this.address.addressId=e.id,this.textareaStatus=!0,this.address.address=!1,this.computedPrice()},getAddressRecognition:function(e){var t=this,n=e.detail.value;this.addressRecognitionDetail!=n.trim&&n.trim&&(0,s.addressRecognition)({addressRecognition:e.detail.value,id:this.addressRecognitionId}).then((function(n){n.data&&(t.addressRecognitionId=n.data.id,t.addressInfo=n.data,t.address.addressId=n.data.id,t.addressRecognitionDetail=e.detail.value,t.addressInfo&&t.computedPrice())})).catch((function(e){return t.$util.Tips({title:e})}))},bindHideKeyboard:function(e){this.mark=e.detail.value},getCouponList:function(){var e=this;(0,i.getCouponsOrderPrice)(this.preOrderNo).then((function(t){e.$set(e.coupon,"list",t.data),e.openType=1}))},getaddressInfo:function(){var e=this;e.addressId?(0,s.getAddressDetail)(e.addressId).then((function(t){t.data&&(t.data.isDefault=parseInt(t.data.isDefault),e.addressInfo=t.data||{},e.addressId=t.data.id||0,e.address.addressId=t.data.id||0)})):getAddressDefault().then((function(t){t.data&&(t.data.isDefault=parseInt(t.data.isDefault),e.addressInfo=t.data||{},e.addressId=t.data.id||0,e.address.addressId=t.data.id||0)}))},payItem:function(e){var t=this,n=e;t.active=n,t.animated=!0,t.payType=t.cartArr[n].value,setTimeout((function(){t.car()}),500)},couponTap:function(){this.coupon.coupon=!0,this.coupon.list.length||this.getCouponList()},car:function(){this.animated=!1},onAddress:function(){this.textareaStatus=!1,this.address.address=!0,this.pagesUrl="/pages/users/user_address_list/index?preOrderNo="+this.preOrderNo},payment:function(t){var n=this;(0,i.orderCreate)(t).then((function(e){n.getOrderPay(e.data.orderNo,"支付成功")})).catch((function(t){return e.hideLoading(),n.$util.Tips({title:t},"/pages/users/order_list/index")}))},getOrderPay:function(t,n){var r=this,o="/pages/order_pay_status/index?order_id="+t;(0,i.orderPay)({orderNo:t,payChannel:r.payChannel,payType:r.payType,scene:"normal"===r.productType?0:1177}).then((function(i){var s=i.data.jsConfig;switch(i.data.payType){case"weixin":r.weixinPay(s,t,o);break;case"yue":return e.hideLoading(),r.$util.Tips({title:n},{tab:5,url:o+"&status=1"});case"weixinh5":e.hideLoading(),setTimeout((function(){location.href=s.mwebUrl+"&redirect_url="+window.location.protocol+"//"+window.location.host+o+"&status=1"}),100);break;case"alipay":break}})).catch((function(t){return e.hideLoading(),r.$util.Tips({title:t})}))},weixinPay:function(t,n,r){var o=this;e.requestPayment({timeStamp:t.timeStamp,nonceStr:t.nonceStr,package:t.packages,signType:t.signType,paySign:t.paySign,ticket:"normal"===o.productType?null:t.ticket,success:function(t){e.hideLoading(),(0,a.openOrderSubscribe)().then((function(){return o.orderInfoVo.bargainId||o.orderInfoVo.combinationId||o.pinkId||o.orderInfoVo.seckillId?o.$util.Tips({title:"支付成功",icon:"success"},{tab:4,url:r}):o.$util.Tips({title:"支付成功",icon:"success"},{tab:5,url:r})}))},fail:function(t){return e.hideLoading(),o.$util.Tips({title:"取消支付"},{tab:5,url:r+"&status=2"})},complete:function(t){if(e.hideLoading(),"requestPayment:cancel"==t.errMsg)return o.$util.Tips({title:"取消支付"},{tab:5,url:r+"&status=2"})}})},SubOrder:function(t){var n,r=this;return r.payType?r.address.addressId||r.shippingType?1==r.shippingType&&0==r.storeList.length?r.$util.Tips({title:"暂无门店,请选择其他方式"}):(n={addressId:r.address.addressId,couponId:r.couponId,payType:r.payType,useIntegral:r.useIntegral,preOrderNo:r.preOrderNo,mark:r.mark,storeId:r.system_store.id||0,shippingType:r.$util.$h.Add(r.shippingType,1),payChannel:r.payChannel,sendAddress:r.sendAddress},"yue"==n.payType&&parseFloat(r.userInfo.nowMoney)<parseFloat(r.totalPrice)?r.$util.Tips({title:"余额不足！"}):(e.showLoading({title:"订单支付中"}),void(0,a.openPaySubscribe)().then((function(){r.payment(n)})))):r.$util.Tips({title:"请选择收货地址"}):r.$util.Tips({title:"请选择支付方式"})}}};t.default=f}).call(this,n("df3c")["default"])},a61e:function(e,t,n){"use strict";n.r(t);var r=n("9c37"),o=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=o.a},b034:function(e,t,n){}},[["59a5","common/runtime","common/vendor"]]]);