<view data-theme="{{theme}}"><view style="height:100%;"><view class="evaluate-list"><view class="generalComment acea-row row-between-wrapper"><view class="acea-row row-middle font_color"><view class="evaluate">评分</view><view class="{{['start','star'+(replyData.sumCount===0?'3':$root.g0)]}}"></view></view><view><text class="font_color">{{replyData.replyChance*100+"%"}}</text>好评率</view></view><view class="nav acea-row row-middle"><view data-event-opts="{{[['tap',[['changeType',[0]]]]]}}" class="{{['item',type==0?'bg-color':'']}}" bindtap="__e">{{"全部("+replyData.sumCount+')'}}</view><view data-event-opts="{{[['tap',[['changeType',[1]]]]]}}" class="{{['item',type==1?'bg-color':'']}}" bindtap="__e">{{"好评("+replyData.goodCount+')'}}</view><view data-event-opts="{{[['tap',[['changeType',[2]]]]]}}" class="{{['item',type==2?'bg-color':'']}}" bindtap="__e">{{"中评("+replyData.inCount+')'}}</view><view data-event-opts="{{[['tap',[['changeType',[3]]]]]}}" class="{{['item',type==3?'bg-color':'']}}" bindtap="__e">{{"差评("+replyData.poorCount+')'}}</view></view><user-evaluation vue-id="75d1685c-1" reply="{{reply}}" bind:__l="__l"></user-evaluation></view><view class="loadingicon acea-row row-center-wrapper"><text class="loading iconfont icon-jiazai" hidden="{{loading==false}}"></text>{{loadTitle+''}}</view><block wx:if="{{!replyData.sumCount&&page>1}}"><view class="noCommodity"><view class="pictrue"><image src="../static/noEvaluate.png"></image></view></view></block></view></view>