<view data-theme="{{theme}}" class="data-v-6691ce81"><nav-bar vue-id="25148134-1" navTitle="{{navTitle}}" class="data-v-6691ce81" bind:__l="__l"></nav-bar><view class="my-order data-v-6691ce81"><view class="header bg_color data-v-6691ce81" style="{{'margin-top:'+(homeTop+'rpx')+';'}}"><view class="picTxt acea-row row-between-wrapper data-v-6691ce81"><view class="text data-v-6691ce81"><view class="name data-v-6691ce81">订单信息</view><view class="data-v-6691ce81">{{"消费订单："+(orderData.orderCount||0)+" 总消费：￥"+(orderData.sumPrice?$root.g0:0)}}</view></view><view class="pictrue data-v-6691ce81"><image src="../../../static/images/orderTime.png" class="data-v-6691ce81"></image></view></view></view><view class="nav acea-row row-around data-v-6691ce81" style="margin-bottom:10px;left:0%;"><view class="acea-row row-between-wrapper input data-v-6691ce81"><text class="iconfont icon-sousuo data-v-6691ce81"></text><input type="text" placeholder="点击搜索订单信息" confirm-type="search" name="search" placeholder-class="placeholder" maxlength="20" data-event-opts="{{[['blur',[['getOrderList1',['$event']]]]]}}" bindblur="__e" class="data-v-6691ce81"/></view></view><view class="nav acea-row row-around data-v-6691ce81"><view data-event-opts="{{[['tap',[['statusClick',[99]]]]]}}" class="{{['item','data-v-6691ce81',orderStatus==99?'on':'']}}" bindtap="__e"><view class="data-v-6691ce81">全部</view><view class="num data-v-6691ce81">{{orderData.orderCount||0}}</view></view><view data-event-opts="{{[['tap',[['statusClick',[0]]]]]}}" class="{{['item','data-v-6691ce81',orderStatus==0?'on':'']}}" bindtap="__e"><view class="data-v-6691ce81">待付款</view><view class="num data-v-6691ce81">{{orderData.unPaidCount||0}}</view></view><view data-event-opts="{{[['tap',[['statusClick',[1]]]]]}}" class="{{['item','data-v-6691ce81',orderStatus==1?'on':'']}}" bindtap="__e"><view class="data-v-6691ce81">待发货</view><view class="num data-v-6691ce81">{{orderData.unShippedCount||0}}</view></view><view data-event-opts="{{[['tap',[['statusClick',[2]]]]]}}" class="{{['item','data-v-6691ce81',orderStatus==2?'on':'']}}" bindtap="__e"><view class="data-v-6691ce81">待收货</view><view class="num data-v-6691ce81">{{orderData.receivedCount||0}}</view></view><view data-event-opts="{{[['tap',[['statusClick',[3]]]]]}}" class="{{['item','data-v-6691ce81',orderStatus==3?'on':'']}}" bindtap="__e"><view class="data-v-6691ce81">待评价</view><view class="num data-v-6691ce81">{{orderData.evaluatedCount||0}}</view></view><view data-event-opts="{{[['tap',[['statusClick',[4]]]]]}}" class="{{['item','data-v-6691ce81',orderStatus==4?'on':'']}}" bindtap="__e"><view class="data-v-6691ce81">已完成</view><view class="num data-v-6691ce81">{{orderData.completeCount||0}}</view></view></view><view class="list data-v-6691ce81"><block wx:for="{{orderList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item data-v-6691ce81"><view data-event-opts="{{[['tap',[['goOrderDetails',['$0'],[[['orderList','',index,'orderId']]]]]]]}}" bindtap="__e" class="data-v-6691ce81"><view class="title acea-row row-between-wrapper data-v-6691ce81"><view class="acea-row row-middle data-v-6691ce81"><block wx:if="{{item.activityType!=='普通'&&item.activityType!=='核销'}}"><text class="sign cart-color acea-row row-center-wrapper data-v-6691ce81">{{item.activityType}}</text></block><view class="data-v-6691ce81">{{item.createTime}}</view></view><view class="font_color data-v-6691ce81">{{item.orderStatus}}</view></view><block wx:for="{{item.orderInfoList}}" wx:for-item="items" wx:for-index="index" wx:key="index"><view class="item-info acea-row row-between row-top data-v-6691ce81"><view class="pictrue data-v-6691ce81"><image src="{{items.image}}" class="data-v-6691ce81"></image></view><view class="text acea-row row-between data-v-6691ce81"><view class="name line2 data-v-6691ce81">{{items.storeName}}</view><view class="money data-v-6691ce81"><view class="data-v-6691ce81">{{"￥"+items.price}}</view><view class="data-v-6691ce81">{{"x"+items.cartNum}}</view></view></view></view></block><view class="totalPrice data-v-6691ce81">{{"单号:"+item.orderId+";地址:"+item.userAddress+''}}</view><view class="totalPrice data-v-6691ce81">{{"共"+item.totalNum+'件商品，总金额'}}<text class="money data-v-6691ce81">{{"￥"+item.payPrice}}</text></view></view><view class="bottom acea-row row-right row-middle data-v-6691ce81"><block wx:if="{{!item.paid}}"><view data-event-opts="{{[['tap',[['cancelOrder',[index,'$0'],[[['orderList','',index,'id']]]]]]]}}" class="bnt cancelBnt data-v-6691ce81" bindtap="__e">取消订单</view></block><block wx:if="{{!item.paid}}"><view data-event-opts="{{[['tap',[['goPay',['$0','$1'],[[['orderList','',index,'payPrice']],[['orderList','',index,'orderId']]]]]]]}}" class="bnt bg_color data-v-6691ce81" bindtap="__e">立即付款</view></block><block wx:else><block wx:if="{{item.status==0||item.status==1||item.status==3}}"><view data-event-opts="{{[['tap',[['goOrderDetails',['$0'],[[['orderList','',index,'orderId']]]]]]]}}" class="bnt bg_color data-v-6691ce81" bindtap="__e">查看详情</view></block><block wx:else><block wx:if="{{item.status==2}}"><view data-event-opts="{{[['tap',[['goOrderDetails',['$0',0],[[['orderList','',index,'orderId']]]]]]]}}" class="bnt bg_color data-v-6691ce81" bindtap="__e">去评价</view></block></block></block><block wx:if="{{item.status==3}}"><view data-event-opts="{{[['tap',[['delOrder',['$0',index],[[['orderList','',index,'id']]]]]]]}}" class="bnt cancelBnt data-v-6691ce81" bindtap="__e">删除订单</view></block></view></view></block></view><view class="loadingicon acea-row row-center-wrapper data-v-6691ce81"><text class="loading iconfont icon-jiazai data-v-6691ce81" hidden="{{loading==false}}"></text>{{($root.g1>0?loadTitle:'')+''}}</view><block wx:if="{{$root.g2}}"><view class="noCart data-v-6691ce81"><view class="pictrue data-v-6691ce81"><image src="/static/images/noOrder.png" class="data-v-6691ce81"></image></view></view></block></view><payment vue-id="25148134-2" payMode="{{payMode}}" pay_close="{{pay_close}}" order_id="{{pay_order_id}}" totalPrice="{{totalPrice}}" data-event-opts="{{[['^onChangeFun',[['onChangeFun']]]]}}" bind:onChangeFun="__e" class="data-v-6691ce81" bind:__l="__l"></payment></view>