(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/users/order_list/index"],{"18cd":function(t,e,i){},"20ef":function(t,e,i){"use strict";var n=i("18cd"),o=i.n(n);o.a},"2b01":function(t,e,i){"use strict";i.r(e);var n=i("ce69"),o=i("a7b0");for(var r in o)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(r);i("20ef");var a=i("828b"),s=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,"6691ce81",null,!1,n["a"],void 0);e["default"]=s.exports},"390f":function(t,e,i){"use strict";(function(t,e){var n=i("47a9");i("9e89");n(i("3240"));var o=n(i("2b01"));t.__webpack_require_UNI_MP_PLUGIN__=i,e(o.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},a7b0:function(t,e,i){"use strict";i.r(e);var n=i("aaf4"),o=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=o.a},aaf4:function(t,e,i){"use strict";(function(t){var n=i("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=i("8d22"),r=(i("e32c"),i("2e55")),a=i("8f59"),s=i("fbb9"),c=(n(i("4bef")),getApp()),u={components:{payment:function(){Promise.all([i.e("common/vendor"),i.e("components/payment/index")]).then(function(){return resolve(i("65d9"))}.bind(null,i)).catch(i.oe)},home:function(){i.e("components/home/<USER>").then(function(){return resolve(i("1e4d"))}.bind(null,i)).catch(i.oe)},emptyPage:function(){i.e("components/emptyPage").then(function(){return resolve(i("7180"))}.bind(null,i)).catch(i.oe)},navBar:function(){Promise.all([i.e("common/vendor"),i.e("components/navBar")]).then(function(){return resolve(i("b165"))}.bind(null,i)).catch(i.oe)}},data:function(){return{navTitle:"我的订单",loading:!1,loadend:!1,loadTitle:"加载更多",orderList:[],orderData:{},orderStatus:0,page:1,f5:1,search:"",limit:20,homeTop:0,payMode:[{name:"微信支付",icon:"icon-weixinzhifu",value:"weixin",title:"微信快捷支付",payStatus:1},{name:"余额支付",icon:"icon-yuezhifu",value:"yue",title:"可用余额:",number:0,payStatus:1}],pay_close:!1,pay_order_id:"",totalPrice:"0",isShow:!1,isAuto:!1,isShowAuth:!1,theme:c.globalData.theme,bgColor:"#e93323"}},computed:(0,a.mapGetters)(["isLogin","userInfo"]),onShow:function(){this.bgColor=(0,s.setThemeColor)(),t.setNavigationBarColor({frontColor:"#ffffff",backgroundColor:this.bgColor}),this.isLogin?(this.loadend=!1,this.page=1,1==this.f5&&(this.$set(this,"orderList",[]),this.getOrderData(),this.getOrderList(),this.f5=2),this.payMode[1].number=this.userInfo.nowMoney,this.$set(this,"payMode",this.payMode)):(0,r.toLogin)()},mounted:function(){this.homeTop=124},methods:{onLoadFun:function(){this.getOrderData(),this.getOrderList()},authColse:function(t){this.isShowAuth=t},onChangeFun:function(t){var e=t,i=e.action||null,n=void 0!=e.value?e.value:null;i&&this[i]&&this[i](n)},payClose:function(){this.pay_close=!1},onLoad:function(t){t.status&&(this.orderStatus=t.status)},getOrderData:function(){var t=this;(0,o.orderData)().then((function(e){t.$set(t,"orderData",e.data)}))},cancelOrder:function(e,i){var n=this;if(!i)return n.$util.Tips({title:"缺少订单号无法取消订单"});t.showModal({content:"确定取消该订单",cancelText:"取消",confirmText:"确定",showCancel:!0,confirmColor:"#f55850",success:function(r){r.confirm&&(t.showLoading({title:"正在取消中"}),(0,o.orderCancel)(i).then((function(i){return t.hideLoading(),n.$util.Tips({title:"取消成功",icon:"success"},(function(){n.orderList.splice(e,1),n.$set(n,"orderList",n.orderList),n.$set(n.orderData,"unpaid_count",n.orderData.unpaid_count-1),n.getOrderData()}))})).catch((function(t){return n.$util.Tips({title:t})})))}})},goPay:function(t,e){this.$set(this,"pay_close",!0),this.$set(this,"pay_order_id",e),this.$set(this,"totalPrice",t)},pay_complete:function(){this.loadend=!1,this.page=1,this.$set(this,"orderList",[]),this.$set(this,"pay_close",!1),this.getOrderData(),this.getOrderList()},pay_fail:function(){this.pay_close=!1},goOrderDetails:function(e,i){if(!e)return that.$util.Tips({title:"缺少订单号无法查看订单详情"});t.navigateTo({url:"/pages/order_details/index?order_id="+e})},statusClick:function(t){t!=this.orderStatus&&(this.orderStatus=t,this.loadend=!1,this.page=1,this.$set(this,"orderList",[]),this.getOrderList())},getOrderList1:function(t){this.search=t.detail.value,this.loadend=!1,this.page=1,this.$set(this,"orderList",[]),this.getOrderList()},getOrderList:function(){var t=this;t.loadend||t.loading||(t.loading=!0,t.loadTitle="加载更多",(0,o.getOrderList)({type:t.orderStatus,page:t.page,search:t.search,limit:t.limit}).then((function(e){var i=e.data.list||[],n=i.length<t.limit;t.orderList=t.$util.SplitArray(i,t.orderList),t.$set(t,"orderList",t.orderList),t.loadend=n,t.loading=!1,t.loadTitle=n?"我也是有底线的":"加载更多",t.page=t.page+1,t.isShow=!0})).catch((function(e){t.loading=!1,t.loadTitle="加载更多"})))},delOrder:function(e,i){var n=this;t.showModal({content:"确定删除该订单",cancelText:"取消",confirmText:"确定",showCancel:!0,confirmColor:"#f55850",success:function(t){if(t.confirm){var r=n;(0,o.orderDel)(e).then((function(t){return r.orderList.splice(i,1),r.$set(r,"orderList",r.orderList),r.$set(r.orderData,"unpaid_count",r.orderData.unpaid_count-1),r.getOrderData(),r.$util.Tips({title:"删除成功",icon:"success"})})).catch((function(t){return r.$util.Tips({title:t})}))}}})}},onReachBottom:function(){this.getOrderList()}};e.default=u}).call(this,i("df3c")["default"])},ce69:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=(this._self._c,this.orderData.sumPrice?Number(this.orderData.sumPrice).toFixed(2):null),i=this.orderList.length,n=0==this.orderList.length&&this.isShow&&!this.loading;this.$mp.data=Object.assign({},{$root:{g0:e,g1:i,g2:n}})},o=[]}},[["390f","common/runtime","common/vendor"]]]);