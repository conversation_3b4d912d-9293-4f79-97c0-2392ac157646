(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/users/user_get_coupon/index"],{"2c27":function(t,n,e){"use strict";var o=e("e50f"),i=e.n(o);i.a},"658e":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var o=e("ad96"),i=e("2e55"),u=e("8f59"),s=getApp(),a={components:{authorize:function(){Promise.all([e.e("common/vendor"),e.e("components/Authorize")]).then(function(){return resolve(e("8de5"))}.bind(null,e)).catch(e.oe)}},data:function(){return{couponsList:[],loading:!1,loadend:!1,loadTitle:"加载更多",page:1,limit:20,isAuto:!1,isShowAuth:!1,type:1,isShow:!1,navList:[{type:1,name:"通用券",count:0},{type:2,name:"商品券",count:0},{type:3,name:"品类券",count:0}],count:0,theme:s.globalData.theme}},computed:(0,u.mapGetters)(["isLogin"]),watch:{isLogin:{handler:function(t,n){t&&this.getUseCoupons()},deep:!0}},onLoad:function(){this.isLogin?this.getUseCoupons():(0,i.toLogin)()},onReachBottom:function(){this.getUseCoupons()},methods:{onLoadFun:function(){this.getUseCoupons()},authColse:function(t){this.isShowAuth=t},getCoupon:function(t,n){var e=this,i=e.couponsList;[].push(t),(0,o.setCouponReceive)(t).then((function(t){i[n].isUse=!0,e.$set(e,"couponsList",i),e.$util.Tips({title:"领取成功"})}),(function(t){return e.$util.Tips({title:t})}))},getUseCoupons:function(){var t=this;return!t.loadend&&(!t.loading&&(t.loading=!0,void(0,o.getCoupons)({page:t.page,limit:t.limit,type:t.type}).then((function(n){var e=n.data,o=e.length<t.limit,i=t.$util.SplitArray(e,t.couponsList);t.$set(t,"couponsList",i),t.loadend=o,t.loadTitle=o?"我也是有底线的":"加载更多",t.page=t.page+1,t.loading=!1,t.isShow=!0})).catch((function(n){t.loading=!1,t.loadTitle="加载更多"}))))},setType:function(t){this.type!==t&&(this.type=t,this.couponsList=[],this.page=1,this.loadend=!1,this.getUseCoupons())}}};n.default=a},"759f":function(t,n,e){"use strict";e.d(n,"b",(function(){return o})),e.d(n,"c",(function(){return i})),e.d(n,"a",(function(){}));var o=function(){var t=this,n=t.$createElement,e=(t._self._c,t.couponsList.length),o=e?t.__map(t.couponsList,(function(n,e){var o=t.__get_orig(n),i=n.money?Number(n.money):null,u=n.minPrice?Number(n.minPrice):null;return{$orig:o,m0:i,m1:u}})):null,i=t.couponsList.length,u=!t.couponsList.length&&t.isShow&&!t.loading;t.$mp.data=Object.assign({},{$root:{g0:e,l0:o,g1:i,g2:u}})},i=[]},"7bb7":function(t,n,e){"use strict";e.r(n);var o=e("658e"),i=e.n(o);for(var u in o)["default"].indexOf(u)<0&&function(t){e.d(n,t,(function(){return o[t]}))}(u);n["default"]=i.a},c651:function(t,n,e){"use strict";(function(t,n){var o=e("47a9");e("9e89");o(e("3240"));var i=o(e("ebdb"));t.__webpack_require_UNI_MP_PLUGIN__=e,n(i.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},e50f:function(t,n,e){},ebdb:function(t,n,e){"use strict";e.r(n);var o=e("759f"),i=e("7bb7");for(var u in i)["default"].indexOf(u)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(u);e("2c27");var s=e("828b"),a=Object(s["a"])(i["default"],o["b"],o["c"],!1,null,"03af1d8e",null,!1,o["a"],void 0);n["default"]=a.exports}},[["c651","common/runtime","common/vendor"]]]);