(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/users/goods_details_store/index"],{"01e7":function(t,e,n){"use strict";var i=n("3082"),o=n.n(i);o.a},3082:function(t,e,n){},3613:function(t,e,n){"use strict";n.r(e);var i=n("cc48"),o=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=o.a},"7c3f":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){}));var i=function(){var t=this.$createElement;this._self._c},o=[]},"8e27":function(t,e,n){"use strict";n.r(e);var i=n("7c3f"),o=n("3613");for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);n("01e7");var u=n("828b"),s=Object(u["a"])(o["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=s.exports},ad5b:function(t,e,n){"use strict";(function(t,e){var i=n("47a9");n("9e89");i(n("3240"));var o=i(n("8e27"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(o.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},cc48:function(t,e,n){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n("d9cf"),o=(n("342a"),n("8f59"),{name:"storeList",components:{Loading:function(){n.e("components/Loading/index").then(function(){return resolve(n("00d1"))}.bind(null,n)).catch(n.oe)}},data:function(){return{page:1,limit:20,loaded:!1,loading:!1,storeList:[],system_store:{},locationShow:!1,user_latitude:0,user_longitude:0}},onLoad:function(){try{this.user_latitude=t.getStorageSync("user_latitude"),this.user_longitude=t.getStorageSync("user_longitude")}catch(e){}},mounted:function(){this.user_latitude&&this.user_longitude||this.selfLocation(),this.getList()},methods:{call:function(e){t.makePhoneCall({phoneNumber:e})},selfLocation:function(){var e=this,n=this;t.getLocation({type:"wgs84",success:function(i){try{e.user_latitude=i.latitude,e.user_longitude=i.longitude,t.setStorageSync("user_latitude",i.latitude),t.setStorageSync("user_longitude",i.longitude)}catch(o){}n.getList()},complete:function(){n.getList()}})},showMaoLocation:function(e){t.openLocation({latitude:Number(e.latitude),longitude:Number(e.longitude),name:e.name,address:"".concat(e.address,"-").concat(e.detailedAddress),success:function(){console.log("success")}})},checked:function(e){t.$emit("handClick",{address:e}),t.navigateBack()},getList:function(){var t=this;if(!this.loading&&!this.loaded){this.loading=!0;var e={latitude:this.user_latitude||"",longitude:this.user_longitude||"",page:this.page,limit:this.limit};(0,i.storeListApi)(e).then((function(e){t.loading=!1,t.loaded=e.data.list.length<t.limit,t.storeList.push.apply(t.storeList,e.data.list),t.page=t.page+1})).catch((function(e){t.$dialog.error(e)}))}}},onReachBottom:function(){this.getList()}});e.default=o}).call(this,n("df3c")["default"])}},[["ad5b","common/runtime","common/vendor"]]]);