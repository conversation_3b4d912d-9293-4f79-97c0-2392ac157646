<view data-theme="{{theme}}" class="data-v-814d3d5e"><view class="newsDetail data-v-814d3d5e" style="{{'background-color:'+(bgColor)+';'}}"><view class="title data-v-814d3d5e">{{articleInfo.title}}</view><view class="list acea-row row-middle data-v-814d3d5e"><view class="label data-v-814d3d5e">{{articleInfo.author}}</view><view class="item data-v-814d3d5e">{{articleInfo.createTime}}</view><view class="item data-v-814d3d5e"><text class="iconfont icon-liulan data-v-814d3d5e"></text>{{articleInfo.visit}}</view></view><view class="conter data-v-814d3d5e"><jyf-parser vue-id="46270d98-1" html="{{content}}" tag-style="{{tagStyle}}" data-ref="article" class="data-v-814d3d5e vue-ref" bind:__l="__l"></jyf-parser></view><block wx:if="{{store_info.id}}"><view class="picTxt acea-row row-between-wrapper data-v-814d3d5e"><view class="pictrue data-v-814d3d5e"><image src="{{store_info.image}}" class="data-v-814d3d5e"></image></view><view class="text data-v-814d3d5e"><view class="name line1 data-v-814d3d5e">{{store_info.storeName}}</view><view class="money price_color data-v-814d3d5e">￥<text class="num data-v-814d3d5e">{{store_info.price}}</text></view><view class="y_money data-v-814d3d5e">{{"￥"+store_info.otPrice}}</view></view><navigator class="label data-v-814d3d5e" url="{{'/pages/goods_details/index?id='+store_info.id}}" hover-class="none"><text class="span data-v-814d3d5e">查看商品</text></navigator></view></block><button class="bnt bg_color data-v-814d3d5e" open-type="share" hover-class="none">和好友一起分享</button></view><share-info vue-id="46270d98-2" shareInfoStatus="{{shareInfoStatus}}" data-event-opts="{{[['^setShareInfoStatus',[['setShareInfoStatus']]]]}}" bind:setShareInfoStatus="__e" class="data-v-814d3d5e" bind:__l="__l"></share-info><view class="article_theme data-v-814d3d5e"></view></view>