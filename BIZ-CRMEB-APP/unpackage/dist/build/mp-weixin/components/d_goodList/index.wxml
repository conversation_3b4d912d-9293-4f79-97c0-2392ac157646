<view class="goodsList"><block wx:for="{{tempArr}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goDetail',['$0'],[[['tempArr','',index]]]]]]]}}" class="item" bindtap="__e"><view class="pictrue"><block wx:if="{{item.recommend_image}}"><image src="{{item.recommend_image}}" mode="aspectFill"></image></block><block wx:else><image src="{{item.image}}" mode="aspectFill"></image></block></view><view class="text line2">{{item.storeName}}</view><view class="bottom acea-row row-between-wrapper"><view class="sales acea-row row-middle"><view class="money"><text>￥</text>{{item.price+''}}<text class="item_sales">{{"已售 "+item.sales}}</text></view></view><block wx:if="{{item.stock>0}}"><view><block wx:if="{{item.activity&&(item.activity.type==='1'||item.activity.type==='2'||item.activity.type==='3')}}"><view class="bnt">立即购买</view></block><block wx:else><view><view data-event-opts="{{[['tap',[['goCartDuo',['$0'],[[['tempArr','',index]]]]]]]}}" class="bnt" catchtap="__e">加入购物车<block wx:if="{{item.cartNum}}"><view class="num">{{item.cartNum}}</view></block></view></view></block></view></block><block wx:else><view class="end">已售罄</view></block></view></view></block></view>