<view style="touch-action:none;" class="data-v-7b6e2afe"><view class="home data-v-7b6e2afe" style="{{'position:fixed;'+('top:'+(top+'px')+';')}}" id="right-nav" data-event-opts="{{[['touchmove',[['setTouchMove',['$event']]]]]}}" catchtouchmove="__e"><block wx:if="{{homeActive}}"><view class="{{['homeCon','bg-color-red','data-v-7b6e2afe',homeActive===true?'on':'']}}"><navigator class="iconfont icon-shouye-xianxing data-v-7b6e2afe" hover-class="none" url="/pages/index/index" open-type="switchTab"></navigator><navigator class="iconfont icon-caigou-xianxing data-v-7b6e2afe" hover-class="none" url="/pages/order_addcart/order_addcart" open-type="switchTab"></navigator><navigator class="iconfont icon-yonghu1 data-v-7b6e2afe" hover-class="none" url="/pages/user/index" open-type="switchTab"></navigator></view></block><view data-event-opts="{{[['tap',[['open',['$event']]]]]}}" class="pictrueBox data-v-7b6e2afe" bindtap="__e"><view class="pictrue data-v-7b6e2afe"><image class="image data-v-7b6e2afe" src="{{homeActive===true?'/static/images/close.gif':'/static/images/open.gif'}}"></image></view></view></view></view>