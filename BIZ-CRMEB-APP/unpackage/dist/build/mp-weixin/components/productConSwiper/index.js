(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/productConSwiper/index"],{"229e":function(t,n,e){},"63f3":function(t,n,e){"use strict";e.r(n);var i=e("7cf7"),o=e.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(r);n["default"]=o.a},"7cf7":function(t,n,e){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var i=e("fbb9"),o={props:{imgUrls:{type:Array,default:function(){return[]}},videoline:{type:String,value:""}},data:function(){return{indicatorDots:!0,circular:!0,autoplay:!0,interval:3e3,duration:500,currents:"1",controls:!0,isPlay:!0,videoContext:"",indicatorBg:"#e93323"}},created:function(){this.indicatorBg=(0,i.setThemeColor)()},mounted:function(){this.videoline&&this.imgUrls.shift(),this.videoContext=t.createVideoContext("myVideo",this)},methods:{videoPause:function(t){},bindPause:function(){this.videoContext.play(),this.$set(this,"controls",!1),this.autoplay=!1},change:function(t){this.$set(this,"currents",t.detail.current+1)}}};n.default=o}).call(this,e("df3c")["default"])},"83fb":function(t,n,e){"use strict";e.d(n,"b",(function(){return i})),e.d(n,"c",(function(){return o})),e.d(n,"a",(function(){}));var i=function(){var t=this.$createElement;this._self._c},o=[]},"867f":function(t,n,e){"use strict";e.r(n);var i=e("83fb"),o=e("63f3");for(var r in o)["default"].indexOf(r)<0&&function(t){e.d(n,t,(function(){return o[t]}))}(r);e("927b");var u=e("828b"),c=Object(u["a"])(o["default"],i["b"],i["c"],!1,null,"4ae89226",null,!1,i["a"],void 0);n["default"]=c.exports},"927b":function(t,n,e){"use strict";var i=e("229e"),o=e.n(i);o.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/productConSwiper/index-create-component',
    {
        'components/productConSwiper/index-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("867f"))
        })
    },
    [['components/productConSwiper/index-create-component']]
]);
