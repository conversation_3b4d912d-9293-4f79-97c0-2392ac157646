<view class="data-v-03d147f5"><view class="{{['address-window','data-v-03d147f5',address.address==true?'on':'']}}"><view class="title data-v-03d147f5">选择地址<text data-event-opts="{{[['tap',[['close',['$event']]]]]}}" class="iconfont icon-guanbi data-v-03d147f5" bindtap="__e"></text></view><view class="list data-v-03d147f5"><block wx:for="{{addressList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['tapAddress',[index,'$0'],[[['addressList','',index,'id']]]]]]]}}" class="{{['item','acea-row','row-between-wrapper','data-v-03d147f5',active==index?'font_color':'']}}" bindtap="__e"><text class="{{['iconfont','icon-ditu','data-v-03d147f5',active==index?'font_color':'']}}"></text><view class="address data-v-03d147f5"><view class="{{['name','data-v-03d147f5',active==index?'font_color':'']}}">{{item.realName}}<text class="phone data-v-03d147f5">{{item.phone}}</text></view><view class="line1 data-v-03d147f5">{{item.province+item.city+item.district+item.detail}}</view></view><text class="{{['iconfont','icon-complete','data-v-03d147f5',active==index?'font-color':'']}}"></text></view></block></view><block wx:if="{{$root.g0}}"><view class="pictrue data-v-03d147f5"><image src="../../static/images/noAddress.png" class="data-v-03d147f5"></image></view></block><view data-event-opts="{{[['tap',[['goAddressPages',['$event']]]]]}}" class="addressBnt bg_color data-v-03d147f5" bindtap="__e">选择其他地址</view></view><view class="mask data-v-03d147f5" catchtouchmove="true" hidden="{{address.address==false}}" data-event-opts="{{[['tap',[['close',['$event']]]]]}}" bindtap="__e"></view></view>