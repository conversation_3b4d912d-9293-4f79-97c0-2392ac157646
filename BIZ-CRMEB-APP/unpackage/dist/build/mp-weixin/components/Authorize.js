(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/Authorize"],{"0c11":function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var o=function(){var t=this.$createElement;this._self._c},a=[]},3463:function(t,e,n){"use strict";var o=n("3604"),a=n.n(o);a.a},3604:function(t,e,n){},"4a49":function(t,e,n){"use strict";(function(t){var o=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=o(n("63e6")),i=n("84f5"),u=n("6152"),r=n("8f59"),s=o(n("f0ff")),f=getApp(),c={name:"Authorize",props:{isAuto:{type:Boolean,default:!0},isGoIndex:{type:Boolean,default:!0},isShowAuth:{type:Boolean,default:!1}},data:function(){return{logoUrl:""}},computed:(0,r.mapGetters)(["isLogin","userInfo"]),watch:{isLogin:function(t){!0===t&&this.$emit("onLoadFun",this.userInfo)}},created:function(){this.getLogoUrl(),this.setAuthStatus()},methods:{setAuthStatus:function(){var t=this;s.default.authorize().then((function(e){!1===e.islogin?t.setUserInfo():t.$emit("onLoadFun",t.userInfo)})).catch((function(e){t.isAuto&&t.$emit("authColse",!0)}))},getUserInfo:function(e){var n=this;s.default.getUserInfo().then((function(o){var a=o.userInfo;a.code=e,a.spread_spid=f.globalData.spread,a.spread_code=f.globalData.code,a.avatar=a.userInfo.avatarUrl,a.city=a.userInfo.city,a.country=a.userInfo.country,a.nickName=a.userInfo.nickName,a.province=a.userInfo.province,a.sex=a.userInfo.gender,s.default.authUserInfo(e,a).then((function(e){t.hideLoading(),n.$emit("authColse",!1),n.$emit("onLoadFun",n.userInfo)})).catch((function(e){t.hideLoading(),t.showToast({title:e.message,icon:"none",duration:2e3})}))})).catch((function(e){t.hideLoading()}))},setUserInfo:function(){var e=this;t.showLoading({title:"正在登录中"}),s.default.getCode().then((function(t){e.getUserInfo(t)})).catch((function(e){t.hideLoading()}))},getLogoUrl:function(){var t=this;a.default.has(u.LOGO_URL)?this.logoUrl=a.default.get(u.LOGO_URL):(0,i.getLogo)().then((function(e){t.logoUrl=e.data.logoUrl,a.default.set(u.LOGO_URL,t.logoUrl)}))},close:function(){var e=getCurrentPages();e[e.length-1];this.isGoIndex?t.switchTab({url:"/pages/index/index"}):this.$emit("authColse",!1)}}};e.default=c}).call(this,n("df3c")["default"])},"8de5":function(t,e,n){"use strict";n.r(e);var o=n("0c11"),a=n("b87c");for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);n("3463");var u=n("828b"),r=Object(u["a"])(a["default"],o["b"],o["c"],!1,null,"00a5bc1f",null,!1,o["a"],void 0);e["default"]=r.exports},b87c:function(t,e,n){"use strict";n.r(e);var o=n("4a49"),a=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);e["default"]=a.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/Authorize-create-component',
    {
        'components/Authorize-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("8de5"))
        })
    },
    [['components/Authorize-create-component']]
]);
