(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/accredit/index"],{"212a":function(t,e,n){},4093:function(t,e,n){"use strict";n.r(e);var o=n("9840"),i=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);e["default"]=i.a},"48d2":function(t,e,n){"use strict";var o=n("212a"),i=n.n(o);i.a},7667:function(t,e,n){"use strict";n.r(e);var o=n("de77"),i=n("4093");for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);n("48d2");var u=n("828b"),c=Object(u["a"])(i["default"],o["b"],o["c"],!1,null,"5400057e",null,!1,o["a"],void 0);e["default"]=c.exports},9840:function(t,e,n){"use strict";(function(t){var o=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=o(n("f0ff")),a=n("20ce"),u=n("84f5"),c=(getApp(),{name:"",props:{locationType:{type:Boolean,default:!1},userPhoneType:{type:Boolean,default:!1},authKey:{type:String,default:""},isPhoneBox:{type:Boolean,default:!1},content:{type:String,default:"申请获取用于完整服务"}},data:function(){return{isStatus:!1}},methods:{modelCancel:function(){this.$emit("closeModel",{isStatus:this.isStatus})},modelConfirm:function(){this.$emit("confirmModel")},getphonenumber:function(e){var n=this;t.showLoading({title:"加载中"}),i.default.getCode().then((function(t){n.getUserPhoneNumber(e.detail.encryptedData,e.detail.iv,t)})).catch((function(e){t.hideLoading()}))},getUserPhoneNumber:function(e,n,o){var i=this;(0,u.getUserPhone)({encryptedData:e,iv:n,code:o,key:this.authKey,type:"routine"}).then((function(t){i.$store.commit("LOGIN",{token:t.data.token}),i.$store.commit("SETUID",t.data.uid),i.getUserInfo()})).catch((function(e){t.hideLoading(),i.$util.Tips({title:e})}))},getUserInfo:function(){var e=this,n=this;(0,a.getUserInfo)().then((function(o){t.hideLoading(),n.$store.commit("UPDATE_USERINFO",o.data),n.isStatus=!0,e.modelCancel()}))}}});e.default=c}).call(this,n("df3c")["default"])},de77:function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){}));var o=function(){var t=this.$createElement;this._self._c},i=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/accredit/index-create-component',
    {
        'components/accredit/index-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("7667"))
        })
    },
    [['components/accredit/index-create-component']]
]);
