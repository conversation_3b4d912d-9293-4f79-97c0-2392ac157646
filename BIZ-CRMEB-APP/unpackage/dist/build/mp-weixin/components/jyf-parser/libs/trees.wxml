<wxs src="./handler.wxs" module="handler"></wxs>
<view class="interlayer"><block wx:for="{{nodes}}" wx:for-item="n" wx:for-index="index" wx:key="index"><block><block wx:if="{{n.name=='img'}}"><rich-text class="_img" style="{{(''+handler.getStyle(n.attrs.style))}}" id="{{n.attrs.id}}" nodes="{{handler.getNode(n,!lazyLoad||imgLoad)}}" data-attrs="{{n.attrs}}" data-event-opts="{{[['tap',[['imgtap',['$event']]]],['longpress',[['imglongtap',['$event']]]]]}}" bindtap="__e" bindlongpress="__e"></rich-text></block><block wx:else><block wx:if="{{n.decode}}"><rich-text class="_entity" nodes="{{[n]}}"></rich-text></block><block wx:else><block wx:if="{{n.type=='text'}}"><text decode="{{true}}">{{n.text}}</text></block><block wx:else><block wx:if="{{n.name=='br'}}"><text>\n</text></block><block wx:else><block wx:if="{{n.name=='video'}}"><view><block wx:if="{{(!loadVideo||n.lazyLoad)&&!(controls[n.attrs.id]&&controls[n.attrs.id].play)}}"><view class="{{['_video '+(n.attrs.class||'')]}}" style="{{(n.attrs.style)}}" id="{{n.attrs.id}}" data-event-opts="{{[['tap',[['_loadVideo',['$event']]]]]}}" bindtap="__e"></view></block><block wx:else><video class="{{[n.attrs.class]}}" style="{{(n.attrs.style)}}" id="{{n.attrs.id}}" autoplay="{{n.attrs.autoplay||controls[n.attrs.id]&&controls[n.attrs.id].play}}" controls="{{n.attrs.controls}}" loop="{{n.attrs.loop}}" poster="{{n.attrs.poster}}" src="{{n.attrs.source[controls[n.attrs.id]&&controls[n.attrs.id].index||0]}}" unit-id="{{n.attrs['unit-id']}}" data-id="{{n.attrs.id}}" data-from="video" data-source="source" data-event-opts="{{[['error',[['error',['$event']]]],['play',[['play',['$event']]]]]}}" muted="{{n.attrs.muted}}" binderror="__e" bindplay="__e"></video></block></view></block><block wx:else><block wx:if="{{n.name=='audio'}}"><audio class="{{[n.attrs.class]}}" style="{{(n.attrs.style)}}" author="{{n.attrs.author}}" autoplay="{{n.attrs.autoplay}}" controls="{{n.attrs.controls}}" loop="{{n.attrs.loop}}" name="{{n.attrs.name}}" poster="{{n.attrs.poster}}" src="{{n.attrs.source[controls[n.attrs.id]&&controls[n.attrs.id].index||0]}}" data-id="{{n.attrs.id}}" data-from="audio" data-source="source" data-event-opts="{{[['error',[['error',['$event']]]],['play',[['play',['$event']]]]]}}" binderror="__e" bindplay="__e"></audio></block><block wx:else><block wx:if="{{n.name=='a'}}"><view class="{{['_a '+(n.attrs.class||'')]}}" style="{{(n.attrs.style)}}" hover-class="_hover" data-attrs="{{n.attrs}}" data-event-opts="{{[['tap',[['linkpress',['$event']]]]]}}" bindtap="__e"><trees class="_span" vue-id="{{'676b0423-1-'+index}}" nodes="{{n.children}}" bind:__l="__l"></trees></view></block><block wx:else><block wx:if="{{n.name=='li'}}"><view class="{{[n.attrs.class]}}" style="{{((n.attrs.style||'')+';display:flex')}}" id="{{n.attrs.id}}"><block wx:if="{{n.type=='ol'}}"><view class="_ol-bef">{{n.num}}</view></block><block wx:else><view class="_ul-bef"><block wx:if="{{n.floor%3==0}}"><view class="_ul-p1">█</view></block><block wx:else><block wx:if="{{n.floor%3==2}}"><view class="_ul-p2"></view></block><block wx:else><view class="_ul-p1" style="border-radius:50%;">█</view></block></block></view></block><trees class="_li" vue-id="{{'676b0423-2-'+index}}" nodes="{{n.children}}" lazyLoad="{{lazyLoad}}" loadVideo="{{loadVideo}}" bind:__l="__l"></trees></view></block><block wx:else><block wx:if="{{n.name=='table'&&n.c}}"><view class="{{[n.attrs.class]}}" style="{{((n.attrs.style||'')+';display:table')}}" id="{{n.attrs.id}}"><block wx:for="{{n.children}}" wx:for-item="tbody" wx:for-index="i" wx:key="i"><view class="{{[tbody.attrs.class]}}" style="{{((tbody.attrs.style||'')+(tbody.name[0]=='t'?';display:table-'+(tbody.name=='tr'?'row':'row-group'):''))}}"><block wx:for="{{tbody.children}}" wx:for-item="tr" wx:for-index="j" wx:key="j"><view class="{{[tr.attrs.class]}}" style="{{((tr.attrs.style||'')+(tr.name[0]=='t'?';display:table-'+(tr.name=='tr'?'row':'cell'):''))}}"><block wx:if="{{tr.name=='td'}}"><trees vue-id="{{'676b0423-3-'+index+'-'+i+'-'+j}}" nodes="{{tr.children}}" lazyLoad="{{lazyLoad}}" loadVideo="{{loadVideo}}" bind:__l="__l"></trees></block><block wx:else><block><block wx:for="{{tr.children}}" wx:for-item="td" wx:for-index="k" wx:key="k"><trees class="{{[td.attrs.class]}}" style="{{((td.attrs.style||'')+(td.name[0]=='t'?';display:table-'+(td.name=='tr'?'row':'cell'):''))}}" vue-id="{{'676b0423-4-'+index+'-'+i+'-'+j+'-'+k}}" nodes="{{td.children}}" lazyLoad="{{lazyLoad}}" loadVideo="{{loadVideo}}" bind:__l="__l"></trees></block></block></block></view></block></view></block></view></block><block wx:else><block wx:if="{{handler.useRichText(n)}}"><rich-text class="{{['_p __'+n.name]}}" id="{{n.attrs.id}}" nodes="{{[n]}}"></rich-text></block><block wx:else><trees class="{{[(n.attrs.id||'')+' _'+n.name+' '+(n.attrs.class||'')]}}" style="{{(n.attrs.style)}}" vue-id="{{'676b0423-5-'+index}}" nodes="{{n.children}}" lazyLoad="{{lazyLoad}}" loadVideo="{{loadVideo}}" bind:__l="__l"></trees></block></block></block></block></block></block></block></block></block></block></block></block></view>