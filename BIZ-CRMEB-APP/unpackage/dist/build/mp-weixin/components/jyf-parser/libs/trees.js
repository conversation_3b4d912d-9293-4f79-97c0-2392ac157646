(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/jyf-parser/libs/trees"],{"01e0":function(t,e,r){"use strict";var i=r("6dd3"),n=r.n(i);n.a},"6dd3":function(t,e,r){},7720:function(t,e,r){"use strict";(function(t,i){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,t.Parser={};var n={components:{trees:function(){Promise.resolve().then(function(){return resolve(r("b950"))}.bind(null,r)).catch(r.oe)}},name:"trees",data:function(){return{controls:{},imgLoad:!1,loadVideo:!0}},props:{nodes:Array,lazyLoad:Boolean},mounted:function(){this.top=this.$parent;while("parser"!=this.top.$options.name){if(this.top.top){this.top=this.top.top;break}this.top=this.top.$parent}},beforeDestroy:function(){this.observer&&this.observer.disconnect()},methods:{play:function(t){if(this.top.videoContexts.length>1&&this.top.autopause)for(var e=this.top.videoContexts.length;e--;)this.top.videoContexts[e].id!=t.currentTarget.dataset.id&&this.top.videoContexts[e].pause()},imgtap:function(e){var r=e.currentTarget.dataset.attrs;if(!r.ignore){var n=!0,o={id:e.target.id,src:r.src,ignore:function(){return n=!1}};if(t.Parser.onImgtap&&t.Parser.onImgtap(o),this.top.$emit("imgtap",o),n){var s=this.top.imgList,a=s[r.i]?parseInt(r.i):(s=[r.src],0);i.previewImage({current:a,urls:s})}}},imglongtap:function(t){var e=t.item.dataset.attrs;e.ignore||this.top.$emit("imglongtap",{id:t.target.id,src:e.src})},linkpress:function(e){var r=!0,n=e.currentTarget.dataset.attrs;if(n.ignore=function(){return r=!1},t.Parser.onLinkpress&&t.Parser.onLinkpress(n),this.top.$emit("linkpress",n),r){if(n["app-id"])return i.navigateToMiniProgram({appId:n["app-id"],path:n.path});n.href&&("#"==n.href[0]?this.top.useAnchor&&this.top.navigateTo({id:n.href.substring(1)}):0==n.href.indexOf("http")||0==n.href.indexOf("//")?i.setClipboardData({data:n.href,success:function(){return i.showToast({title:"链接已复制"})}}):i.navigateTo({url:n.href}))}},error:function(t){var e,r=t.currentTarget,n=r.dataset.from;if("video"==n||"audio"==n){var o=this.controls[r.id]?this.controls[r.id].index+1:1;o<r.dataset.source.length&&this.$set(this.controls,r.id+".index",o),"video"==n&&(e=i.createVideoContext(r.id,this))}this.top&&this.top.$emit("error",{source:n,target:r,errMsg:t.detail.errMsg,errCode:t.detail.errCode,context:e})},_loadVideo:function(t){this.$set(this.controls,t.currentTarget.id,{play:!0,index:0})}}};e.default=n}).call(this,r("0ee4"),r("df3c")["default"])},"8b38":function(t,e,r){"use strict";r.d(e,"b",(function(){return i})),r.d(e,"c",(function(){return n})),r.d(e,"a",(function(){}));var i=function(){var t=this.$createElement;this._self._c},n=[]},"8ef6":function(t,e,r){"use strict";e["a"]=function(t){t.options.wxsCallMethods||(t.options.wxsCallMethods=[])}},"9ec3":function(t,e,r){"use strict";r.r(e);var i=r("7720"),n=r.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){r.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},b950:function(t,e,r){"use strict";r.r(e);var i=r("8b38"),n=r("9ec3");for(var o in n)["default"].indexOf(o)<0&&function(t){r.d(e,t,(function(){return n[t]}))}(o);r("01e0");var s=r("828b"),a=r("8ef6"),u=Object(s["a"])(n["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);"function"===typeof a["a"]&&Object(a["a"])(u),e["default"]=u.exports}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/jyf-parser/libs/trees-create-component',
    {
        'components/jyf-parser/libs/trees-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("b950"))
        })
    },
    [['components/jyf-parser/libs/trees-create-component']]
]);
