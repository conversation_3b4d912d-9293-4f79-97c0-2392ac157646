(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/promotionGood/index"],{"3fea":function(t,n,e){"use strict";e.d(n,"b",(function(){return a})),e.d(n,"c",(function(){return i})),e.d(n,"a",(function(){}));var a=function(){var t=this,n=t.$createElement,e=(t._self._c,t.benefit.slice(0,6));t._isMounted||(t.e0=function(n,e){var a=arguments[arguments.length-1].currentTarget.dataset,i=a.eventParams||a["event-params"];e=i.item;return t.goDetail(e)}),t.$mp.data=Object.assign({},{$root:{l0:e}})},i=[]},"71f1":function(t,n,e){},"8f83":function(t,n,e){"use strict";e.r(n);var a=e("3fea"),i=e("f628");for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);e("9b8e");var u=e("828b"),r=Object(u["a"])(i["default"],a["b"],a["c"],!1,null,"03f63b8a",null,!1,a["a"],void 0);n["default"]=r.exports},"9b8e":function(t,n,e){"use strict";var a=e("71f1"),i=e.n(a);i.a},b556:function(t,n,e){"use strict";(function(t){var a=e("47a9");Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var i=a(e("4bef")),o={props:{benefit:{type:Array,default:function(){return[]}}},data:function(){return{}},methods:{goDetail:function(n){t.navigateTo({animationType:i.default.type,animationDuration:i.default.duration,url:"/pages/goods_details/index?id=".concat(n.id)})}}};n.default=o}).call(this,e("df3c")["default"])},f628:function(t,n,e){"use strict";e.r(n);var a=e("b556"),i=e.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);n["default"]=i.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/promotionGood/index-create-component',
    {
        'components/promotionGood/index-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("8f83"))
        })
    },
    [['components/promotionGood/index-create-component']]
]);
