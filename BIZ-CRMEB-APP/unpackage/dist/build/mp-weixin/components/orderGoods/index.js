(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/orderGoods/index"],{3117:function(t,e,n){"use strict";n.r(e);var u=n("d9cfe"),r=n.n(u);for(var o in u)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return u[t]}))}(o);e["default"]=r.a},"5ca4":function(t,e,n){},b5d5:function(t,e,n){"use strict";n.r(e);var u=n("ee87"),r=n("3117");for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);n("b66d");var a=n("828b"),c=Object(a["a"])(r["default"],u["b"],u["c"],!1,null,"098c4d7c",null,!1,u["a"],void 0);e["default"]=c.exports},b66d:function(t,e,n){"use strict";var u=n("5ca4"),r=n.n(u);r.a},d9cfe:function(t,e,n){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={props:{evaluate:{type:Number,default:0},cartInfo:{type:Array,default:function(){return[]}},orderId:{type:String,default:""},ids:{type:Number,default:0},jump:{type:Boolean,default:!1},orderProNum:{type:Number,default:function(){return 0}},productType:{type:Number,default:function(){return 0}}},data:function(){return{totalNmu:""}},watch:{cartInfo:function(t,e){var n=0;t.forEach((function(t,e){n+=t.cartNum})),this.totalNmu=n}},methods:{evaluateTap:function(e){t.navigateTo({url:"/pages/users/goods_comment_con/index?unique="+e.attrId+"&orderId="+this.orderId+"&id="+this.ids})},jumpCon:function(e){var n=0==this.productType?"normal":"video";this.jump&&t.navigateTo({url:"/pages/goods_details/index?id=".concat(e,"&type=").concat(n)})}}};e.default=n}).call(this,n("df3c")["default"])},ee87:function(t,e,n){"use strict";n.d(e,"b",(function(){return u})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){}));var u=function(){var t=this.$createElement;this._self._c},r=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/orderGoods/index-create-component',
    {
        'components/orderGoods/index-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("b5d5"))
        })
    },
    [['components/orderGoods/index-create-component']]
]);
