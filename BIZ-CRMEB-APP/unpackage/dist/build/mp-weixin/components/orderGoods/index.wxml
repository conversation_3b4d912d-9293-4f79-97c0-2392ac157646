<view class="orderGoods borRadius14 data-v-098c4d7c"><view class="total data-v-098c4d7c">{{"共"+(orderProNum?orderProNum:totalNmu)+"件商品"}}</view><view class="goodWrapper pad30 data-v-098c4d7c"><block wx:for="{{cartInfo}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['jumpCon',['$0'],[[['cartInfo','',index,'productId']]]]]]]}}" class="item acea-row row-between-wrapper data-v-098c4d7c" bindtap="__e"><view class="pictrue data-v-098c4d7c"><image src="{{item.image}}" class="data-v-098c4d7c"></image></view><view class="text data-v-098c4d7c"><view class="acea-row row-between-wrapper data-v-098c4d7c"><view class="name line1 data-v-098c4d7c">{{item.productName?item.productName:item.storeName}}</view><view class="num data-v-098c4d7c">{{"x "+(item.payNum?item.payNum:item.cartNum)}}</view></view><block wx:if="{{item.sku}}"><view class="attr line1 data-v-098c4d7c">{{item.sku}}</view></block><view class="money data-v-098c4d7c">·</view><block wx:if="{{item.isReply==0&&evaluate==2}}"><view data-event-opts="{{[['tap',[['evaluateTap',['$0'],[[['cartInfo','',index]]]]]]]}}" class="evaluate data-v-098c4d7c" catchtap="__e">评价</view></block><block wx:else><block wx:if="{{item.isReply==1}}"><view class="evaluate data-v-098c4d7c">已评价</view></block></block></view></view></block></view></view>