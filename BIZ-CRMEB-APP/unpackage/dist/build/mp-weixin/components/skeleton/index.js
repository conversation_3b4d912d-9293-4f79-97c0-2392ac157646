(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/skeleton/index"],{2476:function(t,e,n){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={name:"skeleton",props:{bgcolor:{type:String,value:"#FFF"},selector:{type:String,value:"skeleton"},loading:{type:String,value:"spin"},show:{type:Boolean,value:!1},isNodes:{type:Number,value:!1}},data:function(){return{loadingAni:["spin","chiaroscuro"],systemInfo:{},skeletonRectLists:[],skeletonCircleLists:[]}},watch:{isNodes:function(t){this.readyAction()}},mounted:function(){this.attachedAction()},methods:{attachedAction:function(){var e=t.getSystemInfoSync();this.systemInfo={width:e.windowWidth,height:e.windowHeight},this.loading=this.loadingAni.includes(this.loading)?this.loading:"spin"},readyAction:function(){var e=this;t.createSelectorQuery().selectAll(".".concat(this.selector)).boundingClientRect().exec((function(t){t[0].length>0&&(e.systemInfo.height=t[0][0].height+t[0][0].top)})),this.rectHandle(),this.radiusHandle()},rectHandle:function(){var e=this;t.createSelectorQuery().selectAll(".".concat(this.selector,"-rect")).boundingClientRect().exec((function(t){e.skeletonRectLists=t[0]}))},radiusHandle:function(){var e=this;t.createSelectorQuery().selectAll(".".concat(this.selector,"-radius")).boundingClientRect().exec((function(t){e.skeletonCircleLists=t[0]}))}}};e.default=n}).call(this,n("df3c")["default"])},"2b61":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return c})),n.d(e,"a",(function(){}));var i=function(){var t=this.$createElement;this._self._c},c=[]},3154:function(t,e,n){"use strict";n.r(e);var i=n("2b61"),c=n("4d9c");for(var o in c)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return c[t]}))}(o);n("a7fc");var s=n("828b"),a=Object(s["a"])(c["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=a.exports},"4d9c":function(t,e,n){"use strict";n.r(e);var i=n("2476"),c=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=c.a},"5b282":function(t,e,n){},a7fc:function(t,e,n){"use strict";var i=n("5b282"),c=n.n(i);c.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/skeleton/index-create-component',
    {
        'components/skeleton/index-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("3154"))
        })
    },
    [['components/skeleton/index-create-component']]
]);
