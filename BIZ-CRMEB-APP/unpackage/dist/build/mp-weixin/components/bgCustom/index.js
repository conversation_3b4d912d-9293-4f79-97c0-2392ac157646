(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/bgCustom/index"],{"337d":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=n("8f59"),c={name:"Home",props:{},data:function(){return{top:"500"}},computed:(0,o.mapGetters)(["homeActive"]),methods:{setTouchMove:function(t){t.touches[0].clientY<545&&t.touches[0].clientY>66&&(this.top=t.touches[0].clientY)},open:function(){this.homeActive?this.$store.commit("CLOSE_HOME"):this.$store.commit("OPEN_HOME")},bgTheme:function(t){this.$emit("getTheme",t)}},created:function(){}};e.default=c},"574e":function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return c})),n.d(e,"a",(function(){}));var o=function(){var t=this.$createElement;this._self._c},c=[]},"57ab":function(t,e,n){"use strict";var o=n("fa1c"),c=n.n(o);c.a},"8b89":function(t,e,n){"use strict";n.r(e);var o=n("337d"),c=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);e["default"]=c.a},d09c:function(t,e,n){"use strict";n.r(e);var o=n("574e"),c=n("8b89");for(var i in c)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return c[t]}))}(i);n("57ab");var u=n("828b"),r=Object(u["a"])(c["default"],o["b"],o["c"],!1,null,"12d78224",null,!1,o["a"],void 0);e["default"]=r.exports},fa1c:function(t,e,n){}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/bgCustom/index-create-component',
    {
        'components/bgCustom/index-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("d09c"))
        })
    },
    [['components/bgCustom/index-create-component']]
]);
