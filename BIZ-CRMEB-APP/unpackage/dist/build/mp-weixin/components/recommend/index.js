(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/recommend/index"],{"074d":function(t,n,e){"use strict";(function(t){var u=e("47a9");Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=e("8f59"),i=e("3f87"),o=u(e("4bef")),r={computed:(0,a.mapGetters)(["uid"]),props:{hostProduct:{type:Array,default:function(){return[]}}},data:function(){return{}},methods:{goDetail:function(n){(0,i.goShopDetail)(n,this.uid).then((function(e){t.navigateTo({animationType:o.default.type,animationDuration:o.default.duration,url:"/pages/goods_details/index?id=".concat(n.id)})}))}}};n.default=r}).call(this,e("df3c")["default"])},"0793":function(t,n,e){"use strict";e.d(n,"b",(function(){return u})),e.d(n,"c",(function(){return a})),e.d(n,"a",(function(){}));var u=function(){var t=this.$createElement;this._self._c},a=[]},"1bb5":function(t,n,e){"use strict";var u=e("b329"),a=e.n(u);a.a},"405b":function(t,n,e){"use strict";e.r(n);var u=e("074d"),a=e.n(u);for(var i in u)["default"].indexOf(i)<0&&function(t){e.d(n,t,(function(){return u[t]}))}(i);n["default"]=a.a},"66cd":function(t,n,e){"use strict";e.r(n);var u=e("0793"),a=e("405b");for(var i in a)["default"].indexOf(i)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(i);e("1bb5");var o=e("828b"),r=Object(o["a"])(a["default"],u["b"],u["c"],!1,null,"d09e5804",null,!1,u["a"],void 0);n["default"]=r.exports},b329:function(t,n,e){}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/recommend/index-create-component',
    {
        'components/recommend/index-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("66cd"))
        })
    },
    [['components/recommend/index-create-component']]
]);
