(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/login_mobile/index"],{3955:function(t,e,n){},"5a05":function(t,e,n){"use strict";var i=n("3955"),o=n.n(i);o.a},"5d03":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){}));var i=function(){var t=this.$createElement;this._self._c},o=[]},7756:function(t,e,n){"use strict";(function(t){var i=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=i(n("7eb4")),u=i(n("ee10")),a=i(n("bb5a")),c=(i(n("f0ff")),n("8f59")),s=n("20ce"),r=(n("ad96"),n("84f5")),f=getApp(),l={name:"login_mobile",computed:(0,c.mapGetters)(["userInfo","isLogin"]),props:{isUp:{type:Boolean,default:!1},authKey:{type:String,default:""},isShow:{type:Boolean,default:!0},isPos:{type:Boolean,default:!0},appleShow:{type:String,default:""},platform:{type:String,default:""}},data:function(){return{keyCode:"",account:"",codeNum:"",isApp:0}},mixins:[a.default],mounted:function(){},onLoad:function(){},methods:{code:function(){var t=this;return(0,u.default)(o.default.mark((function e(){var n;return o.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=t,n.account){e.next=3;break}return e.abrupt("return",n.$util.Tips({title:"请填写手机号码"}));case 3:if(/^1(3|4|5|7|8|9|6)\d{9}$/i.test(n.account)){e.next=5;break}return e.abrupt("return",n.$util.Tips({title:"请输入正确的手机号码"}));case 5:return e.next=7,(0,s.registerVerify)(n.account).then((function(t){n.$util.Tips({title:t.msg}),n.sendCode()})).catch((function(t){return n.$util.Tips({title:t})}));case 7:case"end":return e.stop()}}),e)})))()},getCode:function(){var t=this;(0,s.getCodeApi)().then((function(e){t.keyCode=e.data.key})).catch((function(e){t.$util.Tips({title:e})}))},close:function(){this.$emit("close",!1)},loginBtn:function(){var e=this;return e.account?/^1(3|4|5|7|8|9|6)\d{9}$/i.test(e.account)?e.codeNum?/^[\w\d]+$/i.test(e.codeNum)?(t.showLoading({title:!this.userInfo.phone&&this.isLogin?"正在绑定中":"正在登录中"}),void(!this.userInfo.phone&&this.isLogin?(0,r.iosBinding)({captcha:e.codeNum,phone:e.account}).then((function(t){e.$util.Tips({title:"绑定手机号成功",icon:"success"},{tab:3}),e.isApp=1,e.getUserInfo()})).catch((function(n){t.hideLoading(),e.$util.Tips({title:n})})):(0,r.getUserPhone)({captcha:e.codeNum,phone:e.account,key:e.authKey}).then((function(t){e.$store.commit("LOGIN",{token:t.data.token}),e.$store.commit("SETUID",t.data.uid),e.getUserInfo()})).catch((function(n){t.hideLoading(),e.$util.Tips({title:n})})))):e.$util.Tips({title:"请输入正确的验证码"}):e.$util.Tips({title:"请填写验证码"}):e.$util.Tips({title:"请输入正确的手机号码"}):e.$util.Tips({title:"请填写手机号码"})},phoneSilenceAuth:function(t){var e=this,n=this;(0,s.phoneSilenceAuth)({code:t,spid:f.globalData.spread,phone:this.account,captcha:this.codeNum}).then((function(t){e.$store.commit("LOGIN",t.data.token),e.$store.commit("SETUID",t.data.uid),e.getUserInfo()})).catch((function(t){n.$util.Tips({title:t})}))},getUserInfo:function(){var e=this;(0,s.getUserInfo)().then((function(n){t.hideLoading(),e.$store.commit("UPDATE_USERINFO",n.data),e.$util.Tips({title:"登录成功",icon:"success"},{tab:3}),e.close()}))}}};e.default=l}).call(this,n("df3c")["default"])},a277:function(t,e,n){"use strict";n.r(e);var i=n("7756"),o=n.n(i);for(var u in i)["default"].indexOf(u)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(u);e["default"]=o.a},c211:function(t,e,n){"use strict";n.r(e);var i=n("5d03"),o=n("a277");for(var u in o)["default"].indexOf(u)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(u);n("5a05");var a=n("828b"),c=Object(a["a"])(o["default"],i["b"],i["c"],!1,null,"4baaaaa8",null,!1,i["a"],void 0);e["default"]=c.exports}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/login_mobile/index-create-component',
    {
        'components/login_mobile/index-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("c211"))
        })
    },
    [['components/login_mobile/index-create-component']]
]);
