<block wx:if="{{isUp}}"><view class="data-v-4baaaaa8"><block wx:if="{{isShow}}"><view data-event-opts="{{[['tap',[['close',['$event']]]]]}}" class="mobile-bg data-v-4baaaaa8" bindtap="__e"></view></block><view class="{{['mobile-mask','animated','data-v-4baaaaa8',(isUp)?'slideInUp':'']}}" style="{{'position:'+(isPos?'fixed':'static')+';'}}"><view class="input-item data-v-4baaaaa8"><input type="number" placeholder="输入手机号" maxlength="11" data-event-opts="{{[['input',[['__set_model',['','account','$event',[]]]]]]}}" value="{{account}}" bindinput="__e" class="data-v-4baaaaa8"/></view><view class="input-item data-v-4baaaaa8"><input type="number" placeholder="输入验证码" maxlength="6" data-event-opts="{{[['input',[['__set_model',['','codeNum','$event',[]]]]]]}}" value="{{codeNum}}" bindinput="__e" class="data-v-4baaaaa8"/><button class="code data-v-4baaaaa8" disabled="{{disabled}}" data-event-opts="{{[['tap',[['code',['$event']]]]]}}" bindtap="__e">{{text}}</button></view><view data-event-opts="{{[['tap',[['loginBtn',['$event']]]]]}}" class="sub_btn data-v-4baaaaa8" bindtap="__e">{{!userInfo.phone&&isLogin||userInfo.phone&&isLogin?'立即绑定':'立即登录'}}</view></view></view></block>