(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/navBar"],{"01b8":function(n,e,t){},"31ad":function(n,e,t){"use strict";var i=t("01b8"),a=t.n(i);a.a},7235:function(n,e,t){"use strict";(function(n){var i=t("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(t("4bef")),u=getApp(),o={data:function(){return{homeTop:20,navH:"",currentPage:!1,selectNavList:[{name:"首页",icon:"icon-shouye8",url:"/pages/index/index"},{name:"搜索",icon:"icon-sousuo6",url:"/pages/goods_search/index"},{name:"我的收藏",icon:"icon-shoucang3",url:"/pages/users/user_goods_collection/index"},{name:"个人中心",icon:"icon-gerenzhongxin1",url:"/pages/user/index"}]}},props:{navTitle:{type:String,default:""}},created:function(){n.getSystemInfo({success:function(n){u.globalData.navHeight=n.statusBarHeight*(750/n.windowWidth)+91}}),this.navH=u.globalData.navHeight,this.$emit("getNavH",this.navH)},onReady:function(){this.$nextTick((function(){var e=this,t=n.getMenuButtonBoundingClientRect(),i=n.createSelectorQuery().in(this);i.select("#home").boundingClientRect((function(n){e.homeTop=2*t.top+t.height-n.height})).exec()}))},methods:{returns:function(){n.switchTab({url:"/pages/index/index"})},showNav:function(){this.currentPage=!this.currentPage},linkPage:function(e){"/pages/index/index"==e||"/pages/user/index"==e?n.switchTab({url:e}):n.navigateTo({animationType:a.default.type,animationDuration:a.default.duration,url:e}),this.currentPage=!1},touchStart:function(){this.currentPage=!1}}};e.default=o}).call(this,t("df3c")["default"])},b165:function(n,e,t){"use strict";t.r(e);var i=t("c41c"),a=t("d55d");for(var u in a)["default"].indexOf(u)<0&&function(n){t.d(e,n,(function(){return a[n]}))}(u);t("31ad");var o=t("828b"),c=Object(o["a"])(a["default"],i["b"],i["c"],!1,null,"69f4fe4b",null,!1,i["a"],void 0);e["default"]=c.exports},c41c:function(n,e,t){"use strict";t.d(e,"b",(function(){return i})),t.d(e,"c",(function(){return a})),t.d(e,"a",(function(){}));var i=function(){var n=this.$createElement;this._self._c},a=[]},d55d:function(n,e,t){"use strict";t.r(e);var i=t("7235"),a=t.n(i);for(var u in i)["default"].indexOf(u)<0&&function(n){t.d(e,n,(function(){return i[n]}))}(u);e["default"]=a.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/navBar-create-component',
    {
        'components/navBar-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("b165"))
        })
    },
    [['components/navBar-create-component']]
]);
