(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["common/vendor"],{"011a":function(t,e){function n(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>,[],(function(){})))}catch(e){}return(t.exports=n=function(){return!!e},t.exports.__esModule=!0,t.exports["default"]=t.exports)()}t.exports=n,t.exports.__esModule=!0,t.exports["default"]=t.exports},"0a3e":function(t,e){t.exports={HTTP_REQUEST_URL:"https://api1.tyy815.com",HTTP_H5_URL:"https://app.tyy815.com",HEADER:{"content-type":"application/json"},HEADERPARAMS:{"content-type":"application/x-www-form-urlencoded"},TOKENNAME:"Authori-zation",EXPIRE:0,LIMIT:10}},"0bdb":function(t,e,n){var r=n("d551");function i(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,r(i.key),i)}}t.exports=function(t,e,n){return e&&i(t.prototype,e),n&&i(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t},t.exports.__esModule=!0,t.exports["default"]=t.exports},"0ee4":function(t,e){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(r){"object"===typeof window&&(n=window)}t.exports=n},1178:function(t,e,n){"use strict";(function(t){var r=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n("0a3e"),o=n("2e55"),a=r(n("5908"));var s={};["options","get","post","put","head","delete","trace","connect"].forEach((function(e){s[e]=function(n,r,s,u){return function(e,n,r,s,u){var c=s.noAuth,l=void 0!==c&&c,f=s.noVerify,d=void 0!==f&&f,h=i.HTTP_REQUEST_URL,p=i.HEADER;return void 0!=u&&(p=i.HEADERPARAMS),l||a.default.state.app.token||(0,o.checkLogin)()?(a.default.state.app.token&&(p[i.TOKENNAME]=a.default.state.app.token),new Promise((function(i,a){t.request({url:h+"/api/front/"+e,method:n||"GET",header:p,data:r||{},success:function(t){d||200==t.data.code?i(t.data,t):-1!==[41e4,410001,410002,401].indexOf(t.data.code)?((0,o.toLogin)(),a(t.data)):500==t.data.code?a(t.data.message||"系统异常"):400==t.data.code?a(t.data.message||"参数校验失败"):404==t.data.code?a(t.data.message||"没有找到相关数据"):403==t.data.code?a(t.data.message||"没有相关权限"):a(t.data.message||"系统错误")},fail:function(t){a("请求失败")}})}))):((0,o.toLogin)(),Promise.reject({msg:"未登录"}))}(n,e,r,s||{},u)}}));var u=s;e.default=u}).call(this,n("df3c")["default"])},"1f75":function(t,e,n){"use strict";var r=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=r(n("67ad")),o=r(n("0bdb")),a=n("84f5"),s=function(){function t(){(0,i.default)(this,t)}return(0,o.default)(t,[{key:"authApp",value:function(t){return new Promise((function(e,n){(0,a.appAuth)(t,{spread_spid:0}).then((function(n){var r=n.data;e(r),Cache.set(WX_AUTH,t),Cache.clear(STATE_KEY),loginType&&Cache.clear(LOGINTYPE)})).catch(n)}))}}]),t}(),u=new s;e.default=u},"20ce":function(t,e,n){"use strict";var r=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.addressRecognition=function(t){return o.default.post("address/addressRecognition",t)},e.alipayFull=function(t){return o.default.post("recharge/alipay",t,{})},e.appWechat=function(t){return o.default.post("recharge/wechat/app",t)},e.brokerageRankNumber=function(t){return o.default.get("user/brokerageRankNumber",t)},e.delAddress=function(t){return o.default.post("address/del",{id:t})},e.editAddress=function(t){return o.default.post("address/edit",t)},e.extractBank=function(){return o.default.get("extract/bank")},e.extractCash=function(t){return o.default.post("extract/cash",t)},e.extractUser=function(){return o.default.get("extract/user")},e.getAddressDefault=function(){return o.default.get("address/default")},e.getAddressDetail=function(t){return o.default.get("address/detail/"+t)},e.getAddressList=function(t){return o.default.get("address/list",t)},e.getBillList=function(t){return o.default.get("recharge/bill/record",t)},e.getBrokerageRank=function(t){return o.default.get("brokerage_rank",t)},e.getChatRecord=function(t,e){return o.default.get("user/service/record/"+t,e)},e.getCodeApi=function(){return o.default.get("verify_code",{},{noAuth:!0})},e.getCommissionInfo=function(t){return o.default.get("spread/commission/detail",t)},e.getCountApi=function(){return o.default.get("extract/totalMoney")},e.getIntegralList=function(t){return o.default.get("integral/list",t)},e.getLogout=function(){return o.default.get("logout")},e.getMenuList=function(){return o.default.get("menu/user")},e.getRankList=function(t){return o.default.get("rank",t)},e.getRechargeApi=function(){return o.default.get("recharge/index")},e.getRecordApi=function(t){return o.default.get("extract/record",t)},e.getSignConfig=function(){return o.default.get("user/sign/config")},e.getSignList=function(t){return o.default.get("user/sign/list",t)},e.getSignMonthList=function(t){return o.default.get("user/sign/month",t)},e.getSpreadInfo=function(){return o.default.get("commission")},e.getUserInfo=function(){return o.default.get("user")},e.getlevelExpList=function(t){return o.default.get("user/expList",t)},e.getlevelInfo=function(){return o.default.get("user/level/grade")},e.getuserDalance=function(){return o.default.get("user/balance")},e.loginH5=function(t){return o.default.post("login",t,{noAuth:!0})},e.loginMobile=function(t){return o.default.post("login/mobile",t,{noAuth:!0})},e.postIntegralUser=function(){return o.default.get("integral/user")},e.postSignUser=function(t){return o.default.post("user/sign/user",t)},e.rechargeRoutine=function(t){return o.default.post("recharge/routine",t)},e.rechargeWechat=function(t){return o.default.post("recharge/wechat",t)},e.register=function(t){return o.default.post("register",t,{noAuth:!0})},e.registerReset=function(t){return o.default.post("register/reset",t,{noAuth:!0})},e.registerVerify=function(t){return o.default.post("sendCode",{phone:t},{noAuth:!0},1)},e.serviceList=function(){return o.default.get("user/service/lst")},e.setAddressDefault=function(t){return o.default.post("address/default/set",{id:t})},e.setSignIntegral=function(){return o.default.get("user/sign/integral")},e.setVisit=function(t){return o.default.post("user/set_visit",function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?a(Object(n),!0).forEach((function(e){(0,i.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):a(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}({},t),{noAuth:!0})},e.spread=function(t){return o.default.get("user/bindSpread?spreadPid="+t)},e.spreadBanner=function(t){return o.default.get("user/spread/banner",t)},e.spreadCount=function(t){return o.default.get("spread/count/"+t)},e.spreadOrder=function(t){return o.default.get("spread/order",t)},e.spreadPeoCount=function(){return o.default.get("spread/people/count")},e.spreadPeople=function(t){return o.default.get("spread/people",t)},e.transferIn=function(t){return o.default.post("recharge/transferIn",t,{},1)},e.userActivity=function(){return o.default.get("user/activity")},e.userEdit=function(t){return o.default.post("user/edit",t)},e.userLevelDetection=function(){return o.default.get("user/level/detection")},e.userLevelGrade=function(){return o.default.get("user/level/grade")},e.userLevelTask=function(t){return o.default.get("user/level/task/"+t)},e.userShare=function(){return o.default.post("user/share")};var i=r(n("7ca3")),o=r(n("1178"));r(n("63e6"));function a(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}},2243:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAF4AAABeCAMAAACdDFNcAAAA81BMVEUAAADq6urd3d3d3d3d3d3d3d3f39/e3t7c3Nzc3Nzd3d3c3Nzf39/////5+fno6Oj////c3Nzd3d3d3d3g4ODd3d3+/v7////////d3d3d3d3////c3Nz////d3d3d3d3+/v7j4+P////////j4+P////d3d319fXd3d3////////d3d3c3Nzd3d3d3d3////d3d3u7u7////n5+f////29vb////+/v76+vrs7Oz////z8/Pu7u79/f3y8vL29vb////t7e3u7u7////c3Nz7+/vu7u7j4+Pf39/k5OT19fX5+fn39/fy8vLo6Ojw8PDq6urQU62mAAAAQ3RSTlMAC/Yjt8qUDvnZT/w4NwUE9O7P0UQWD8rGxmBOG8CyrKugil5LJt3Ev7SQi3t3Z2Zb89rZ0LmfnpSReWZfSfTu7cqrEVjcVgAABCtJREFUaN69mmlz2jAQhmUXCARCgTR3c7e5r973ua8kyyf//9cUhzATEhethdvnA8Mw9uP1Ilk7WgsWXufLwd76m34b8Put472D644nqsFbbAzaUEQ0xJh49F23B43FuS+x+rXWxAidjZRSyvwjyj81RjRrm6vCnaX3PQkYFUp6hAyVAUzv/ZKjfHEjt47VxcgsHH0evxDleVYHFHHQqD8rKX9SAwIlOXapAqD2RPDpXvoTOfcC/sVTrv3lmkEqqQQyBX695NkPeyRDKkmYyt4hQ/60IcmVj9YELdSQutqlri3Mtq8sI4hd9VGAwcos+/MWdETORBqt5zNiH9klzYGc5V9dtthZ/uW/5L+7nVjsPP+7bqH+jKogiumsyL5FlbFV8BDzw8r0P548mqx1pFXZQ9QfTt9LBLIqvQxw+TA1CKkyQvjT6dlBShWSYndq5eOmhp+e++vjAIpYxDEvDIX6/SGfSHtEyuAWrSJG+MNvYsI243aHuIeJrMfT9sS+xEhKgGkSsjKpr/ZDq32clDgPK1aa5/9wt/75iKwDYaqmUhgRW+xDf/xk3oSh2ZiH0SYAtPWkTZFTQ2ZPjXp8QUtKM9Ryu9eEtMXxaNZFAIaWjKLp5YUwjO24gmJWc7KzONI3bDM2KfofhwCsM7cx0i8jth0GWH4sJMZACE/G9tRrF73UqceYsgYwVJQczsS9JhuJUgmV/2tzrsXBjNTYpoKVA7HrtApqAPanZvJb1F30QwCGcYsbooXIxc5ZPSMciT6kk50RlERfNCHd7danjgDc7BwAATjZuXpdrqZXALhnSLTFTypDWMJOMnslXpesvkrVc6/FOpVAAaVm4brYCyNiEwCaSrAnzqGISwQg48uT9JO4KlF6ZwBivj7FleiUuF0FgPhodITXhiyrZw97T4gBfywkxhi+PsTAXoi4o9Cwl1Hu6NsyynsV0b9A6uZtiXxK/4h3IueQ2MSxJD6HIme1J3knyXGdz6V3t329jyFxyJDDjD9LPogxHeZD1gDsp4IM0BF3bEBVHb3CiZjwghe+1AAUN/h72+515lmJiokZfP3/bVmIHSRV6lPsTG8XhVQlYfBgN+0zVcpnMU13jSrM/lr3UcvhO7Jq3EnSK2hE3KCa3boY+CoKaFQyOmWAhiiie4JKtqhPuqKQhYo22Ge0B4bz2lsrM5obb+aMPm09n9lAXZ9Pv74iZuKdknEc/4mkU8/aFvsEpA4pkgbmjNM3vPGhyzf1AvRvmN3UOhxaknV2T/XphQ9dLnT/olumHbwTlgk+GLWDy/FtjcjY97ojJYnWXLrlW8cATDazFW8Atb0l3Ojs+0DxMJrs5/v7HeHOwmatebvKZyoJo7vXIMJE5V9uX4NYmP8ljo9vJWlMEZF8a3uJg4+3dHW+u3HUbwPt/tHG7vmXJZ76D+x3Egvqv61YAAAAAElFTkSuQmCC"},2365:function(t,e,n){(function(t){var r,i,o,a=n("3b2d");
/*!
 * clipboard.js v2.0.6
 * https://clipboardjs.com/
 * 
 * Licensed MIT © Zeno Rocha
 */
(function(n,s){"object"===a(e)&&"object"===a(t)?t.exports=s():(i=[],r=s,o="function"===typeof r?r.apply(e,i):r,void 0===o||(t.exports=o))})(0,(function(){return function(t){var e={};function n(r){if(e[r])return e[r].exports;var i=e[r]={i:r,l:!1,exports:{}};return t[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},n.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"===a(t)&&t&&t.__esModule)return t;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)n.d(r,i,function(e){return t[e]}.bind(null,i));return r},n.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=6)}([function(t,e){t.exports=function(t){var e;if("SELECT"===t.nodeName)t.focus(),e=t.value;else if("INPUT"===t.nodeName||"TEXTAREA"===t.nodeName){var n=t.hasAttribute("readonly");n||t.setAttribute("readonly",""),t.select(),t.setSelectionRange(0,t.value.length),n||t.removeAttribute("readonly"),e=t.value}else{t.hasAttribute("contenteditable")&&t.focus();var r=window.getSelection(),i=document.createRange();i.selectNodeContents(t),r.removeAllRanges(),r.addRange(i),e=r.toString()}return e}},function(t,e){function n(){}n.prototype={on:function(t,e,n){var r=this.e||(this.e={});return(r[t]||(r[t]=[])).push({fn:e,ctx:n}),this},once:function(t,e,n){var r=this;function i(){r.off(t,i),e.apply(n,arguments)}return i._=e,this.on(t,i,n)},emit:function(t){var e=[].slice.call(arguments,1),n=((this.e||(this.e={}))[t]||[]).slice(),r=0,i=n.length;for(r;r<i;r++)n[r].fn.apply(n[r].ctx,e);return this},off:function(t,e){var n=this.e||(this.e={}),r=n[t],i=[];if(r&&e)for(var o=0,a=r.length;o<a;o++)r[o].fn!==e&&r[o].fn._!==e&&i.push(r[o]);return i.length?n[t]=i:delete n[t],this}},t.exports=n,t.exports.TinyEmitter=n},function(t,e,n){var r=n(3),i=n(4);t.exports=function(t,e,n){if(!t&&!e&&!n)throw new Error("Missing required arguments");if(!r.string(e))throw new TypeError("Second argument must be a String");if(!r.fn(n))throw new TypeError("Third argument must be a Function");if(r.node(t))return function(t,e,n){return t.addEventListener(e,n),{destroy:function(){t.removeEventListener(e,n)}}}(t,e,n);if(r.nodeList(t))return function(t,e,n){return Array.prototype.forEach.call(t,(function(t){t.addEventListener(e,n)})),{destroy:function(){Array.prototype.forEach.call(t,(function(t){t.removeEventListener(e,n)}))}}}(t,e,n);if(r.string(t))return function(t,e,n){return i(document.body,t,e,n)}(t,e,n);throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList")}},function(t,e){e.node=function(t){return void 0!==t&&t instanceof HTMLElement&&1===t.nodeType},e.nodeList=function(t){var n=Object.prototype.toString.call(t);return void 0!==t&&("[object NodeList]"===n||"[object HTMLCollection]"===n)&&"length"in t&&(0===t.length||e.node(t[0]))},e.string=function(t){return"string"===typeof t||t instanceof String},e.fn=function(t){var e=Object.prototype.toString.call(t);return"[object Function]"===e}},function(t,e,n){var r=n(5);function i(t,e,n,r,i){var a=o.apply(this,arguments);return t.addEventListener(n,a,i),{destroy:function(){t.removeEventListener(n,a,i)}}}function o(t,e,n,i){return function(n){n.delegateTarget=r(n.target,e),n.delegateTarget&&i.call(t,n)}}t.exports=function(t,e,n,r,o){return"function"===typeof t.addEventListener?i.apply(null,arguments):"function"===typeof n?i.bind(null,document).apply(null,arguments):("string"===typeof t&&(t=document.querySelectorAll(t)),Array.prototype.map.call(t,(function(t){return i(t,e,n,r,o)})))}},function(t,e){if("undefined"!==typeof Element&&!Element.prototype.matches){var n=Element.prototype;n.matches=n.matchesSelector||n.mozMatchesSelector||n.msMatchesSelector||n.oMatchesSelector||n.webkitMatchesSelector}t.exports=function(t,e){while(t&&9!==t.nodeType){if("function"===typeof t.matches&&t.matches(e))return t;t=t.parentNode}}},function(t,e,n){"use strict";n.r(e);var r=n(0),i=n.n(r),o="function"===typeof Symbol&&"symbol"===a(Symbol.iterator)?function(t){return a(t)}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":a(t)},s=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}();var u=function(){function t(e){(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")})(this,t),this.resolveOptions(e),this.initSelection()}return s(t,[{key:"resolveOptions",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.action=t.action,this.container=t.container,this.emitter=t.emitter,this.target=t.target,this.text=t.text,this.trigger=t.trigger,this.selectedText=""}},{key:"initSelection",value:function(){this.text?this.selectFake():this.target&&this.selectTarget()}},{key:"selectFake",value:function(){var t=this,e="rtl"==document.documentElement.getAttribute("dir");this.removeFake(),this.fakeHandlerCallback=function(){return t.removeFake()},this.fakeHandler=this.container.addEventListener("click",this.fakeHandlerCallback)||!0,this.fakeElem=document.createElement("textarea"),this.fakeElem.style.fontSize="12pt",this.fakeElem.style.border="0",this.fakeElem.style.padding="0",this.fakeElem.style.margin="0",this.fakeElem.style.position="absolute",this.fakeElem.style[e?"right":"left"]="-9999px";var n=window.pageYOffset||document.documentElement.scrollTop;this.fakeElem.style.top=n+"px",this.fakeElem.setAttribute("readonly",""),this.fakeElem.value=this.text,this.container.appendChild(this.fakeElem),this.selectedText=i()(this.fakeElem),this.copyText()}},{key:"removeFake",value:function(){this.fakeHandler&&(this.container.removeEventListener("click",this.fakeHandlerCallback),this.fakeHandler=null,this.fakeHandlerCallback=null),this.fakeElem&&(this.container.removeChild(this.fakeElem),this.fakeElem=null)}},{key:"selectTarget",value:function(){this.selectedText=i()(this.target),this.copyText()}},{key:"copyText",value:function(){var t=void 0;try{t=document.execCommand(this.action)}catch(e){t=!1}this.handleResult(t)}},{key:"handleResult",value:function(t){this.emitter.emit(t?"success":"error",{action:this.action,text:this.selectedText,trigger:this.trigger,clearSelection:this.clearSelection.bind(this)})}},{key:"clearSelection",value:function(){this.trigger&&this.trigger.focus(),document.activeElement.blur(),window.getSelection().removeAllRanges()}},{key:"destroy",value:function(){this.removeFake()}},{key:"action",set:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"copy";if(this._action=t,"copy"!==this._action&&"cut"!==this._action)throw new Error('Invalid "action" value, use either "copy" or "cut"')},get:function(){return this._action}},{key:"target",set:function(t){if(void 0!==t){if(!t||"object"!==("undefined"===typeof t?"undefined":o(t))||1!==t.nodeType)throw new Error('Invalid "target" value, use a valid Element');if("copy"===this.action&&t.hasAttribute("disabled"))throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');if("cut"===this.action&&(t.hasAttribute("readonly")||t.hasAttribute("disabled")))throw new Error('Invalid "target" attribute. You can\'t cut text from elements with "readonly" or "disabled" attributes');this._target=t}},get:function(){return this._target}}]),t}(),c=u,l=n(1),f=n.n(l),d=n(2),h=n.n(d),p="function"===typeof Symbol&&"symbol"===a(Symbol.iterator)?function(t){return a(t)}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":a(t)},v=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}();var g=function(t){function e(t,n){(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")})(this,e);var r=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==a(e)&&"function"!==typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this));return r.resolveOptions(n),r.listenClick(t),r}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+a(e));t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),v(e,[{key:"resolveOptions",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.action="function"===typeof t.action?t.action:this.defaultAction,this.target="function"===typeof t.target?t.target:this.defaultTarget,this.text="function"===typeof t.text?t.text:this.defaultText,this.container="object"===p(t.container)?t.container:document.body}},{key:"listenClick",value:function(t){var e=this;this.listener=h()(t,"click",(function(t){return e.onClick(t)}))}},{key:"onClick",value:function(t){var e=t.delegateTarget||t.currentTarget;this.clipboardAction&&(this.clipboardAction=null),this.clipboardAction=new c({action:this.action(e),target:this.target(e),text:this.text(e),container:this.container,trigger:e,emitter:this})}},{key:"defaultAction",value:function(t){return y("action",t)}},{key:"defaultTarget",value:function(t){var e=y("target",t);if(e)return document.querySelector(e)}},{key:"defaultText",value:function(t){return y("text",t)}},{key:"destroy",value:function(){this.listener.destroy(),this.clipboardAction&&(this.clipboardAction.destroy(),this.clipboardAction=null)}}],[{key:"isSupported",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:["copy","cut"],e="string"===typeof t?[t]:t,n=!!document.queryCommandSupported;return e.forEach((function(t){n=n&&!!document.queryCommandSupported(t)})),n}}]),e}(f.a);function y(t,e){var n="data-clipboard-"+t;if(e.hasAttribute(n))return e.getAttribute(n)}e["default"]=g}])["default"]}))}).call(this,n("dc84")(t))},"2df1":function(t,e,n){"use strict";(function(t){var r=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.base64src=function(e,n,r){var a=/data:image\/(\w+);base64,(.*)/.exec(e)||[],s=(0,i.default)(a,3),u=s[1],c=s[2];if(!u)return new Error("ERROR_BASE64SRC_PARSE");var l="".concat(t.env.USER_DATA_PATH,"/").concat(n+"tmp_base64src",".").concat(u),f=t.base64ToArrayBuffer(c);o.writeFile({filePath:l,data:f,encoding:"binary",success:function(){r(l)},fail:function(){return new Error("ERROR_BASE64SRC_WRITE")}})};var i=r(n("34cf")),o=t.getFileSystemManager?t.getFileSystemManager():null}).call(this,n("3223")["default"])},"2e55":function(t,e,n){"use strict";(function(t){var r=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e._toLogin=c,e.checkLogin=function(){var t=o.default.get(s.LOGIN_STATUS),e=o.default.get(s.EXPIRES_TIME),n=Math.round(new Date/1e3);if(e<n||!t)return o.default.clear(s.LOGIN_STATUS),o.default.clear(s.EXPIRES_TIME),o.default.clear(s.USER_INFO),o.default.clear(s.STATE_R_KEY),!1;i.default.commit("UPDATE_LOGIN",t);var r=o.default.get(s.USER_INFO,!0);return r&&i.default.commit("UPDATE_USERINFO",r),!0},e.toLogin=void 0;var i=r(n("5908")),o=r(n("63e6")),a=n("8b39"),s=n("6152");var u=(0,a.Debounce)(c,800);function c(e,n){i.default.commit("LOGOUT");var r=function(){var t=getCurrentPages(),e=t[t.length-1];return e.route}(),a=o.default.get(s.BACK_URL);n||(n="/page/users/login/index",o.default.set(s.BACK_URL,r)),-1==["pages/user/index","/pages/user/index"].indexOf(a)&&t.showModal({title:"登录提示",content:"登录以后可体验商城完整功能",cancelColor:"#000000",confirmColor:"#526BB1",success:function(e){e.confirm?t.navigateTo({url:"/pages/users/wechat_login/index"}):e.cancel}})}e.toLogin=u}).call(this,n("df3c")["default"])},3223:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=["qy","env","error","version","lanDebug","cloud","serviceMarket","router","worklet","__webpack_require_UNI_MP_PLUGIN__"],i=["lanDebug","router","worklet"],o="undefined"!==typeof globalThis?globalThis:function(){return this}(),a=["w","x"].join(""),s=o[a],u=s.getLaunchOptionsSync?s.getLaunchOptionsSync():null;function c(t){return(!u||1154!==u.scene||!i.includes(t))&&(r.indexOf(t)>-1||"function"===typeof s[t])}o[a]=function(){var t={};for(var e in s)c(e)&&(t[e]=s[e]);return t}(),o[a].canIUse("getAppBaseInfo")||(o[a].getAppBaseInfo=o[a].getSystemInfoSync),o[a].canIUse("getWindowInfo")||(o[a].getWindowInfo=o[a].getSystemInfoSync),o[a].canIUse("getDeviceInfo")||(o[a].getDeviceInfo=o[a].getSystemInfoSync);var l=o[a];e.default=l},3240:function(t,e,n){"use strict";n.r(e),function(t){
/*!
 * Vue.js v2.6.11
 * (c) 2014-2024 Evan You
 * Released under the MIT License.
 */
var n=Object.freeze({});function r(t){return void 0===t||null===t}function i(t){return void 0!==t&&null!==t}function o(t){return!0===t}function a(t){return"string"===typeof t||"number"===typeof t||"symbol"===typeof t||"boolean"===typeof t}function s(t){return null!==t&&"object"===typeof t}var u=Object.prototype.toString;function c(t){return"[object Object]"===u.call(t)}function l(t){var e=parseFloat(String(t));return e>=0&&Math.floor(e)===e&&isFinite(t)}function f(t){return i(t)&&"function"===typeof t.then&&"function"===typeof t.catch}function d(t){return null==t?"":Array.isArray(t)||c(t)&&t.toString===u?JSON.stringify(t,null,2):String(t)}function h(t){var e=parseFloat(t);return isNaN(e)?t:e}function p(t,e){for(var n=Object.create(null),r=t.split(","),i=0;i<r.length;i++)n[r[i]]=!0;return e?function(t){return n[t.toLowerCase()]}:function(t){return n[t]}}p("slot,component",!0);var v=p("key,ref,slot,slot-scope,is");function g(t,e){if(t.length){var n=t.indexOf(e);if(n>-1)return t.splice(n,1)}}var y=Object.prototype.hasOwnProperty;function m(t,e){return y.call(t,e)}function _(t){var e=Object.create(null);return function(n){var r=e[n];return r||(e[n]=t(n))}}var b=/-(\w)/g,A=_((function(t){return t.replace(b,(function(t,e){return e?e.toUpperCase():""}))})),w=_((function(t){return t.charAt(0).toUpperCase()+t.slice(1)})),O=/\B([A-Z])/g,x=_((function(t){return t.replace(O,"-$1").toLowerCase()}));var S=Function.prototype.bind?function(t,e){return t.bind(e)}:function(t,e){function n(n){var r=arguments.length;return r?r>1?t.apply(e,arguments):t.call(e,n):t.call(e)}return n._length=t.length,n};function k(t,e){e=e||0;var n=t.length-e,r=new Array(n);while(n--)r[n]=t[n+e];return r}function E(t,e){for(var n in e)t[n]=e[n];return t}function T(t){for(var e={},n=0;n<t.length;n++)t[n]&&E(e,t[n]);return e}function C(t,e,n){}var P=function(t,e,n){return!1},$=function(t){return t};function L(t,e){if(t===e)return!0;var n=s(t),r=s(e);if(!n||!r)return!n&&!r&&String(t)===String(e);try{var i=Array.isArray(t),o=Array.isArray(e);if(i&&o)return t.length===e.length&&t.every((function(t,n){return L(t,e[n])}));if(t instanceof Date&&e instanceof Date)return t.getTime()===e.getTime();if(i||o)return!1;var a=Object.keys(t),u=Object.keys(e);return a.length===u.length&&a.every((function(n){return L(t[n],e[n])}))}catch(c){return!1}}function j(t,e){for(var n=0;n<t.length;n++)if(L(t[n],e))return n;return-1}function I(t){var e=!1;return function(){e||(e=!0,t.apply(this,arguments))}}var M=["component","directive","filter"],D=["beforeCreate","created","beforeMount","mounted","beforeUpdate","updated","beforeDestroy","destroyed","activated","deactivated","errorCaptured","serverPrefetch"],N={optionMergeStrategies:Object.create(null),silent:!1,productionTip:!1,devtools:!1,performance:!1,errorHandler:null,warnHandler:null,ignoredElements:[],keyCodes:Object.create(null),isReservedTag:P,isReservedAttr:P,isUnknownElement:P,getTagNamespace:C,parsePlatformTagName:$,mustUseProp:P,async:!0,_lifecycleHooks:D},R=/a-zA-Z\u00B7\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u037D\u037F-\u1FFF\u200C-\u200D\u203F-\u2040\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD/;function U(t){var e=(t+"").charCodeAt(0);return 36===e||95===e}function H(t,e,n,r){Object.defineProperty(t,e,{value:n,enumerable:!!r,writable:!0,configurable:!0})}var B=new RegExp("[^"+R.source+".$_\\d]");var F,V="__proto__"in{},z="undefined"!==typeof window,G="undefined"!==typeof WXEnvironment&&!!WXEnvironment.platform,K=G&&WXEnvironment.platform.toLowerCase(),W=z&&window.navigator&&window.navigator.userAgent.toLowerCase(),X=W&&/msie|trident/.test(W),J=(W&&W.indexOf("msie 9.0"),W&&W.indexOf("edge/")>0),Y=(W&&W.indexOf("android"),W&&/iphone|ipad|ipod|ios/.test(W)||"ios"===K),q=(W&&/chrome\/\d+/.test(W),W&&/phantomjs/.test(W),W&&W.match(/firefox\/(\d+)/),{}.watch);if(z)try{var Z={};Object.defineProperty(Z,"passive",{get:function(){}}),window.addEventListener("test-passive",null,Z)}catch(Nn){}var Q=function(){return void 0===F&&(F=!z&&!G&&"undefined"!==typeof t&&(t["process"]&&"server"===t["process"].env.VUE_ENV)),F},tt=z&&window.__VUE_DEVTOOLS_GLOBAL_HOOK__;function et(t){return"function"===typeof t&&/native code/.test(t.toString())}var nt,rt="undefined"!==typeof Symbol&&et(Symbol)&&"undefined"!==typeof Reflect&&et(Reflect.ownKeys);nt="undefined"!==typeof Set&&et(Set)?Set:function(){function t(){this.set=Object.create(null)}return t.prototype.has=function(t){return!0===this.set[t]},t.prototype.add=function(t){this.set[t]=!0},t.prototype.clear=function(){this.set=Object.create(null)},t}();var it=C,ot=0,at=function(){this.id=ot++,this.subs=[]};function st(t){at.SharedObject.targetStack.push(t),at.SharedObject.target=t,at.target=t}function ut(){at.SharedObject.targetStack.pop(),at.SharedObject.target=at.SharedObject.targetStack[at.SharedObject.targetStack.length-1],at.target=at.SharedObject.target}at.prototype.addSub=function(t){this.subs.push(t)},at.prototype.removeSub=function(t){g(this.subs,t)},at.prototype.depend=function(){at.SharedObject.target&&at.SharedObject.target.addDep(this)},at.prototype.notify=function(){var t=this.subs.slice();for(var e=0,n=t.length;e<n;e++)t[e].update()},at.SharedObject={},at.SharedObject.target=null,at.SharedObject.targetStack=[];var ct=function(t,e,n,r,i,o,a,s){this.tag=t,this.data=e,this.children=n,this.text=r,this.elm=i,this.ns=void 0,this.context=o,this.fnContext=void 0,this.fnOptions=void 0,this.fnScopeId=void 0,this.key=e&&e.key,this.componentOptions=a,this.componentInstance=void 0,this.parent=void 0,this.raw=!1,this.isStatic=!1,this.isRootInsert=!0,this.isComment=!1,this.isCloned=!1,this.isOnce=!1,this.asyncFactory=s,this.asyncMeta=void 0,this.isAsyncPlaceholder=!1},lt={child:{configurable:!0}};lt.child.get=function(){return this.componentInstance},Object.defineProperties(ct.prototype,lt);var ft=function(t){void 0===t&&(t="");var e=new ct;return e.text=t,e.isComment=!0,e};function dt(t){return new ct(void 0,void 0,void 0,String(t))}var ht=Array.prototype,pt=Object.create(ht);["push","pop","shift","unshift","splice","sort","reverse"].forEach((function(t){var e=ht[t];H(pt,t,(function(){var n=[],r=arguments.length;while(r--)n[r]=arguments[r];var i,o=e.apply(this,n),a=this.__ob__;switch(t){case"push":case"unshift":i=n;break;case"splice":i=n.slice(2);break}return i&&a.observeArray(i),a.dep.notify(),o}))}));var vt=Object.getOwnPropertyNames(pt),gt=!0;function yt(t){gt=t}var mt=function(t){this.value=t,this.dep=new at,this.vmCount=0,H(t,"__ob__",this),Array.isArray(t)?(V?t.push!==t.__proto__.push?_t(t,pt,vt):function(t,e){t.__proto__=e}(t,pt):_t(t,pt,vt),this.observeArray(t)):this.walk(t)};function _t(t,e,n){for(var r=0,i=n.length;r<i;r++){var o=n[r];H(t,o,e[o])}}function bt(t,e){var n;if(s(t)&&!(t instanceof ct))return m(t,"__ob__")&&t.__ob__ instanceof mt?n=t.__ob__:!gt||Q()||!Array.isArray(t)&&!c(t)||!Object.isExtensible(t)||t._isVue||t.__v_isMPComponent||(n=new mt(t)),e&&n&&n.vmCount++,n}function At(t,e,n,r,i){var o=new at,a=Object.getOwnPropertyDescriptor(t,e);if(!a||!1!==a.configurable){var s=a&&a.get,u=a&&a.set;s&&!u||2!==arguments.length||(n=t[e]);var c=!i&&bt(n);Object.defineProperty(t,e,{enumerable:!0,configurable:!0,get:function(){var e=s?s.call(t):n;return at.SharedObject.target&&(o.depend(),c&&(c.dep.depend(),Array.isArray(e)&&xt(e))),e},set:function(e){var r=s?s.call(t):n;e===r||e!==e&&r!==r||s&&!u||(u?u.call(t,e):n=e,c=!i&&bt(e),o.notify())}})}}function wt(t,e,n){if(Array.isArray(t)&&l(e))return t.length=Math.max(t.length,e),t.splice(e,1,n),n;if(e in t&&!(e in Object.prototype))return t[e]=n,n;var r=t.__ob__;return t._isVue||r&&r.vmCount?n:r?(At(r.value,e,n),r.dep.notify(),n):(t[e]=n,n)}function Ot(t,e){if(Array.isArray(t)&&l(e))t.splice(e,1);else{var n=t.__ob__;t._isVue||n&&n.vmCount||m(t,e)&&(delete t[e],n&&n.dep.notify())}}function xt(t){for(var e=void 0,n=0,r=t.length;n<r;n++)e=t[n],e&&e.__ob__&&e.__ob__.dep.depend(),Array.isArray(e)&&xt(e)}mt.prototype.walk=function(t){for(var e=Object.keys(t),n=0;n<e.length;n++)At(t,e[n])},mt.prototype.observeArray=function(t){for(var e=0,n=t.length;e<n;e++)bt(t[e])};var St=N.optionMergeStrategies;function kt(t,e){if(!e)return t;for(var n,r,i,o=rt?Reflect.ownKeys(e):Object.keys(e),a=0;a<o.length;a++)n=o[a],"__ob__"!==n&&(r=t[n],i=e[n],m(t,n)?r!==i&&c(r)&&c(i)&&kt(r,i):wt(t,n,i));return t}function Et(t,e,n){return n?function(){var r="function"===typeof e?e.call(n,n):e,i="function"===typeof t?t.call(n,n):t;return r?kt(r,i):i}:e?t?function(){return kt("function"===typeof e?e.call(this,this):e,"function"===typeof t?t.call(this,this):t)}:e:t}function Tt(t,e){var n=e?t?t.concat(e):Array.isArray(e)?e:[e]:t;return n?function(t){for(var e=[],n=0;n<t.length;n++)-1===e.indexOf(t[n])&&e.push(t[n]);return e}(n):n}function Ct(t,e,n,r){var i=Object.create(t||null);return e?E(i,e):i}St.data=function(t,e,n){return n?Et(t,e,n):e&&"function"!==typeof e?t:Et(t,e)},D.forEach((function(t){St[t]=Tt})),M.forEach((function(t){St[t+"s"]=Ct})),St.watch=function(t,e,n,r){if(t===q&&(t=void 0),e===q&&(e=void 0),!e)return Object.create(t||null);if(!t)return e;var i={};for(var o in E(i,t),e){var a=i[o],s=e[o];a&&!Array.isArray(a)&&(a=[a]),i[o]=a?a.concat(s):Array.isArray(s)?s:[s]}return i},St.props=St.methods=St.inject=St.computed=function(t,e,n,r){if(!t)return e;var i=Object.create(null);return E(i,t),e&&E(i,e),i},St.provide=Et;var Pt=function(t,e){return void 0===e?t:e};function $t(t,e,n){if("function"===typeof e&&(e=e.options),function(t,e){var n=t.props;if(n){var r,i,o,a={};if(Array.isArray(n)){r=n.length;while(r--)i=n[r],"string"===typeof i&&(o=A(i),a[o]={type:null})}else if(c(n))for(var s in n)i=n[s],o=A(s),a[o]=c(i)?i:{type:i};else 0;t.props=a}}(e),function(t,e){var n=t.inject;if(n){var r=t.inject={};if(Array.isArray(n))for(var i=0;i<n.length;i++)r[n[i]]={from:n[i]};else if(c(n))for(var o in n){var a=n[o];r[o]=c(a)?E({from:o},a):{from:a}}else 0}}(e),function(t){var e=t.directives;if(e)for(var n in e){var r=e[n];"function"===typeof r&&(e[n]={bind:r,update:r})}}(e),!e._base&&(e.extends&&(t=$t(t,e.extends,n)),e.mixins))for(var r=0,i=e.mixins.length;r<i;r++)t=$t(t,e.mixins[r],n);var o,a={};for(o in t)s(o);for(o in e)m(t,o)||s(o);function s(r){var i=St[r]||Pt;a[r]=i(t[r],e[r],n,r)}return a}function Lt(t,e,n,r){if("string"===typeof n){var i=t[e];if(m(i,n))return i[n];var o=A(n);if(m(i,o))return i[o];var a=w(o);if(m(i,a))return i[a];var s=i[n]||i[o]||i[a];return s}}function jt(t,e,n,r){var i=e[t],o=!m(n,t),a=n[t],s=Dt(Boolean,i.type);if(s>-1)if(o&&!m(i,"default"))a=!1;else if(""===a||a===x(t)){var u=Dt(String,i.type);(u<0||s<u)&&(a=!0)}if(void 0===a){a=function(t,e,n){if(!m(e,"default"))return;var r=e.default;0;if(t&&t.$options.propsData&&void 0===t.$options.propsData[n]&&void 0!==t._props[n])return t._props[n];return"function"===typeof r&&"Function"!==It(e.type)?r.call(t):r}(r,i,t);var c=gt;yt(!0),bt(a),yt(c)}return a}function It(t){var e=t&&t.toString().match(/^\s*function (\w+)/);return e?e[1]:""}function Mt(t,e){return It(t)===It(e)}function Dt(t,e){if(!Array.isArray(e))return Mt(e,t)?0:-1;for(var n=0,r=e.length;n<r;n++)if(Mt(e[n],t))return n;return-1}function Nt(t,e,n){st();try{if(e){var r=e;while(r=r.$parent){var i=r.$options.errorCaptured;if(i)for(var o=0;o<i.length;o++)try{var a=!1===i[o].call(r,t,e,n);if(a)return}catch(Nn){Ut(Nn,r,"errorCaptured hook")}}}Ut(t,e,n)}finally{ut()}}function Rt(t,e,n,r,i){var o;try{o=n?t.apply(e,n):t.call(e),o&&!o._isVue&&f(o)&&!o._handled&&(o.catch((function(t){return Nt(t,r,i+" (Promise/async)")})),o._handled=!0)}catch(Nn){Nt(Nn,r,i)}return o}function Ut(t,e,n){if(N.errorHandler)try{return N.errorHandler.call(null,t,e,n)}catch(Nn){Nn!==t&&Ht(Nn,null,"config.errorHandler")}Ht(t,e,n)}function Ht(t,e,n){if(!z&&!G||"undefined"===typeof console)throw t;console.error(t)}var Bt,Ft=[],Vt=!1;function zt(){Vt=!1;var t=Ft.slice(0);Ft.length=0;for(var e=0;e<t.length;e++)t[e]()}if("undefined"!==typeof Promise&&et(Promise)){var Gt=Promise.resolve();Bt=function(){Gt.then(zt),Y&&setTimeout(C)}}else if(X||"undefined"===typeof MutationObserver||!et(MutationObserver)&&"[object MutationObserverConstructor]"!==MutationObserver.toString())Bt="undefined"!==typeof setImmediate&&et(setImmediate)?function(){setImmediate(zt)}:function(){setTimeout(zt,0)};else{var Kt=1,Wt=new MutationObserver(zt),Xt=document.createTextNode(String(Kt));Wt.observe(Xt,{characterData:!0}),Bt=function(){Kt=(Kt+1)%2,Xt.data=String(Kt)}}function Jt(t,e){var n;if(Ft.push((function(){if(t)try{t.call(e)}catch(Nn){Nt(Nn,e,"nextTick")}else n&&n(e)})),Vt||(Vt=!0,Bt()),!t&&"undefined"!==typeof Promise)return new Promise((function(t){n=t}))}var Yt=new nt;function qt(t){(function t(e,n){var r,i,o=Array.isArray(e);if(!o&&!s(e)||Object.isFrozen(e)||e instanceof ct)return;if(e.__ob__){var a=e.__ob__.dep.id;if(n.has(a))return;n.add(a)}if(o){r=e.length;while(r--)t(e[r],n)}else{i=Object.keys(e),r=i.length;while(r--)t(e[i[r]],n)}})(t,Yt),Yt.clear()}var Zt=_((function(t){var e="&"===t.charAt(0);t=e?t.slice(1):t;var n="~"===t.charAt(0);t=n?t.slice(1):t;var r="!"===t.charAt(0);return t=r?t.slice(1):t,{name:t,once:n,capture:r,passive:e}}));function Qt(t,e){function n(){var t=arguments,r=n.fns;if(!Array.isArray(r))return Rt(r,null,arguments,e,"v-on handler");for(var i=r.slice(),o=0;o<i.length;o++)Rt(i[o],null,t,e,"v-on handler")}return n.fns=t,n}function te(t,e,n,o){var a=e.options.mpOptions&&e.options.mpOptions.properties;if(r(a))return n;var s=e.options.mpOptions.externalClasses||[],u=t.attrs,c=t.props;if(i(u)||i(c))for(var l in a){var f=x(l),d=ee(n,c,l,f,!0)||ee(n,u,l,f,!1);d&&n[l]&&-1!==s.indexOf(f)&&o[A(n[l])]&&(n[l]=o[A(n[l])])}return n}function ee(t,e,n,r,o){if(i(e)){if(m(e,n))return t[n]=e[n],o||delete e[n],!0;if(m(e,r))return t[n]=e[r],o||delete e[r],!0}return!1}function ne(t){return a(t)?[dt(t)]:Array.isArray(t)?function t(e,n){var s,u,c,l,f=[];for(s=0;s<e.length;s++)u=e[s],r(u)||"boolean"===typeof u||(c=f.length-1,l=f[c],Array.isArray(u)?u.length>0&&(u=t(u,(n||"")+"_"+s),re(u[0])&&re(l)&&(f[c]=dt(l.text+u[0].text),u.shift()),f.push.apply(f,u)):a(u)?re(l)?f[c]=dt(l.text+u):""!==u&&f.push(dt(u)):re(u)&&re(l)?f[c]=dt(l.text+u.text):(o(e._isVList)&&i(u.tag)&&r(u.key)&&i(n)&&(u.key="__vlist"+n+"_"+s+"__"),f.push(u)));return f}(t):void 0}function re(t){return i(t)&&i(t.text)&&function(t){return!1===t}(t.isComment)}function ie(t){var e=t.$options.provide;e&&(t._provided="function"===typeof e?e.call(t):e)}function oe(t){var e=ae(t.$options.inject,t);e&&(yt(!1),Object.keys(e).forEach((function(n){At(t,n,e[n])})),yt(!0))}function ae(t,e){if(t){for(var n=Object.create(null),r=rt?Reflect.ownKeys(t):Object.keys(t),i=0;i<r.length;i++){var o=r[i];if("__ob__"!==o){var a=t[o].from,s=e;while(s){if(s._provided&&m(s._provided,a)){n[o]=s._provided[a];break}s=s.$parent}if(!s)if("default"in t[o]){var u=t[o].default;n[o]="function"===typeof u?u.call(e):u}else 0}}return n}}function se(t,e){if(!t||!t.length)return{};for(var n={},r=0,i=t.length;r<i;r++){var o=t[r],a=o.data;if(a&&a.attrs&&a.attrs.slot&&delete a.attrs.slot,o.context!==e&&o.fnContext!==e||!a||null==a.slot)o.asyncMeta&&o.asyncMeta.data&&"page"===o.asyncMeta.data.slot?(n["page"]||(n["page"]=[])).push(o):(n.default||(n.default=[])).push(o);else{var s=a.slot,u=n[s]||(n[s]=[]);"template"===o.tag?u.push.apply(u,o.children||[]):u.push(o)}}for(var c in n)n[c].every(ue)&&delete n[c];return n}function ue(t){return t.isComment&&!t.asyncFactory||" "===t.text}function ce(t,e,r){var i,o=Object.keys(e).length>0,a=t?!!t.$stable:!o,s=t&&t.$key;if(t){if(t._normalized)return t._normalized;if(a&&r&&r!==n&&s===r.$key&&!o&&!r.$hasNormal)return r;for(var u in i={},t)t[u]&&"$"!==u[0]&&(i[u]=le(e,u,t[u]))}else i={};for(var c in e)c in i||(i[c]=fe(e,c));return t&&Object.isExtensible(t)&&(t._normalized=i),H(i,"$stable",a),H(i,"$key",s),H(i,"$hasNormal",o),i}function le(t,e,n){var r=function(){var t=arguments.length?n.apply(null,arguments):n({});return t=t&&"object"===typeof t&&!Array.isArray(t)?[t]:ne(t),t&&(0===t.length||1===t.length&&t[0].isComment)?void 0:t};return n.proxy&&Object.defineProperty(t,e,{get:r,enumerable:!0,configurable:!0}),r}function fe(t,e){return function(){return t[e]}}function de(t,e){var n,r,o,a,u;if(Array.isArray(t)||"string"===typeof t)for(n=new Array(t.length),r=0,o=t.length;r<o;r++)n[r]=e(t[r],r,r,r);else if("number"===typeof t)for(n=new Array(t),r=0;r<t;r++)n[r]=e(r+1,r,r,r);else if(s(t))if(rt&&t[Symbol.iterator]){n=[];var c=t[Symbol.iterator](),l=c.next();while(!l.done)n.push(e(l.value,n.length,r,r++)),l=c.next()}else for(a=Object.keys(t),n=new Array(a.length),r=0,o=a.length;r<o;r++)u=a[r],n[r]=e(t[u],u,r,r);return i(n)||(n=[]),n._isVList=!0,n}function he(t,e,n,r){var i,o=this.$scopedSlots[t];o?(n=n||{},r&&(n=E(E({},r),n)),i=o(n,this,n._i)||e):i=this.$slots[t]||e;var a=n&&n.slot;return a?this.$createElement("template",{slot:a},i):i}function pe(t){return Lt(this.$options,"filters",t)||$}function ve(t,e){return Array.isArray(t)?-1===t.indexOf(e):t!==e}function ge(t,e,n,r,i){var o=N.keyCodes[e]||n;return i&&r&&!N.keyCodes[e]?ve(i,r):o?ve(o,t):r?x(r)!==e:void 0}function ye(t,e,n,r,i){if(n)if(s(n)){var o;Array.isArray(n)&&(n=T(n));var a=function(a){if("class"===a||"style"===a||v(a))o=t;else{var s=t.attrs&&t.attrs.type;o=r||N.mustUseProp(e,s,a)?t.domProps||(t.domProps={}):t.attrs||(t.attrs={})}var u=A(a),c=x(a);if(!(u in o)&&!(c in o)&&(o[a]=n[a],i)){var l=t.on||(t.on={});l["update:"+a]=function(t){n[a]=t}}};for(var u in n)a(u)}else;return t}function me(t,e){var n=this._staticTrees||(this._staticTrees=[]),r=n[t];return r&&!e||(r=n[t]=this.$options.staticRenderFns[t].call(this._renderProxy,null,this),be(r,"__static__"+t,!1)),r}function _e(t,e,n){return be(t,"__once__"+e+(n?"_"+n:""),!0),t}function be(t,e,n){if(Array.isArray(t))for(var r=0;r<t.length;r++)t[r]&&"string"!==typeof t[r]&&Ae(t[r],e+"_"+r,n);else Ae(t,e,n)}function Ae(t,e,n){t.isStatic=!0,t.key=e,t.isOnce=n}function we(t,e){if(e)if(c(e)){var n=t.on=t.on?E({},t.on):{};for(var r in e){var i=n[r],o=e[r];n[r]=i?[].concat(i,o):o}}else;return t}function Oe(t,e,n,r){e=e||{$stable:!n};for(var i=0;i<t.length;i++){var o=t[i];Array.isArray(o)?Oe(o,e,n):o&&(o.proxy&&(o.fn.proxy=!0),e[o.key]=o.fn)}return r&&(e.$key=r),e}function xe(t,e){for(var n=0;n<e.length;n+=2){var r=e[n];"string"===typeof r&&r&&(t[e[n]]=e[n+1])}return t}function Se(t,e){return"string"===typeof t?e+t:t}function ke(t){t._o=_e,t._n=h,t._s=d,t._l=de,t._t=he,t._q=L,t._i=j,t._m=me,t._f=pe,t._k=ge,t._b=ye,t._v=dt,t._e=ft,t._u=Oe,t._g=we,t._d=xe,t._p=Se}function Ee(t,e,r,i,a){var s,u=this,c=a.options;m(i,"_uid")?(s=Object.create(i),s._original=i):(s=i,i=i._original);var l=o(c._compiled),f=!l;this.data=t,this.props=e,this.children=r,this.parent=i,this.listeners=t.on||n,this.injections=ae(c.inject,i),this.slots=function(){return u.$slots||ce(t.scopedSlots,u.$slots=se(r,i)),u.$slots},Object.defineProperty(this,"scopedSlots",{enumerable:!0,get:function(){return ce(t.scopedSlots,this.slots())}}),l&&(this.$options=c,this.$slots=this.slots(),this.$scopedSlots=ce(t.scopedSlots,this.$slots)),c._scopeId?this._c=function(t,e,n,r){var o=Ie(s,t,e,n,r,f);return o&&!Array.isArray(o)&&(o.fnScopeId=c._scopeId,o.fnContext=i),o}:this._c=function(t,e,n,r){return Ie(s,t,e,n,r,f)}}function Te(t,e,n,r,i){var o=function(t){var e=new ct(t.tag,t.data,t.children&&t.children.slice(),t.text,t.elm,t.context,t.componentOptions,t.asyncFactory);return e.ns=t.ns,e.isStatic=t.isStatic,e.key=t.key,e.isComment=t.isComment,e.fnContext=t.fnContext,e.fnOptions=t.fnOptions,e.fnScopeId=t.fnScopeId,e.asyncMeta=t.asyncMeta,e.isCloned=!0,e}(t);return o.fnContext=n,o.fnOptions=r,e.slot&&((o.data||(o.data={})).slot=e.slot),o}function Ce(t,e){for(var n in e)t[A(n)]=e[n]}ke(Ee.prototype);var Pe={init:function(t,e){if(t.componentInstance&&!t.componentInstance._isDestroyed&&t.data.keepAlive){var n=t;Pe.prepatch(n,n)}else{var r=t.componentInstance=function(t,e){var n={_isComponent:!0,_parentVnode:t,parent:e},r=t.data.inlineTemplate;i(r)&&(n.render=r.render,n.staticRenderFns=r.staticRenderFns);return new t.componentOptions.Ctor(n)}(t,Ve);r.$mount(e?t.elm:void 0,e)}},prepatch:function(t,e){var r=e.componentOptions,i=e.componentInstance=t.componentInstance;(function(t,e,r,i,o){0;var a=i.data.scopedSlots,s=t.$scopedSlots,u=!!(a&&!a.$stable||s!==n&&!s.$stable||a&&t.$scopedSlots.$key!==a.$key),c=!!(o||t.$options._renderChildren||u);t.$options._parentVnode=i,t.$vnode=i,t._vnode&&(t._vnode.parent=i);if(t.$options._renderChildren=o,t.$attrs=i.data.attrs||n,t.$listeners=r||n,e&&t.$options.props){yt(!1);for(var l=t._props,f=t.$options._propKeys||[],d=0;d<f.length;d++){var h=f[d],p=t.$options.props;l[h]=jt(h,p,e,t)}yt(!0),t.$options.propsData=e}t._$updateProperties&&t._$updateProperties(t),r=r||n;var v=t.$options._parentListeners;t.$options._parentListeners=r,Fe(t,r,v),c&&(t.$slots=se(o,i.context),t.$forceUpdate());0})(i,r.propsData,r.listeners,e,r.children)},insert:function(t){var e=t.context,n=t.componentInstance;n._isMounted||(Ke(n,"onServiceCreated"),Ke(n,"onServiceAttached"),n._isMounted=!0,Ke(n,"mounted")),t.data.keepAlive&&(e._isMounted?function(t){t._inactive=!1,Xe.push(t)}(n):Ge(n,!0))},destroy:function(t){var e=t.componentInstance;e._isDestroyed||(t.data.keepAlive?function t(e,n){if(n&&(e._directInactive=!0,ze(e)))return;if(!e._inactive){e._inactive=!0;for(var r=0;r<e.$children.length;r++)t(e.$children[r]);Ke(e,"deactivated")}}(e,!0):e.$destroy())}},$e=Object.keys(Pe);function Le(t,e,a,u,c){if(!r(t)){var l=a.$options._base;if(s(t)&&(t=l.extend(t)),"function"===typeof t){var d;if(r(t.cid)&&(d=t,t=function(t,e){if(o(t.error)&&i(t.errorComp))return t.errorComp;if(i(t.resolved))return t.resolved;var n=De;n&&i(t.owners)&&-1===t.owners.indexOf(n)&&t.owners.push(n);if(o(t.loading)&&i(t.loadingComp))return t.loadingComp;if(n&&!i(t.owners)){var a=t.owners=[n],u=!0,c=null,l=null;n.$on("hook:destroyed",(function(){return g(a,n)}));var d=function(t){for(var e=0,n=a.length;e<n;e++)a[e].$forceUpdate();t&&(a.length=0,null!==c&&(clearTimeout(c),c=null),null!==l&&(clearTimeout(l),l=null))},h=I((function(n){t.resolved=Ne(n,e),u?a.length=0:d(!0)})),p=I((function(e){i(t.errorComp)&&(t.error=!0,d(!0))})),v=t(h,p);return s(v)&&(f(v)?r(t.resolved)&&v.then(h,p):f(v.component)&&(v.component.then(h,p),i(v.error)&&(t.errorComp=Ne(v.error,e)),i(v.loading)&&(t.loadingComp=Ne(v.loading,e),0===v.delay?t.loading=!0:c=setTimeout((function(){c=null,r(t.resolved)&&r(t.error)&&(t.loading=!0,d(!1))}),v.delay||200)),i(v.timeout)&&(l=setTimeout((function(){l=null,r(t.resolved)&&p(null)}),v.timeout)))),u=!1,t.loading?t.loadingComp:t.resolved}}(d,l),void 0===t))return function(t,e,n,r,i){var o=ft();return o.asyncFactory=t,o.asyncMeta={data:e,context:n,children:r,tag:i},o}(d,e,a,u,c);e=e||{},pn(t),i(e.model)&&function(t,e){var n=t.model&&t.model.prop||"value",r=t.model&&t.model.event||"input";(e.attrs||(e.attrs={}))[n]=e.model.value;var o=e.on||(e.on={}),a=o[r],s=e.model.callback;i(a)?(Array.isArray(a)?-1===a.indexOf(s):a!==s)&&(o[r]=[s].concat(a)):o[r]=s}(t.options,e);var h=function(t,e,n,o){var a=e.options.props;if(r(a))return te(t,e,{},o);var s={},u=t.attrs,c=t.props;if(i(u)||i(c))for(var l in a){var f=x(l);ee(s,c,l,f,!0)||ee(s,u,l,f,!1)}return te(t,e,s,o)}(e,t,0,a);if(o(t.options.functional))return function(t,e,r,o,a){var s=t.options,u={},c=s.props;if(i(c))for(var l in c)u[l]=jt(l,c,e||n);else i(r.attrs)&&Ce(u,r.attrs),i(r.props)&&Ce(u,r.props);var f=new Ee(r,u,a,o,t),d=s.render.call(null,f._c,f);if(d instanceof ct)return Te(d,r,f.parent,s,f);if(Array.isArray(d)){for(var h=ne(d)||[],p=new Array(h.length),v=0;v<h.length;v++)p[v]=Te(h[v],r,f.parent,s,f);return p}}(t,h,e,a,u);var p=e.on;if(e.on=e.nativeOn,o(t.options.abstract)){var v=e.slot;e={},v&&(e.slot=v)}(function(t){for(var e=t.hook||(t.hook={}),n=0;n<$e.length;n++){var r=$e[n],i=e[r],o=Pe[r];i===o||i&&i._merged||(e[r]=i?je(o,i):o)}})(e);var y=t.options.name||c,m=new ct("vue-component-"+t.cid+(y?"-"+y:""),e,void 0,void 0,void 0,a,{Ctor:t,propsData:h,listeners:p,tag:c,children:u},d);return m}}}function je(t,e){var n=function(n,r){t(n,r),e(n,r)};return n._merged=!0,n}function Ie(t,e,n,u,c,l){return(Array.isArray(n)||a(n))&&(c=u,u=n,n=void 0),o(l)&&(c=2),function(t,e,n,a,u){if(i(n)&&i(n.__ob__))return ft();i(n)&&i(n.is)&&(e=n.is);if(!e)return ft();0;Array.isArray(a)&&"function"===typeof a[0]&&(n=n||{},n.scopedSlots={default:a[0]},a.length=0);2===u?a=ne(a):1===u&&(a=function(t){for(var e=0;e<t.length;e++)if(Array.isArray(t[e]))return Array.prototype.concat.apply([],t);return t}(a));var c,l;if("string"===typeof e){var f;l=t.$vnode&&t.$vnode.ns||N.getTagNamespace(e),c=N.isReservedTag(e)?new ct(N.parsePlatformTagName(e),n,a,void 0,void 0,t):n&&n.pre||!i(f=Lt(t.$options,"components",e))?new ct(e,n,a,void 0,void 0,t):Le(f,n,t,a,e)}else c=Le(e,n,t,a);return Array.isArray(c)?c:i(c)?(i(l)&&function t(e,n,a){e.ns=n,"foreignObject"===e.tag&&(n=void 0,a=!0);if(i(e.children))for(var s=0,u=e.children.length;s<u;s++){var c=e.children[s];i(c.tag)&&(r(c.ns)||o(a)&&"svg"!==c.tag)&&t(c,n,a)}}(c,l),i(n)&&function(t){s(t.style)&&qt(t.style);s(t.class)&&qt(t.class)}(n),c):ft()}(t,e,n,u,c)}var Me,De=null;function Ne(t,e){return(t.__esModule||rt&&"Module"===t[Symbol.toStringTag])&&(t=t.default),s(t)?e.extend(t):t}function Re(t){return t.isComment&&t.asyncFactory}function Ue(t,e){Me.$on(t,e)}function He(t,e){Me.$off(t,e)}function Be(t,e){var n=Me;return function r(){var i=e.apply(null,arguments);null!==i&&n.$off(t,r)}}function Fe(t,e,n){Me=t,function(t,e,n,i,a,s){var u,c,l,f;for(u in t)c=t[u],l=e[u],f=Zt(u),r(c)||(r(l)?(r(c.fns)&&(c=t[u]=Qt(c,s)),o(f.once)&&(c=t[u]=a(f.name,c,f.capture)),n(f.name,c,f.capture,f.passive,f.params)):c!==l&&(l.fns=c,t[u]=l));for(u in e)r(t[u])&&(f=Zt(u),i(f.name,e[u],f.capture))}(e,n||{},Ue,He,Be,t),Me=void 0}var Ve=null;function ze(t){while(t&&(t=t.$parent))if(t._inactive)return!0;return!1}function Ge(t,e){if(e){if(t._directInactive=!1,ze(t))return}else if(t._directInactive)return;if(t._inactive||null===t._inactive){t._inactive=!1;for(var n=0;n<t.$children.length;n++)Ge(t.$children[n]);Ke(t,"activated")}}function Ke(t,e){st();var n=t.$options[e],r=e+" hook";if(n)for(var i=0,o=n.length;i<o;i++)Rt(n[i],t,null,t,r);t._hasHookEvent&&t.$emit("hook:"+e),ut()}var We=[],Xe=[],Je={},Ye=!1,qe=!1,Ze=0;var Qe=Date.now;if(z&&!X){var tn=window.performance;tn&&"function"===typeof tn.now&&Qe()>document.createEvent("Event").timeStamp&&(Qe=function(){return tn.now()})}function en(){var t,e;for(Qe(),qe=!0,We.sort((function(t,e){return t.id-e.id})),Ze=0;Ze<We.length;Ze++)t=We[Ze],t.before&&t.before(),e=t.id,Je[e]=null,t.run();var n=Xe.slice(),r=We.slice();(function(){Ze=We.length=Xe.length=0,Je={},Ye=qe=!1})(),function(t){for(var e=0;e<t.length;e++)t[e]._inactive=!0,Ge(t[e],!0)}(n),function(t){var e=t.length;while(e--){var n=t[e],r=n.vm;r._watcher===n&&r._isMounted&&!r._isDestroyed&&Ke(r,"updated")}}(r),tt&&N.devtools&&tt.emit("flush")}var nn=0,rn=function(t,e,n,r,i){this.vm=t,i&&(t._watcher=this),t._watchers.push(this),r?(this.deep=!!r.deep,this.user=!!r.user,this.lazy=!!r.lazy,this.sync=!!r.sync,this.before=r.before):this.deep=this.user=this.lazy=this.sync=!1,this.cb=n,this.id=++nn,this.active=!0,this.dirty=this.lazy,this.deps=[],this.newDeps=[],this.depIds=new nt,this.newDepIds=new nt,this.expression="","function"===typeof e?this.getter=e:(this.getter=function(t){if(!B.test(t)){var e=t.split(".");return function(t){for(var n=0;n<e.length;n++){if(!t)return;t=t[e[n]]}return t}}}(e),this.getter||(this.getter=C)),this.value=this.lazy?void 0:this.get()};rn.prototype.get=function(){var t;st(this);var e=this.vm;try{t=this.getter.call(e,e)}catch(Nn){if(!this.user)throw Nn;Nt(Nn,e,'getter for watcher "'+this.expression+'"')}finally{this.deep&&qt(t),ut(),this.cleanupDeps()}return t},rn.prototype.addDep=function(t){var e=t.id;this.newDepIds.has(e)||(this.newDepIds.add(e),this.newDeps.push(t),this.depIds.has(e)||t.addSub(this))},rn.prototype.cleanupDeps=function(){var t=this.deps.length;while(t--){var e=this.deps[t];this.newDepIds.has(e.id)||e.removeSub(this)}var n=this.depIds;this.depIds=this.newDepIds,this.newDepIds=n,this.newDepIds.clear(),n=this.deps,this.deps=this.newDeps,this.newDeps=n,this.newDeps.length=0},rn.prototype.update=function(){this.lazy?this.dirty=!0:this.sync?this.run():function(t){var e=t.id;if(null==Je[e]){if(Je[e]=!0,qe){var n=We.length-1;while(n>Ze&&We[n].id>t.id)n--;We.splice(n+1,0,t)}else We.push(t);Ye||(Ye=!0,Jt(en))}}(this)},rn.prototype.run=function(){if(this.active){var t=this.get();if(t!==this.value||s(t)||this.deep){var e=this.value;if(this.value=t,this.user)try{this.cb.call(this.vm,t,e)}catch(Nn){Nt(Nn,this.vm,'callback for watcher "'+this.expression+'"')}else this.cb.call(this.vm,t,e)}}},rn.prototype.evaluate=function(){this.value=this.get(),this.dirty=!1},rn.prototype.depend=function(){var t=this.deps.length;while(t--)this.deps[t].depend()},rn.prototype.teardown=function(){if(this.active){this.vm._isBeingDestroyed||g(this.vm._watchers,this);var t=this.deps.length;while(t--)this.deps[t].removeSub(this);this.active=!1}};var on={enumerable:!0,configurable:!0,get:C,set:C};function an(t,e,n){on.get=function(){return this[e][n]},on.set=function(t){this[e][n]=t},Object.defineProperty(t,n,on)}function sn(t){t._watchers=[];var e=t.$options;e.props&&function(t,e){var n=t.$options.propsData||{},r=t._props={},i=t.$options._propKeys=[],o=!t.$parent;o||yt(!1);var a=function(o){i.push(o);var a=jt(o,e,n,t);At(r,o,a),o in t||an(t,"_props",o)};for(var s in e)a(s);yt(!0)}(t,e.props),e.methods&&function(t,e){t.$options.props;for(var n in e)t[n]="function"!==typeof e[n]?C:S(e[n],t)}(t,e.methods),e.data?function(t){var e=t.$options.data;e=t._data="function"===typeof e?function(t,e){st();try{return t.call(e,e)}catch(Nn){return Nt(Nn,e,"data()"),{}}finally{ut()}}(e,t):e||{},c(e)||(e={});var n=Object.keys(e),r=t.$options.props,i=(t.$options.methods,n.length);while(i--){var o=n[i];0,r&&m(r,o)||U(o)||an(t,"_data",o)}bt(e,!0)}(t):bt(t._data={},!0),e.computed&&function(t,e){var n=t._computedWatchers=Object.create(null),r=Q();for(var i in e){var o=e[i],a="function"===typeof o?o:o.get;0,r||(n[i]=new rn(t,a||C,C,un)),i in t||cn(t,i,o)}}(t,e.computed),e.watch&&e.watch!==q&&function(t,e){for(var n in e){var r=e[n];if(Array.isArray(r))for(var i=0;i<r.length;i++)dn(t,n,r[i]);else dn(t,n,r)}}(t,e.watch)}var un={lazy:!0};function cn(t,e,n){var r=!Q();"function"===typeof n?(on.get=r?ln(e):fn(n),on.set=C):(on.get=n.get?r&&!1!==n.cache?ln(e):fn(n.get):C,on.set=n.set||C),Object.defineProperty(t,e,on)}function ln(t){return function(){var e=this._computedWatchers&&this._computedWatchers[t];if(e)return e.dirty&&e.evaluate(),at.SharedObject.target&&e.depend(),e.value}}function fn(t){return function(){return t.call(this,this)}}function dn(t,e,n,r){return c(n)&&(r=n,n=n.handler),"string"===typeof n&&(n=t[n]),t.$watch(e,n,r)}var hn=0;function pn(t){var e=t.options;if(t.super){var n=pn(t.super),r=t.superOptions;if(n!==r){t.superOptions=n;var i=function(t){var e,n=t.options,r=t.sealedOptions;for(var i in n)n[i]!==r[i]&&(e||(e={}),e[i]=n[i]);return e}(t);i&&E(t.extendOptions,i),e=t.options=$t(n,t.extendOptions),e.name&&(e.components[e.name]=t)}}return e}function vn(t){this._init(t)}function gn(t){t.cid=0;var e=1;t.extend=function(t){t=t||{};var n=this,r=n.cid,i=t._Ctor||(t._Ctor={});if(i[r])return i[r];var o=t.name||n.options.name;var a=function(t){this._init(t)};return a.prototype=Object.create(n.prototype),a.prototype.constructor=a,a.cid=e++,a.options=$t(n.options,t),a["super"]=n,a.options.props&&function(t){var e=t.options.props;for(var n in e)an(t.prototype,"_props",n)}(a),a.options.computed&&function(t){var e=t.options.computed;for(var n in e)cn(t.prototype,n,e[n])}(a),a.extend=n.extend,a.mixin=n.mixin,a.use=n.use,M.forEach((function(t){a[t]=n[t]})),o&&(a.options.components[o]=a),a.superOptions=n.options,a.extendOptions=t,a.sealedOptions=E({},a.options),i[r]=a,a}}function yn(t){return t&&(t.Ctor.options.name||t.tag)}function mn(t,e){return Array.isArray(t)?t.indexOf(e)>-1:"string"===typeof t?t.split(",").indexOf(e)>-1:!!function(t){return"[object RegExp]"===u.call(t)}(t)&&t.test(e)}function _n(t,e){var n=t.cache,r=t.keys,i=t._vnode;for(var o in n){var a=n[o];if(a){var s=yn(a.componentOptions);s&&!e(s)&&bn(n,o,r,i)}}}function bn(t,e,n,r){var i=t[e];!i||r&&i.tag===r.tag||i.componentInstance.$destroy(),t[e]=null,g(n,e)}(function(t){t.prototype._init=function(t){var e=this;e._uid=hn++,e._isVue=!0,t&&t._isComponent?function(t,e){var n=t.$options=Object.create(t.constructor.options),r=e._parentVnode;n.parent=e.parent,n._parentVnode=r;var i=r.componentOptions;n.propsData=i.propsData,n._parentListeners=i.listeners,n._renderChildren=i.children,n._componentTag=i.tag,e.render&&(n.render=e.render,n.staticRenderFns=e.staticRenderFns)}(e,t):e.$options=$t(pn(e.constructor),t||{},e),e._renderProxy=e,e._self=e,function(t){var e=t.$options,n=e.parent;if(n&&!e.abstract){while(n.$options.abstract&&n.$parent)n=n.$parent;n.$children.push(t)}t.$parent=n,t.$root=n?n.$root:t,t.$children=[],t.$refs={},t._watcher=null,t._inactive=null,t._directInactive=!1,t._isMounted=!1,t._isDestroyed=!1,t._isBeingDestroyed=!1}(e),function(t){t._events=Object.create(null),t._hasHookEvent=!1;var e=t.$options._parentListeners;e&&Fe(t,e)}(e),function(t){t._vnode=null,t._staticTrees=null;var e=t.$options,r=t.$vnode=e._parentVnode,i=r&&r.context;t.$slots=se(e._renderChildren,i),t.$scopedSlots=n,t._c=function(e,n,r,i){return Ie(t,e,n,r,i,!1)},t.$createElement=function(e,n,r,i){return Ie(t,e,n,r,i,!0)};var o=r&&r.data;At(t,"$attrs",o&&o.attrs||n,null,!0),At(t,"$listeners",e._parentListeners||n,null,!0)}(e),Ke(e,"beforeCreate"),!e._$fallback&&oe(e),sn(e),!e._$fallback&&ie(e),!e._$fallback&&Ke(e,"created"),e.$options.el&&e.$mount(e.$options.el)}})(vn),function(t){var e={get:function(){return this._data}},n={get:function(){return this._props}};Object.defineProperty(t.prototype,"$data",e),Object.defineProperty(t.prototype,"$props",n),t.prototype.$set=wt,t.prototype.$delete=Ot,t.prototype.$watch=function(t,e,n){if(c(e))return dn(this,t,e,n);n=n||{},n.user=!0;var r=new rn(this,t,e,n);if(n.immediate)try{e.call(this,r.value)}catch(i){Nt(i,this,'callback for immediate watcher "'+r.expression+'"')}return function(){r.teardown()}}}(vn),function(t){var e=/^hook:/;t.prototype.$on=function(t,n){var r=this;if(Array.isArray(t))for(var i=0,o=t.length;i<o;i++)r.$on(t[i],n);else(r._events[t]||(r._events[t]=[])).push(n),e.test(t)&&(r._hasHookEvent=!0);return r},t.prototype.$once=function(t,e){var n=this;function r(){n.$off(t,r),e.apply(n,arguments)}return r.fn=e,n.$on(t,r),n},t.prototype.$off=function(t,e){var n=this;if(!arguments.length)return n._events=Object.create(null),n;if(Array.isArray(t)){for(var r=0,i=t.length;r<i;r++)n.$off(t[r],e);return n}var o,a=n._events[t];if(!a)return n;if(!e)return n._events[t]=null,n;var s=a.length;while(s--)if(o=a[s],o===e||o.fn===e){a.splice(s,1);break}return n},t.prototype.$emit=function(t){var e=this,n=e._events[t];if(n){n=n.length>1?k(n):n;for(var r=k(arguments,1),i='event handler for "'+t+'"',o=0,a=n.length;o<a;o++)Rt(n[o],e,r,e,i)}return e}}(vn),function(t){t.prototype._update=function(t,e){var n=this,r=n.$el,i=n._vnode,o=function(t){var e=Ve;return Ve=t,function(){Ve=e}}(n);n._vnode=t,n.$el=i?n.__patch__(i,t):n.__patch__(n.$el,t,e,!1),o(),r&&(r.__vue__=null),n.$el&&(n.$el.__vue__=n),n.$vnode&&n.$parent&&n.$vnode===n.$parent._vnode&&(n.$parent.$el=n.$el)},t.prototype.$forceUpdate=function(){this._watcher&&this._watcher.update()},t.prototype.$destroy=function(){var t=this;if(!t._isBeingDestroyed){Ke(t,"beforeDestroy"),t._isBeingDestroyed=!0;var e=t.$parent;!e||e._isBeingDestroyed||t.$options.abstract||g(e.$children,t),t._watcher&&t._watcher.teardown();var n=t._watchers.length;while(n--)t._watchers[n].teardown();t._data.__ob__&&t._data.__ob__.vmCount--,t._isDestroyed=!0,t.__patch__(t._vnode,null),Ke(t,"destroyed"),t.$off(),t.$el&&(t.$el.__vue__=null),t.$vnode&&(t.$vnode.parent=null)}}}(vn),function(t){ke(t.prototype),t.prototype.$nextTick=function(t){return Jt(t,this)},t.prototype._render=function(){var t,e=this,n=e.$options,r=n.render,i=n._parentVnode;i&&(e.$scopedSlots=ce(i.data.scopedSlots,e.$slots,e.$scopedSlots)),e.$vnode=i;try{De=e,t=r.call(e._renderProxy,e.$createElement)}catch(Nn){Nt(Nn,e,"render"),t=e._vnode}finally{De=null}return Array.isArray(t)&&1===t.length&&(t=t[0]),t instanceof ct||(t=ft()),t.parent=i,t}}(vn);var An=[String,RegExp,Array],wn={name:"keep-alive",abstract:!0,props:{include:An,exclude:An,max:[String,Number]},created:function(){this.cache=Object.create(null),this.keys=[]},destroyed:function(){for(var t in this.cache)bn(this.cache,t,this.keys)},mounted:function(){var t=this;this.$watch("include",(function(e){_n(t,(function(t){return mn(e,t)}))})),this.$watch("exclude",(function(e){_n(t,(function(t){return!mn(e,t)}))}))},render:function(){var t=this.$slots.default,e=function(t){if(Array.isArray(t))for(var e=0;e<t.length;e++){var n=t[e];if(i(n)&&(i(n.componentOptions)||Re(n)))return n}}(t),n=e&&e.componentOptions;if(n){var r=yn(n),o=this.include,a=this.exclude;if(o&&(!r||!mn(o,r))||a&&r&&mn(a,r))return e;var s=this.cache,u=this.keys,c=null==e.key?n.Ctor.cid+(n.tag?"::"+n.tag:""):e.key;s[c]?(e.componentInstance=s[c].componentInstance,g(u,c),u.push(c)):(s[c]=e,u.push(c),this.max&&u.length>parseInt(this.max)&&bn(s,u[0],u,this._vnode)),e.data.keepAlive=!0}return e||t&&t[0]}},On={KeepAlive:wn};(function(t){var e={get:function(){return N}};Object.defineProperty(t,"config",e),t.util={warn:it,extend:E,mergeOptions:$t,defineReactive:At},t.set=wt,t.delete=Ot,t.nextTick=Jt,t.observable=function(t){return bt(t),t},t.options=Object.create(null),M.forEach((function(e){t.options[e+"s"]=Object.create(null)})),t.options._base=t,E(t.options.components,On),function(t){t.use=function(t){var e=this._installedPlugins||(this._installedPlugins=[]);if(e.indexOf(t)>-1)return this;var n=k(arguments,1);return n.unshift(this),"function"===typeof t.install?t.install.apply(t,n):"function"===typeof t&&t.apply(null,n),e.push(t),this}}(t),function(t){t.mixin=function(t){return this.options=$t(this.options,t),this}}(t),gn(t),function(t){M.forEach((function(e){t[e]=function(t,n){return n?("component"===e&&c(n)&&(n.name=n.name||t,n=this.options._base.extend(n)),"directive"===e&&"function"===typeof n&&(n={bind:n,update:n}),this.options[e+"s"][t]=n,n):this.options[e+"s"][t]}}))}(t)})(vn),Object.defineProperty(vn.prototype,"$isServer",{get:Q}),Object.defineProperty(vn.prototype,"$ssrContext",{get:function(){return this.$vnode&&this.$vnode.ssrContext}}),Object.defineProperty(vn,"FunctionalRenderContext",{value:Ee}),vn.version="2.6.11";var xn="[object Array]",Sn="[object Object]";function kn(t,e){var n={};return function t(e,n){if(e===n)return;var r=Tn(e),i=Tn(n);if(r==Sn&&i==Sn){if(Object.keys(e).length>=Object.keys(n).length)for(var o in n){var a=e[o];void 0===a?e[o]=null:t(a,n[o])}}else r==xn&&i==xn&&e.length>=n.length&&n.forEach((function(n,r){t(e[r],n)}))}(t,e),function t(e,n,r,i){if(e===n)return;var o=Tn(e),a=Tn(n);if(o==Sn)if(a!=Sn||Object.keys(e).length<Object.keys(n).length)En(i,r,e);else{var s=function(o){var a=e[o],s=n[o],u=Tn(a),c=Tn(s);if(u!=xn&&u!=Sn)a!==n[o]&&function(t,e){if(("[object Null]"===t||"[object Undefined]"===t)&&("[object Null]"===e||"[object Undefined]"===e))return!1;return!0}(u,c)&&En(i,(""==r?"":r+".")+o,a);else if(u==xn)c!=xn||a.length<s.length?En(i,(""==r?"":r+".")+o,a):a.forEach((function(e,n){t(e,s[n],(""==r?"":r+".")+o+"["+n+"]",i)}));else if(u==Sn)if(c!=Sn||Object.keys(a).length<Object.keys(s).length)En(i,(""==r?"":r+".")+o,a);else for(var l in a)t(a[l],s[l],(""==r?"":r+".")+o+"."+l,i)};for(var u in e)s(u)}else o==xn?a!=xn||e.length<n.length?En(i,r,e):e.forEach((function(e,o){t(e,n[o],r+"["+o+"]",i)})):En(i,r,e)}(t,e,"",n),n}function En(t,e,n){t[e]=n}function Tn(t){return Object.prototype.toString.call(t)}function Cn(t){if(t.__next_tick_callbacks&&t.__next_tick_callbacks.length){if(Object({NODE_ENV:"production",VUE_APP_DARK_MODE:"false",VUE_APP_NAME:"黄记团圆月",VUE_APP_PLATFORM:"mp-weixin",BASE_URL:"/"}).VUE_APP_DEBUG){var e=t.$scope;console.log("["+ +new Date+"]["+(e.is||e.route)+"]["+t._uid+"]:flushCallbacks["+t.__next_tick_callbacks.length+"]")}var n=t.__next_tick_callbacks.slice(0);t.__next_tick_callbacks.length=0;for(var r=0;r<n.length;r++)n[r]()}}function Pn(t,e){if(!t.__next_tick_pending&&!function(t){return We.find((function(e){return t._watcher===e}))}(t)){if(Object({NODE_ENV:"production",VUE_APP_DARK_MODE:"false",VUE_APP_NAME:"黄记团圆月",VUE_APP_PLATFORM:"mp-weixin",BASE_URL:"/"}).VUE_APP_DEBUG){var n=t.$scope;console.log("["+ +new Date+"]["+(n.is||n.route)+"]["+t._uid+"]:nextVueTick")}return Jt(e,t)}if(Object({NODE_ENV:"production",VUE_APP_DARK_MODE:"false",VUE_APP_NAME:"黄记团圆月",VUE_APP_PLATFORM:"mp-weixin",BASE_URL:"/"}).VUE_APP_DEBUG){var r=t.$scope;console.log("["+ +new Date+"]["+(r.is||r.route)+"]["+t._uid+"]:nextMPTick")}var i;if(t.__next_tick_callbacks||(t.__next_tick_callbacks=[]),t.__next_tick_callbacks.push((function(){if(e)try{e.call(t)}catch(Nn){Nt(Nn,t,"nextTick")}else i&&i(t)})),!e&&"undefined"!==typeof Promise)return new Promise((function(t){i=t}))}function $n(t,e){return e&&(e._isVue||e.__v_isMPComponent)?{}:e}function Ln(){}function jn(t){return Array.isArray(t)?function(t){for(var e,n="",r=0,o=t.length;r<o;r++)i(e=jn(t[r]))&&""!==e&&(n&&(n+=" "),n+=e);return n}(t):s(t)?function(t){var e="";for(var n in t)t[n]&&(e&&(e+=" "),e+=n);return e}(t):"string"===typeof t?t:""}var In=_((function(t){var e={},n=/:(.+)/;return t.split(/;(?![^(]*\))/g).forEach((function(t){if(t){var r=t.split(n);r.length>1&&(e[r[0].trim()]=r[1].trim())}})),e}));var Mn=["createSelectorQuery","createIntersectionObserver","selectAllComponents","selectComponent"];var Dn=["onLaunch","onShow","onHide","onUniNViewMessage","onPageNotFound","onThemeChange","onError","onUnhandledRejection","onInit","onLoad","onReady","onUnload","onPullDownRefresh","onReachBottom","onTabItemTap","onAddToFavorites","onShareTimeline","onShareAppMessage","onResize","onPageScroll","onNavigationBarButtonTap","onBackPress","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputClicked","onUploadDouyinVideo","onNFCReadMessage","onPageShow","onPageHide","onPageResize"];vn.prototype.__patch__=function(t,e){var n=this;if(null!==e&&("page"===this.mpType||"component"===this.mpType)){var r=this.$scope,i=Object.create(null);try{i=function(t){var e=Object.create(null),n=[].concat(Object.keys(t._data||{}),Object.keys(t._computedWatchers||{}));n.reduce((function(e,n){return e[n]=t[n],e}),e);var r=t.__composition_api_state__||t.__secret_vfa_state__,i=r&&r.rawBindings;return i&&Object.keys(i).forEach((function(n){e[n]=t[n]})),Object.assign(e,t.$mp.data||{}),Array.isArray(t.$options.behaviors)&&-1!==t.$options.behaviors.indexOf("uni://form-field")&&(e["name"]=t.name,e["value"]=t.value),JSON.parse(JSON.stringify(e,$n))}(this)}catch(s){console.error(s)}i.__webviewId__=r.data.__webviewId__;var o=Object.create(null);Object.keys(i).forEach((function(t){o[t]=r.data[t]}));var a=!1===this.$shouldDiffData?i:kn(i,o);Object.keys(a).length?(Object({NODE_ENV:"production",VUE_APP_DARK_MODE:"false",VUE_APP_NAME:"黄记团圆月",VUE_APP_PLATFORM:"mp-weixin",BASE_URL:"/"}).VUE_APP_DEBUG&&console.log("["+ +new Date+"]["+(r.is||r.route)+"]["+this._uid+"]差量更新",JSON.stringify(a)),this.__next_tick_pending=!0,r.setData(a,(function(){n.__next_tick_pending=!1,Cn(n)}))):Cn(this)}},vn.prototype.$mount=function(t,e){return function(t,e,n){return t.mpType?("app"===t.mpType&&(t.$options.render=Ln),t.$options.render||(t.$options.render=Ln),!t._$fallback&&Ke(t,"beforeMount"),new rn(t,(function(){t._update(t._render(),n)}),C,{before:function(){t._isMounted&&!t._isDestroyed&&Ke(t,"beforeUpdate")}},!0),n=!1,t):t}(this,0,e)},function(t){var e=t.extend;t.extend=function(t){t=t||{};var n=t.methods;return n&&Object.keys(n).forEach((function(e){-1!==Dn.indexOf(e)&&(t[e]=n[e],delete n[e])})),e.call(this,t)};var n=t.config.optionMergeStrategies,r=n.created;Dn.forEach((function(t){n[t]=r})),t.prototype.__lifecycle_hooks__=Dn}(vn),function(t){t.config.errorHandler=function(e,n,r){t.util.warn("Error in "+r+': "'+e.toString()+'"',n),console.error(e);var i="function"===typeof getApp&&getApp();i&&i.onError&&i.onError(e)};var e=t.prototype.$emit;t.prototype.$emit=function(t){if(this.$scope&&t){var n=this.$scope["_triggerEvent"]||this.$scope["triggerEvent"];if(n)try{n.call(this.$scope,t,{__args__:k(arguments,1)})}catch(r){}}return e.apply(this,arguments)},t.prototype.$nextTick=function(t){return Pn(this,t)},Mn.forEach((function(e){t.prototype[e]=function(t){return this.$scope&&this.$scope[e]?this.$scope[e](t):"undefined"!==typeof my?"createSelectorQuery"===e?my.createSelectorQuery(t):"createIntersectionObserver"===e?my.createIntersectionObserver(t):void 0:void 0}})),t.prototype.__init_provide=ie,t.prototype.__init_injections=oe,t.prototype.__call_hook=function(t,e){var n=this;st();var r,i=n.$options[t],o=t+" hook";if(i)for(var a=0,s=i.length;a<s;a++)r=Rt(i[a],n,e?[e]:null,n,o);return n._hasHookEvent&&n.$emit("hook:"+t,e),ut(),r},t.prototype.__set_model=function(e,n,r,i){Array.isArray(i)&&(-1!==i.indexOf("trim")&&(r=r.trim()),-1!==i.indexOf("number")&&(r=this._n(r))),e||(e=this),t.set(e,n,r)},t.prototype.__set_sync=function(e,n,r){e||(e=this),t.set(e,n,r)},t.prototype.__get_orig=function(t){return c(t)&&t["$orig"]||t},t.prototype.__get_value=function(t,e){return function t(e,n){var r=n.split("."),i=r[0];return 0===i.indexOf("__$n")&&(i=parseInt(i.replace("__$n",""))),1===r.length?e[i]:t(e[i],r.slice(1).join("."))}(e||this,t)},t.prototype.__get_class=function(t,e){return function(t,e){return i(t)||i(e)?function(t,e){return t?e?t+" "+e:t:e||""}(t,jn(e)):""}(e,t)},t.prototype.__get_style=function(t,e){if(!t&&!e)return"";var n=function(t){return Array.isArray(t)?T(t):"string"===typeof t?In(t):t}(t),r=e?E(e,n):n;return Object.keys(r).map((function(t){return x(t)+":"+r[t]})).join(";")},t.prototype.__map=function(t,e){var n,r,i,o,a;if(Array.isArray(t)){for(n=new Array(t.length),r=0,i=t.length;r<i;r++)n[r]=e(t[r],r);return n}if(s(t)){for(o=Object.keys(t),n=Object.create(null),r=0,i=o.length;r<i;r++)a=o[r],n[a]=e(t[a],a,r);return n}if("number"===typeof t){for(n=new Array(t),r=0,i=t;r<i;r++)n[r]=e(r,r);return n}return[]}}(vn),e["default"]=vn}.call(this,n("0ee4"))},"342a":function(t,e,n){"use strict";var r=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,e.getCityList=function(){return new Promise((function(t,e){(0,a.getCity)().then((function(e){t(e.data);o.default.setItem({name:"cityList",value:e.data,expires:6048e5})}))}))},e.isWeixin=function(){return-1!==navigator.userAgent.toLowerCase().indexOf("micromessenger")},e.parseQuery=s,e.silenceBindingSpread=function(){var t=getApp().globalData.spread;t=parseInt(t),Number.isNaN(t)&&(t=0);t?(0,i.spread)(t).then((function(t){})).catch((function(t){getApp().globalData.spread=0})):o.default.set("spread",0)};var i=n("20ce"),o=r(n("63e6")),a=n("ad96");function s(){var t={},e=(location.href.split("?")[1]||"").trim().replace(/^(\?|#|&)/,"");return e?(e.split("&").forEach((function(e){var n=e.replace(/\+/g," ").split("="),r=decodeURIComponent(n.shift()),i=n.length>0?decodeURIComponent(n.join("=")):null;void 0===t[r]?t[r]=i:Array.isArray(t[r])?t[r].push(i):t[r]=[t[r],i]})),t):t}var u=s;e.default=u},"34cf":function(t,e,n){var r=n("ed45"),i=n("7172"),o=n("6382"),a=n("dd3e");t.exports=function(t,e){return r(t)||i(t,e)||o(t,e)||a()},t.exports.__esModule=!0,t.exports["default"]=t.exports},"3b2d":function(t,e){function n(e){return t.exports=n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports["default"]=t.exports,n(e)}t.exports=n,t.exports.__esModule=!0,t.exports["default"]=t.exports},"3f87":function(t,e,n){"use strict";(function(t){var r=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.getPreOrder=function(e,n){return new Promise((function(r,a){(0,i.preOrderApi)({preOrderType:e,orderDetails:n}).then((function(e){t.navigateTo({url:"/pages/users/order_confirm/index?preOrderNo="+e.data.preOrderNo})})).catch((function(t){return o.default.Tips({title:t})}))}))},e.goShopDetail=function(e,n){return new Promise((function(r){e.activityH5&&"1"===e.activityH5.type?t.navigateTo({url:"/pages/activity/goods_seckill_details/index?id=".concat(e.activityH5.id)}):e.activityH5&&"2"===e.activityH5.type?t.navigateTo({url:"/pages/activity/goods_bargain_details/index?id=".concat(e.activityH5.id,"&startBargainUid=").concat(n)}):e.activityH5&&"3"===e.activityH5.type?t.navigateTo({url:"/pages/activity/goods_combination_details/index?id=".concat(e.activityH5.id)}):r(e)}))};var i=n("8d22"),o=r(n("9bad"))}).call(this,n("df3c")["default"])},"458b":function(t,e,n){"use strict";var r=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=r(n("6b33")),o={app:i.default};e.default=o},"47a9":function(t,e){t.exports=function(t){return t&&t.__esModule?t:{default:t}},t.exports.__esModule=!0,t.exports["default"]=t.exports},"4bef":function(t,e){t.exports={type:"zoom-fade-out",duration:200}},"50a1":function(t,e){},5343:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={token:function(t){return t.app.token},isLogin:function(t){return!!t.app.token},backgroundColor:function(t){return t.app.backgroundColor},userInfo:function(t){return t.app.userInfo||{}},uid:function(t){return t.app.uid},homeActive:function(t){return t.app.homeActive},home:function(t){return t.app.home},chatUrl:function(t){return t.app.chatUrl},systemPlatform:function(t){return t.app.systemPlatform},productType:function(t){return t.app.productType}}},5908:function(t,e,n){"use strict";var r=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=r(n("3240")),o=r(n("8f59")),a=r(n("458b")),s=r(n("5343"));i.default.use(o.default);var u=new o.default.Store({modules:a.default,getters:s.default,strict:!1});e.default=u},"5fc2":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.goPage=function(){return new Promise((function(t){if(0!=r.globalData.isIframe)return!1;t(!0)}))};var r=getApp()},6152:function(t,e){t.exports={LOGIN_STATUS:"LOGIN_STATUS_TOKEN",UID:"UID",USER_INFO:"USER_INFO",EXPIRES_TIME:"EXPIRES_TIME",WX_AUTH:"WX_AUTH",STATE_KEY:"wx_authorize_state",LOGINTYPE:"loginType",BACK_URL:"login_back_url",STATE_R_KEY:"roution_authorize_state",LOGO_URL:"LOGO_URL",TIPS_KEY:"TIPS_KEY",SPREAD:"SPREAD",CACHE_LONGITUDE:"LONGITUDE",CACHE_LATITUDE:"LATITUDE",PLATFORM:"systemPlatform"}},6382:function(t,e,n){var r=n("6454");t.exports=function(t,e){if(t){if("string"===typeof t)return r(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(t,e):void 0}},t.exports.__esModule=!0,t.exports["default"]=t.exports},"63e6":function(t,e,n){"use strict";(function(t){var r=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=r(n("3b2d")),o=r(n("67ad")),a=r(n("0bdb")),s=n("0a3e"),u=function(){function e(n){(0,o.default)(this,e),this.cacheSetHandler=t.setStorageSync,this.cacheGetHandler=t.getStorageSync,this.cacheClearHandler=t.removeStorageSync,this.cacheExpire="_expire_2019_12_17_18_44",this.name="storage"}return(0,a.default)(e,[{key:"time",value:function(){return Math.round(new Date/1e3)}},{key:"strTotime",value:function(t){var e=t.substring(0,19);return e=e.replace(/-/g,"/"),Math.round(new Date(e).getTime()/1e3)}},{key:"setExpireCaheTag",value:function(t,e){var n=this;if(e=void 0!==e?e:s.EXPIRE,"number"===typeof e){var r=this.cacheGetHandler(this.cacheExpire),o=[],a=[];"object"===(0,i.default)(r)&&r.length&&(o=r.map((function(r){return a.push(r.key),r.key===t&&(r.expire=0===e?0:n.time()+e),r}))),a.length&&-1!==a.indexOf(t)||o.push({key:t,expire:0===e?0:this.time()+e}),this.cacheSetHandler(this.cacheExpire,o)}}},{key:"setItem",value:function(e){var n={name:"",value:"",expires:"",startTime:(new Date).getTime()},r={};if(Object.assign(r,n,e),r.expires)t.setStorageSync(r.name,JSON.stringify(r));else{Object.prototype.toString.call(r.value);"[object Object]"==Object.prototype.toString.call(r.value)&&(r.value=JSON.stringify(r.value)),"[object Array]"==Object.prototype.toString.call(r.value)&&(r.value=JSON.stringify(r.value)),t.setStorageSync(r.name,r.value)}}},{key:"getExpireCahe",value:function(t,e){try{var n=this.cacheGetHandler(t+this.cacheExpire);if(n){var r=parseInt(n);return!(n&&n<this.time()&&!Number.isNaN(r))||(void 0!==e&&!0!==e||(this.cacheClearHandler(t),this.cacheClearHandler(t+this.cacheExpire)),!1)}return!!this.cacheGetHandler(t)}catch(i){return!1}}},{key:"set",value:function(t,e,n){"object"===(0,i.default)(e)&&(e=JSON.stringify(e));try{return this.setExpireCaheTag(t,n),this.cacheSetHandler(t,e)}catch(r){return!1}}},{key:"has",value:function(t){return this.getExpireCahe(t)}},{key:"get",value:function(t,e,n){try{var r=this.getExpireCahe(t),i=this.cacheGetHandler(t);if(i&&r)return"boolean"===typeof e?JSON.parse(i):i;if("function"===typeof e){var o=e();return this.set(t,o,n),o}return this.set(t,e,n),e}catch(a){return null}}},{key:"clear",value:function(t){try{var e=this.cacheGetHandler(t+this.cacheExpire);return e&&this.cacheClearHandler(t+this.cacheExpire),this.cacheClearHandler(t)}catch(n){return!1}}},{key:"clearOverdue",value:function(){}},{key:"getItem",value:function(e){var n=t.getStorageSync(e);try{n=JSON.parse(n)}catch(i){n=n}if(n.startTime){var r=(new Date).getTime();return r-n.startTime>n.expires?(t.removeStorageSync(e),!1):n.value}return n}}]),e}(),c=new u;e.default=c}).call(this,n("df3c")["default"])},6454:function(t,e){t.exports=function(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r},t.exports.__esModule=!0,t.exports["default"]=t.exports},"67ad":function(t,e){t.exports=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},t.exports.__esModule=!0,t.exports["default"]=t.exports},"6b33":function(t,e,n){"use strict";var r=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n("20ce"),o=n("6152"),a=r(n("63e6")),s={token:a.default.get(o.LOGIN_STATUS)||"",backgroundColor:"#fff",userInfo:a.default.get(o.USER_INFO)?JSON.parse(a.default.get(o.USER_INFO)):null,uid:a.default.get(o.UID)||null,homeActive:!1,chatUrl:a.default.get("chatUrl")||"",systemPlatform:a.default.get(o.PLATFORM)?a.default.get(o.PLATFORM):"",productType:a.default.get("productType")||""},u={LOGIN:function(t,e){t.token=e.token,a.default.set(o.LOGIN_STATUS,e.token)},SETUID:function(t,e){t.uid=e,a.default.set(o.UID,e)},UPDATE_LOGIN:function(t,e){t.token=e},LOGOUT:function(t){t.token=void 0,t.uid=void 0,a.default.clear(o.LOGIN_STATUS),a.default.clear(o.UID),a.default.clear(o.USER_INFO)},BACKGROUND_COLOR:function(t,e){t.color=e,document.body.style.backgroundColor=e},UPDATE_USERINFO:function(t,e){t.userInfo=e,a.default.set(o.USER_INFO,e)},OPEN_HOME:function(t){t.homeActive=!0},CLOSE_HOME:function(t){t.homeActive=!1},SET_CHATURL:function(t,e){t.chatUrl=e},SYSTEM_PLATFORM:function(t,e){t.systemPlatform=e,a.default.set(o.PLATFORM,e)},changInfo:function(t,e){t.userInfo[e.amount1]=e.amount2,a.default.set(o.USER_INFO,t.userInfo)},PRODUCT_TYPE:function(t,e){t.productType=e,a.default.set("productType",e)}},c={USERINFO:function(t,e){t.state;var n=t.commit;return new Promise((function(t){(0,i.getUserInfo)().then((function(e){n("UPDATE_USERINFO",e.data),t(e.data)}))})).catch((function(){}))}},l={state:s,mutations:u,actions:c};e.default=l},7064:function(t,e,n){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={};(function(){function e(t){this.mode=i.MODE_8BIT_BYTE,this.data=t}function r(t,e){this.typeNumber=t,this.errorCorrectLevel=e,this.modules=null,this.moduleCount=0,this.dataCache=null,this.dataList=new Array}e.prototype={getLength:function(t){return this.data.length},write:function(t){for(var e=0;e<this.data.length;e++)t.put(this.data.charCodeAt(e),8)}},r.prototype={addData:function(t){var n=new e(t);this.dataList.push(n),this.dataCache=null},isDark:function(t,e){if(t<0||this.moduleCount<=t||e<0||this.moduleCount<=e)throw new Error(t+","+e);return this.modules[t][e]},getModuleCount:function(){return this.moduleCount},make:function(){if(this.typeNumber<1){var t=1;for(t=1;t<40;t++){for(var e=f.getRSBlocks(t,this.errorCorrectLevel),n=new d,r=0,i=0;i<e.length;i++)r+=e[i].dataCount;for(i=0;i<this.dataList.length;i++){var o=this.dataList[i];n.put(o.mode,4),n.put(o.getLength(),s.getLengthInBits(o.mode,t)),o.write(n)}if(n.getLengthInBits()<=8*r)break}this.typeNumber=t}this.makeImpl(!1,this.getBestMaskPattern())},makeImpl:function(t,e){this.moduleCount=4*this.typeNumber+17,this.modules=new Array(this.moduleCount);for(var n=0;n<this.moduleCount;n++){this.modules[n]=new Array(this.moduleCount);for(var i=0;i<this.moduleCount;i++)this.modules[n][i]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(t,e),this.typeNumber>=7&&this.setupTypeNumber(t),null==this.dataCache&&(this.dataCache=r.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,e)},setupPositionProbePattern:function(t,e){for(var n=-1;n<=7;n++)if(!(t+n<=-1||this.moduleCount<=t+n))for(var r=-1;r<=7;r++)e+r<=-1||this.moduleCount<=e+r||(this.modules[t+n][e+r]=0<=n&&n<=6&&(0==r||6==r)||0<=r&&r<=6&&(0==n||6==n)||2<=n&&n<=4&&2<=r&&r<=4)},getBestMaskPattern:function(){for(var t=0,e=0,n=0;n<8;n++){this.makeImpl(!0,n);var r=s.getLostPoint(this);(0==n||t>r)&&(t=r,e=n)}return e},createMovieClip:function(t,e,n){var r=t.createEmptyMovieClip(e,n);this.make();for(var i=0;i<this.modules.length;i++)for(var o=1*i,a=0;a<this.modules[i].length;a++){var s=1*a,u=this.modules[i][a];u&&(r.beginFill(0,100),r.moveTo(s,o),r.lineTo(s+1,o),r.lineTo(s+1,o+1),r.lineTo(s,o+1),r.endFill())}return r},setupTimingPattern:function(){for(var t=8;t<this.moduleCount-8;t++)null==this.modules[t][6]&&(this.modules[t][6]=t%2==0);for(var e=8;e<this.moduleCount-8;e++)null==this.modules[6][e]&&(this.modules[6][e]=e%2==0)},setupPositionAdjustPattern:function(){for(var t=s.getPatternPosition(this.typeNumber),e=0;e<t.length;e++)for(var n=0;n<t.length;n++){var r=t[e],i=t[n];if(null==this.modules[r][i])for(var o=-2;o<=2;o++)for(var a=-2;a<=2;a++)this.modules[r+o][i+a]=-2==o||2==o||-2==a||2==a||0==o&&0==a}},setupTypeNumber:function(t){for(var e=s.getBCHTypeNumber(this.typeNumber),n=0;n<18;n++){var r=!t&&1==(e>>n&1);this.modules[Math.floor(n/3)][n%3+this.moduleCount-8-3]=r}for(n=0;n<18;n++){r=!t&&1==(e>>n&1);this.modules[n%3+this.moduleCount-8-3][Math.floor(n/3)]=r}},setupTypeInfo:function(t,e){for(var n=this.errorCorrectLevel<<3|e,r=s.getBCHTypeInfo(n),i=0;i<15;i++){var o=!t&&1==(r>>i&1);i<6?this.modules[i][8]=o:i<8?this.modules[i+1][8]=o:this.modules[this.moduleCount-15+i][8]=o}for(i=0;i<15;i++){o=!t&&1==(r>>i&1);i<8?this.modules[8][this.moduleCount-i-1]=o:i<9?this.modules[8][15-i-1+1]=o:this.modules[8][15-i-1]=o}this.modules[this.moduleCount-8][8]=!t},mapData:function(t,e){for(var n=-1,r=this.moduleCount-1,i=7,o=0,a=this.moduleCount-1;a>0;a-=2){6==a&&a--;while(1){for(var u=0;u<2;u++)if(null==this.modules[r][a-u]){var c=!1;o<t.length&&(c=1==(t[o]>>>i&1));var l=s.getMask(e,r,a-u);l&&(c=!c),this.modules[r][a-u]=c,i--,-1==i&&(o++,i=7)}if(r+=n,r<0||this.moduleCount<=r){r-=n,n=-n;break}}}}},r.PAD0=236,r.PAD1=17,r.createData=function(t,e,n){for(var i=f.getRSBlocks(t,e),o=new d,a=0;a<n.length;a++){var u=n[a];o.put(u.mode,4),o.put(u.getLength(),s.getLengthInBits(u.mode,t)),u.write(o)}var c=0;for(a=0;a<i.length;a++)c+=i[a].dataCount;if(o.getLengthInBits()>8*c)throw new Error("code length overflow. ("+o.getLengthInBits()+">"+8*c+")");o.getLengthInBits()+4<=8*c&&o.put(0,4);while(o.getLengthInBits()%8!=0)o.putBit(!1);while(1){if(o.getLengthInBits()>=8*c)break;if(o.put(r.PAD0,8),o.getLengthInBits()>=8*c)break;o.put(r.PAD1,8)}return r.createBytes(o,i)},r.createBytes=function(t,e){for(var n=0,r=0,i=0,o=new Array(e.length),a=new Array(e.length),u=0;u<e.length;u++){var c=e[u].dataCount,f=e[u].totalCount-c;r=Math.max(r,c),i=Math.max(i,f),o[u]=new Array(c);for(var d=0;d<o[u].length;d++)o[u][d]=255&t.buffer[d+n];n+=c;var h=s.getErrorCorrectPolynomial(f),p=new l(o[u],h.getLength()-1),v=p.mod(h);a[u]=new Array(h.getLength()-1);for(d=0;d<a[u].length;d++){var g=d+v.getLength()-a[u].length;a[u][d]=g>=0?v.get(g):0}}var y=0;for(d=0;d<e.length;d++)y+=e[d].totalCount;var m=new Array(y),_=0;for(d=0;d<r;d++)for(u=0;u<e.length;u++)d<o[u].length&&(m[_++]=o[u][d]);for(d=0;d<i;d++)for(u=0;u<e.length;u++)d<a[u].length&&(m[_++]=a[u][d]);return m};for(var i={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8},o={L:1,M:0,Q:3,H:2},a={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7},s={PATTERN_POSITION_TABLE:[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],G15:1335,G18:7973,G15_MASK:21522,getBCHTypeInfo:function(t){var e=t<<10;while(s.getBCHDigit(e)-s.getBCHDigit(s.G15)>=0)e^=s.G15<<s.getBCHDigit(e)-s.getBCHDigit(s.G15);return(t<<10|e)^s.G15_MASK},getBCHTypeNumber:function(t){var e=t<<12;while(s.getBCHDigit(e)-s.getBCHDigit(s.G18)>=0)e^=s.G18<<s.getBCHDigit(e)-s.getBCHDigit(s.G18);return t<<12|e},getBCHDigit:function(t){var e=0;while(0!=t)e++,t>>>=1;return e},getPatternPosition:function(t){return s.PATTERN_POSITION_TABLE[t-1]},getMask:function(t,e,n){switch(t){case a.PATTERN000:return(e+n)%2==0;case a.PATTERN001:return e%2==0;case a.PATTERN010:return n%3==0;case a.PATTERN011:return(e+n)%3==0;case a.PATTERN100:return(Math.floor(e/2)+Math.floor(n/3))%2==0;case a.PATTERN101:return e*n%2+e*n%3==0;case a.PATTERN110:return(e*n%2+e*n%3)%2==0;case a.PATTERN111:return(e*n%3+(e+n)%2)%2==0;default:throw new Error("bad maskPattern:"+t)}},getErrorCorrectPolynomial:function(t){for(var e=new l([1],0),n=0;n<t;n++)e=e.multiply(new l([1,u.gexp(n)],0));return e},getLengthInBits:function(t,e){if(1<=e&&e<10)switch(t){case i.MODE_NUMBER:return 10;case i.MODE_ALPHA_NUM:return 9;case i.MODE_8BIT_BYTE:return 8;case i.MODE_KANJI:return 8;default:throw new Error("mode:"+t)}else if(e<27)switch(t){case i.MODE_NUMBER:return 12;case i.MODE_ALPHA_NUM:return 11;case i.MODE_8BIT_BYTE:return 16;case i.MODE_KANJI:return 10;default:throw new Error("mode:"+t)}else{if(!(e<41))throw new Error("type:"+e);switch(t){case i.MODE_NUMBER:return 14;case i.MODE_ALPHA_NUM:return 13;case i.MODE_8BIT_BYTE:return 16;case i.MODE_KANJI:return 12;default:throw new Error("mode:"+t)}}},getLostPoint:function(t){for(var e=t.getModuleCount(),n=0,r=0;r<e;r++)for(var i=0;i<e;i++){for(var o=0,a=t.isDark(r,i),s=-1;s<=1;s++)if(!(r+s<0||e<=r+s))for(var u=-1;u<=1;u++)i+u<0||e<=i+u||0==s&&0==u||a==t.isDark(r+s,i+u)&&o++;o>5&&(n+=3+o-5)}for(r=0;r<e-1;r++)for(i=0;i<e-1;i++){var c=0;t.isDark(r,i)&&c++,t.isDark(r+1,i)&&c++,t.isDark(r,i+1)&&c++,t.isDark(r+1,i+1)&&c++,0!=c&&4!=c||(n+=3)}for(r=0;r<e;r++)for(i=0;i<e-6;i++)t.isDark(r,i)&&!t.isDark(r,i+1)&&t.isDark(r,i+2)&&t.isDark(r,i+3)&&t.isDark(r,i+4)&&!t.isDark(r,i+5)&&t.isDark(r,i+6)&&(n+=40);for(i=0;i<e;i++)for(r=0;r<e-6;r++)t.isDark(r,i)&&!t.isDark(r+1,i)&&t.isDark(r+2,i)&&t.isDark(r+3,i)&&t.isDark(r+4,i)&&!t.isDark(r+5,i)&&t.isDark(r+6,i)&&(n+=40);var l=0;for(i=0;i<e;i++)for(r=0;r<e;r++)t.isDark(r,i)&&l++;var f=Math.abs(100*l/e/e-50)/5;return n+=10*f,n}},u={glog:function(t){if(t<1)throw new Error("glog("+t+")");return u.LOG_TABLE[t]},gexp:function(t){while(t<0)t+=255;while(t>=256)t-=255;return u.EXP_TABLE[t]},EXP_TABLE:new Array(256),LOG_TABLE:new Array(256)},c=0;c<8;c++)u.EXP_TABLE[c]=1<<c;for(c=8;c<256;c++)u.EXP_TABLE[c]=u.EXP_TABLE[c-4]^u.EXP_TABLE[c-5]^u.EXP_TABLE[c-6]^u.EXP_TABLE[c-8];for(c=0;c<255;c++)u.LOG_TABLE[u.EXP_TABLE[c]]=c;function l(t,e){if(void 0==t.length)throw new Error(t.length+"/"+e);var n=0;while(n<t.length&&0==t[n])n++;this.num=new Array(t.length-n+e);for(var r=0;r<t.length-n;r++)this.num[r]=t[r+n]}function f(t,e){this.totalCount=t,this.dataCount=e}function d(){this.buffer=new Array,this.length=0}l.prototype={get:function(t){return this.num[t]},getLength:function(){return this.num.length},multiply:function(t){for(var e=new Array(this.getLength()+t.getLength()-1),n=0;n<this.getLength();n++)for(var r=0;r<t.getLength();r++)e[n+r]^=u.gexp(u.glog(this.get(n))+u.glog(t.get(r)));return new l(e,0)},mod:function(t){if(this.getLength()-t.getLength()<0)return this;for(var e=u.glog(this.get(0))-u.glog(t.get(0)),n=new Array(this.getLength()),r=0;r<this.getLength();r++)n[r]=this.get(r);for(r=0;r<t.getLength();r++)n[r]^=u.gexp(u.glog(t.get(r))+e);return new l(n,0).mod(t)}},f.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],f.getRSBlocks=function(t,e){var n=f.getRsBlockTable(t,e);if(void 0==n)throw new Error("bad rs block @ typeNumber:"+t+"/errorCorrectLevel:"+e);for(var r=n.length/3,i=new Array,o=0;o<r;o++)for(var a=n[3*o+0],s=n[3*o+1],u=n[3*o+2],c=0;c<a;c++)i.push(new f(s,u));return i},f.getRsBlockTable=function(t,e){switch(e){case o.L:return f.RS_BLOCK_TABLE[4*(t-1)+0];case o.M:return f.RS_BLOCK_TABLE[4*(t-1)+1];case o.Q:return f.RS_BLOCK_TABLE[4*(t-1)+2];case o.H:return f.RS_BLOCK_TABLE[4*(t-1)+3];default:return}},d.prototype={get:function(t){var e=Math.floor(t/8);return 1==(this.buffer[e]>>>7-t%8&1)},put:function(t,e){for(var n=0;n<e;n++)this.putBit(1==(t>>>e-n-1&1))},getLengthInBits:function(){return this.length},putBit:function(t){var e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}},n={defaults:{size:258,margin:0,backgroundColor:"#ffffff",foregroundColor:"#000000",fileType:"png",correctLevel:3,typeNumber:-1},make:function(e){var n={canvasId:e.canvasId,componentInstance:e.componentInstance,text:e.text,size:this.defaults.size,margin:this.defaults.margin,backgroundColor:this.defaults.backgroundColor,foregroundColor:this.defaults.foregroundColor,fileType:this.defaults.fileType,correctLevel:this.defaults.correctLevel,typeNumber:this.defaults.typeNumber};if(e)for(var i in e)n[i]=e[i];e=n,e.canvasId?function(){var n=new r(e.typeNumber,e.correctLevel);n.addData(function(t){for(var e,n="",r=0;r<t.length;r++)e=t.charCodeAt(r),e>=1&&e<=127?n+=t.charAt(r):e>2047?(n+=String.fromCharCode(224|e>>12&15),n+=String.fromCharCode(128|e>>6&63),n+=String.fromCharCode(128|e>>0&63)):(n+=String.fromCharCode(192|e>>6&31),n+=String.fromCharCode(128|e>>0&63));return n}(e.text)),n.make();var i=t.createCanvasContext(e.canvasId,e.componentInstance);i.setFillStyle(e.backgroundColor),i.fillRect(0,0,e.size,e.size);for(var o=(e.size-2*e.margin)/n.getModuleCount(),a=o,s=0;s<n.getModuleCount();s++)for(var u=0;u<n.getModuleCount();u++){var c=n.isDark(s,u)?e.foregroundColor:e.backgroundColor;i.setFillStyle(c);var l=Math.round(u*o)+e.margin,f=Math.round(s*a)+e.margin,d=Math.ceil((u+1)*o)-Math.floor(u*o),h=Math.ceil((s+1)*o)-Math.floor(s*o);i.fillRect(l,f,d,h)}setTimeout((function(){i.draw(!1,(function(){setTimeout((function(){t.canvasToTempFilePath({canvasId:e.canvasId,fileType:e.fileType,width:e.size,height:e.size,destWidth:e.size,destHeight:e.size,success:function(t){e.success&&e.success(t.tempFilePath)},fail:function(t){e.fail&&e.fail(t)},complete:function(t){e.complete&&e.complete(t)}},e.componentInstance)}),e.text.length+100)}))}),150)}():console.error("uQRCode: Please set canvasId!")}}})();var r=n;e.default=r}).call(this,n("df3c")["default"])},7172:function(t,e){t.exports=function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o,a,s=[],u=!0,c=!1;try{if(o=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=o.call(n)).done)&&(s.push(r.value),s.length!==e);u=!0);}catch(t){c=!0,i=t}finally{try{if(!u&&null!=n["return"]&&(a=n["return"](),Object(a)!==a))return}finally{if(c)throw i}}return s}},t.exports.__esModule=!0,t.exports["default"]=t.exports},"74bd":function(t,e){getApp()},7647:function(t,e){function n(e,r){return t.exports=n=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},t.exports.__esModule=!0,t.exports["default"]=t.exports,n(e,r)}t.exports=n,t.exports.__esModule=!0,t.exports["default"]=t.exports},"7ca3":function(t,e,n){var r=n("d551");t.exports=function(t,e,n){return e=r(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t},t.exports.__esModule=!0,t.exports["default"]=t.exports},"7eb4":function(t,e,n){var r=n("9fc1")();t.exports=r},"828b":function(t,e,n){"use strict";function r(t,e,n,r,i,o,a,s,u,c){var l,f="function"===typeof t?t.options:t;if(u){f.components||(f.components={});var d=Object.prototype.hasOwnProperty;for(var h in u)d.call(u,h)&&!d.call(f.components,h)&&(f.components[h]=u[h])}if(c&&("function"===typeof c.beforeCreate&&(c.beforeCreate=[c.beforeCreate]),(c.beforeCreate||(c.beforeCreate=[])).unshift((function(){this[c.__module]=this})),(f.mixins||(f.mixins=[])).push(c)),e&&(f.render=e,f.staticRenderFns=n,f._compiled=!0),r&&(f.functional=!0),o&&(f._scopeId="data-v-"+o),a?(l=function(t){t=t||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,t||"undefined"===typeof __VUE_SSR_CONTEXT__||(t=__VUE_SSR_CONTEXT__),i&&i.call(this,t),t&&t._registeredComponents&&t._registeredComponents.add(a)},f._ssrRegister=l):i&&(l=s?function(){i.call(this,this.$root.$options.shadowRoot)}:i),l)if(f.functional){f._injectStyles=l;var p=f.render;f.render=function(t,e){return l.call(e),p(t,e)}}else{var v=f.beforeCreate;f.beforeCreate=v?[].concat(v,l):[l]}return{exports:t,options:f}}n.d(e,"a",(function(){return r}))},"84f5":function(t,e,n){"use strict";var r=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.appAuth=function(t){return i.default.post("wechat/authorize/app/login",t,{noAuth:!0})},e.appleLogin=function(t){return i.default.post("ios/login",t,{noAuth:!0})},e.copyWords=function(){return i.default.get("copy_words",{},{noAuth:!0})},e.follow=function(){return i.default.get("wechat/follow",{},{noAuth:!0})},e.getLogo=function(){return i.default.get("wechat/getLogo",{},{noAuth:!0})},e.getShare=function(){return i.default.get("share",{},{noAuth:!0})},e.getUserPhone=function(t){return i.default.post("wechat/register/binding/phone",t,{noAuth:!0})},e.getWechatConfig=function(){return i.default.get("wechat/config",{url:encodeURIComponent(o.default.signLink())},{noAuth:!0})},e.imageBase64=function(t){return i.default.post("qrcode/base64",t,{noAuth:!0},1)},e.iosBinding=function(t){return i.default.post("ios/binding/phone",t,{noAuth:!0})},e.login=function(t,e){return i.default.post("wechat/authorize/program/login?code="+t,e,{noAuth:!0})},e.wechatAuth=function(t,e){return e=/^[0-9]+.?[0-9]*$/.test(e)?e:0,i.default.get("wechat/authorize/login?code="+t+"&spread_spid="+e,{},{noAuth:!0})};var i=r(n("1178")),o=r(n("50a1"));n("2e55")},"8b39":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Debounce=void 0,e.checkPhone=function(t){return!!/^1(3|4|5|6|7|8|9)\d{9}$/.test(t)},e.isMoney=function(t){return!!/(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/.test(t)};e.Debounce=function(t,e){var n,r=e||500;return function(){var e=this,i=arguments;n&&clearTimeout(n),n=setTimeout((function(){n=null,t.apply(e,i)}),r)}}},"8cd5":function(t,e,n){"use strict";var r=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.bargainHeaderApi=function(){return i.default.get("bargain/header")},e.bargainUserCancel=function(t){return i.default.post("bargain/user/cancel",{bargainId:t})},e.combinationHeaderApi=function(){return i.default.get("combination/header")},e.getBargainDetail=function(t){return i.default.get("bargain/detail/"+t)},e.getBargainIndexApi=function(){return i.default.get("bargain/index",{},{noAuth:!0})},e.getBargainList=function(t){return i.default.get("bargain/list",t,{noAuth:!0})},e.getBargainPoster=function(t){return i.default.post("bargain/poster",t)},e.getBargainUser=function(t){return i.default.get("bargain/user",t)},e.getBargainUserCancel=function(t){return i.default.post("/bargain/user/cancel",t)},e.getBargainUserList=function(t){return i.default.get("bargain/record",t)},e.getCombinationDetail=function(t){return i.default.get("combination/detail/"+t)},e.getCombinationIndexApi=function(){return i.default.get("combination/index",{},{noAuth:!0})},e.getCombinationList=function(t){return i.default.get("combination/list",t,{noAuth:!0})},e.getCombinationMore=function(t){return i.default.get("combination/more",t,{noAuth:!0})},e.getCombinationPink=function(t){return i.default.get("combination/pink/"+t)},e.getCombinationPoster=function(t){return i.default.post("combination/poster",t)},e.getSeckillDetail=function(t){return i.default.get("seckill/detail/"+t)},e.getSeckillHeaderApi=function(){return i.default.get("seckill/header",{},{noAuth:!0})},e.getSeckillIndexApi=function(){return i.default.get("seckill/index",{},{noAuth:!0})},e.getSeckillIndexTime=function(){return i.default.get("seckill/index",{},{noAuth:!0})},e.getSeckillList=function(t,e){return i.default.get("seckill/list/"+t,e,{noAuth:!0})},e.postBargainHelp=function(t){return i.default.post("bargain/help",t)},e.postBargainHelpList=function(t,e){return i.default.get("bargain/help/list?limit="+t.limit+"&page="+t.page,e,{})},e.postBargainHelpPrice=function(t){return i.default.post("bargain/help/price",t)},e.postBargainStart=function(t){return i.default.post("bargain/start",{bargainId:t})},e.postCombinationRemove=function(t){return i.default.post("combination/remove",t)},e.scombinationCode=function(t){return i.default.get("combination/code/"+t)},e.seckillCode=function(t,e){return i.default.get("seckill/code/"+t,e)};var i=r(n("1178"))},"8d22":function(t,e,n){"use strict";var r=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.alipayQueryPayResult=function(t){return o.default.get("pay/queryAliPayResult?orderNo="+t)},e.applyRefund=function(t){return o.default.get("order/apply/refund/".concat(t))},e.cartDel=function(t){"object"===(0,i.default)(t)&&(t=t.join(","));return o.default.post("cart/delete",{ids:t},{},1)},e.changeCartNum=function(t,e){return o.default.post("cart/num",{id:t,number:e},{},1)},e.express=function(t){return o.default.get("order/express/"+t)},e.getCartCounts=function(t,e){return o.default.get("cart/count?numType="+t+"&type="+e)},e.getCartList=function(t){return o.default.get("cart/list",t)},e.getCouponsOrderPrice=function(t){return o.default.get("coupons/order/".concat(t))},e.getOrderDetail=function(t){return o.default.get("order/detail/"+t)},e.getOrderList=function(t){return o.default.get("order/list",t)},e.getPayConfig=function(t){return o.default.get("order/get/pay/config")},e.getResetCart=function(t){return o.default.post("cart/resetcart",t)},e.loadPreOrderApi=function(t){return o.default.get("order/load/pre/".concat(t))},e.ordeRefundReason=function(){return o.default.get("order/refund/reason")},e.orderAgain=function(t){return o.default.post("order/again",{orderNo:t})},e.orderCancel=function(t){return o.default.post("order/cancel",{id:t},{},1)},e.orderComment=function(t){return o.default.post("order/comment",t)},e.orderConfirm=function(t,e,n,r,i,a){return o.default.post("order/confirm",{cartIds:t,isNew:e,addAgain:n,secKill:r,combination:i,bargain:a})},e.orderCreate=function(t){return o.default.post("order/create",t)},e.orderData=function(){return o.default.get("order/data")},e.orderDel=function(t){return o.default.post("order/del",{id:t},{},1)},e.orderPay=function(t){return o.default.post("pay/payment",t)},e.orderProduct=function(t){return o.default.post("order/product",t)},e.orderRefundVerify=function(t){return o.default.post("order/refund",t)},e.orderTake=function(t){return o.default.post("order/take",{id:t},{},1)},e.postOrderComputed=function(t){return o.default.post("order/computed/price",t)},e.preOrderApi=function(t){return o.default.post("order/pre/order",t)},e.qrcodeApi=function(t){return o.default.post("qrcode/str2base64",t,{},1)},e.wechatQueryPayResult=function(t){return o.default.get("pay/queryPayResult?orderNo="+t)};var i=r(n("3b2d")),o=r(n("1178"))},"8f59":function(t,e,n){"use strict";(function(e){var n="undefined"!==typeof window?window:"undefined"!==typeof e?e:{},r=n.__VUE_DEVTOOLS_GLOBAL_HOOK__;function i(t,e){if(void 0===e&&(e=[]),null===t||"object"!==typeof t)return t;var n=function(t,e){return t.filter(e)[0]}(e,(function(e){return e.original===t}));if(n)return n.copy;var r=Array.isArray(t)?[]:{};return e.push({original:t,copy:r}),Object.keys(t).forEach((function(n){r[n]=i(t[n],e)})),r}function o(t,e){Object.keys(t).forEach((function(n){return e(t[n],n)}))}function a(t){return null!==t&&"object"===typeof t}var s=function(t,e){this.runtime=e,this._children=Object.create(null),this._rawModule=t;var n=t.state;this.state=("function"===typeof n?n():n)||{}},u={namespaced:{configurable:!0}};u.namespaced.get=function(){return!!this._rawModule.namespaced},s.prototype.addChild=function(t,e){this._children[t]=e},s.prototype.removeChild=function(t){delete this._children[t]},s.prototype.getChild=function(t){return this._children[t]},s.prototype.hasChild=function(t){return t in this._children},s.prototype.update=function(t){this._rawModule.namespaced=t.namespaced,t.actions&&(this._rawModule.actions=t.actions),t.mutations&&(this._rawModule.mutations=t.mutations),t.getters&&(this._rawModule.getters=t.getters)},s.prototype.forEachChild=function(t){o(this._children,t)},s.prototype.forEachGetter=function(t){this._rawModule.getters&&o(this._rawModule.getters,t)},s.prototype.forEachAction=function(t){this._rawModule.actions&&o(this._rawModule.actions,t)},s.prototype.forEachMutation=function(t){this._rawModule.mutations&&o(this._rawModule.mutations,t)},Object.defineProperties(s.prototype,u);var c=function(t){this.register([],t,!1)};c.prototype.get=function(t){return t.reduce((function(t,e){return t.getChild(e)}),this.root)},c.prototype.getNamespace=function(t){var e=this.root;return t.reduce((function(t,n){return e=e.getChild(n),t+(e.namespaced?n+"/":"")}),"")},c.prototype.update=function(t){(function t(e,n,r){0;if(n.update(r),r.modules)for(var i in r.modules){if(!n.getChild(i))return void 0;t(e.concat(i),n.getChild(i),r.modules[i])}})([],this.root,t)},c.prototype.register=function(t,e,n){var r=this;void 0===n&&(n=!0);var i=new s(e,n);if(0===t.length)this.root=i;else{var a=this.get(t.slice(0,-1));a.addChild(t[t.length-1],i)}e.modules&&o(e.modules,(function(e,i){r.register(t.concat(i),e,n)}))},c.prototype.unregister=function(t){var e=this.get(t.slice(0,-1)),n=t[t.length-1],r=e.getChild(n);r&&r.runtime&&e.removeChild(n)},c.prototype.isRegistered=function(t){var e=this.get(t.slice(0,-1)),n=t[t.length-1];return!!e&&e.hasChild(n)};var l;var f=function(t){var e=this;void 0===t&&(t={}),!l&&"undefined"!==typeof window&&window.Vue&&_(window.Vue);var n=t.plugins;void 0===n&&(n=[]);var i=t.strict;void 0===i&&(i=!1),this._committing=!1,this._actions=Object.create(null),this._actionSubscribers=[],this._mutations=Object.create(null),this._wrappedGetters=Object.create(null),this._modules=new c(t),this._modulesNamespaceMap=Object.create(null),this._subscribers=[],this._watcherVM=new l,this._makeLocalGettersCache=Object.create(null);var o=this,a=this.dispatch,s=this.commit;this.dispatch=function(t,e){return a.call(o,t,e)},this.commit=function(t,e,n){return s.call(o,t,e,n)},this.strict=i;var u=this._modules.root.state;g(this,u,[],this._modules.root),v(this,u),n.forEach((function(t){return t(e)}));var f=void 0!==t.devtools?t.devtools:l.config.devtools;f&&function(t){r&&(t._devtoolHook=r,r.emit("vuex:init",t),r.on("vuex:travel-to-state",(function(e){t.replaceState(e)})),t.subscribe((function(t,e){r.emit("vuex:mutation",t,e)}),{prepend:!0}),t.subscribeAction((function(t,e){r.emit("vuex:action",t,e)}),{prepend:!0}))}(this)},d={state:{configurable:!0}};function h(t,e,n){return e.indexOf(t)<0&&(n&&n.prepend?e.unshift(t):e.push(t)),function(){var n=e.indexOf(t);n>-1&&e.splice(n,1)}}function p(t,e){t._actions=Object.create(null),t._mutations=Object.create(null),t._wrappedGetters=Object.create(null),t._modulesNamespaceMap=Object.create(null);var n=t.state;g(t,n,[],t._modules.root,!0),v(t,n,e)}function v(t,e,n){var r=t._vm;t.getters={},t._makeLocalGettersCache=Object.create(null);var i=t._wrappedGetters,a={};o(i,(function(e,n){a[n]=function(t,e){return function(){return t(e)}}(e,t),Object.defineProperty(t.getters,n,{get:function(){return t._vm[n]},enumerable:!0})}));var s=l.config.silent;l.config.silent=!0,t._vm=new l({data:{$$state:e},computed:a}),l.config.silent=s,t.strict&&function(t){t._vm.$watch((function(){return this._data.$$state}),(function(){0}),{deep:!0,sync:!0})}(t),r&&(n&&t._withCommit((function(){r._data.$$state=null})),l.nextTick((function(){return r.$destroy()})))}function g(t,e,n,r,i){var o=!n.length,a=t._modules.getNamespace(n);if(r.namespaced&&(t._modulesNamespaceMap[a],t._modulesNamespaceMap[a]=r),!o&&!i){var s=y(e,n.slice(0,-1)),u=n[n.length-1];t._withCommit((function(){l.set(s,u,r.state)}))}var c=r.context=function(t,e,n){var r=""===e,i={dispatch:r?t.dispatch:function(n,r,i){var o=m(n,r,i),a=o.payload,s=o.options,u=o.type;return s&&s.root||(u=e+u),t.dispatch(u,a)},commit:r?t.commit:function(n,r,i){var o=m(n,r,i),a=o.payload,s=o.options,u=o.type;s&&s.root||(u=e+u),t.commit(u,a,s)}};return Object.defineProperties(i,{getters:{get:r?function(){return t.getters}:function(){return function(t,e){if(!t._makeLocalGettersCache[e]){var n={},r=e.length;Object.keys(t.getters).forEach((function(i){if(i.slice(0,r)===e){var o=i.slice(r);Object.defineProperty(n,o,{get:function(){return t.getters[i]},enumerable:!0})}})),t._makeLocalGettersCache[e]=n}return t._makeLocalGettersCache[e]}(t,e)}},state:{get:function(){return y(t.state,n)}}}),i}(t,a,n);r.forEachMutation((function(e,n){var r=a+n;(function(t,e,n,r){var i=t._mutations[e]||(t._mutations[e]=[]);i.push((function(e){n.call(t,r.state,e)}))})(t,r,e,c)})),r.forEachAction((function(e,n){var r=e.root?n:a+n,i=e.handler||e;(function(t,e,n,r){var i=t._actions[e]||(t._actions[e]=[]);i.push((function(e){var i=n.call(t,{dispatch:r.dispatch,commit:r.commit,getters:r.getters,state:r.state,rootGetters:t.getters,rootState:t.state},e);return function(t){return t&&"function"===typeof t.then}(i)||(i=Promise.resolve(i)),t._devtoolHook?i.catch((function(e){throw t._devtoolHook.emit("vuex:error",e),e})):i}))})(t,r,i,c)})),r.forEachGetter((function(e,n){var r=a+n;(function(t,e,n,r){if(t._wrappedGetters[e])return void 0;t._wrappedGetters[e]=function(t){return n(r.state,r.getters,t.state,t.getters)}})(t,r,e,c)})),r.forEachChild((function(r,o){g(t,e,n.concat(o),r,i)}))}function y(t,e){return e.reduce((function(t,e){return t[e]}),t)}function m(t,e,n){return a(t)&&t.type&&(n=e,e=t,t=t.type),{type:t,payload:e,options:n}}function _(t){l&&t===l||(l=t,
/*!
 * vuex v3.6.2
 * (c) 2021 Evan You
 * @license MIT
 */
function(t){var e=Number(t.version.split(".")[0]);if(e>=2)t.mixin({beforeCreate:r});else{var n=t.prototype._init;t.prototype._init=function(t){void 0===t&&(t={}),t.init=t.init?[r].concat(t.init):r,n.call(this,t)}}function r(){var t=this.$options;t.store?this.$store="function"===typeof t.store?t.store():t.store:t.parent&&t.parent.$store&&(this.$store=t.parent.$store)}}(l))}d.state.get=function(){return this._vm._data.$$state},d.state.set=function(t){0},f.prototype.commit=function(t,e,n){var r=this,i=m(t,e,n),o=i.type,a=i.payload,s=(i.options,{type:o,payload:a}),u=this._mutations[o];u&&(this._withCommit((function(){u.forEach((function(t){t(a)}))})),this._subscribers.slice().forEach((function(t){return t(s,r.state)})))},f.prototype.dispatch=function(t,e){var n=this,r=m(t,e),i=r.type,o=r.payload,a={type:i,payload:o},s=this._actions[i];if(s){try{this._actionSubscribers.slice().filter((function(t){return t.before})).forEach((function(t){return t.before(a,n.state)}))}catch(c){0}var u=s.length>1?Promise.all(s.map((function(t){return t(o)}))):s[0](o);return new Promise((function(t,e){u.then((function(e){try{n._actionSubscribers.filter((function(t){return t.after})).forEach((function(t){return t.after(a,n.state)}))}catch(c){0}t(e)}),(function(t){try{n._actionSubscribers.filter((function(t){return t.error})).forEach((function(e){return e.error(a,n.state,t)}))}catch(c){0}e(t)}))}))}},f.prototype.subscribe=function(t,e){return h(t,this._subscribers,e)},f.prototype.subscribeAction=function(t,e){var n="function"===typeof t?{before:t}:t;return h(n,this._actionSubscribers,e)},f.prototype.watch=function(t,e,n){var r=this;return this._watcherVM.$watch((function(){return t(r.state,r.getters)}),e,n)},f.prototype.replaceState=function(t){var e=this;this._withCommit((function(){e._vm._data.$$state=t}))},f.prototype.registerModule=function(t,e,n){void 0===n&&(n={}),"string"===typeof t&&(t=[t]),this._modules.register(t,e),g(this,this.state,t,this._modules.get(t),n.preserveState),v(this,this.state)},f.prototype.unregisterModule=function(t){var e=this;"string"===typeof t&&(t=[t]),this._modules.unregister(t),this._withCommit((function(){var n=y(e.state,t.slice(0,-1));l.delete(n,t[t.length-1])})),p(this)},f.prototype.hasModule=function(t){return"string"===typeof t&&(t=[t]),this._modules.isRegistered(t)},f.prototype[[104,111,116,85,112,100,97,116,101].map((function(t){return String.fromCharCode(t)})).join("")]=function(t){this._modules.update(t),p(this,!0)},f.prototype._withCommit=function(t){var e=this._committing;this._committing=!0,t(),this._committing=e},Object.defineProperties(f.prototype,d);var b=S((function(t,e){var n={};return x(e).forEach((function(e){var r=e.key,i=e.val;n[r]=function(){var e=this.$store.state,n=this.$store.getters;if(t){var r=k(this.$store,"mapState",t);if(!r)return;e=r.context.state,n=r.context.getters}return"function"===typeof i?i.call(this,e,n):e[i]},n[r].vuex=!0})),n})),A=S((function(t,e){var n={};return x(e).forEach((function(e){var r=e.key,i=e.val;n[r]=function(){var e=[],n=arguments.length;while(n--)e[n]=arguments[n];var r=this.$store.commit;if(t){var o=k(this.$store,"mapMutations",t);if(!o)return;r=o.context.commit}return"function"===typeof i?i.apply(this,[r].concat(e)):r.apply(this.$store,[i].concat(e))}})),n})),w=S((function(t,e){var n={};return x(e).forEach((function(e){var r=e.key,i=e.val;i=t+i,n[r]=function(){if(!t||k(this.$store,"mapGetters",t))return this.$store.getters[i]},n[r].vuex=!0})),n})),O=S((function(t,e){var n={};return x(e).forEach((function(e){var r=e.key,i=e.val;n[r]=function(){var e=[],n=arguments.length;while(n--)e[n]=arguments[n];var r=this.$store.dispatch;if(t){var o=k(this.$store,"mapActions",t);if(!o)return;r=o.context.dispatch}return"function"===typeof i?i.apply(this,[r].concat(e)):r.apply(this.$store,[i].concat(e))}})),n}));function x(t){return function(t){return Array.isArray(t)||a(t)}(t)?Array.isArray(t)?t.map((function(t){return{key:t,val:t}})):Object.keys(t).map((function(e){return{key:e,val:t[e]}})):[]}function S(t){return function(e,n){return"string"!==typeof e?(n=e,e=""):"/"!==e.charAt(e.length-1)&&(e+="/"),t(e,n)}}function k(t,e,n){var r=t._modulesNamespaceMap[n];return r}function E(t,e,n){var r=n?t.groupCollapsed:t.group;try{r.call(t,e)}catch(i){t.log(e)}}function T(t){try{t.groupEnd()}catch(e){t.log("—— log end ——")}}function C(){var t=new Date;return" @ "+P(t.getHours(),2)+":"+P(t.getMinutes(),2)+":"+P(t.getSeconds(),2)+"."+P(t.getMilliseconds(),3)}function P(t,e){return function(t,e){return new Array(e+1).join(t)}("0",e-t.toString().length)+t}var $={Store:f,install:_,version:"3.6.2",mapState:b,mapMutations:A,mapGetters:w,mapActions:O,createNamespacedHelpers:function(t){return{mapState:b.bind(null,t),mapGetters:w.bind(null,t),mapMutations:A.bind(null,t),mapActions:O.bind(null,t)}},createLogger:function(t){void 0===t&&(t={});var e=t.collapsed;void 0===e&&(e=!0);var n=t.filter;void 0===n&&(n=function(t,e,n){return!0});var r=t.transformer;void 0===r&&(r=function(t){return t});var o=t.mutationTransformer;void 0===o&&(o=function(t){return t});var a=t.actionFilter;void 0===a&&(a=function(t,e){return!0});var s=t.actionTransformer;void 0===s&&(s=function(t){return t});var u=t.logMutations;void 0===u&&(u=!0);var c=t.logActions;void 0===c&&(c=!0);var l=t.logger;return void 0===l&&(l=console),function(t){var f=i(t.state);"undefined"!==typeof l&&(u&&t.subscribe((function(t,a){var s=i(a);if(n(t,f,s)){var u=C(),c=o(t),d="mutation "+t.type+u;E(l,d,e),l.log("%c prev state","color: #9E9E9E; font-weight: bold",r(f)),l.log("%c mutation","color: #03A9F4; font-weight: bold",c),l.log("%c next state","color: #4CAF50; font-weight: bold",r(s)),T(l)}f=s})),c&&t.subscribeAction((function(t,n){if(a(t,n)){var r=C(),i=s(t),o="action "+t.type+r;E(l,o,e),l.log("%c action","color: #03A9F4; font-weight: bold",i),T(l)}})))}}};t.exports=$}).call(this,n("0ee4"))},9008:function(t,e){t.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},t.exports.__esModule=!0,t.exports["default"]=t.exports},"931d":function(t,e,n){var r=n("7647"),i=n("011a");t.exports=function(t,e,n){if(i())return Reflect.construct.apply(null,arguments);var o=[null];o.push.apply(o,e);var a=new(t.bind.apply(t,o));return n&&r(a,n.prototype),a},t.exports.__esModule=!0,t.exports["default"]=t.exports},"9bad":function(t,e,n){"use strict";(function(t){var r=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=r(n("7eb4")),o=r(n("ee10")),a=r(n("7ca3")),s=r(n("34cf")),u=r(n("3b2d")),c=n("0a3e"),l=r(n("5908")),f=(n("eded"),{Tips:function(e,n){"string"==typeof e&&(n=e,e={});var r=e.title||"",i=e.icon||"none",o=e.endtime||2e3,a=e.success;if(r&&t.showToast({title:r,icon:i,duration:o,success:a}),void 0!=n)if("object"==(0,u.default)(n)){var s=n.tab||1,c=n.url||"";switch(s){case 1:setTimeout((function(){t.switchTab({url:c})}),o);break;case 2:setTimeout((function(){t.navigateTo({url:c})}),o);break;case 3:setTimeout((function(){t.navigateBack({delta:parseInt(c)})}),o);break;case 4:setTimeout((function(){t.reLaunch({url:c})}),o);break;case 5:setTimeout((function(){t.redirectTo({url:c})}),o);break}}else"function"==typeof n?setTimeout((function(){n&&n()}),o):setTimeout((function(){t.navigateTo({url:n})}),r?o:0)},ArrayRemove:function(t,e,n){var r=[];if(t instanceof Array)for(var i=0;i<t.length;i++)("number"==typeof e&&t[e]!=i||"string"==typeof e&&t[i][e]!=n)&&r.push(t[i]);return r},textByteLength:function(t,e){for(var n=0,r=1,i=0,o=[],a=0;a<t.length;a++)t.charCodeAt(a)>255?(n+=2,n>r*e&&(n++,o.push(t.slice(i,a)),i=a,r++)):(n++,n>r*e&&(o.push(t.slice(i,a)),i=a,r++));return o.push(t.slice(i,t.length)),[n,o,r]},PosterCanvas:function(e,n,r,i,o){var a=this,u=t.createCanvasContext("firstCanvas");u.clearRect(0,0,0,0),u.fillStyle="#fff",u.fillRect(0,0,750,1150),t.getImageInfo({src:e[0],success:function(c){var l=c.width,f=c.height;u.drawImage(e[1],0,0,l,l),u.save();var d=110;u.arc(590,900,d,0,2*Math.PI),u.drawImage(e[2],480,790,220,220),u.restore();var h=a.textByteLength(n,20),p=(0,s.default)(h,3),v=(p[0],p[1]),g=p[2];if(g>2){g=2;var y=v.slice(0,2);y[y.length-1]+="……",v=y}u.setTextAlign("left"),u.setFontSize(36),u.setFillStyle("#000");for(var m=0;m<v.length;m++)m?u.fillText(v[m],50,1e3+36*m+18,1100):u.fillText(v[m],50,1e3+36*m,1100);u.setTextAlign("left"),u.setFontSize(72),u.setFillStyle("#DA4F2A"),u.fillText("￥"+r,40,856),u.setTextAlign("left"),u.setFontSize(36),u.setFillStyle("#999"),u.fillText("￥"+i,50,912);(function(t,e,n,r,i,o,a,s){var u=t.measureText(e).width;switch(t.textAlign){case"center":n-=u/2;break;case"right":n-=u;break}r+=i+s,t.beginPath(),t.strokeStyle=o,t.lineWidth=a,t.moveTo(n,r),t.lineTo(n+u,r),t.stroke()})(u,"￥"+i,55,865,36,"#999",2,0),u.setTextAlign("left"),u.setFontSize(28),u.setFillStyle("#999"),u.fillText("长按或扫描查看",490,1066),u.draw(!0,(function(){t.canvasToTempFilePath({canvasId:"firstCanvas",fileType:"png",destWidth:l,destHeight:f,success:function(t){o&&o(t.tempFilePath)}})}))},fail:function(e){console.log("失败",e),t.hideLoading(),a.Tips({title:"无法获取图片信息"})}})},canvasWraptitleText:function(t,e,n,r,i,o,a){if("string"==typeof e&&"number"==typeof n&&"number"==typeof r){for(var s=e.split(""),u="",c=1,l=0;l<s.length;l++){var f=u+s[l],d=t.measureText(f),h=d.width;if(h>i&&l>0){if(c>=a){var p=f.split("");p.splice(-9);var v=p.join("");return v+="...",void t.fillText(v,n,r)}t.fillText(u,n,r),u=s[l],r+=o,c+=1}else u=f}t.fillText(u,n,r)}},activityCanvas:function(e,n,r,i,o,a,s){var u=this,c=t.createCanvasContext("activityCanvas");c.clearRect(0,0,0,0),c.fillStyle="#fff",c.fillRect(0,0,594,850),t.getImageInfo({src:e[0],success:function(l){c.drawImage(e[0],0,0,594,850),c.setFontSize(28),c.setFillStyle("#333333"),u.canvasWraptitleText(c,n,220,220,460,60,1),c.drawImage(e[2],136,388,320,320),c.save(),c.setFontSize(28),c.setFillStyle("#fc4141"),c.fillText("￥",314,290),c.setFontSize(48),c.setFillStyle("#fc4141"),c.fillText(r,340,290),c.setFontSize(20),c.setFillStyle("#fff"),c.fillText(i,236,286),c.setFontSize(24),c.setFillStyle("#666666"),c.setTextAlign("center"),c.fillText(o,2*(167-a),332),u.handleBorderRect(c,54,188,150,150,12),c.clip(),c.drawImage(e[1],54,188,150,150),c.draw(!0,(function(){t.canvasToTempFilePath({canvasId:"activityCanvas",fileType:"png",destWidth:594,destHeight:850,success:function(t){s&&s(t.tempFilePath)}})}))},fail:function(e){console.log("失败",e),t.hideLoading(),u.Tips({title:"无法获取图片信息"})}})},handleBorderRect:function(t,e,n,r,i,o){t.beginPath(),t.arc(e+o,n+o,o,Math.PI,1.5*Math.PI),t.moveTo(e+o,n),t.lineTo(e+r-o,n),t.lineTo(e+r,n+o),t.arc(e+r-o,n+o,o,1.5*Math.PI,2*Math.PI),t.lineTo(e+r,n+i-o),t.lineTo(e+r-o,n+i),t.arc(e+r-o,n+i-o,o,0,.5*Math.PI),t.lineTo(e+o,n+i),t.lineTo(e,n+i-o),t.arc(e+o,n+i-o,o,.5*Math.PI,Math.PI),t.lineTo(e,n+o),t.lineTo(e+o,n),t.fill(),t.closePath()},uploadImageOne:function(e,n,r){var i=this;if("string"===typeof e){var o=e;e={},e.url=o}var s=e.count||1,u=e.sizeType||["compressed"],f=e.sourceType||["album","camera"],d=(e.is_load,e.url,e.name||"pics"),h=e.pid,p=e.model;t.chooseImage({count:s,sizeType:u,sourceType:f,success:function(e){t.showLoading({title:"图片上传中"});var o=c.HTTP_REQUEST_URL+"/api/front/upload/image?model="+p+"&pid="+h,s=e.tempFilePaths[0];t.uploadFile({url:o,filePath:s,name:d,header:(0,a.default)({"Content-Type":"multipart/form-data"},c.TOKENNAME,l.default.state.app.token),success:function(e){if(t.hideLoading(),403==e.statusCode)i.Tips({title:e.data});else{var o=e.data?JSON.parse(e.data):{};200==o.code?(o.data.localPath=s,n&&n(o)):(r&&r(o),i.Tips({title:o.message}))}},fail:function(e){t.hideLoading(),i.Tips({title:"上传图片失败"})}})}})},getUrlParams:function(t,e,n){if("string"!=typeof t)return{};e=e||"&",n=n||"=";var r={};if(-1!==t.indexOf(e)){for(var i in t=t.split(e),t)if(-1!==t[i].indexOf(n)){var o=t[i].split(n);r[o[0]]=o[1]}}else{if(-1===t.indexOf(n))return t;o=t.split(n);r[o[0]]=o[1]}return r},formatMpQrCodeData:function(t){var e=t.split(","),n={};if(2===e.length){var r=e[0].split(":");"pid"===r[0]?n.spread=r[1]:n.id=r[1];var i=e[1].split(":");"pid"===i[0]?n.spread=i[1]:n.id=i[1]}else n.spread=e[0].split(":")[1];return n},SplitArray:function(t,e){if("object"!=(0,u.default)(t))return[];void 0===e&&(e=[]);for(var n=0;n<t.length;n++)e.push(t[n]);return e},trim:function(t){return String.prototype.trim.call(t)},$h:{Div:function(t,e){t=parseFloat(t),e=parseFloat(e);var n,r,i=0,o=0;try{i=t.toString().split(".")[1].length}catch(a){}try{o=e.toString().split(".")[1].length}catch(a){}return n=Number(t.toString().replace(".","")),r=Number(e.toString().replace(".","")),this.Mul(n/r,Math.pow(10,o-i))},Add:function(t,e){var n,r,i;e=parseFloat(e);try{n=t.toString().split(".")[1].length}catch(o){n=0}try{r=e.toString().split(".")[1].length}catch(o){r=0}return i=Math.pow(100,Math.max(n,r)),(this.Mul(t,i)+this.Mul(e,i))/i},Sub:function(t,e){var n,r,i,o;t=parseFloat(t),e=parseFloat(e);try{n=t.toString().split(".")[1].length}catch(a){n=0}try{r=e.toString().split(".")[1].length}catch(a){r=0}return i=Math.pow(10,Math.max(n,r)),o=n>=r?n:r,((this.Mul(t,i)-this.Mul(e,i))/i).toFixed(o)},Mul:function(t,e){t=parseFloat(t),e=parseFloat(e);var n=0,r=t.toString(),i=e.toString();try{n+=r.split(".")[1].length}catch(o){}try{n+=i.split(".")[1].length}catch(o){}return Number(r.replace(".",""))*Number(i.replace(".",""))/Math.pow(10,n)}},$L:{getLocation:function(){var t=this;return(0,o.default)(i.default.mark((function e(){var n;return i.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.getSetting();case 2:if(n=e.sent,2!==n){e.next=6;break}return t.openSetting(),e.abrupt("return");case 6:t.doGetLocation();case 7:case"end":return e.stop()}}),e)})))()},doGetLocation:function(){t.getLocation({success:function(e){t.removeStorageSync("CACHE_LONGITUDE"),t.removeStorageSync("CACHE_LATITUDE"),t.setStorageSync("CACHE_LONGITUDE",e.longitude),t.setStorageSync("CACHE_LATITUDE",e.latitude)},fail:function(e){e.errMsg.indexOf("auth deny")>=0?t.showToast({title:"访问位置被拒绝"}):t.showToast({title:e.errMsg})}})},getSetting:function(){return new Promise((function(e,n){t.getSetting({success:function(t){void 0!==t.authSetting["scope.userLocation"]?t.authSetting["scope.userLocation"]?e(1):e(2):e(0)}})}))},openSetting:function(){var e=this;t.openSetting({success:function(t){t.authSetting&&t.authSetting["scope.userLocation"]&&e.doGetLocation()},fail:function(t){}})},checkPermission:function(){return(0,o.default)(i.default.mark((function e(){var n;return i.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!permision.isIOS){e.next=6;break}return e.next=3,permision.requestIOS("location");case 3:e.t0=e.sent,e.next=9;break;case 6:return e.next=8,permision.requestAndroid("android.permission.ACCESS_FINE_LOCATION");case 8:e.t0=e.sent;case 9:return n=e.t0,null===n||1===n?n=1:2===n?t.showModal({content:"系统定位已关闭",confirmText:"确定",showCancel:!1,success:function(t){}}):n.code?t.showModal({content:n.message}):t.showModal({content:"需要定位权限",confirmText:"设置",success:function(t){t.confirm&&permision.gotoAppSetting()}}),e.abrupt("return",n);case 12:case"end":return e.stop()}}),e)})))()}},toStringValue:function(t){function e(e){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}((function(t){if(t instanceof Array){for(var e=[],n=0;n<t.length;n++)e[n]=toStringValue(t[n]);return e}if("object"==(0,u.default)(t))for(var r in t)t[r]=toStringValue(t[r]);else"number"==typeof t&&(t+="");return t})),setDomain:function(t){return t=t?t.toString():"",t.indexOf("https://")>-1?t:t.replace("http://","https://")},formatName:function(t){return t.substr(0,1)+new Array(t.length).join("*")}});e.default=f}).call(this,n("df3c")["default"])},"9e89":function(t,e){},"9fc1":function(t,e,n){var r=n("3b2d")["default"];function i(){"use strict";
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */t.exports=i=function(){return n},t.exports.__esModule=!0,t.exports["default"]=t.exports;var e,n={},o=Object.prototype,a=o.hasOwnProperty,s=Object.defineProperty||function(t,e,n){t[e]=n.value},u="function"==typeof Symbol?Symbol:{},c=u.iterator||"@@iterator",l=u.asyncIterator||"@@asyncIterator",f=u.toStringTag||"@@toStringTag";function d(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{d({},"")}catch(e){d=function(t,e,n){return t[e]=n}}function h(t,e,n,r){var i=e&&e.prototype instanceof _?e:_,o=Object.create(i.prototype),a=new L(r||[]);return s(o,"_invoke",{value:T(t,n,a)}),o}function p(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}n.wrap=h;var v="suspendedStart",g="executing",y="completed",m={};function _(){}function b(){}function A(){}var w={};d(w,c,(function(){return this}));var O=Object.getPrototypeOf,x=O&&O(O(j([])));x&&x!==o&&a.call(x,c)&&(w=x);var S=A.prototype=_.prototype=Object.create(w);function k(t){["next","throw","return"].forEach((function(e){d(t,e,(function(t){return this._invoke(e,t)}))}))}function E(t,e){function n(i,o,s,u){var c=p(t[i],t,o);if("throw"!==c.type){var l=c.arg,f=l.value;return f&&"object"==r(f)&&a.call(f,"__await")?e.resolve(f.__await).then((function(t){n("next",t,s,u)}),(function(t){n("throw",t,s,u)})):e.resolve(f).then((function(t){l.value=t,s(l)}),(function(t){return n("throw",t,s,u)}))}u(c.arg)}var i;s(this,"_invoke",{value:function(t,r){function o(){return new e((function(e,i){n(t,r,e,i)}))}return i=i?i.then(o,o):o()}})}function T(t,n,r){var i=v;return function(o,a){if(i===g)throw Error("Generator is already running");if(i===y){if("throw"===o)throw a;return{value:e,done:!0}}for(r.method=o,r.arg=a;;){var s=r.delegate;if(s){var u=C(s,r);if(u){if(u===m)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===v)throw i=y,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=g;var c=p(t,n,r);if("normal"===c.type){if(i=r.done?y:"suspendedYield",c.arg===m)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(i=y,r.method="throw",r.arg=c.arg)}}}function C(t,n){var r=n.method,i=t.iterator[r];if(i===e)return n.delegate=null,"throw"===r&&t.iterator["return"]&&(n.method="return",n.arg=e,C(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),m;var o=p(i,t.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,m;var a=o.arg;return a?a.done?(n[t.resultName]=a.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,m):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,m)}function P(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function $(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function L(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(P,this),this.reset(!0)}function j(t){if(t||""===t){var n=t[c];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,o=function n(){for(;++i<t.length;)if(a.call(t,i))return n.value=t[i],n.done=!1,n;return n.value=e,n.done=!0,n};return o.next=o}}throw new TypeError(r(t)+" is not iterable")}return b.prototype=A,s(S,"constructor",{value:A,configurable:!0}),s(A,"constructor",{value:b,configurable:!0}),b.displayName=d(A,f,"GeneratorFunction"),n.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},n.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,A):(t.__proto__=A,d(t,f,"GeneratorFunction")),t.prototype=Object.create(S),t},n.awrap=function(t){return{__await:t}},k(E.prototype),d(E.prototype,l,(function(){return this})),n.AsyncIterator=E,n.async=function(t,e,r,i,o){void 0===o&&(o=Promise);var a=new E(h(t,e,r,i),o);return n.isGeneratorFunction(e)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},k(S),d(S,f,"Generator"),d(S,c,(function(){return this})),d(S,"toString",(function(){return"[object Generator]"})),n.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},n.values=j,L.prototype={constructor:L,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach($),!t)for(var n in this)"t"===n.charAt(0)&&a.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function r(r,i){return s.type="throw",s.arg=t,n.next=r,i&&(n.method="next",n.arg=e),!!i}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var u=a.call(o,"catchLoc"),c=a.call(o,"finallyLoc");if(u&&c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(u){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&a.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),$(n),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;$(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:j(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),m}},n}t.exports=i,t.exports.__esModule=!0,t.exports["default"]=t.exports},a708:function(t,e,n){var r=n("6454");t.exports=function(t){if(Array.isArray(t))return r(t)},t.exports.__esModule=!0,t.exports["default"]=t.exports},ad96:function(t,e,n){"use strict";var r=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.bindingPhone=function(t){return i.default.post("update/binding",t)},e.bindingVerify=function(t){return i.default.post("update/binding/verify",t)},e.getAppVersion=function(){return i.default.get("index/get/version",{},{noAuth:!0})},e.getArticleBannerList=function(){return i.default.get("article/banner/list",{},{noAuth:!0})},e.getArticleCategoryList=function(){return i.default.get("article/category/list",{},{noAuth:!0})},e.getArticleDetails=function(t){return i.default.get("article/info",t,{noAuth:!0})},e.getArticleHotList=function(){return i.default.get("article/hot/list",{},{noAuth:!0})},e.getArticleList=function(t,e){return i.default.get("article/list/"+t,e,{noAuth:!0})},e.getCity=function(){return i.default.get("city/list",{},{noAuth:!0})},e.getCoupons=function(t){return i.default.get("coupons",t,{noAuth:!0})},e.getImageDomain=function(){return i.default.get("image/domain",{},{noAuth:!0})},e.getIndexData=function(){return i.default.get("index",{},{noAuth:!0})},e.getLiveList=function(t,e){return i.default.get("wechat/live",{page:t,limit:e},{noAuth:!0})},e.getLogo=function(){return i.default.get("wechat/getLogo",{},{noAuth:!0})},e.getQrcode=function(t){return i.default.post("qrcode/get",t,{noAuth:!0})},e.getTemlIds=function(t){return i.default.get("wechat/program/my/temp/list",t,{noAuth:!0})},e.getTheme=function(){return i.default.get("index/color/config",{},{noAuth:!0})},e.getUserCoupons=function(t){return i.default.get("coupon/list",t)},e.loginMobile=function(t){return i.default.post("login/mobile",t,{noAuth:!0})},e.logout=function(){return i.default.get("logout")},e.phoneLogin=function(t){return i.default.post("login",t,{noAuth:!0})},e.phoneRegister=function(t){return i.default.post("register",t,{noAuth:!0})},e.phoneRegisterReset=function(t){return i.default.post("register/reset",t,{noAuth:!0})},e.pink=function(){return i.default.get("pink",{},{noAuth:!0})},e.productRank=function(){return i.default.get("product/leaderboard",{},{noAuth:!0})},e.registerVerify=function(t){return i.default.post("sendCode",{phone:t},{noAuth:!0},1)},e.setCouponReceive=function(t){return i.default.post("coupon/receive",{couponId:t})},e.setFormId=function(t){return i.default.post("wechat/set_form_id",{formId:t})},e.switchH5Login=function(){return i.default.post("switch_h5",{from:"routine"})},e.verifyCode=function(){return i.default.get("verify_code",{},{noAuth:!0})};var i=r(n("1178"))},af34:function(t,e,n){var r=n("a708"),i=n("b893"),o=n("6382"),a=n("9008");t.exports=function(t){return r(t)||i(t)||o(t)||a()},t.exports.__esModule=!0,t.exports["default"]=t.exports},b893:function(t,e){t.exports=function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)},t.exports.__esModule=!0,t.exports["default"]=t.exports},bac5:function(t,e,n){var r,i,o=n("3b2d");!function(a,s){"object"==o(e)&&"undefined"!=typeof t?t.exports=s():(r=s,i="function"===typeof r?r.call(e,n,e,t):r,void 0===i||(t.exports=i))}(0,(function(){"use strict";var t="millisecond",e="second",n="minute",r="hour",i="day",a="week",s="month",u="quarter",c="year",l=/^(\d{4})-?(\d{1,2})-?(\d{0,2})[^0-9]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?.?(\d{1,3})?$/,f=/\[([^\]]+)]|Y{2,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,d=function(t,e,n){var r=String(t);return!r||r.length>=e?t:""+Array(e+1-r.length).join(n)+t},h={s:d,z:function(t){var e=-t.utcOffset(),n=Math.abs(e),r=Math.floor(n/60),i=n%60;return(e<=0?"+":"-")+d(r,2,"0")+":"+d(i,2,"0")},m:function(t,e){var n=12*(e.year()-t.year())+(e.month()-t.month()),r=t.clone().add(n,s),i=e-r<0,o=t.clone().add(n+(i?-1:1),s);return Number(-(n+(e-r)/(i?r-o:o-r))||0)},a:function(t){return t<0?Math.ceil(t)||0:Math.floor(t)},p:function(o){return{M:s,y:c,w:a,d:i,D:"date",h:r,m:n,s:e,ms:t,Q:u}[o]||String(o||"").toLowerCase().replace(/s$/,"")},u:function(t){return void 0===t}},p={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_")},v="en",g={};g[v]=p;var y=function(t){return t instanceof A},m=function(t,e,n){var r;if(!t)return v;if("string"==typeof t)g[t]&&(r=t),e&&(g[t]=e,r=t);else{var i=t.name;g[i]=t,r=i}return!n&&r&&(v=r),r||!n&&v},_=function(t,e){if(y(t))return t.clone();var n="object"==o(e)?e:{};return n.date=t,n.args=arguments,new A(n)},b=h;b.l=m,b.i=y,b.w=function(t,e){return _(t,{locale:e.$L,utc:e.$u,$offset:e.$offset})};var A=function(){function o(t){this.$L=this.$L||m(t.locale,null,!0),this.parse(t)}var d=o.prototype;return d.parse=function(t){this.$d=function(t){var e=t.date,n=t.utc;if(null===e)return new Date(NaN);if(b.u(e))return new Date;if(e instanceof Date)return new Date(e);if("string"==typeof e&&!/Z$/i.test(e)){var r=e.match(l);if(r)return n?new Date(Date.UTC(r[1],r[2]-1,r[3]||1,r[4]||0,r[5]||0,r[6]||0,r[7]||0)):new Date(r[1],r[2]-1,r[3]||1,r[4]||0,r[5]||0,r[6]||0,r[7]||0)}return new Date(e)}(t),this.init()},d.init=function(){var t=this.$d;this.$y=t.getFullYear(),this.$M=t.getMonth(),this.$D=t.getDate(),this.$W=t.getDay(),this.$H=t.getHours(),this.$m=t.getMinutes(),this.$s=t.getSeconds(),this.$ms=t.getMilliseconds()},d.$utils=function(){return b},d.isValid=function(){return!("Invalid Date"===this.$d.toString())},d.isSame=function(t,e){var n=_(t);return this.startOf(e)<=n&&n<=this.endOf(e)},d.isAfter=function(t,e){return _(t)<this.startOf(e)},d.isBefore=function(t,e){return this.endOf(e)<_(t)},d.$g=function(t,e,n){return b.u(t)?this[e]:this.set(n,t)},d.year=function(t){return this.$g(t,"$y",c)},d.month=function(t){return this.$g(t,"$M",s)},d.day=function(t){return this.$g(t,"$W",i)},d.date=function(t){return this.$g(t,"$D","date")},d.hour=function(t){return this.$g(t,"$H",r)},d.minute=function(t){return this.$g(t,"$m",n)},d.second=function(t){return this.$g(t,"$s",e)},d.millisecond=function(e){return this.$g(e,"$ms",t)},d.unix=function(){return Math.floor(this.valueOf()/1e3)},d.valueOf=function(){return this.$d.getTime()},d.startOf=function(t,o){var u=this,l=!!b.u(o)||o,f=b.p(t),d=function(t,e){var n=b.w(u.$u?Date.UTC(u.$y,e,t):new Date(u.$y,e,t),u);return l?n:n.endOf(i)},h=function(t,e){return b.w(u.toDate()[t].apply(u.toDate("s"),(l?[0,0,0,0]:[23,59,59,999]).slice(e)),u)},p=this.$W,v=this.$M,g=this.$D,y="set"+(this.$u?"UTC":"");switch(f){case c:return l?d(1,0):d(31,11);case s:return l?d(1,v):d(0,v+1);case a:var m=this.$locale().weekStart||0,_=(p<m?p+7:p)-m;return d(l?g-_:g+(6-_),v);case i:case"date":return h(y+"Hours",0);case r:return h(y+"Minutes",1);case n:return h(y+"Seconds",2);case e:return h(y+"Milliseconds",3);default:return this.clone()}},d.endOf=function(t){return this.startOf(t,!1)},d.$set=function(o,a){var u,l=b.p(o),f="set"+(this.$u?"UTC":""),d=(u={},u[i]=f+"Date",u.date=f+"Date",u[s]=f+"Month",u[c]=f+"FullYear",u[r]=f+"Hours",u[n]=f+"Minutes",u[e]=f+"Seconds",u[t]=f+"Milliseconds",u)[l],h=l===i?this.$D+(a-this.$W):a;if(l===s||l===c){var p=this.clone().set("date",1);p.$d[d](h),p.init(),this.$d=p.set("date",Math.min(this.$D,p.daysInMonth())).toDate()}else d&&this.$d[d](h);return this.init(),this},d.set=function(t,e){return this.clone().$set(t,e)},d.get=function(t){return this[b.p(t)]()},d.add=function(t,o){var u,l=this;t=Number(t);var f=b.p(o),d=function(e){var n=_(l);return b.w(n.date(n.date()+Math.round(e*t)),l)};if(f===s)return this.set(s,this.$M+t);if(f===c)return this.set(c,this.$y+t);if(f===i)return d(1);if(f===a)return d(7);var h=(u={},u[n]=6e4,u[r]=36e5,u[e]=1e3,u)[f]||1,p=this.$d.getTime()+t*h;return b.w(p,this)},d.subtract=function(t,e){return this.add(-1*t,e)},d.format=function(t){var e=this;if(!this.isValid())return"Invalid Date";var n=t||"YYYY-MM-DDTHH:mm:ssZ",r=b.z(this),i=this.$locale(),o=this.$H,a=this.$m,s=this.$M,u=i.weekdays,c=i.months,l=function(t,r,i,o){return t&&(t[r]||t(e,n))||i[r].substr(0,o)},d=function(t){return b.s(o%12||12,t,"0")},h=i.meridiem||function(t,e,n){var r=t<12?"AM":"PM";return n?r.toLowerCase():r},p={YY:String(this.$y).slice(-2),YYYY:this.$y,M:s+1,MM:b.s(s+1,2,"0"),MMM:l(i.monthsShort,s,c,3),MMMM:c[s]||c(this,n),D:this.$D,DD:b.s(this.$D,2,"0"),d:String(this.$W),dd:l(i.weekdaysMin,this.$W,u,2),ddd:l(i.weekdaysShort,this.$W,u,3),dddd:u[this.$W],H:String(o),HH:b.s(o,2,"0"),h:d(1),hh:d(2),a:h(o,a,!0),A:h(o,a,!1),m:String(a),mm:b.s(a,2,"0"),s:String(this.$s),ss:b.s(this.$s,2,"0"),SSS:b.s(this.$ms,3,"0"),Z:r};return n.replace(f,(function(t,e){return e||p[t]||r.replace(":","")}))},d.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},d.diff=function(t,o,l){var f,d=b.p(o),h=_(t),p=6e4*(h.utcOffset()-this.utcOffset()),v=this-h,g=b.m(this,h);return g=(f={},f[c]=g/12,f[s]=g,f[u]=g/3,f[a]=(v-p)/6048e5,f[i]=(v-p)/864e5,f[r]=v/36e5,f[n]=v/6e4,f[e]=v/1e3,f)[d]||v,l?g:b.a(g)},d.daysInMonth=function(){return this.endOf(s).$D},d.$locale=function(){return g[this.$L]},d.locale=function(t,e){if(!t)return this.$L;var n=this.clone(),r=m(t,e,!0);return r&&(n.$L=r),n},d.clone=function(){return b.w(this.$d,this)},d.toDate=function(){return new Date(this.valueOf())},d.toJSON=function(){return this.isValid()?this.toISOString():null},d.toISOString=function(){return this.$d.toISOString()},d.toString=function(){return this.$d.toUTCString()},o}();return _.prototype=A.prototype,_.extend=function(t,e){return t(e,A,_),_},_.locale=m,_.isDayjs=y,_.unix=function(t){return _(1e3*t)},_.en=g[v],_.Ls=g,_}))},bb5a:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r={data:function(){return{disabled:!1,text:"获取验证码"}},methods:{sendCode:function(){var t=this;if(!this.disabled){this.disabled=!0;var e=60;this.text="剩余 "+e+"s";var n=setInterval((function(){e-=1,e<0&&clearInterval(n),t.text="剩余 "+e+"s",t.text<"剩余 0s"&&(t.disabled=!1,t.text="重新获取")}),1e3)}}}};e.default=r},d3b4:function(t,e,n){"use strict";(function(t,r){var i=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.LOCALE_ZH_HANT=e.LOCALE_ZH_HANS=e.LOCALE_FR=e.LOCALE_ES=e.LOCALE_EN=e.I18n=e.Formatter=void 0,e.compileI18nJsonStr=function(t,e){var n=e.locale,r=e.locales,i=e.delimiters;if(!S(t,i))return t;O||(O=new f);var o=[];Object.keys(r).forEach((function(t){t!==n&&o.push({locale:t,values:r[t]})})),o.unshift({locale:n,values:r[n]});try{return JSON.stringify(E(JSON.parse(t),o,i),null,2)}catch(a){}return t},e.hasI18nJson=function t(e,n){O||(O=new f);return T(e,(function(e,r){var i=e[r];return x(i)?!!S(i,n)||void 0:t(i,n)}))},e.initVueI18n=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0;if("string"!==typeof t){var i=[e,t];t=i[0],e=i[1]}"string"!==typeof t&&(t=w());"string"!==typeof n&&(n="undefined"!==typeof __uniConfig&&__uniConfig.fallbackLocale||"en");var o=new b({locale:t,fallbackLocale:n,messages:e,watcher:r}),a=function(t,e){if("function"!==typeof getApp)a=function(t,e){return o.t(t,e)};else{var n=!1;a=function(t,e){var r=getApp().$vm;return r&&(r.$locale,n||(n=!0,A(r,o))),o.t(t,e)}}return a(t,e)};return{i18n:o,f:function(t,e,n){return o.f(t,e,n)},t:function(t,e){return a(t,e)},add:function(t,e){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return o.add(t,e,n)},watch:function(t){return o.watchLocale(t)},getLocale:function(){return o.getLocale()},setLocale:function(t){return o.setLocale(t)}}},e.isI18nStr=S,e.isString=void 0,e.normalizeLocale=_,e.parseI18nJson=function t(e,n,r){O||(O=new f);return T(e,(function(e,i){var o=e[i];x(o)?S(o,r)&&(e[i]=k(o,n,r)):t(o,n,r)})),e},e.resolveLocale=function(t){return function(e){return e?(e=_(e)||e,function(t){var e=[],n=t.split("-");while(n.length)e.push(n.join("-")),n.pop();return e}(e).find((function(e){return t.indexOf(e)>-1}))):e}};var o=i(n("34cf")),a=i(n("67ad")),s=i(n("0bdb")),u=i(n("3b2d")),c=function(t){return null!==t&&"object"===(0,u.default)(t)},l=["{","}"],f=function(){function t(){(0,a.default)(this,t),this._caches=Object.create(null)}return(0,s.default)(t,[{key:"interpolate",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:l;if(!e)return[t];var r=this._caches[t];return r||(r=p(t,n),this._caches[t]=r),v(r,e)}}]),t}();e.Formatter=f;var d=/^(?:\d)+/,h=/^(?:\w)+/;function p(t,e){var n=(0,o.default)(e,2),r=n[0],i=n[1],a=[],s=0,u="";while(s<t.length){var c=t[s++];if(c===r){u&&a.push({type:"text",value:u}),u="";var l="";c=t[s++];while(void 0!==c&&c!==i)l+=c,c=t[s++];var f=c===i,p=d.test(l)?"list":f&&h.test(l)?"named":"unknown";a.push({value:l,type:p})}else u+=c}return u&&a.push({type:"text",value:u}),a}function v(t,e){var n=[],r=0,i=Array.isArray(e)?"list":c(e)?"named":"unknown";if("unknown"===i)return n;while(r<t.length){var o=t[r];switch(o.type){case"text":n.push(o.value);break;case"list":n.push(e[parseInt(o.value,10)]);break;case"named":"named"===i&&n.push(e[o.value]);break;case"unknown":0;break}r++}return n}e.LOCALE_ZH_HANS="zh-Hans";e.LOCALE_ZH_HANT="zh-Hant";e.LOCALE_EN="en";e.LOCALE_FR="fr";e.LOCALE_ES="es";var g=Object.prototype.hasOwnProperty,y=function(t,e){return g.call(t,e)},m=new f;function _(t,e){if(t){if(t=t.trim().replace(/_/g,"-"),e&&e[t])return t;if(t=t.toLowerCase(),"chinese"===t)return"zh-Hans";if(0===t.indexOf("zh"))return t.indexOf("-hans")>-1?"zh-Hans":t.indexOf("-hant")>-1||function(t,e){return!!e.find((function(e){return-1!==t.indexOf(e)}))}(t,["-tw","-hk","-mo","-cht"])?"zh-Hant":"zh-Hans";var n=["en","fr","es"];e&&Object.keys(e).length>0&&(n=Object.keys(e));var r=function(t,e){return e.find((function(e){return 0===t.indexOf(e)}))}(t,n);return r||void 0}}var b=function(){function t(e){var n=e.locale,r=e.fallbackLocale,i=e.messages,o=e.watcher,s=e.formater;(0,a.default)(this,t),this.locale="en",this.fallbackLocale="en",this.message={},this.messages={},this.watchers=[],r&&(this.fallbackLocale=r),this.formater=s||m,this.messages=i||{},this.setLocale(n||"en"),o&&this.watchLocale(o)}return(0,s.default)(t,[{key:"setLocale",value:function(t){var e=this,n=this.locale;this.locale=_(t,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],n!==this.locale&&this.watchers.forEach((function(t){t(e.locale,n)}))}},{key:"getLocale",value:function(){return this.locale}},{key:"watchLocale",value:function(t){var e=this,n=this.watchers.push(t)-1;return function(){e.watchers.splice(n,1)}}},{key:"add",value:function(t,e){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=this.messages[t];r?n?Object.assign(r,e):Object.keys(e).forEach((function(t){y(r,t)||(r[t]=e[t])})):this.messages[t]=e}},{key:"f",value:function(t,e,n){return this.formater.interpolate(t,e,n).join("")}},{key:"t",value:function(t,e,n){var r=this.message;return"string"===typeof e?(e=_(e,this.messages),e&&(r=this.messages[e])):n=e,y(r,t)?this.formater.interpolate(r[t],n).join(""):(console.warn("Cannot translate the value of keypath ".concat(t,". Use the value of keypath as default.")),t)}}]),t}();function A(t,e){t.$watchLocale?t.$watchLocale((function(t){e.setLocale(t)})):t.$watch((function(){return t.$locale}),(function(t){e.setLocale(t)}))}function w(){return"undefined"!==typeof t&&t.getLocale?t.getLocale():"undefined"!==typeof r&&r.getLocale?r.getLocale():"en"}e.I18n=b;var O,x=function(t){return"string"===typeof t};function S(t,e){return t.indexOf(e[0])>-1}function k(t,e,n){return O.interpolate(t,e,n).join("")}function E(t,e,n){return T(t,(function(t,r){(function(t,e,n,r){var i=t[e];if(x(i)){if(S(i,r)&&(t[e]=k(i,n[0].values,r),n.length>1)){var o=t[e+"Locales"]={};n.forEach((function(t){o[t.locale]=k(i,t.values,r)}))}}else E(i,n,r)})(t,r,e,n)})),t}function T(t,e){if(Array.isArray(t)){for(var n=0;n<t.length;n++)if(e(t,n))return!0}else if(c(t))for(var r in t)if(e(t,r))return!0;return!1}e.isString=x}).call(this,n("df3c")["default"],n("0ee4"))},d551:function(t,e,n){var r=n("3b2d")["default"],i=n("e6db");t.exports=function(t){var e=i(t,"string");return"symbol"==r(e)?e:e+""},t.exports.__esModule=!0,t.exports["default"]=t.exports},d56e:function(t,e,n){(function(e){var r=n("67ad"),i=n("0bdb"),o=n("7ca3"),a=n("f7bd"),s=a.blankChar,u=n("ffbc"),c=e.getSystemInfoSync(),l=c.screenWidth,f=c.system,d=function(){"use strict";function t(e){var n=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};r(this,t),o(this,"getName",(function(t){return n.xml?t:t.toLowerCase()})),o(this,"isClose",(function(){return">"==n.data[n.i]||"/"==n.data[n.i]&&">"==n.data[n.i+1]})),o(this,"section",(function(){return n.data.substring(n.start,n.i)})),o(this,"siblings",(function(){return n.STACK.length?n.STACK[n.STACK.length-1].children:n.DOM})),this.attrs={},this.compress=i.compress,this.CssHandler=new u(i.tagStyle,l),this.data=e,this.domain=i.domain,this.DOM=[],this.i=this.start=this.audioNum=this.imgNum=this.videoNum=0,this.protocol=this.domain&&this.domain.includes("://")?this.domain.split("://")[0]:"",this.state=this.Text,this.STACK=[],this.useAnchor=i.useAnchor,this.xml=i.xml}return i(t,[{key:"parse",value:function(){for(var t;t=this.data[this.i];this.i++)this.state(t);this.state==this.Text&&this.setText();while(this.STACK.length)this.popNode(this.STACK.pop());return this.DOM.length&&(this.DOM[0].PoweredBy="Parser",this.title&&(this.DOM[0].title=this.title)),this.DOM}},{key:"setAttr",value:function(){var t=this.getName(this.attrName);a.trustAttrs[t]&&(this.attrVal?this.attrs[t]="src"==t?this.getUrl(this.attrVal.replace(/&amp;/g,"&")):this.attrVal:a.boolAttrs[t]&&(this.attrs[t]="T")),this.attrVal="";while(s[this.data[this.i]])this.i++;this.isClose()?this.setNode():(this.start=this.i,this.state=this.AttrName)}},{key:"setText",value:function(){var t,e=this.section();if(e)if(e=a.onText&&a.onText(e,(function(){return t=!0}))||e,t){this.data=this.data.substr(0,this.start)+e+this.data.substr(this.i);var n=this.start+e.length;for(this.i=this.start;this.i<n;this.i++)this.state(this.data[this.i])}else{if(!this.pre){for(var r,i=[],o=e.length;r=e[--o];)(!s[r]||!s[i[0]]&&(r=" "))&&i.unshift(r);if(e=i.join("")," "==e)return}var u,c,l=this.siblings(),f=-1;while(1){if(-1==(f=e.indexOf("&",f+1)))break;if(-1==(u=e.indexOf(";",f+2)))break;"#"==e[f+1]?(c=parseInt(("x"==e[f+2]?"0":"")+e.substring(f+2,u)),isNaN(c)||(e=e.substr(0,f)+String.fromCharCode(c)+e.substring(u+1))):(c=e.substring(f+1,u),"nbsp"==c?e=e.substr(0,f)+" "+e.substr(u+1):"lt"!=c&&"gt"!=c&&"amp"!=c&&"ensp"!=c&&"emsp"!=c&&"quot"!=c&&"apos"!=c&&(f&&l.push({type:"text",text:e.substr(0,f)}),l.push({type:"text",text:"&".concat(c,";"),en:1}),e=e.substr(u+1),f=-1))}e&&l.push({type:"text",text:e})}}},{key:"setNode",value:function(){var t={name:this.tagName.toLowerCase(),attrs:this.attrs},e=a.selfClosingTags[t.name]||this.xml&&"/"==this.data[this.i];if(this.attrs={},a.ignoreTags[t.name])if(e)if("source"==t.name){var n=this.STACK[this.STACK.length-1],r=t.attrs;if(n&&r.src)if("video"==n.name||"audio"==n.name)n.attrs.source.push(r.src);else{var i,o=r.media;"picture"!=n.name||n.attrs.src||r.src.indexOf(".webp")&&f.includes("iOS")||!(!o||o.includes("px")&&(-1!=(i=o.indexOf("min-width"))&&-1!=(i=o.indexOf(":",i+8))&&l>parseInt(o.substr(i+1))||-1!=(i=o.indexOf("max-width"))&&-1!=(i=o.indexOf(":",i+8))&&l<parseInt(o.substr(i+1))))||(n.attrs.src=r.src)}}else"base"!=t.name||this.domain||(this.domain=t.attrs.href);else this.remove(t);else this.matchAttr(t),e?a.filter&&0==a.filter(t,this)||this.siblings().push(t):(t.children=[],"pre"==t.name&&a.highlight&&(this.remove(t),this.pre=t.pre=!0),this.siblings().push(t),this.STACK.push(t));"/"==this.data[this.i]&&this.i++,this.start=this.i+1,this.state=this.Text}},{key:"remove",value:function(t){var e=t.name,n=this.i;while(1){if(-1==(this.i=this.data.indexOf("</",this.i+1)))return void(this.i="pre"==e||"svg"==e?n:this.data.length);this.start=this.i+=2;while(!s[this.data[this.i]]&&!this.isClose())this.i++;if(this.getName(this.section())==e){if("pre"==e)return this.data=this.data.substr(0,n+1)+a.highlight(this.data.substring(n+1,this.i-5),t.attrs)+this.data.substr(this.i-5),this.i=n;if("style"==e?this.CssHandler.getStyle(this.data.substring(n+1,this.i-7)):"title"==e&&(this.title=this.data.substring(n+1,this.i-7)),-1==(this.i=this.data.indexOf(">",this.i))&&(this.i=this.data.length),"svg"==e){var r=this.data.substring(n,this.i+1);t.attrs.xmlns||(r=' xmlns="http://www.w3.org/2000/svg"'+r);var i=n;while("<"!=this.data[n])n--;r=this.data.substring(n,i)+r;var o=this.STACK[this.STACK.length-1];"100%"==t.attrs.width&&o&&(o.attrs.style||"").includes("inline")&&(o.attrs.style="width:300px;max-width:100%;"+o.attrs.style),this.siblings().push({name:"img",attrs:{src:"data:image/svg+xml;utf8,"+r.replace(/#/g,"%23"),ignore:"T"}})}return}}}},{key:"matchAttr",value:function(t){var e=t.attrs,n=this.CssHandler.match(t.name,e,t)+(e.style||""),r={};switch(e.id&&(1&this.compress?e.id=void 0:this.useAnchor&&this.bubble()),2&this.compress&&e.class&&(e.class=void 0),t.name){case"img":e["data-src"]&&(e.src=e.src||e["data-src"],e["data-src"]=void 0),e.src&&!e.ignore&&(this.bubble()?e.i=(this.imgNum++).toString():e.ignore="T");break;case"a":case"ad":this.bubble();break;case"font":if(e.color&&(r["color"]=e.color,e.color=void 0),e.face&&(r["font-family"]=e.face,e.face=void 0),e.size){var i=parseInt(e.size);i<1?i=1:i>7&&(i=7);r["font-size"]=["xx-small","x-small","small","medium","large","x-large","xx-large"][i-1],e.size=void 0}break;case"video":case"audio":e.id?this["".concat(t.name,"Num")]++:e.id=t.name+ ++this["".concat(t.name,"Num")],"video"==t.name&&(e.width&&(n="width:".concat(parseFloat(e.width)+(e.width.includes("%")?"%":"px"),";").concat(n),e.width=void 0),e.height&&(n="height:".concat(parseFloat(e.height)+(e.height.includes("%")?"%":"px"),";").concat(n),e.height=void 0),this.videoNum>3&&(t.lazyLoad=!0)),e.source=[],e.src&&e.source.push(e.src),e.controls||e.autoplay||console.warn("存在没有 controls 属性的 ".concat(t.name," 标签，可能导致无法播放"),t),this.bubble();break;case"td":case"th":if(e.colspan||e.rowspan)for(var o,a=this.STACK.length;o=this.STACK[--a];)if("table"==o.name){o.c=void 0;break}}e.align&&(r["text-align"]=e.align,e.align=void 0);var u=n.replace(/&quot;/g,'"').replace(/&amp;/g,"&").split(";");n="";for(var c=0,f=u.length;c<f;c++){var d=u[c].split(":");if(!(d.length<2)){var h=d[0].trim().toLowerCase(),p=d.slice(1).join(":").trim();p.includes("-webkit")||p.includes("-moz")||p.includes("-ms")||p.includes("-o")||p.includes("safe")?n+=";".concat(h,":").concat(p):r[h]&&!p.includes("import")&&r[h].includes("import")||(r[h]=p)}}for(var v in"img"==t.name&&parseInt(r.width||e.width)>l&&(r.height="auto"),r){var g=r[v];if((v.includes("flex")||"order"==v||"self-align"==v)&&(t.c=1),g.includes("url")){var y=g.indexOf("(");if(-1!=y++){while('"'==g[y]||"'"==g[y]||s[g[y]])y++;g=g.substr(0,y)+this.getUrl(g.substr(y))}}else g.includes("rpx")?g=g.replace(/[0-9.]+\s*rpx/g,(function(t){return parseFloat(t)*l/750+"px"})):"white-space"==v&&g.includes("pre")&&(this.pre=t.pre=!0);n+=";".concat(v,":").concat(g)}n=n.substr(1),n&&(e.style=n)}},{key:"popNode",value:function(t){if(t.pre){t.pre=this.pre=void 0;for(var e=this.STACK.length;e--;)this.STACK[e].pre&&(this.pre=!0)}if("head"==t.name||a.filter&&0==a.filter(t,this))return this.siblings().pop();var n=t.attrs;if("picture"==t.name)return t.name="img",n.src||"img"!=(t.children[0]||"").name||(n.src=t.children[0].attrs.src),n.src&&!n.ignore&&(n.i=(this.imgNum++).toString()),t.children=void 0;if(a.blockTags[t.name]?t.name="div":a.trustTags[t.name]||(t.name="span"),t.c)if("ul"==t.name){for(var r=1,i=this.STACK.length;i--;)"ul"==this.STACK[i].name&&r++;if(1!=r)for(var o=t.children.length;o--;)t.children[o].floor=r}else if("ol"==t.name)for(var s,u=0,c=1;s=t.children[u++];)"li"==s.name&&(s.type="ol",s.num=function(t,e){if("a"==e)return String.fromCharCode(97+(t-1)%26);if("A"==e)return String.fromCharCode(65+(t-1)%26);if("i"==e||"I"==e){t=(t-1)%99+1;var n=(["X","XX","XXX","XL","L","LX","LXX","LXXX","XC"][Math.floor(t/10)-1]||"")+(["I","II","III","IV","V","VI","VII","VIII","IX"][t%10-1]||"");return"i"==e?n.toLowerCase():n}return t}(c++,n.type)+".");if("table"==t.name){var l=n.cellpadding,f=n.cellspacing,d=n.border;t.c&&(this.bubble(),l||(l=2),f||(f=2)),d&&(n.style="border:".concat(d,"px solid gray;").concat(n.style||"")),f&&(n.style="border-spacing:".concat(f,"px;").concat(n.style||"")),(d||l)&&function t(e){for(var n,r=0;n=e[r];r++)"th"==n.name||"td"==n.name?(d&&(n.attrs.style="border:".concat(d,"px solid gray;").concat(n.attrs.style)),l&&(n.attrs.style="padding:".concat(l,"px;").concat(n.attrs.style))):t(n.children||[])}(t.children)}if(this.CssHandler.pop&&this.CssHandler.pop(t),"div"==t.name&&!Object.keys(n).length){var h=this.siblings();1==t.children.length&&"div"==t.children[0].name&&(h[h.length-1]=t.children[0])}}},{key:"bubble",value:function(){for(var t,e=this.STACK.length;t=this.STACK[--e];){if(a.richOnlyTags[t.name])return"table"!=t.name||Object.hasOwnProperty.call(t,"c")||(t.c=1),!1;t.c=1}return!0}},{key:"getUrl",value:function(t){return"/"==t[0]?"/"==t[1]?t=this.protocol+":"+t:this.domain&&(t=this.domain+t):this.domain&&0!=t.indexOf("data:")&&!t.includes("://")&&(t=this.domain+"/"+t),t}},{key:"Text",value:function(t){if("<"==t){var e=this.data[this.i+1],n=function(t){return t>="a"&&t<="z"||t>="A"&&t<="Z"};n(e)?(this.setText(),this.start=this.i+1,this.state=this.TagName):"/"==e?(this.setText(),n(this.data[1+ ++this.i])?(this.start=this.i+1,this.state=this.EndTag):this.Comment()):"!"==e&&(this.setText(),this.Comment())}}},{key:"Comment",value:function(){var t;t="--"==this.data.substring(this.i+2,this.i+4)?"--\x3e":"[CDATA["==this.data.substring(this.i+2,this.i+9)?"]]>":">",-1==(this.i=this.data.indexOf(t,this.i+2))?this.i=this.data.length:this.i+=t.length-1,this.start=this.i+1,this.state=this.Text}},{key:"TagName",value:function(t){if(s[t]){this.tagName=this.section();while(s[this.data[this.i]])this.i++;this.isClose()?this.setNode():(this.start=this.i,this.state=this.AttrName)}else this.isClose()&&(this.tagName=this.section(),this.setNode())}},{key:"AttrName",value:function(t){var e=s[t];if(e&&(this.attrName=this.section(),t=this.data[this.i]),"="==t){e||(this.attrName=this.section());while(s[this.data[++this.i]]);this.start=this.i--,this.state=this.AttrValue}else e?this.setAttr():this.isClose()&&(this.attrName=this.section(),this.setAttr())}},{key:"AttrValue",value:function(t){if('"'==t||"'"==t){if(this.start++,-1==(this.i=this.data.indexOf(t,this.i+1)))return this.i=this.data.length;this.attrVal=this.section(),this.i++}else{for(;!s[this.data[this.i]]&&!this.isClose();this.i++);this.attrVal=this.section()}this.setAttr()}},{key:"EndTag",value:function(t){if(s[t]||">"==t||"/"==t){for(var e=this.getName(this.section()),n=this.STACK.length;n--;)if(this.STACK[n].name==e)break;if(-1!=n){var r;while((r=this.STACK.pop()).name!=e);this.popNode(r)}else"p"!=e&&"br"!=e||this.siblings().push({name:e,attrs:{}});this.i=this.data.indexOf(">",this.i),this.start=this.i+1,-1==this.i?this.i=this.data.length:this.state=this.Text}}}]),t}();t.exports=d}).call(this,n("3223")["default"])},d9cf:function(t,e,n){"use strict";var r=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.collectAdd=function(t,e){return i.default.post("collect/add",{id:t,category:void 0===e?"product":e})},e.collectAll=function(t,e){return i.default.post("collect/all",{id:t,category:void 0===e?"product":e})},e.collectDel=function(t){return i.default.post("collect/cancel/".concat(t))},e.collectDelete=function(t){return i.default.post("collect/delete",t)},e.getAttr=function(t){return i.default.get("product/sku/detail/"+t)},e.getCategoryList=function(){return i.default.get("category",{},{noAuth:!0})},e.getCollectUserList=function(t){return i.default.get("collect/user",t)},e.getGroomList=function(t,e){return i.default.get("index/product/"+t,e,{noAuth:!0})},e.getProductCode=function(t){return i.default.get("product/code/"+t,{user_type:"routine"})},e.getProductDetail=function(t,e){return i.default.get("product/detail/"+t+"?type="+e,{},{noAuth:!0})},e.getProductGood=function(){return i.default.get("product/good",{},{noAuth:!0})},e.getProductHot=function(t,e){return i.default.get("product/hot",{page:void 0===t?1:t,limit:void 0===e?4:e},{noAuth:!0})},e.getProductslist=function(t){return i.default.get("products",t,{noAuth:!0})},e.getReplyConfig=function(t){return i.default.get("reply/config/"+t,{},{noAuth:!0})},e.getReplyList=function(t,e){return i.default.get("reply/list/"+t,e,{noAuth:!0})},e.getReplyProduct=function(t){return i.default.get("reply/product/"+t,{},{noAuth:!0})},e.getSearchKeyword=function(){return i.default.get("search/keyword",{},{noAuth:!0})},e.postCartAdd=function(t){return i.default.post("cart/save",t,{})},e.productList=function(t){return i.default.get("product/list",t,{noAuth:!0})},e.storeListApi=function(t){return i.default.post("store/list",t,{},1)};var i=r(n("1178"))},dc84:function(t,e){t.exports=function(t){return t.webpackPolyfill||(t.deprecate=function(){},t.paths=[],t.children||(t.children=[]),Object.defineProperty(t,"loaded",{enumerable:!0,get:function(){return t.l}}),Object.defineProperty(t,"id",{enumerable:!0,get:function(){return t.i}}),t.webpackPolyfill=1),t}},dd3e:function(t,e){t.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},t.exports.__esModule=!0,t.exports["default"]=t.exports},df3c:function(t,e,n){"use strict";(function(t,r){var i=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.createApp=Ie,e.createComponent=ze,e.createPage=Ve,e.createPlugin=Ke,e.createSubpackageApp=Ge,e.default=void 0;var o,a=i(n("34cf")),s=i(n("7ca3")),u=i(n("931d")),c=i(n("af34")),l=i(n("3b2d")),f=n("d3b4"),d=i(n("3240"));function h(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function p(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?h(Object(n),!0).forEach((function(e){(0,s.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var v="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",g=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function y(){var e,n=t.getStorageSync("uni_id_token")||"",r=n.split(".");if(!n||3!==r.length)return{uid:null,role:[],permission:[],tokenExpired:0};try{e=JSON.parse(function(t){return decodeURIComponent(o(t).split("").map((function(t){return"%"+("00"+t.charCodeAt(0).toString(16)).slice(-2)})).join(""))}(r[1]))}catch(i){throw new Error("获取当前用户信息出错，详细错误信息为："+i.message)}return e.tokenExpired=1e3*e.exp,delete e.exp,delete e.iat,e}o="function"!==typeof atob?function(t){if(t=String(t).replace(/[\t\n\f\r ]+/g,""),!g.test(t))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var e;t+="==".slice(2-(3&t.length));for(var n,r,i="",o=0;o<t.length;)e=v.indexOf(t.charAt(o++))<<18|v.indexOf(t.charAt(o++))<<12|(n=v.indexOf(t.charAt(o++)))<<6|(r=v.indexOf(t.charAt(o++))),i+=64===n?String.fromCharCode(e>>16&255):64===r?String.fromCharCode(e>>16&255,e>>8&255):String.fromCharCode(e>>16&255,e>>8&255,255&e);return i}:atob;var m=Object.prototype.toString,_=Object.prototype.hasOwnProperty;function b(t){return"function"===typeof t}function A(t){return"string"===typeof t}function w(t){return"[object Object]"===m.call(t)}function O(t,e){return _.call(t,e)}function x(){}function S(t){var e=Object.create(null);return function(n){var r=e[n];return r||(e[n]=t(n))}}var k=/-(\w)/g,E=S((function(t){return t.replace(k,(function(t,e){return e?e.toUpperCase():""}))}));function T(t){var e={};return w(t)&&Object.keys(t).sort().forEach((function(n){e[n]=t[n]})),Object.keys(e)?e:t}var C=["invoke","success","fail","complete","returnValue"],P={},$={};function L(t,e){Object.keys(e).forEach((function(n){-1!==C.indexOf(n)&&b(e[n])&&(t[n]=function(t,e){var n=e?t?t.concat(e):Array.isArray(e)?e:[e]:t;return n?function(t){for(var e=[],n=0;n<t.length;n++)-1===e.indexOf(t[n])&&e.push(t[n]);return e}(n):n}(t[n],e[n]))}))}function j(t,e){t&&e&&Object.keys(e).forEach((function(n){-1!==C.indexOf(n)&&b(e[n])&&function(t,e){var n=t.indexOf(e);-1!==n&&t.splice(n,1)}(t[n],e[n])}))}function I(t,e){return function(n){return t(n,e)||n}}function M(t){return!!t&&("object"===(0,l.default)(t)||"function"===typeof t)&&"function"===typeof t.then}function D(t,e,n){for(var r=!1,i=0;i<t.length;i++){var o=t[i];if(r)r=Promise.resolve(I(o,n));else{var a=o(e,n);if(M(a)&&(r=Promise.resolve(a)),!1===a)return{then:function(){}}}}return r||{then:function(t){return t(e)}}}function N(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return["success","fail","complete"].forEach((function(n){if(Array.isArray(t[n])){var r=e[n];e[n]=function(i){D(t[n],i,e).then((function(t){return b(r)&&r(t)||t}))}}})),e}function R(t,e){var n=[];Array.isArray(P.returnValue)&&n.push.apply(n,(0,c.default)(P.returnValue));var r=$[t];return r&&Array.isArray(r.returnValue)&&n.push.apply(n,(0,c.default)(r.returnValue)),n.forEach((function(t){e=t(e)||e})),e}function U(t){var e=Object.create(null);Object.keys(P).forEach((function(t){"returnValue"!==t&&(e[t]=P[t].slice())}));var n=$[t];return n&&Object.keys(n).forEach((function(t){"returnValue"!==t&&(e[t]=(e[t]||[]).concat(n[t]))})),e}function H(t,e,n){for(var r=arguments.length,i=new Array(r>3?r-3:0),o=3;o<r;o++)i[o-3]=arguments[o];var a=U(t);if(a&&Object.keys(a).length){if(Array.isArray(a.invoke)){var s=D(a.invoke,n);return s.then((function(n){return e.apply(void 0,[N(U(t),n)].concat(i))}))}return e.apply(void 0,[N(a,n)].concat(i))}return e.apply(void 0,[n].concat(i))}var B={returnValue:function(t){return M(t)?new Promise((function(e,n){t.then((function(t){t?t[0]?n(t[0]):e(t[1]):e(t)}))})):t}},F=/^\$|__f__|Window$|WindowStyle$|sendHostEvent|sendNativeEvent|restoreGlobal|requireGlobal|getCurrentSubNVue|getMenuButtonBoundingClientRect|^report|interceptors|Interceptor$|getSubNVueById|requireNativePlugin|rpx2px|upx2px|hideKeyboard|canIUse|^create|Sync$|Manager$|base64ToArrayBuffer|arrayBufferToBase64|getLocale|setLocale|invokePushCallback|getWindowInfo|getDeviceInfo|getAppBaseInfo|getSystemSetting|getAppAuthorizeSetting|initUTS|requireUTS|registerUTS/,V=/^create|Manager$/,z=["createBLEConnection"],G=["createBLEConnection","createPushMessage"],K=/^on|^off/;function W(t){return V.test(t)&&-1===z.indexOf(t)}function X(t){return F.test(t)&&-1===G.indexOf(t)}function J(t){return t.then((function(t){return[null,t]})).catch((function(t){return[t]}))}function Y(t){return!(W(t)||X(t)||function(t){return K.test(t)&&"onPush"!==t}(t))}function q(t,e){return Y(t)&&b(e)?function(){for(var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length,i=new Array(r>1?r-1:0),o=1;o<r;o++)i[o-1]=arguments[o];return b(n.success)||b(n.fail)||b(n.complete)?R(t,H.apply(void 0,[t,e,Object.assign({},n)].concat(i))):R(t,J(new Promise((function(r,o){H.apply(void 0,[t,e,Object.assign({},n,{success:r,fail:o})].concat(i))}))))}:e}Promise.prototype.finally||(Promise.prototype.finally=function(t){var e=this.constructor;return this.then((function(n){return e.resolve(t()).then((function(){return n}))}),(function(n){return e.resolve(t()).then((function(){throw n}))}))});var Z=!1,Q=0,tt=0;function et(e,n){if(0===Q&&function(){var e,n,r,i="function"===typeof t.getWindowInfo&&t.getWindowInfo()?t.getWindowInfo():t.getSystemInfoSync(),o="function"===typeof t.getDeviceInfo&&t.getDeviceInfo()?t.getDeviceInfo():t.getSystemInfoSync();e=i.windowWidth,n=i.pixelRatio,r=o.platform,Q=e,tt=n,Z="ios"===r}(),e=Number(e),0===e)return 0;var r=e/750*(n||Q);return r<0&&(r=-r),r=Math.floor(r+1e-4),0===r&&(r=1!==tt&&Z?.5:1),e<0?-r:r}var nt,rt={};function it(){var e,n="function"===typeof t.getAppBaseInfo&&t.getAppBaseInfo()?t.getAppBaseInfo():t.getSystemInfoSync(),r=n&&n.language?n.language:"en";return e=st(r)||"en",e}nt=it(),function(){if(function(){return"undefined"!==typeof __uniConfig&&__uniConfig.locales&&!!Object.keys(__uniConfig.locales).length}()){var t=Object.keys(__uniConfig.locales);t.length&&t.forEach((function(t){var e=rt[t],n=__uniConfig.locales[t];e?Object.assign(e,n):rt[t]=n}))}}();var ot=(0,f.initVueI18n)(nt,{}),at=ot.t;ot.mixin={beforeCreate:function(){var t=this,e=ot.i18n.watchLocale((function(){t.$forceUpdate()}));this.$once("hook:beforeDestroy",(function(){e()}))},methods:{$$t:function(t,e){return at(t,e)}}},ot.setLocale,ot.getLocale;function st(t,e){if(t){if(t=t.trim().replace(/_/g,"-"),e&&e[t])return t;if(t=t.toLowerCase(),"chinese"===t)return"zh-Hans";if(0===t.indexOf("zh"))return t.indexOf("-hans")>-1?"zh-Hans":t.indexOf("-hant")>-1||function(t,e){return!!e.find((function(e){return-1!==t.indexOf(e)}))}(t,["-tw","-hk","-mo","-cht"])?"zh-Hant":"zh-Hans";var n=function(t,e){return e.find((function(e){return 0===t.indexOf(e)}))}(t,["en","fr","es"]);return n||void 0}}function ut(){if(b(getApp)){var t=getApp({allowDefault:!0});if(t&&t.$vm)return t.$vm.$locale}return it()}var ct=[];"undefined"!==typeof r&&(r.getLocale=ut);var lt={promiseInterceptor:B},ft=Object.freeze({__proto__:null,upx2px:et,rpx2px:et,getLocale:ut,setLocale:function(t){var e=!!b(getApp)&&getApp();if(!e)return!1;var n=e.$vm.$locale;return n!==t&&(e.$vm.$locale=t,ct.forEach((function(e){return e({locale:t})})),!0)},onLocaleChange:function(t){-1===ct.indexOf(t)&&ct.push(t)},addInterceptor:function(t,e){"string"===typeof t&&w(e)?L($[t]||($[t]={}),e):w(t)&&L(P,t)},removeInterceptor:function(t,e){"string"===typeof t?w(e)?j($[t],e):delete $[t]:w(t)&&j(P,t)},interceptors:lt});var dt,ht={name:function(t){return"back"===t.exists&&t.delta?"navigateBack":"redirectTo"},args:function(t){if("back"===t.exists&&t.url){var e=function(t){var e=getCurrentPages(),n=e.length;while(n--){var r=e[n];if(r.$page&&r.$page.fullPath===t)return n}return-1}(t.url);if(-1!==e){var n=getCurrentPages().length-1-e;n>0&&(t.delta=n)}}}},pt={args:function(t){var e=parseInt(t.current);if(!isNaN(e)){var n=t.urls;if(Array.isArray(n)){var r=n.length;if(r)return e<0?e=0:e>=r&&(e=r-1),e>0?(t.current=n[e],t.urls=n.filter((function(t,r){return!(r<e)||t!==n[e]}))):t.current=n[0],{indicator:!1,loop:!1}}}}};function vt(e){dt=dt||t.getStorageSync("__DC_STAT_UUID"),dt||(dt=Date.now()+""+Math.floor(1e7*Math.random()),t.setStorage({key:"__DC_STAT_UUID",data:dt})),e.deviceId=dt}function gt(t){if(t.safeArea){var e=t.safeArea;t.safeAreaInsets={top:e.top,left:e.left,right:t.windowWidth-e.right,bottom:t.screenHeight-e.bottom}}}function yt(t,e){var n="",r="";switch(n=t.split(" ")[0]||e,r=t.split(" ")[1]||"",n=n.toLocaleLowerCase(),n){case"harmony":case"ohos":case"openharmony":n="harmonyos";break;case"iphone os":n="ios";break;case"mac":case"darwin":n="macos";break;case"windows_nt":n="windows";break}return{osName:n,osVersion:r}}function mt(t,e){for(var n=t.deviceType||"phone",r={ipad:"pad",windows:"pc",mac:"pc"},i=Object.keys(r),o=e.toLocaleLowerCase(),a=0;a<i.length;a++){var s=i[a];if(-1!==o.indexOf(s)){n=r[s];break}}return n}function _t(t){var e=t;return e&&(e=t.toLocaleLowerCase()),e}function bt(t){return ut?ut():t}function At(t){var e=t.hostName||"WeChat";return t.environment?e=t.environment:t.host&&t.host.env&&(e=t.host.env),e}var wt={returnValue:function(t){vt(t),gt(t),function(t){var e=t.brand,n=void 0===e?"":e,r=t.model,i=void 0===r?"":r,o=t.system,a=void 0===o?"":o,s=t.language,u=void 0===s?"":s,c=t.theme,l=t.version,f=t.platform,d=t.fontSizeSetting,h=t.SDKVersion,p=t.pixelRatio,v=t.deviceOrientation,g=yt(a,f),y=g.osName,m=g.osVersion,_=l,b=mt(t,i),A=_t(n),w=At(t),O=v,x=p,S=h,k=(u||"").replace(/_/g,"-"),E={appId:"__UNI__9200679",appName:"黄记团圆月",appVersion:"2.1",appVersionCode:"2",appLanguage:bt(k),uniCompileVersion:"4.75",uniCompilerVersion:"4.75",uniRuntimeVersion:"4.75",uniPlatform:"mp-weixin",deviceBrand:A,deviceModel:i,deviceType:b,devicePixelRatio:x,deviceOrientation:O,osName:y.toLocaleLowerCase(),osVersion:m,hostTheme:c,hostVersion:_,hostLanguage:k,hostName:w,hostSDKVersion:S,hostFontSizeSetting:d,windowTop:0,windowBottom:0,osLanguage:void 0,osTheme:void 0,ua:void 0,hostPackageName:void 0,browserName:void 0,browserVersion:void 0,isUniAppX:!1};Object.assign(t,E,{})}(t)}},Ot={args:function(t){"object"===(0,l.default)(t)&&(t.alertText=t.title)}},xt={returnValue:function(t){var e=t,n=e.version,r=e.language,i=e.SDKVersion,o=e.theme,a=At(t),s=(r||"").replace("_","-");t=T(Object.assign(t,{appId:"__UNI__9200679",appName:"黄记团圆月",appVersion:"2.1",appVersionCode:"2",appLanguage:bt(s),hostVersion:n,hostLanguage:s,hostName:a,hostSDKVersion:i,hostTheme:o,isUniAppX:!1,uniPlatform:"mp-weixin",uniCompileVersion:"4.75",uniCompilerVersion:"4.75",uniRuntimeVersion:"4.75"}))}},St={returnValue:function(t){var e=t,n=e.brand,r=e.model,i=e.system,o=void 0===i?"":i,a=e.platform,s=void 0===a?"":a,u=mt(t,r),c=_t(n);vt(t);var l=yt(o,s),f=l.osName,d=l.osVersion;t=T(Object.assign(t,{deviceType:u,deviceBrand:c,deviceModel:r,osName:f,osVersion:d}))}},kt={returnValue:function(t){gt(t),t=T(Object.assign(t,{windowTop:0,windowBottom:0}))}},Et={redirectTo:ht,previewImage:pt,getSystemInfo:wt,getSystemInfoSync:wt,showActionSheet:Ot,getAppBaseInfo:xt,getDeviceInfo:St,getWindowInfo:kt,getAppAuthorizeSetting:{returnValue:function(t){var e=t.locationReducedAccuracy;t.locationAccuracy="unsupported",!0===e?t.locationAccuracy="reduced":!1===e&&(t.locationAccuracy="full")}},compressImage:{args:function(t){t.compressedHeight&&!t.compressHeight&&(t.compressHeight=t.compressedHeight),t.compressedWidth&&!t.compressWidth&&(t.compressWidth=t.compressedWidth)}}},Tt=["success","fail","cancel","complete"];function Ct(t,e,n){return function(r){return e($t(t,r,n))}}function Pt(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(w(e)){var o=!0===i?e:{};for(var a in b(n)&&(n=n(e,o)||{}),e)if(O(n,a)){var s=n[a];b(s)&&(s=s(e[a],e,o)),s?A(s)?o[s]=e[a]:w(s)&&(o[s.name?s.name:a]=s.value):console.warn("The '".concat(t,"' method of platform '微信小程序' does not support option '").concat(a,"'"))}else-1!==Tt.indexOf(a)?b(e[a])&&(o[a]=Ct(t,e[a],r)):i||(o[a]=e[a]);return o}return b(e)&&(e=Ct(t,e,r)),e}function $t(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return b(Et.returnValue)&&(e=Et.returnValue(t,e)),Pt(t,e,n,{},r)}function Lt(e,n){if(O(Et,e)){var r=Et[e];return r?function(n,i){var o=r;b(r)&&(o=r(n)),n=Pt(e,n,o.args,o.returnValue);var a=[n];"undefined"!==typeof i&&a.push(i),b(o.name)?e=o.name(n):A(o.name)&&(e=o.name);var s=t[e].apply(t,a);return X(e)?$t(e,s,o.returnValue,W(e)):s}:function(){console.error("Platform '微信小程序' does not support '".concat(e,"'."))}}return n}var jt=Object.create(null);["onTabBarMidButtonTap","subscribePush","unsubscribePush","onPush","offPush","share"].forEach((function(t){jt[t]=function(t){return function(e){var n=e.fail,r=e.complete,i={errMsg:"".concat(t,":fail method '").concat(t,"' not supported")};b(n)&&n(i),b(r)&&r(i)}}(t)}));var It={oauth:["weixin"],share:["weixin"],payment:["wxpay"],push:["weixin"]};var Mt=Object.freeze({__proto__:null,getProvider:function(t){var e=t.service,n=t.success,r=t.fail,i=t.complete,o=!1;It[e]?(o={errMsg:"getProvider:ok",service:e,provider:It[e]},b(n)&&n(o)):(o={errMsg:"getProvider:fail service not found"},b(r)&&r(o)),b(i)&&i(o)}}),Dt=function(){var t;return function(){return t||(t=new d.default),t}}();function Nt(t,e,n){return t[e].apply(t,n)}var Rt,Ut,Ht,Bt=Object.freeze({__proto__:null,$on:function(){return Nt(Dt(),"$on",Array.prototype.slice.call(arguments))},$off:function(){return Nt(Dt(),"$off",Array.prototype.slice.call(arguments))},$once:function(){return Nt(Dt(),"$once",Array.prototype.slice.call(arguments))},$emit:function(){return Nt(Dt(),"$emit",Array.prototype.slice.call(arguments))}});function Ft(t){return function(){try{return t.apply(t,arguments)}catch(e){console.error(e)}}}function Vt(t){try{return JSON.parse(t)}catch(e){}return t}var zt=[];function Gt(t,e){zt.forEach((function(n){n(t,e)})),zt.length=0}var Kt=[];var Wt=t.getAppBaseInfo&&t.getAppBaseInfo();Wt||(Wt=t.getSystemInfoSync());var Xt=Wt?Wt.host:null,Jt=Xt&&"SAAASDK"===Xt.env?t.miniapp.shareVideoMessage:t.shareVideoMessage,Yt=Object.freeze({__proto__:null,shareVideoMessage:Jt,getPushClientId:function(t){w(t)||(t={});var e=function(t){var e={};for(var n in t){var r=t[n];b(r)&&(e[n]=Ft(r),delete t[n])}return e}(t),n=e.success,r=e.fail,i=e.complete,o=b(n),a=b(r),s=b(i);Promise.resolve().then((function(){"undefined"===typeof Ht&&(Ht=!1,Rt="",Ut="uniPush is not enabled"),zt.push((function(t,e){var u;t?(u={errMsg:"getPushClientId:ok",cid:t},o&&n(u)):(u={errMsg:"getPushClientId:fail"+(e?" "+e:"")},a&&r(u)),s&&i(u)})),"undefined"!==typeof Rt&&Gt(Rt,Ut)}))},onPushMessage:function(t){-1===Kt.indexOf(t)&&Kt.push(t)},offPushMessage:function(t){if(t){var e=Kt.indexOf(t);e>-1&&Kt.splice(e,1)}else Kt.length=0},invokePushCallback:function(t){if("enabled"===t.type)Ht=!0;else if("clientId"===t.type)Rt=t.cid,Ut=t.errMsg,Gt(Rt,t.errMsg);else if("pushMsg"===t.type)for(var e={type:"receive",data:Vt(t.message)},n=0;n<Kt.length;n++){var r=Kt[n];if(r(e),e.stopped)break}else"click"===t.type&&Kt.forEach((function(e){e({type:"click",data:Vt(t.message)})}))},__f__:function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];console[t].apply(console,n)}}),qt=["__route__","__wxExparserNodeId__","__wxWebviewId__"];function Zt(t){return Behavior(t)}function Qt(){return!!this.route}function te(t){this.triggerEvent("__l",t)}function ee(t){var e=t.$scope,n={};Object.defineProperty(t,"$refs",{get:function(){var t={};(function t(e,n,r){var i=e.selectAllComponents(n)||[];i.forEach((function(e){var i=e.dataset.ref;r[i]=e.$vm||ie(e),"scoped"===e.dataset.vueGeneric&&e.selectAllComponents(".scoped-ref").forEach((function(e){t(e,n,r)}))}))})(e,".vue-ref",t);var r=e.selectAllComponents(".vue-ref-in-for")||[];return r.forEach((function(e){var n=e.dataset.ref;t[n]||(t[n]=[]),t[n].push(e.$vm||ie(e))})),function(t,e){var n=(0,u.default)(Set,(0,c.default)(Object.keys(t))),r=Object.keys(e);return r.forEach((function(r){var i=t[r],o=e[r];Array.isArray(i)&&Array.isArray(o)&&i.length===o.length&&o.every((function(t){return i.includes(t)}))||(t[r]=o,n.delete(r))})),n.forEach((function(e){delete t[e]})),t}(n,t)}})}function ne(t){var e,n=t.detail||t.value,r=n.vuePid,i=n.vueOptions;r&&(e=function t(e,n){for(var r,i=e.$children,o=i.length-1;o>=0;o--){var a=i[o];if(a.$scope._$vueId===n)return a}for(var s=i.length-1;s>=0;s--)if(r=t(i[s],n),r)return r}(this.$vm,r)),e||(e=this.$vm),i.parent=e}function re(t){return Object.defineProperty(t,"__v_isMPComponent",{configurable:!0,enumerable:!1,value:!0}),t}function ie(t){return function(t){return null!==t&&"object"===(0,l.default)(t)}(t)&&Object.isExtensible(t)&&Object.defineProperty(t,"__ob__",{configurable:!0,enumerable:!1,value:(0,s.default)({},"__v_skip",!0)}),t}var oe=/_(.*)_worklet_factory_/;var ae=Page,se=Component,ue=/:/g,ce=S((function(t){return E(t.replace(ue,"-"))}));function le(t){var e=t.triggerEvent,n=function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];if(this.$vm||this.dataset&&this.dataset.comType)t=ce(t);else{var o=ce(t);o!==t&&e.apply(this,[o].concat(r))}return e.apply(this,[t].concat(r))};try{t.triggerEvent=n}catch(r){t._triggerEvent=n}}function fe(t,e,n){var r=e[t];e[t]=function(){if(re(this),le(this),r){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return r.apply(this,e)}}}ae.__$wrappered||(ae.__$wrappered=!0,Page=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return fe("onLoad",t),ae(t)},Page.after=ae.after,Component=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return fe("created",t),se(t)});function de(t,e,n){e.forEach((function(e){(function t(e,n){if(!n)return!0;if(d.default.options&&Array.isArray(d.default.options[e]))return!0;if(n=n.default||n,b(n))return!!b(n.extendOptions[e])||!!(n.super&&n.super.options&&Array.isArray(n.super.options[e]));if(b(n[e])||Array.isArray(n[e]))return!0;var r=n.mixins;return Array.isArray(r)?!!r.find((function(n){return t(e,n)})):void 0})(e,n)&&(t[e]=function(t){return this.$vm&&this.$vm.__call_hook(e,t)})}))}function he(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];pe(e).forEach((function(e){return ve(t,e,n)}))}function pe(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return t&&Object.keys(t).forEach((function(n){0===n.indexOf("on")&&b(t[n])&&e.push(n)})),e}function ve(t,e,n){-1!==n.indexOf(e)||O(t,e)||(t[e]=function(t){return this.$vm&&this.$vm.__call_hook(e,t)})}function ge(t,e){var n;return e=e.default||e,n=b(e)?e:t.extend(e),e=n.options,[n,e]}function ye(t,e){if(Array.isArray(e)&&e.length){var n=Object.create(null);e.forEach((function(t){n[t]=!0})),t.$scopedSlots=t.$slots=n}}function me(t,e){t=(t||"").split(",");var n=t.length;1===n?e._$vueId=t[0]:2===n&&(e._$vueId=t[0],e._$vuePid=t[1])}function _e(t,e){var n=t.data||{},r=t.methods||{};if("function"===typeof n)try{n=n.call(e)}catch(i){Object({NODE_ENV:"production",VUE_APP_DARK_MODE:"false",VUE_APP_NAME:"黄记团圆月",VUE_APP_PLATFORM:"mp-weixin",BASE_URL:"/"}).VUE_APP_DEBUG&&console.warn("根据 Vue 的 data 函数初始化小程序 data 失败，请尽量确保 data 函数中不访问 vm 对象，否则可能影响首次数据渲染速度。",n)}else try{n=JSON.parse(JSON.stringify(n))}catch(i){}return w(n)||(n={}),Object.keys(r).forEach((function(t){-1!==e.__lifecycle_hooks__.indexOf(t)||O(n,t)||(n[t]=r[t])})),n}var be=[String,Number,Boolean,Object,Array,null];function Ae(t){return function(e,n){this.$vm&&(this.$vm[t]=e)}}function we(t,e){var n=t.behaviors,r=t.extends,i=t.mixins,o=t.props;o||(t.props=o=[]);var a=[];return Array.isArray(n)&&n.forEach((function(t){a.push(t.replace("uni://","wx".concat("://"))),"uni://form-field"===t&&(Array.isArray(o)?(o.push("name"),o.push("value")):(o.name={type:String,default:""},o.value={type:[String,Number,Boolean,Array,Object,Date],default:""}))})),w(r)&&r.props&&a.push(e({properties:xe(r.props,!0)})),Array.isArray(i)&&i.forEach((function(t){w(t)&&t.props&&a.push(e({properties:xe(t.props,!0)}))})),a}function Oe(t,e,n,r){return Array.isArray(e)&&1===e.length?e[0]:e}function xe(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>3?arguments[3]:void 0,r={};return e||(r.vueId={type:String,value:""},n.virtualHost&&(r.virtualHostStyle={type:null,value:""},r.virtualHostClass={type:null,value:""}),r.scopedSlotsCompiler={type:String,value:""},r.vueSlots={type:null,value:[],observer:function(t,e){var n=Object.create(null);t.forEach((function(t){n[t]=!0})),this.setData({$slots:n})}}),Array.isArray(t)?t.forEach((function(t){r[t]={type:null,observer:Ae(t)}})):w(t)&&Object.keys(t).forEach((function(e){var n=t[e];if(w(n)){var i=n.default;b(i)&&(i=i()),n.type=Oe(0,n.type),r[e]={type:-1!==be.indexOf(n.type)?n.type:null,value:i,observer:Ae(e)}}else{var o=Oe(0,n);r[e]={type:-1!==be.indexOf(o)?o:null,observer:Ae(e)}}})),r}function Se(t,e,n,r){var i={};return Array.isArray(e)&&e.length&&e.forEach((function(e,o){"string"===typeof e?e?"$event"===e?i["$"+o]=n:"arguments"===e?i["$"+o]=n.detail&&n.detail.__args__||r:0===e.indexOf("$event.")?i["$"+o]=t.__get_value(e.replace("$event.",""),n):i["$"+o]=t.__get_value(e):i["$"+o]=t:i["$"+o]=function(t,e){var n=t;return e.forEach((function(e){var r=e[0],i=e[2];if(r||"undefined"!==typeof i){var o,a=e[1],s=e[3];Number.isInteger(r)?o=r:r?"string"===typeof r&&r&&(o=0===r.indexOf("#s#")?r.substr(3):t.__get_value(r,n)):o=n,Number.isInteger(o)?n=i:a?Array.isArray(o)?n=o.find((function(e){return t.__get_value(a,e)===i})):w(o)?n=Object.keys(o).find((function(e){return t.__get_value(a,o[e])===i})):console.error("v-for 暂不支持循环数据：",o):n=o[i],s&&(n=t.__get_value(s,n))}})),n}(t,e)})),i}function ke(t){for(var e={},n=1;n<t.length;n++){var r=t[n];e[r[0]]=r[1]}return e}function Ee(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[],i=arguments.length>4?arguments[4]:void 0,o=arguments.length>5?arguments[5]:void 0,a=!1,s=w(e.detail)&&e.detail.__args__||[e.detail];if(i&&(a=e.currentTarget&&e.currentTarget.dataset&&"wx"===e.currentTarget.dataset.comType,!n.length))return a?[e]:s;var u=Se(t,r,e,s),c=[];return n.forEach((function(t){"$event"===t?"__set_model"!==o||i?i&&!a?c.push(s[0]):c.push(e):c.push(e.target.value):Array.isArray(t)&&"o"===t[0]?c.push(ke(t)):"string"===typeof t&&O(u,t)?c.push(u[t]):c.push(t)})),c}function Te(t){var e=this;t=function(t){try{t.mp=JSON.parse(JSON.stringify(t))}catch(e){}return t.stopPropagation=x,t.preventDefault=x,t.target=t.target||{},O(t,"detail")||(t.detail={}),O(t,"markerId")&&(t.detail="object"===(0,l.default)(t.detail)?t.detail:{},t.detail.markerId=t.markerId),w(t.detail)&&(t.target=Object.assign({},t.target,t.detail)),t}(t);var n=(t.currentTarget||t.target).dataset;if(!n)return console.warn("事件信息不存在");var r=n.eventOpts||n["event-opts"];if(!r)return console.warn("事件信息不存在");var i=t.type,o=[];return r.forEach((function(n){var r=n[0],a=n[1],s="^"===r.charAt(0);r=s?r.slice(1):r;var u="~"===r.charAt(0);r=u?r.slice(1):r,a&&function(t,e){return t===e||"regionchange"===e&&("begin"===t||"end"===t)}(i,r)&&a.forEach((function(n){var r=n[0];if(r){var i=e.$vm;if(i.$options.generic&&(i=function(t){var e=t.$parent;while(e&&e.$parent&&(e.$options.generic||e.$parent.$options.generic||e.$scope._$vuePid))e=e.$parent;return e&&e.$parent}(i)||i),"$emit"===r)return void i.$emit.apply(i,Ee(e.$vm,t,n[1],n[2],s,r));var a=i[r];if(!b(a)){var c="page"===e.$vm.mpType?"Page":"Component",l=e.route||e.is;throw new Error("".concat(c,' "').concat(l,'" does not have a method "').concat(r,'"'))}if(u){if(a.once)return;a.once=!0}var f=Ee(e.$vm,t,n[1],n[2],s,r);f=Array.isArray(f)?f:[],/=\s*\S+\.eventParams\s*\|\|\s*\S+\[['"]event-params['"]\]/.test(a.toString())&&(f=f.concat([,,,,,,,,,,t])),o.push(a.apply(i,f))}}))})),"input"===i&&1===o.length&&"undefined"!==typeof o[0]?o[0]:void 0}var Ce={};var Pe=["onShow","onHide","onError","onPageNotFound","onThemeChange","onUnhandledRejection"];function $e(){d.default.prototype.getOpenerEventChannel=function(){return this.$scope.getOpenerEventChannel()};var t=d.default.prototype.__call_hook;d.default.prototype.__call_hook=function(e,n){return"onLoad"===e&&n&&n.__id__&&(this.__eventChannel__=function(t){var e=Ce[t];return delete Ce[t],e}(n.__id__),delete n.__id__),t.call(this,e,n)}}function Le(e,n){var r=n.mocks,i=n.initRefs;$e(),function(){var t={},e={};function n(t){var e=this.$options.propsData.vueId;if(e){var n=e.split(",")[0];t(n)}}d.default.prototype.$hasSSP=function(n){var r=t[n];return r||(e[n]=this,this.$on("hook:destroyed",(function(){delete e[n]}))),r},d.default.prototype.$getSSP=function(e,n,r){var i=t[e];if(i){var o=i[n]||[];return r?o:o[0]}},d.default.prototype.$setSSP=function(e,r){var i=0;return n.call(this,(function(n){var o=t[n],a=o[e]=o[e]||[];a.push(r),i=a.length-1})),i},d.default.prototype.$initSSP=function(){n.call(this,(function(e){t[e]={}}))},d.default.prototype.$callSSP=function(){n.call(this,(function(t){e[t]&&e[t].$forceUpdate()}))},d.default.mixin({destroyed:function(){var n=this.$options.propsData,r=n&&n.vueId;r&&(delete t[r],delete e[r])}})}(),e.$options.store&&(d.default.prototype.$store=e.$options.store),function(t){t.prototype.uniIDHasRole=function(t){var e=y(),n=e.role;return n.indexOf(t)>-1},t.prototype.uniIDHasPermission=function(t){var e=y(),n=e.permission;return this.uniIDHasRole("admin")||n.indexOf(t)>-1},t.prototype.uniIDTokenValid=function(){var t=y(),e=t.tokenExpired;return e>Date.now()}}(d.default),d.default.prototype.mpHost="mp-weixin",d.default.mixin({beforeCreate:function(){if(this.$options.mpType){if(this.mpType=this.$options.mpType,this.$mp=(0,s.default)({data:{}},this.mpType,this.$options.mpInstance),this.$scope=this.$options.mpInstance,delete this.$options.mpType,delete this.$options.mpInstance,"page"===this.mpType&&"function"===typeof getApp){var t=getApp();t.$vm&&t.$vm.$i18n&&(this._i18n=t.$vm.$i18n)}"app"!==this.mpType&&(i(this),function(t,e){var n=t.$mp[t.mpType];e.forEach((function(e){O(n,e)&&(t[e]=n[e])}))}(this,r))}}});var o={onLaunch:function(n){this.$vm||(t.canIUse&&!t.canIUse("nextTick")&&console.error("当前微信基础库版本过低，请将 微信开发者工具-详情-项目设置-调试基础库版本 更换为`2.3.0`以上"),this.$vm=e,this.$vm.$mp={app:this},this.$vm.$scope=this,this.$vm.globalData=this.globalData,this.$vm._isMounted=!0,this.$vm.__call_hook("mounted",n),this.$vm.__call_hook("onLaunch",n))}};o.globalData=e.$options.globalData||{};var a=e.$options.methods;return a&&Object.keys(a).forEach((function(t){o[t]=a[t]})),function(t,e,n){var r=t.observable({locale:n||ot.getLocale()}),i=[];e.$watchLocale=function(t){i.push(t)},Object.defineProperty(e,"$locale",{get:function(){return r.locale},set:function(t){r.locale=t,i.forEach((function(e){return e(t)}))}})}(d.default,e,function(){var e,n=t.getAppBaseInfo(),r=n&&n.language?n.language:"en";return e=st(r)||"en",e}()),de(o,Pe),he(o,e.$options),o}function je(t){return Le(t,{mocks:qt,initRefs:ee})}function Ie(t){return App(je(t)),t}var Me=/[!'()*]/g,De=function(t){return"%"+t.charCodeAt(0).toString(16)},Ne=/%2C/g,Re=function(t){return encodeURIComponent(t).replace(Me,De).replace(Ne,",")};function Ue(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Re,n=t?Object.keys(t).map((function(n){var r=t[n];if(void 0===r)return"";if(null===r)return e(n);if(Array.isArray(r)){var i=[];return r.forEach((function(t){void 0!==t&&(null===t?i.push(e(n)):i.push(e(n)+"="+e(t)))})),i.join("&")}return e(n)+"="+e(r)})).filter((function(t){return t.length>0})).join("&"):null;return n?"?".concat(n):""}function He(t,e){return function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.isPage,r=e.initRelation,i=arguments.length>2?arguments[2]:void 0,o=ge(d.default,t),s=(0,a.default)(o,2),u=s[0],c=s[1],l=p({multipleSlots:!0,addGlobalClass:!0},c.options||{});c["mp-weixin"]&&c["mp-weixin"].options&&Object.assign(l,c["mp-weixin"].options);var f={options:l,data:_e(c,d.default.prototype),behaviors:we(c,Zt),properties:xe(c.props,!1,c.__file,l),lifetimes:{attached:function(){var t=this.properties,e={mpType:n.call(this)?"page":"component",mpInstance:this,propsData:t};me(t.vueId,this),r.call(this,{vuePid:this._$vuePid,vueOptions:e}),this.$vm=new u(e),ye(this.$vm,t.vueSlots),this.$vm.$mount()},ready:function(){this.$vm&&(this.$vm._isMounted=!0,this.$vm.__call_hook("mounted"),this.$vm.__call_hook("onReady"))},detached:function(){this.$vm&&this.$vm.$destroy()}},pageLifetimes:{show:function(t){this.$vm&&this.$vm.__call_hook("onPageShow",t)},hide:function(){this.$vm&&this.$vm.__call_hook("onPageHide")},resize:function(t){this.$vm&&this.$vm.__call_hook("onPageResize",t)}},methods:{__l:ne,__e:Te}};return c.externalClasses&&(f.externalClasses=c.externalClasses),Array.isArray(c.wxsCallMethods)&&c.wxsCallMethods.forEach((function(t){f.methods[t]=function(e){return this.$vm[t](e)}})),i?[f,c,u]:n?f:[f,u]}(t,{isPage:Qt,initRelation:te},e)}var Be=["onShow","onHide","onUnload"];function Fe(t){var e=He(t,!0),n=(0,a.default)(e,2),r=n[0],i=n[1];return de(r.methods,Be,i),r.methods.onLoad=function(t){this.options=t;var e=Object.assign({},t);delete e.__id__,this.$page={fullPath:"/"+(this.route||this.is)+Ue(e)},this.$vm.$mp.query=t,this.$vm.__call_hook("onLoad",t)},he(r.methods,t,["onReady"]),function(t,e){e&&Object.keys(e).forEach((function(n){var r=n.match(oe);if(r){var i=r[1];t[n]=e[n],t[i]=e[i]}}))}(r.methods,i.methods),r}function Ve(t){return Component(function(t){return Fe(t)}(t))}function ze(t){return Component(He(t))}function Ge(e){var n=je(e),r=getApp({allowDefault:!0});e.$scope=r;var i=r.globalData;if(i&&Object.keys(n.globalData).forEach((function(t){O(i,t)||(i[t]=n.globalData[t])})),Object.keys(n).forEach((function(t){O(r,t)||(r[t]=n[t])})),b(n.onShow)&&t.onAppShow&&t.onAppShow((function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];e.__call_hook("onShow",n)})),b(n.onHide)&&t.onAppHide&&t.onAppHide((function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];e.__call_hook("onHide",n)})),b(n.onLaunch)){var o=t.getLaunchOptionsSync&&t.getLaunchOptionsSync();e.__call_hook("onLaunch",o)}return e}function Ke(e){var n=je(e);if(b(n.onShow)&&t.onAppShow&&t.onAppShow((function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];e.__call_hook("onShow",n)})),b(n.onHide)&&t.onAppHide&&t.onAppHide((function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];e.__call_hook("onHide",n)})),b(n.onLaunch)){var r=t.getLaunchOptionsSync&&t.getLaunchOptionsSync();e.__call_hook("onLaunch",r)}return e}Be.push.apply(Be,["onPullDownRefresh","onReachBottom","onAddToFavorites","onShareTimeline","onShareAppMessage","onPageScroll","onResize","onTabItemTap"]),["vibrate","preloadPage","unPreloadPage","loadSubPackage"].forEach((function(t){Et[t]=!1})),[].forEach((function(e){var n=Et[e]&&Et[e].name?Et[e].name:e;t.canIUse(n)||(Et[e]=!1)}));var We={};"undefined"!==typeof Proxy?We=new Proxy({},{get:function(e,n){return O(e,n)?e[n]:ft[n]?ft[n]:Yt[n]?q(n,Yt[n]):Mt[n]?q(n,Mt[n]):jt[n]?q(n,jt[n]):Bt[n]?Bt[n]:q(n,Lt(n,t[n]))},set:function(t,e,n){return t[e]=n,!0}}):(Object.keys(ft).forEach((function(t){We[t]=ft[t]})),Object.keys(jt).forEach((function(t){We[t]=q(t,jt[t])})),Object.keys(Mt).forEach((function(t){We[t]=q(t,Mt[t])})),Object.keys(Bt).forEach((function(t){We[t]=Bt[t]})),Object.keys(Yt).forEach((function(t){We[t]=q(t,Yt[t])})),Object.keys(t).forEach((function(e){(O(t,e)||O(Et,e))&&(We[e]=q(e,Lt(e,t[e])))}))),t.createApp=Ie,t.createPage=Ve,t.createComponent=ze,t.createSubpackageApp=Ge,t.createPlugin=Ke;var Xe=We,Je=Xe;e.default=Je}).call(this,n("3223")["default"],n("0ee4"))},e32c:function(t,e,n){"use strict";(function(t,n){Object.defineProperty(e,"__esModule",{value:!0}),e.openBargainSubscribe=function(){var e=t.getStorageSync("tempID"+r[2]);return i(e)},e.openOrderSubscribe=function(){var e=t.getStorageSync("tempID"+r[1]);return i(e)},e.openPaySubscribe=function(){var e=t.getStorageSync("tempID"+r[0]);return i(e)},e.openPinkSubscribe=function(){var e=t.getStorageSync("tempID"+r[3]);return i(e)},e.subscribe=i;var r=["beforePay","afterPay","createBargain","pink"];function i(t){var e=n;return new Promise((function(n,r){e.requestSubscribeMessage({tmplIds:t,success:function(t){return n(t)},fail:function(t){return n(t)}})}))}}).call(this,n("df3c")["default"],n("3223")["default"])},e6db:function(t,e,n){var r=n("3b2d")["default"];t.exports=function(t,e){if("object"!=r(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var i=n.call(t,e||"default");if("object"!=r(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)},t.exports.__esModule=!0,t.exports["default"]=t.exports},ed45:function(t,e){t.exports=function(t){if(Array.isArray(t))return t},t.exports.__esModule=!0,t.exports["default"]=t.exports},eded:function(t,e,n){"use strict";(function(t){var r=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.base64ToPath=function(e){return new Promise((function(n,r){if("object"===("undefined"===typeof window?"undefined":(0,i.default)(window))&&"document"in window){e=e.split(",");var o=e[0].match(/:(.*?);/)[1],a=atob(e[1]),s=a.length,u=new Uint8Array(s);while(s--)u[s]=a.charCodeAt(s);return n((window.URL||window.webkitURL).createObjectURL(new Blob([u],{type:o})))}var c=e.match(/data\:\S+\/(\S+);/);c?c=c[1]:r(new Error("base64 error"));var l=Date.now()+"."+c;if("object"!==("undefined"===typeof plus?"undefined":(0,i.default)(plus)))if("object"===("undefined"===typeof t?"undefined":(0,i.default)(t))&&t.canIUse("getFileSystemManager")){var f=t.env.USER_DATA_PATH+"/"+l;t.getFileSystemManager().writeFile({filePath:f,data:e.replace(/^data:\S+\/\S+;base64,/,""),encoding:"base64",success:function(){n(f)},fail:function(t){r(t)}})}else r(new Error("not support"));else{var d=new plus.nativeObj.Bitmap("bitmap"+Date.now());d.loadBase64Data(e,(function(){var t="_doc/uniapp_temp/"+l;d.save(t,{},(function(){d.clear(),n(t)}),(function(t){d.clear(),r(t)}))}),(function(t){d.clear(),r(t)}))}}))},e.pathToBase64=function(e){return new Promise((function(n,r){if("object"===("undefined"===typeof window?"undefined":(0,i.default)(window))&&"document"in window){if("function"===typeof FileReader){var o=new XMLHttpRequest;return o.open("GET",e,!0),o.responseType="blob",o.onload=function(){if(200===this.status){var t=new FileReader;t.onload=function(t){n(t.target.result)},t.onerror=r,t.readAsDataURL(this.response)}},o.onerror=r,void o.send()}var a=document.createElement("canvas"),s=a.getContext("2d"),u=new Image;return u.onload=function(){a.width=u.width,a.height=u.height,s.drawImage(u,0,0),n(a.toDataURL()),a.height=a.width=0},u.onerror=r,void(u.src=e)}"object"!==("undefined"===typeof plus?"undefined":(0,i.default)(plus))?"object"===("undefined"===typeof t?"undefined":(0,i.default)(t))&&t.canIUse("getFileSystemManager")?t.getFileSystemManager().readFile({filePath:e,encoding:"base64",success:function(t){n("data:image/png;base64,"+t.data)},fail:function(t){r(t)}}):r(new Error("not support")):plus.io.resolveLocalFileSystemURL(function(t){if(0===t.indexOf("_www")||0===t.indexOf("_doc")||0===t.indexOf("_documents")||0===t.indexOf("_downloads"))return t;if(0===t.indexOf("file://"))return t;if(0===t.indexOf("/storage/emulated/0/"))return t;if(0===t.indexOf("/")){var e=plus.io.convertAbsoluteFileSystem(t);if(e!==t)return e;t=t.substr(1)}return"_www/"+t}(e),(function(t){t.file((function(t){var e=new plus.io.FileReader;e.onload=function(t){n(t.target.result)},e.onerror=function(t){r(t)},e.readAsDataURL(t)}),(function(t){r(t)}))}),(function(t){r(t)}))}))};var i=r(n("3b2d"))}).call(this,n("3223")["default"])},ee10:function(t,e){function n(t,e,n,r,i,o,a){try{var s=t[o](a),u=s.value}catch(c){return void n(c)}s.done?e(u):Promise.resolve(u).then(r,i)}t.exports=function(t){return function(){var e=this,r=arguments;return new Promise((function(i,o){var a=t.apply(e,r);function s(t){n(a,i,o,s,u,"next",t)}function u(t){n(a,i,o,s,u,"throw",t)}s(void 0)}))}},t.exports.__esModule=!0,t.exports["default"]=t.exports},f0ff:function(t,e,n){"use strict";(function(t){var r=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=r(n("7eb4")),o=r(n("ee10")),a=r(n("67ad")),s=r(n("0bdb")),u=r(n("5908")),c=n("2e55"),l=n("84f5"),f=r(n("63e6")),d=(n("20ce"),n("6152")),h=function(){function e(){(0,a.default)(this,e),this.scopeUserInfo="scope.userInfo"}return(0,s.default)(e,[{key:"getUserCode",value:function(){var t=(0,o.default)(i.default.mark((function t(){var e,n;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.isAuth();case 2:if(e=t.sent,n="",!e){t.next=8;break}return t.next=7,this.getCode();case 7:n=t.sent;case 8:return t.abrupt("return",n);case 9:case"end":return t.stop()}}),t,this)})));return function(){return t.apply(this,arguments)}}()},{key:"getUserProfile",value:function(){var e=this.getUserCode();return new Promise((function(n,r){t.getUserProfile({lang:"zh_CN",desc:"用于完善会员资料",success:function(t){e&&(t.code=e),n({userInfo:t,islogin:!1})},fail:function(t){r(t)}})}))}},{key:"authorize",value:function(){var e=this;return new Promise((function(n,r){if((0,c.checkLogin)())return n({userInfo:f.default.get(d.USER_INFO,!0),islogin:!0});t.authorize({scope:e.scopeUserInfo,success:function(){n({islogin:!1})},fail:function(t){r(t)}})}))}},{key:"getCode",value:function(){var e=(0,o.default)(i.default.mark((function e(){var n;return i.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.getProvider();case 2:return n=e.sent,e.abrupt("return",new Promise((function(e,r){t.login({provider:n,success:function(t){return t.code&&f.default.set(d.STATE_R_KEY,t.code,10800),e(t.code)},fail:function(){return r(null)}})})));case 4:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"getProvider",value:function(){return new Promise((function(e,n){t.getProvider({service:"oauth",success:function(t){e(t.provider)},fail:function(){e(!1)}})}))}},{key:"isAuth",value:function(){var e=this;return new Promise((function(n,r){t.getSetting({success:function(t){t.authSetting[e.scopeUserInfo],n(!0)},fail:function(){n(!1)}})}))}},{key:"authUserInfo",value:function(t,e){return new Promise((function(n,r){(0,l.login)(t,e).then((function(t){return"login"===t.data.type&&(u.default.commit("LOGIN",{token:t.data.token}),u.default.commit("SETUID",t.data.uid)),n(t)})).catch((function(t){return r(t)}))}))}}]),e}(),p=new h;e.default=p}).call(this,n("df3c")["default"])},f7bd:function(t,e,n){(function(e){var n=e.canIUse("editor");function r(t){for(var e={},n=t.split(","),r=n.length;r--;)e[n[r]]=!0;return e}t.exports={filter:null,highlight:null,onText:null,blankChar:r(" , ,\t,\r,\n,\f"),blockTags:r("address,article,aside,body,caption,center,cite,footer,header,html,nav,section"+(n?"":",pre")),ignoreTags:r("area,base,basefont,canvas,command,frame,input,isindex,keygen,link,map,meta,param,script,source,style,svg,textarea,title,track,use,wbr"+(n?",rp":"")+",embed,iframe"),richOnlyTags:r("a,colgroup,fieldset,legend,picture,table"+(n?",bdi,bdo,caption,rt,ruby":"")),selfClosingTags:r("area,base,basefont,br,col,circle,ellipse,embed,frame,hr,img,input,isindex,keygen,line,link,meta,param,path,polygon,rect,source,track,use,wbr"),trustAttrs:r("align,alt,app-id,author,autoplay,border,cellpadding,cellspacing,class,color,colspan,controls,data-src,dir,face,height,href,id,ignore,loop,media,muted,name,path,poster,rowspan,size,span,src,start,style,type,unit-id,width,xmlns"),boolAttrs:r("autoplay,controls,ignore,loop,muted"),trustTags:r("a,abbr,ad,audio,b,blockquote,br,code,col,colgroup,dd,del,dl,dt,div,em,fieldset,h1,h2,h3,h4,h5,h6,hr,i,img,ins,label,legend,li,ol,p,q,source,span,strong,sub,sup,table,tbody,td,tfoot,th,thead,tr,title,ul,video"+(n?",bdi,bdo,caption,pre,rt,ruby":"")),userAgentStyles:{address:"font-style:italic",big:"display:inline;font-size:1.2em",blockquote:"background-color:#f6f6f6;border-left:3px solid #dbdbdb;color:#6c6c6c;padding:5px 0 5px 10px",caption:"display:table-caption;text-align:center",center:"text-align:center",cite:"font-style:italic",dd:"margin-left:40px",img:"max-width:100%",mark:"background-color:yellow",picture:"max-width:100%",pre:"font-family:monospace;white-space:pre;overflow:scroll",s:"text-decoration:line-through",small:"display:inline;font-size:0.8em",u:"text-decoration:underline"}}}).call(this,n("3223")["default"])},f993:function(t,e,n){"use strict";(function(t){var r=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.checkOverdue=function(e){var n=i.HTTP_REQUEST_URL,r=i.HEADER;t.request({url:n+"/api/front/user",method:"GET",header:r,success:function(t){-1!==[41e4,410001,410002,401].indexOf(t.data.code)&&o.default.commit("LOGOUT")}})};var i=n("0a3e"),o=r(n("5908"))}).call(this,n("df3c")["default"])},fbb9:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setThemeColor=function(){switch(r.globalData.theme){case"theme1":return"#e93323";case"theme2":return"#FE5C2D";case"theme3":return"#42CA4D";case"theme4":return"#1DB0FC";case"theme5":return"#FF448F"}};var r=getApp()},ffbc:function(t,e,n){var r=n("67ad"),i=n("0bdb"),o=n("7ca3"),a=n("f7bd"),s=function(){"use strict";function t(e){var n=this;r(this,t),o(this,"getStyle",(function(t){return n.styles=new u(t,n.styles).parse()}));var i=Object.assign({},a.userAgentStyles);for(var s in e)i[s]=(i[s]?i[s]+";":"")+e[s];this.styles=i}return i(t,[{key:"match",value:function(t,e){var n,r=(n=this.styles[t])?n+";":"";if(e.class)for(var i,o=e.class.split(" "),a=0;i=o[a];a++)(n=this.styles["."+i])&&(r+=n+";");return(n=this.styles["#"+e.id])&&(r+=n+";"),r}}]),t}();t.exports=s;var u=function(){"use strict";function t(e,n){var i=this;r(this,t),o(this,"section",(function(){return i.data.substring(i.start,i.i)})),o(this,"isLetter",(function(t){return t>="a"&&t<="z"||t>="A"&&t<="Z"})),this.data=e,this.floor=0,this.i=0,this.list=[],this.res=n,this.state=this.Space}return i(t,[{key:"parse",value:function(){for(var t;t=this.data[this.i];this.i++)this.state(t);return this.res}},{key:"Space",value:function(t){"."==t||"#"==t||this.isLetter(t)?(this.start=this.i,this.state=this.Name):"/"==t&&"*"==this.data[this.i+1]?this.Comment():a.blankChar[t]||";"==t||(this.state=this.Ignore)}},{key:"Comment",value:function(){this.i=this.data.indexOf("*/",this.i)+1,this.i||(this.i=this.data.length),this.state=this.Space}},{key:"Ignore",value:function(t){"{"==t?this.floor++:"}"!=t||--this.floor||(this.state=this.Space)}},{key:"Name",value:function(t){a.blankChar[t]?(this.list.push(this.section()),this.state=this.NameSpace):"{"==t?(this.list.push(this.section()),this.Content()):","==t?(this.list.push(this.section()),this.Comma()):!this.isLetter(t)&&(t<"0"||t>"9")&&"-"!=t&&"_"!=t&&(this.state=this.Ignore)}},{key:"NameSpace",value:function(t){"{"==t?this.Content():","==t?this.Comma():a.blankChar[t]||(this.state=this.Ignore)}},{key:"Comma",value:function(){while(a.blankChar[this.data[++this.i]]);"{"==this.data[this.i]?this.Content():(this.start=this.i--,this.state=this.Name)}},{key:"Content",value:function(){this.start=++this.i,-1==(this.i=this.data.indexOf("}",this.i))&&(this.i=this.data.length);for(var t,e=this.section(),n=0;t=this.list[n++];)this.res[t]?this.res[t]+=";"+e:this.res[t]=e;this.list=[],this.state=this.Space}}]),t}()}}]);