(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["common/main"],{"0c5a":function(e,t,n){},"1bed":function(e,t,n){"use strict";(function(e,t){var a=n("47a9"),o=n("3b2d"),r=a(n("7ca3"));n("9e89");var c=a(n("3240")),u=a(n("6ca5")),i=a(n("5908")),f=a(n("63e6")),l=a(n("9bad")),d=a(n("0a3e")),s=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==o(e)&&"function"!==typeof e)return{default:e};var n=p(t);if(n&&n.has(e))return n.get(e);var a={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var c in e)if("default"!==c&&Object.prototype.hasOwnProperty.call(e,c)){var u=r?Object.getOwnPropertyDescriptor(e,c):null;u&&(u.get||u.set)?Object.defineProperty(a,c,u):a[c]=e[c]}a.default=e,n&&n.set(e,a);return a}(n("3f87"));function p(e){if("function"!==typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(p=function(e){return e?n:t})(e)}function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach((function(t){(0,r.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}e.__webpack_require_UNI_MP_PLUGIN__=n;c.default.component("skeleton",(function(){n.e("components/skeleton/index").then(function(){return resolve(n("3154"))}.bind(null,n)).catch(n.oe)})),c.default.prototype.$util=l.default,c.default.prototype.$config=d.default,c.default.prototype.$Cache=f.default,c.default.prototype.$eventHub=new c.default,c.default.config.productionTip=!1,c.default.prototype.$Order=s,u.default.mpType="app";var g=new c.default(b(b({},u.default),{},{store:i.default,Cache:f.default}));t(g).$mount()}).call(this,n("3223")["default"],n("df3c")["createApp"])},"6ca5":function(e,t,n){"use strict";n.r(t);var a=n("f447");for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);n("f0c8");var r=n("828b"),c=Object(r["a"])(a["default"],void 0,void 0,!1,null,null,null,!1,void 0,void 0);t["default"]=c.exports},c1c1:function(e,t,n){"use strict";(function(e){var a=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(n("7eb4")),r=a(n("ee10")),c=(n("2e55"),n("0a3e")),u=(a(n("50a1")),a(n("f0ff"))),i=(a(n("1f75")),n("ad96")),f=(n("8f59"),n("20ce")),l={globalData:{spread:0,code:0,isLogin:!1,userInfo:{},MyMenus:[],windowHeight:0,navHeight:0,navH:0,id:0,isIframe:!1,theme:"theme1"},onLaunch:function(t){var n=this,a=this,o=e.getUpdateManager();if(o.onCheckForUpdate((function(t){t.hasUpdate&&o.onUpdateReady((function(t){e.showModal({title:"更新提示",content:"发现新版本，是否重启应用?",cancelColor:"#eeeeee",confirmColor:"#FF0000",success:function(e){e.confirm&&o.applyUpdate()}})}))})),o.onUpdateFailed((function(t){e.showModal({title:"提示",content:"检查到有新版本，但下载失败，请检查网络设置",success:function(e){e.confirm&&o.applyUpdate()}})})),""==c.HTTP_REQUEST_URL)return console.error("请配置根目录下的config.js文件中的 'HTTP_REQUEST_URL'\n\n请修改开发者工具中【详情】->【AppID】改为自己的Appid\n\n请前往后台【小程序】->【小程序配置】填写自己的 appId and AppSecret"),!1;if(t.query.hasOwnProperty("scene"))switch(t.scene){case 1047:case 1048:case 1049:case 1001:var r=this.$util.getUrlParams(decodeURIComponent(t.query.scene));a.globalData=this.$util.formatMpQrCodeData(r);break}t.spread&&(a.globalData.spread=t.spread),e.getSystemInfo({success:function(e){a.globalData.navHeight=e.statusBarHeight*(750/e.windowWidth)+91}});var l=e.getMenuButtonBoundingClientRect();a.globalData.navH=2*l.top+l.height/2,this.$store.getters.isLogin?setTimeout((function(){(0,f.spread)(a.globalData.spread).then((function(e){})).catch((function(e){}))}),2e3):u.default.getCode().then((function(e){u.default.authUserInfo(e,n.globalData.spread)})).catch((function(t){e.hideLoading()})),(0,i.getTheme)().then((function(e){a.globalData.theme="theme".concat(Number(e.data.value)),a.$Cache.set("theme",a.globalData.theme)}))},mounted:function(){var e=this;return(0,r.default)(o.default.mark((function t(){return o.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.$store.getters.isLogin||e.$Cache.get("USER_INFO")){t.next=3;break}return t.next=3,e.$store.dispatch("USERINFO");case 3:case"end":return t.stop()}}),t)})))()},methods:{},onShow:function(){},onHide:function(){}};t.default=l}).call(this,n("df3c")["default"])},f0c8:function(e,t,n){"use strict";var a=n("0c5a"),o=n.n(a);o.a},f447:function(e,t,n){"use strict";n.r(t);var a=n("c1c1"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=o.a}},[["1bed","common/runtime","common/vendor"]]]);