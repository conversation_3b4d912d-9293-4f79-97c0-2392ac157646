package com.zbkj.service.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.zbkj.common.constants.Constants;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.system.SystemCity;
import com.zbkj.common.model.user.User;
import com.zbkj.common.model.user.UserAddress;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.UserAddressRecognitionRequest;
import com.zbkj.common.request.UserAddressRequest;
import com.zbkj.service.address.AddressParse;
import com.zbkj.service.address.ParseResult;
import com.zbkj.service.address.TencentAddressParse;
import com.zbkj.service.dao.UserAddressDao;
import com.zbkj.service.dto.AddressInfo;
import com.zbkj.service.service.SystemCityService;
import com.zbkj.service.service.SystemConfigService;
import com.zbkj.service.service.UserAddressService;
import com.zbkj.service.service.UserService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import java.util.Base64;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * UserAddressServiceImpl 接口实现








 */
@Service
public class UserAddressServiceImpl extends ServiceImpl<UserAddressDao, UserAddress> implements UserAddressService {

    @Resource
    private UserAddressDao dao;

    @Autowired
    private SystemCityService systemCityService;

    @Autowired
    private UserService userService;


    @Autowired
    private SystemConfigService systemConfigService;


    /**
     * 列表
     *
     * @return List<UserAddress>
     */
    @Override
    public List<UserAddress> getList(PageParamRequest pageParamRequest) {
        Integer UserId = userService.getUserIdException();
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        LambdaQueryWrapper<UserAddress> lqw = Wrappers.lambdaQuery();
        lqw.select(UserAddress::getId, UserAddress::getRealName, UserAddress::getPhone, UserAddress::getProvince,
                UserAddress::getCity, UserAddress::getDistrict, UserAddress::getDetail, UserAddress::getIsDefault);
        lqw.eq(UserAddress::getUid, UserId);
        lqw.eq(UserAddress::getIsDel, false);
        lqw.orderByDesc(UserAddress::getIsDefault);
        lqw.orderByDesc(UserAddress::getUpdateTime);
        lqw.orderByDesc(UserAddress::getId);
        return dao.selectList(lqw);
    }

    /**
     * 创建地址
     *
     * @param request UserAddressRequest 参数
     * @return List<UserAddress>
     */
    @Override
    public UserAddress create(UserAddressRequest request) {
        UserAddress userAddress = new UserAddress();
        BeanUtils.copyProperties(request, userAddress);
        userAddress.setCity(request.getAddress().getCity());
        userAddress.setCityId(request.getAddress().getCityId());
        userAddress.setDistrict(request.getAddress().getDistrict());
        userAddress.setProvince(request.getAddress().getProvince());

        // 添加地址时cityId和城市名称不能同时为空，如果id为空，必须用城市名称自查后set CityId
        if (request.getAddress().getCityId() == 0 && StrUtil.isBlank(request.getAddress().getDistrict())) {
            throw new CrmebException("请选择正确城市数据");
        }
        if (StrUtil.isNotBlank(request.getAddress().getCity()) && request.getAddress().getCityId() == 0) {
            SystemCity systemCity = systemCityService.getCityByCityName(request.getAddress().getCity());
            if (ObjectUtil.isNull(systemCity)) {
                throw new CrmebException("当前城市未找到");
            }
            SystemCity currentCity = systemCityService.getByAreaNameAndPid(request.getAddress().getDistrict(), systemCity.getCityId());
            if (ObjectUtil.isNull(currentCity)) throw new CrmebException("当前城市区域未找到！");
            userAddress.setCityId(currentCity.getCityId());
        }

        if (request.getAddress().getCityId() > 0 && StrUtil.isNotBlank(request.getAddress().getDistrict())) {
            checkCity(userAddress.getCityId());
        }
        userAddress.setUid(userService.getUserIdException());
        if (userAddress.getIsDefault()) {
            //把当前用户其他默认地址取消
            cancelDefault(userAddress.getUid());
        }
        saveOrUpdate(userAddress);
        return userAddress;
    }

    /**
     * 地址智能识别
     *
     * @param request 地址请求参数
     * @return UserAddress
     */
    @Override
    public UserAddress addressRecognition(UserAddressRecognitionRequest request) {

        try {
            AddressInfo result = this.getAddressParse(request.getAddressRecognition());
            if (null == result) {
                return null;
//            throw new CrmebException("请正确添加地址");
            }


            //省市县
            SystemCity province = systemCityService.getCityByParseResult(result.getProvince(), Constants.NUM_ZERO, null);

            SystemCity city1 = systemCityService.getCityByParseResult(result.getCity(), Constants.NUM_ONE, province == null ? null : province.getCityId());
            if (null != city1) {
                UserAddress userAddress = new UserAddress();
                if (!StringUtils.isEmpty(result.getDistrict())) {
                    SystemCity area = systemCityService.getCityByParseResult(result.getDistrict(), Constants.NUM_TWO, city1.getCityId());
                    if (null != area) {
                        userAddress.setDistrict(area.getName());
                        userAddress.setCityId(area.getCityId());
                    }
                }
                if (request.getId() != null) {
                    userAddress.setId(request.getId());
                }
                userAddress.setUid(userService.getUserId());
                userAddress.setCity(city1.getName());
                if (userAddress.getCityId() == null) {
                    userAddress.setCityId(city1.getCityId());
                }
                userAddress.setProvince(province== null ? result.getProvince() :  province.getName());
                userAddress.setDetail(result.getAddressDetail());
                userAddress.setPhone(result.getMobileNO() == null ? "" : result.getMobileNO());
                userAddress.setRealName(result.getPersonalName());
                if (StrUtil.isNotBlank(result.getDivisionZip())) {
                    userAddress.setPostCode(Integer.valueOf(result.getDivisionZip()));
                }

                userAddress.setIsDefault(false);
                if (userAddress.getIsDefault()) {
                    //把当前用户其他默认地址取消
                    cancelDefault(userAddress.getUid());
                }
                saveOrUpdate(userAddress);
                return userAddress;
            }else if( null != result.getDistrict() ){
                UserAddress userAddress = new UserAddress();
                if (!StringUtils.isEmpty(result.getDistrict())) {
                    SystemCity area = systemCityService.getCityByParseResult(result.getDistrict(), Constants.NUM_TWO, null);
                    if (null != area) {
                        userAddress.setDistrict(area.getName());
                        userAddress.setCityId(area.getCityId());
                    }
                }
                if (request.getId() != null) {
                    userAddress.setId(request.getId());
                }
                userAddress.setUid(userService.getUserId());

                userAddress.setProvince(province== null ? result.getProvince() :  province.getName());
                userAddress.setDetail(result.getAddressDetail());
                userAddress.setPhone(result.getMobileNO() == null ? "" : result.getMobileNO());
                userAddress.setRealName(result.getPersonalName());
                if (StrUtil.isNotBlank(result.getDivisionZip())) {
                    userAddress.setPostCode(Integer.valueOf(result.getDivisionZip()));
                }

                userAddress.setIsDefault(false);
                if (userAddress.getIsDefault()) {
                    //把当前用户其他默认地址取消
                    cancelDefault(userAddress.getUid());
                }
                saveOrUpdate(userAddress);
                return userAddress;
            }


        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;

    }


    private String calcAuthorization(String source, String secretId, String secretKey, String datetime)
            throws NoSuchAlgorithmException, UnsupportedEncodingException, InvalidKeyException {
        String signStr = "x-date: " + datetime + "\n" + "x-source: " + source;
        Mac mac = Mac.getInstance("HmacSHA1");
        Key sKey = new SecretKeySpec(secretKey.getBytes("UTF-8"), mac.getAlgorithm());
        mac.init(sKey);
        byte[] hash = mac.doFinal(signStr.getBytes("UTF-8"));
        String sig = Base64.getEncoder().encodeToString(hash);

        String auth = "hmac id=\"" + secretId + "\", algorithm=\"hmac-sha1\", headers=\"x-date x-source\", signature=\"" + sig + "\"";
        return auth;
    }

    private String urlencode(Map<?, ?> map) throws UnsupportedEncodingException {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<?, ?> entry : map.entrySet()) {
            if (sb.length() > 0) {
                sb.append("&");
            }
            sb.append(String.format("%s=%s",
                    URLEncoder.encode(entry.getKey().toString(), "UTF-8"),
                    URLEncoder.encode(entry.getValue().toString(), "UTF-8")
            ));
        }
        return sb.toString();
    }


    private AddressInfo getAddressParse(String address) throws NoSuchAlgorithmException, UnsupportedEncodingException, InvalidKeyException {


        //云市场分配的密钥Id
        String secretId = systemConfigService.getValueByKey("SecretID");
        //云市场分配的密钥Key
        String secretKey = systemConfigService.getValueByKey("SecretKey");
        if (StrUtil.isBlank(secretId) || StrUtil.isBlank(secretKey)) {
            throw new CrmebException("地址解析未配置，请联系管理员配置");
        }
        String source = "market";

        Calendar cd = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss 'GMT'", Locale.US);
        sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
        String datetime = sdf.format(cd.getTime());
        // 签名
        String auth = calcAuthorization(source, secretId, secretKey, datetime);
        // 请求方法
        String method = "GET";
        // 请求头
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("X-Source", source);
        headers.put("X-Date", datetime);
        headers.put("Authorization", auth);

        // 查询参数
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("UseFullMunicipality", "");
        queryParams.put("address", address);
        // body参数
        Map<String, String> bodyParams = new HashMap<String, String>();

        // url参数拼接
        String url = "https://service-7daeqy5n-1301652365.bj.apigw.tencentcs.com/release/address_parse";
        if (!queryParams.isEmpty()) {
            url += "?" + urlencode(queryParams);
        }

        BufferedReader in = null;
        try {
            URL realUrl = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) realUrl.openConnection();
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(5000);
            conn.setRequestMethod(method);

            // request headers
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                conn.setRequestProperty(entry.getKey(), entry.getValue());
            }

            // request body
            Map<String, Boolean> methods = new HashMap<>();
            methods.put("POST", true);
            methods.put("PUT", true);
            methods.put("PATCH", true);
            Boolean hasBody = methods.get(method);
            if (hasBody != null) {
                conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");

                conn.setDoOutput(true);
                DataOutputStream out = new DataOutputStream(conn.getOutputStream());
                out.writeBytes(urlencode(bodyParams));
                out.flush();
                out.close();
            }

            // 定义 BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            String line;
            String result = "";
            while ((line = in.readLine()) != null) {
                result += line;
            }
            JSONObject jsonObject = JSON.parseObject(result);
            if (jsonObject.get("Result") != null) {
                Map<String, JSONObject> objectMap = (Map<String, JSONObject>) jsonObject.get("Result");
                JSONObject info = objectMap.get("data");
                AddressInfo addressInfo = JSONObject.parseObject(info.toJSONString(), AddressInfo.class);
                return addressInfo;
            }

        } catch (Exception e) {
            System.out.println(e);
            e.printStackTrace();
        } finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception e2) {
                e2.printStackTrace();
            }
        }
        return null;
    }


    /**
     * 设置默认
     *
     * @param id Integer id
     * @return UserAddress
     */
    @Override
    public Boolean def(Integer id) {
        //把当前用户其他默认地址取消
        cancelDefault(userService.getUserIdException());
        UserAddress userAddress = new UserAddress();
        userAddress.setId(id);
        userAddress.setUid(userService.getUserIdException());
        userAddress.setIsDefault(true);
        return updateById(userAddress);
    }

    /**
     * 删除
     *
     * @param id Integer id
     * @return UserAddress
     */
    @Override
    public Boolean delete(Integer id) {
        //把当前用户其他默认地址取消
        LambdaQueryWrapper<UserAddress> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(UserAddress::getId, id);
        lambdaQueryWrapper.eq(UserAddress::getUid, userService.getUserIdException());
        dao.delete(lambdaQueryWrapper);
        return true;
    }

    /**
     * 获取默认地址
     *
     * @return UserAddress
     */
    @Override
    public UserAddress getDefault() {
        //把当前用户其他默认地址取消
        LambdaQueryWrapper<UserAddress> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(UserAddress::getIsDefault, true);
        lambdaQueryWrapper.eq(UserAddress::getUid, userService.getUserId());
        lambdaQueryWrapper.eq(UserAddress::getIsDel, false);
        return dao.selectOne(lambdaQueryWrapper);
    }

    @Override
    public UserAddress getById(Integer addressId) {
        LambdaQueryWrapper<UserAddress> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(UserAddress::getId, addressId);
        lambdaQueryWrapper.eq(UserAddress::getIsDel, false);
        return dao.selectOne(lambdaQueryWrapper);
    }

    /**
     * 获取地址详情
     *
     * @param id 地址id
     * @return UserAddress
     */
    @Override
    public UserAddress getDetail(Integer id) {
        Integer UserId = userService.getUserIdException();
        LambdaQueryWrapper<UserAddress> lqw = Wrappers.lambdaQuery();
        lqw.select(UserAddress::getId, UserAddress::getRealName, UserAddress::getPhone, UserAddress::getProvince,
                UserAddress::getCity, UserAddress::getDistrict, UserAddress::getDetail, UserAddress::getIsDefault);
        lqw.eq(UserAddress::getId, id);
        lqw.eq(UserAddress::getUid, UserId);
        lqw.eq(UserAddress::getIsDel, false);
        return dao.selectOne(lqw);
    }

    /**
     * 获取默认地址
     *
     * @return UserAddress
     */
    @Override
    public UserAddress getDefaultByUid(Integer uid) {
        LambdaQueryWrapper<UserAddress> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(UserAddress::getIsDefault, true);
        lambdaQueryWrapper.eq(UserAddress::getUid, uid);
        return dao.selectOne(lambdaQueryWrapper);
    }

    /**
     * 检测城市id是否合法
     *
     * @param cityId Integer 城市id
     */
    private void checkCity(Integer cityId) {
        //检测城市Id是否存在
        SystemCity systemCity = systemCityService.getByAreaId(cityId);
        if (ObjectUtil.isNull(systemCity)) {
            throw new CrmebException("请选择正确的城市");
        }
    }

    /**
     * 取消默认地址
     *
     * @param userId Integer 城市id
     */
    private void cancelDefault(Integer userId) {
        //检测城市Id是否存在
        UserAddress userAddress = new UserAddress();
        userAddress.setIsDefault(false);
        LambdaQueryWrapper<UserAddress> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(UserAddress::getUid, userId);
        update(userAddress, lambdaQueryWrapper);
    }

}

